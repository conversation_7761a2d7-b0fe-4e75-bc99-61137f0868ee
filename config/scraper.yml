# YAML configuration for scrape_pacer step
step_name: "scrape_pacer"
input_source: null # Or path to court list if applicable
output_location: "data/" # Directory where scraper outputs raw data

# Date Configuration
date: '06/03/25' # MM/DD/YY format, set as per main params
start_date: '06/03/25'   # MM/DD/YY format or null
end_date: null     # MM/DD/YY format or null

# LLM Configuration (generally not used by scraper but part of main params)
llm_provider: 'deepseek'

# Scraper Configuration
scraper: True
headless: False
reset_chrome: False
docket_num: [ ]
process_single_court: []  # Leave empty to use start_at_court MAKE SURE process_review_cases && process
#multiple_courts: [ ]  # List of dictionaries with court_id and docket_num
process_review_cases: False  # Process cases that need review (renamed from process_irrelevant_cases)
process_irrelevant_cases: False  # DEPRECATED: Use process_review_cases instead
reprocess_failed: False
skip_courts: [ 'ohndad', 'nmid', 'gud','vid' ]
start_after_court: null
start_at_court: null
html_only: False
run_parallel: True  # This ensures sequential processing

  # 1:25-cv-04871-RMB-SAK
  # 2:25-cv-04359
  # 2:25-cv-04360
  # 2:25-cv-04591
  # 2:25-cv-04814


  #[
  #  {"court_id": "ctd",
  #  "docket_num": "3:25-cv-00769"},
  #  {"court_id": "ctd",
  #    "docket_num": "3:25-cv-00770"},
  #  {"court_id": "ctd",
  #    "docket_num": "3:25-cv-00771"},
  #  {"court_id": "med",
#    "docket_num": "2:25-cv-00237"},
#  {"court_id": "txed",
#    "docket_num": "4:25-cv-00500"}
#]
