# Automated Rule Optimization System - Detailed Plan

## Overview
Build an unsupervised, parallel optimization system that automatically discovers optimal text field combinations, clusters ads into tight categories, and generates non-overlapping rules. The system will leverage your Mac M4's 128GB RAM for massive parallel processing.

## Architecture

### Phase 1: Data Preparation & Field Combination Discovery

#### 1.1 Field Combination Generator
```python
# Generate all meaningful combinations of text fields
field_combinations = [
    ["Title", "Body"],
    ["Title", "LinkDescription"],
    ["PageName", "Title"],
    ["PageName", "Body"],
    ["Title", "Body", "LinkDescription"],
    ["PageName", "Title", "Body"],
    # ... up to 15 combinations
]
```

#### 1.2 Deduplication Strategy
- Create hash-based deduplication for each field combination
- Track which ads collapse into same group under different combinations
- Identify "stable groups" (same ads group together across multiple field combinations)

### Phase 2: Parallel Clustering Optimization

#### 2.1 Multi-threaded Processing Architecture
```
Main Process
├── Field Combination Workers (15 parallel)
│   ├── Worker 1: ["Title", "Body"]
│   ├── Worker 2: ["PageName", "Title"]
│   └── ... 
├── Clustering Algorithm Workers (per combination)
│   ├── DBSCAN (multiple eps/min_samples)
│   ├── HDBSCAN (multiple min_cluster_size)
│   └── Agglomerative (multiple thresholds)
└── Rule Generation Workers
```

#### 2.2 Clustering Optimization Metrics
- **Silhouette Score**: Measure cluster cohesion
- **Noise Ratio**: Percentage of points labeled as noise
- **Cluster Stability**: How consistent clusters are across embeddings
- **Inter-cluster Similarity**: Minimize overlap between clusters

### Phase 3: Intelligent Rule Generation

#### 3.1 Category-Specific Strategies

**For Privacy/Data Breach Categories:**
```python
rule_strategies = {
    "Data Privacy": {
        "primary_key": "company_name",  # From NER
        "secondary_patterns": ["data breach", "personal information"],
        "exclude_overlap": True
    },
    "False Advertising": {
        "primary_key": "company_or_product",
        "secondary_patterns": ["misleading", "false claims"],
        "industry_grouping": True
    }
}
```

#### 3.2 Rule Optimization Algorithm
1. Extract candidate terms using improved TF-IDF
2. Calculate mutual information between terms and clusters
3. Use genetic algorithm to optimize rule combinations:
   - Fitness function: (precision × recall) - (overlap_penalty)
   - Mutations: add/remove terms, adjust thresholds
   - Crossover: combine successful rules from different clusters

### Phase 4: Incremental Learning System

#### 4.1 New Data Integration
```python
def process_new_ads(new_ads, existing_model):
    # 1. Generate embeddings for new ads
    # 2. Check fit with existing clusters
    # 3. If confidence < threshold, flag for new rule
    # 4. Retrain only affected clusters
```

## Implementation Plan

### Stage 1: Core Infrastructure (Week 1)
1. **Build Parallel Processing Framework**
   - Create `parallel_optimizer.py` with multiprocessing.Pool
   - Implement shared memory for embeddings (using Ray or shared_memory)
   - Set up progress tracking and result aggregation

2. **Implement Field Combination Engine**
   - Create `field_combination_analyzer.py`
   - Build deduplication logic for each combination
   - Generate combination quality metrics

### Stage 2: Clustering Optimization (Week 2)
1. **Multi-Algorithm Clustering**
   ```python
   class ClusterOptimizer:
       def __init__(self, n_workers=15):
           self.algorithms = {
               'dbscan': [(eps, min_samples) 
                         for eps in [0.1, 0.2, 0.3, 0.4]
                         for min_samples in [3, 5, 10, 15]],
               'hdbscan': [{'min_cluster_size': s} 
                          for s in [10, 20, 30, 50]],
               'agglomerative': [{'n_clusters': None, 'distance_threshold': t}
                               for t in [0.5, 1.0, 1.5, 2.0]]
           }
   ```

2. **Parallel Evaluation**
   - Run all algorithm/parameter combinations in parallel
   - Score each based on cluster quality metrics
   - Select best per field combination

### Stage 3: Rule Generation Engine (Week 3)
1. **Category-Aware Rule Builder**
   ```python
   class CategoryAwareRuleBuilder:
       def __init__(self):
           self.category_strategies = {
               "Data Privacy": DataPrivacyStrategy(),
               "False Advertising": CompanyBasedStrategy(),
               "Products Liability": ProductBasedStrategy()
           }
       
       def generate_rules(self, cluster, category_hint=None):
           # Use NER entities as primary discriminators
           # Add pattern-based secondary rules
           # Implement overlap detection and resolution
   ```

2. **Overlap Minimization**
   - Build term overlap matrix across all clusters
   - Use integer linear programming to select non-overlapping term sets
   - Implement hierarchical rule structure (general → specific)

### Stage 4: Monitoring & Adaptation (Week 4)
1. **Performance Dashboard**
   - Real-time clustering metrics
   - Rule effectiveness tracking
   - Overlap detection alerts

2. **Incremental Learning Pipeline**
   - Weekly retraining scheduler
   - Drift detection for existing clusters
   - Automated rule proposal system

## Technical Stack

### Required Libraries
```yaml
dependencies:
  # Core ML
  - scikit-learn>=1.3.0
  - hdbscan>=0.8.33
  - sentence-transformers>=2.2.0
  
  # Parallel Processing
  - ray>=2.8.0  # For distributed computing
  - joblib>=1.3.0
  - multiprocessing-logging>=0.3.4
  
  # Optimization
  - optuna>=3.4.0  # Hyperparameter optimization
  - pulp>=2.7.0  # Linear programming for overlap
  
  # Analysis
  - pandas>=2.0.0
  - numpy>=1.24.0
  - networkx>=3.0  # For cluster relationship graphs
  
  # Visualization
  - plotly>=5.17.0  # Interactive cluster viz
  - dash>=2.14.0  # Real-time dashboard
```

### System Configuration
```python
# config/optimization_config.yaml
system:
  max_workers: 15  # For M4 Mac
  memory_limit: 100GB  # Leave 28GB for system
  chunk_size: 10000  # Process in chunks
  
clustering:
  algorithms: ["dbscan", "hdbscan", "agglomerative"]
  min_cluster_size: 10
  max_noise_ratio: 0.15
  
rule_generation:
  min_precision: 0.85
  max_overlap: 0.10
  use_ner_entities: true
  category_specific_rules: true
```

## Execution Plan

### Week 1: Foundation
- [ ] Set up parallel processing framework
- [ ] Implement field combination analysis
- [ ] Create embedding cache with mmap for shared memory

### Week 2: Clustering
- [ ] Implement multi-algorithm clustering
- [ ] Build cluster quality evaluation
- [ ] Create cluster stability testing

### Week 3: Rules
- [ ] Build category-aware rule generation
- [ ] Implement overlap minimization
- [ ] Create rule validation framework

### Week 4: Integration
- [ ] Build monitoring dashboard
- [ ] Implement incremental learning
- [ ] Create automated testing suite

## Success Metrics

1. **Clustering Quality**
   - Average silhouette score > 0.6
   - Noise ratio < 15%
   - Inter-cluster similarity < 0.2

2. **Rule Effectiveness**
   - Precision > 85%
   - Recall > 80%
   - Rule overlap < 10%

3. **Performance**
   - Full dataset processing < 2 hours
   - Incremental updates < 10 minutes
   - Memory usage < 100GB

## Next Steps

1. **Approve this plan**
2. **Prioritize which components to build first**
3. **Decide on specific category handling rules**
4. **Set up development environment with Ray**

## Key Innovations

### 1. Field Combination Optimization
Instead of using a fixed combination (like Summary), the system automatically discovers which field combinations produce the most stable and meaningful clusters. This solves the LLM temperature issue where identical ads have different summaries.

### 2. Parallel Architecture
Leverages all 128GB RAM and M4 processing power to evaluate hundreds of clustering configurations simultaneously, reducing optimization time from days to hours.

### 3. Category-Specific Intelligence
- **Data Privacy/Breaches**: Uses company names from NER as primary grouping
- **False/Deceptive Advertising**: Groups by company/industry
- **Products Liability**: Groups by product and harm type

### 4. Incremental Learning
New ads are automatically evaluated against existing clusters. Only creates new rules when confidence is low, preventing rule proliferation.

### 5. Overlap Prevention
Uses linear programming to ensure rules don't overlap, creating clear boundaries between categories.

## File Structure
```
lexgenius/
├── src/
│   ├── optimization/
│   │   ├── __init__.py
│   │   ├── parallel_optimizer.py
│   │   ├── field_combination_analyzer.py
│   │   ├── cluster_optimizer.py
│   │   ├── rule_generator.py
│   │   └── incremental_learner.py
│   ├── strategies/
│   │   ├── __init__.py
│   │   ├── data_privacy_strategy.py
│   │   ├── false_advertising_strategy.py
│   │   └── products_liability_strategy.py
│   └── monitoring/
│       ├── __init__.py
│       ├── dashboard.py
│       └── metrics_tracker.py
├── config/
│   └── optimization_config.yaml
├── tests/
│   └── optimization/
│       ├── test_clustering.py
│       └── test_rules.py
└── notebooks/
    └── optimization_analysis.ipynb
```