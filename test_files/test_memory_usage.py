#!/usr/bin/env python
"""Test script to monitor memory usage during classification"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import psutil
import time
from src.scripts.hybrid_classifier import HybridClassifier

def get_memory_usage():
    """Get current memory usage in MB"""
    process = psutil.Process()
    return process.memory_info().rss / (1024 * 1024)

def test_memory_usage():
    """Monitor memory usage during classifier initialization and usage"""
    
    print("Testing memory usage with lazy loading...")
    print(f"Initial memory: {get_memory_usage():.1f} MB")
    
    # Initialize classifier
    print("\nInitializing classifier...")
    start_mem = get_memory_usage()
    
    classifier = HybridClassifier(
        campaign_config_path='src/config/fb_ad_categorizer/campaign_config.json',
        text_processing_fields=['Title', 'Body', 'Summary'],
        embedder_cache='embedding_roberta-large-v1.pkl',
        ner_cache_file='ner_cache_results.pkl',
        max_workers=1
    )
    
    after_init_mem = get_memory_usage()
    print(f"Memory after init: {after_init_mem:.1f} MB (delta: +{after_init_mem - start_mem:.1f} MB)")
    
    # Test classification (this should trigger cache loading)
    test_ad = {
        'AdArchiveID': 'memory_test',
        'Title': 'Test Ad',
        'Body': 'This is a test ad for memory usage monitoring',
        'Summary': 'Test summary'
    }
    
    print("\nClassifying one ad (should trigger cache loading)...")
    before_classify = get_memory_usage()
    
    result = classifier._classify_single_ad_item(test_ad)
    
    after_classify = get_memory_usage()
    print(f"Memory after classification: {after_classify:.1f} MB (delta: +{after_classify - before_classify:.1f} MB)")
    
    print(f"\nTotal memory usage: {after_classify:.1f} MB")
    print(f"Total increase from start: +{after_classify - start_mem:.1f} MB")
    
    # Check cache status
    if hasattr(classifier.embedder, '_cache_loaded'):
        print(f"\nEmbedding cache loaded: {classifier.embedder._cache_loaded}")
    if hasattr(classifier, '_ner_cache_loaded'):
        print(f"NER cache loaded: {classifier._ner_cache_loaded}")

if __name__ == "__main__":
    test_memory_usage()