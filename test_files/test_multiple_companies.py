#!/usr/bin/env python
"""Test multiple ads to verify company extraction"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.scripts.hybrid_classifier import HybridClassifier

# Initialize classifier
classifier = HybridClassifier(
    campaign_config_path='src/config/fb_ad_categorizer/campaign_config.json',
    text_processing_fields=['ad_creative_bodies', 'page_name'],
    embedder_model_name='all-MiniLM-L6-v2',
    embedder_cache_file='test_multiple_cache.pkl',
    company_ner_model='en_core_web_sm',
    use_llm=False,
    ner_cache_file='test_multiple_ner_cache.pkl',
    log_level='INFO'
)

# Test ads
test_ads = [
    {
        'id': 'test1',
        'page_name': 'Test Law Firm',
        'ad_creative_bodies': [{'text': 'Monsanto Roundup weed killer causing cancer? Contact us for legal help.'}],
        'ad_delivery_start_time': '2024-01-01'
    },
    {
        'id': 'test2',
        'page_name': 'Another Law Firm',
        'ad_creative_bodies': [{'text': 'Exposed to AFFF firefighting foam? You may have a claim against 3M and other manufacturers.'}],
        'ad_delivery_start_time': '2024-01-01'
    },
    {
        'id': 'test3',
        'page_name': 'Legal Services LLC',
        'ad_creative_bodies': [{'text': 'Depo-Provera birth control shot linked to brain tumors. Pfizer may be liable.'}],
        'ad_delivery_start_time': '2024-01-01'
    },
    {
        'id': 'test4',
        'page_name': 'Morgan & Morgan',
        'ad_creative_bodies': [{'text': 'Abbott baby formula recall - premature infants affected by NEC.'}],
        'ad_delivery_start_time': '2024-01-01'
    },
    {
        'id': 'test5',
        'page_name': 'Test Firm',
        'ad_creative_bodies': [{'text': 'Oxbryta sickle cell medication recalled by Pfizer due to increased deaths.'}],
        'ad_delivery_start_time': '2024-01-01'
    }
]

print("Testing multiple ads for company extraction...")
print("=" * 60)

# Classify ads
results = classifier.classify_batch(test_ads)

# Display results
for i, result in enumerate(results):
    print(f"\nAd {i+1}:")
    print(f"  Campaign: {result.campaign}")
    print(f"  Company: {result.company or 'N/A'}")
    print(f"  Original: {result.original_name or 'N/A'}")
    print(f"  Method: {result.method}")
    print(f"  Confidence: {result.confidence:.2f}")

# Check if companies match expected values
expected_companies = {
    'test1': 'Monsanto',
    'test2': '3M',
    'test3': 'Pfizer',
    'test4': 'Abbott Laboratories',
    'test5': 'Pfizer'
}

print("\n" + "=" * 60)
print("Verification:")
for i, (ad, result) in enumerate(zip(test_ads, results)):
    ad_id = ad['id']
    expected = expected_companies.get(ad_id, 'Unknown')
    actual = result.company or 'N/A'
    status = "✓" if actual == expected else "✗"
    print(f"{status} Ad {ad_id}: Expected '{expected}', Got '{actual}'")

# Clean up
import os
for cache_file in ['test_multiple_cache.pkl', 'test_multiple_ner_cache.pkl']:
    if os.path.exists(cache_file):
        os.remove(cache_file)