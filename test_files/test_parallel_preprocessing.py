#!/usr/bin/env python
"""Test parallel preprocessing functionality"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import json
import time
from src.scripts.hybrid_classifier import HybridClassifier
from src.scripts.parallel_preprocessing import ParallelPreprocessor, parallel_preprocess_ads

def test_parallel_preprocessing():
    """Test parallel preprocessing with sample ads"""
    
    # Initialize classifier with correct parameters
    classifier = HybridClassifier(
        campaign_config_path='src/config/fb_ad_categorizer/campaign_config.json',
        text_processing_fields=['ad_creative_bodies', 'page_name'],
        embedder_model_name='all-MiniLM-L6-v2',
        embedder_cache_file='test_embedding_cache.pkl',
        company_ner_model='en_core_web_sm',
        use_llm=False,
        ner_cache_file='test_ner_cache.pkl',
        force_parallel_preprocessing=True  # Force parallel processing for test
    )
    
    # Create test ads
    test_ads = [
        {
            'id': 'ad1',
            'page_name': 'Test Law Firm',
            'ad_creative_bodies': [{'text': 'Were you diagnosed with cancer after using Roundup weed killer? You may be entitled to compensation.'}],
            'ad_delivery_start_time': '2024-01-01'
        },
        {
            'id': 'ad2', 
            'page_name': 'Another Law Firm',
            'ad_creative_bodies': [{'text': 'Depo-Provera brain tumor lawsuit. If you developed meningioma after using this birth control, contact us.'}],
            'ad_delivery_start_time': '2024-01-02'
        },
        {
            'id': 'ad3',
            'page_name': 'Legal Services LLC',
            'ad_creative_bodies': [{'text': 'Ozempic weight loss injection causing severe gastrointestinal issues? File your claim today.'}],
            'ad_delivery_start_time': '2024-01-03'
        },
        {
            'id': 'ad4',
            'page_name': 'Morgan & Morgan',
            'ad_creative_bodies': [{'text': 'AFFF firefighting foam exposure linked to cancer. Veterans and firefighters may qualify for compensation.'}],
            'ad_delivery_start_time': '2024-01-04'
        },
        {
            'id': 'ad5',
            'page_name': 'Test Firm 5',
            'ad_creative_bodies': [{'text': 'Hair relaxer products causing uterine cancer? Join the mass tort litigation now.'}],
            'ad_delivery_start_time': '2024-01-05'
        }
    ]
    
    print("Testing parallel preprocessing functionality...\n")
    
    # Test 1: Parallel preprocessor initialization
    print("Test 1: Initialize ParallelPreprocessor")
    preprocessor = ParallelPreprocessor(classifier)
    print(f"✓ Preprocessor initialized with max_parallel_tasks: {preprocessor.max_parallel_tasks}")
    print("-" * 50)
    
    # Test 2: Process ads with progress tracking
    print("\nTest 2: Process ads with parallel preprocessing")
    start_time = time.time()
    
    # Run parallel preprocessing
    embedding_results, ner_results = preprocessor.preprocess_with_progress(test_ads)
    
    elapsed = time.time() - start_time
    print(f"\n✓ Processing completed in {elapsed:.2f} seconds")
    print(f"  - NER results: {len(ner_results)} ads processed")
    print(f"  - Embedding results: {len(embedding_results)} ads processed")
    
    # Show sample NER results
    if ner_results:
        print("\nSample NER results:")
        for idx, entities in list(ner_results.items())[:2]:
            ad = test_ads[idx]
            print(f"\nAd {idx}: {ad['page_name']}")
            print(f"Text: {ad['ad_creative_bodies'][0]['text'][:100]}...")
            print(f"Entities found: {entities}")
    
    print("-" * 50)
    
    # Test 3: Test the main entry point function
    print("\nTest 3: Test parallel_preprocess_ads entry point")
    start_time = time.time()
    
    embedding_results2, ner_results2 = parallel_preprocess_ads(classifier, test_ads)
    
    elapsed = time.time() - start_time
    print(f"\n✓ Entry point function completed in {elapsed:.2f} seconds")
    
    # Test 4: Verify memory safety
    print("\nTest 4: Memory safety check")
    try:
        import psutil
        process = psutil.Process()
        memory_info = process.memory_info()
        print(f"✓ Current memory usage: {memory_info.rss / (1024**3):.2f} GB")
        print(f"  - Available system memory: {psutil.virtual_memory().available / (1024**3):.2f} GB")
    except ImportError:
        print("⚠ psutil not available, skipping memory check")
    
    print("\n" + "="*60)
    print("All tests completed successfully!")
    print("="*60)

if __name__ == "__main__":
    test_parallel_preprocessing()