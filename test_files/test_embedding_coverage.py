#!/usr/bin/env python3
"""
Test script to analyze DynamoDB FBAdArchive embedding coverage.

This script:
1. <PERSON>ads DynamoDB FBAdArchive data
2. Determines unique text combinations using hybrid_classifier.py logic
3. Analyzes existing embeddings in roberta_embedding_cache.pkl
4. Calculates missing embeddings and prompts user to generate them
5. Uses rich formatting for beautiful output
"""

import sys
import os
import pickle
import hashlib
from pathlib import Path
from collections import Counter
from typing import Dict, List, Set, Any, Tuple

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich.prompt import Confirm, Prompt
from rich.panel import Panel
from rich.columns import Columns
from rich.text import Text
from rich import box

# Import project modules
from scripts.hybrid_classifier import HybridClassifier

console = Console()

def load_dynamodb_data() -> List[Dict[str, Any]]:
    """Load data from DynamoDB FBAdArchive table."""
    console.print("\n[bold blue]📊 Loading DynamoDB FBAdArchive Data[/bold blue]")
    
    # Use direct DynamoDB access since the import paths are complex
    try:
        import boto3
        from boto3.dynamodb.conditions import Key
        
        dynamodb = boto3.resource(
            'dynamodb',
            endpoint_url='http://localhost:8000',
            region_name='us-east-1'
        )
        
        table = dynamodb.Table('FBAdArchive')
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Scanning DynamoDB table...", total=None)
            
            response = table.scan()
            ads_data = response['Items']
            
            # Handle pagination
            while 'LastEvaluatedKey' in response:
                response = table.scan(ExclusiveStartKey=response['LastEvaluatedKey'])
                ads_data.extend(response['Items'])
            
            progress.update(task, completed=100, total=100)
        
        console.print(f"[green]✅ Successfully loaded {len(ads_data):,} ads from DynamoDB[/green]")
        return ads_data
        
    except Exception as e:
        console.print(f"[red]❌ Error loading from DynamoDB: {e}[/red]")
        raise

def prepare_text_combinations(ads_data: List[Dict[str, Any]]) -> Tuple[List[str], Dict[str, int]]:
    """
    Prepare unique text combinations using the same logic as hybrid_classifier.py.
    Returns (unique_texts, text_frequency_counter).
    """
    console.print("\n[bold blue]🔤 Analyzing Text Combinations[/bold blue]")
    
    # Use the same configuration as hybrid_classifier.py 
    # Config uses uppercase, but processing method expects lowercase
    text_processing_fields = ['title', 'body', 'summary', 'imagetext', 'linkdescription', 'pagename']
    invalid_summary_strings = {s.lower() for s in ['NA', 'SKIPPED', '', 'None', 'null', "Summary generation failed"]}
    
    all_texts = []
    text_counter = Counter()
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TaskProgressColumn(),
        console=console
    ) as progress:
        task = progress.add_task("Processing ad texts...", total=len(ads_data))
        
        for i, ad_data in enumerate(ads_data):
            # Use the exact same logic as HybridClassifier._prepare_ad_text_for_processing
            raw_text, normalized_text = HybridClassifier._prepare_ad_text_for_processing(
                ad_data,
                text_processing_fields,
                invalid_summary_strings
            )
            
            if raw_text:  # Only include non-empty texts
                all_texts.append(raw_text)
                text_counter[raw_text] += 1
            
            if i % 1000 == 0:
                progress.update(task, completed=i)
        
        progress.update(task, completed=len(ads_data))
    
    unique_texts = list(set(all_texts))
    
    console.print(f"[green]✅ Processed {len(ads_data):,} ads into {len(unique_texts):,} unique text combinations[/green]")
    
    if len(all_texts) > 0:
        console.print(f"[cyan]📈 Deduplication ratio: {(1 - len(unique_texts)/len(all_texts))*100:.1f}%[/cyan]")
    else:
        console.print("[yellow]⚠️  No texts found - check field names or data format[/yellow]")
    
    return unique_texts, text_counter

def load_existing_embeddings(cache_file: str) -> Dict[str, Any]:
    """Load existing embeddings from cache file."""
    console.print(f"\n[bold blue]💾 Loading Existing Embeddings[/bold blue]")
    
    cache_path = Path(cache_file)
    
    if not cache_path.exists():
        console.print(f"[yellow]⚠️  Cache file not found: {cache_file}[/yellow]")
        console.print(f"[blue]📁 Creating new empty cache[/blue]")
        return {}
    
    try:
        with open(cache_path, 'rb') as f:
            embeddings_cache = pickle.load(f)
        
        console.print(f"[green]✅ Loaded {len(embeddings_cache):,} existing embeddings from {cache_file}[/green]")
        return embeddings_cache
        
    except Exception as e:
        console.print(f"[red]❌ Error loading embeddings cache: {e}[/red]")
        return {}

def analyze_coverage(unique_texts: List[str], embeddings_cache: Dict[str, Any], text_counter: Dict[str, int]) -> Tuple[Set[str], Set[str], int, int, int]:
    """
    Analyze embedding coverage using the same hashing logic as the embedder.
    Returns (covered_texts, missing_texts).
    """
    console.print("\n[bold blue]🔍 Analyzing Embedding Coverage[/bold blue]")
    
    def get_cache_key(text: str) -> str:
        """Generate MD5 hash key for text (same as M4OptimizedEmbedder)."""
        return hashlib.md5(text.encode('utf-8')).hexdigest()
    
    covered_texts = set()
    missing_texts = set()
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TaskProgressColumn(),
        console=console
    ) as progress:
        task = progress.add_task("Checking embedding coverage...", total=len(unique_texts))
        
        for i, text in enumerate(unique_texts):
            cache_key = get_cache_key(text)
            
            if cache_key in embeddings_cache:
                covered_texts.add(text)
            else:
                missing_texts.add(text)
            
            if i % 1000 == 0:
                progress.update(task, completed=i)
        
        progress.update(task, completed=len(unique_texts))
    
    # Calculate impact statistics
    covered_ad_count = sum(text_counter[text] for text in covered_texts)
    missing_ad_count = sum(text_counter[text] for text in missing_texts)
    total_ads = covered_ad_count + missing_ad_count
    
    return covered_texts, missing_texts, covered_ad_count, missing_ad_count, total_ads

def display_coverage_summary(covered_texts: Set[str], missing_texts: Set[str], 
                           covered_ad_count: int, missing_ad_count: int, total_ads: int):
    """Display comprehensive coverage analysis."""
    console.print("\n[bold green]📊 Embedding Coverage Summary[/bold green]")
    
    # Create summary table
    summary_table = Table(title="Coverage Analysis", box=box.ROUNDED)
    summary_table.add_column("Metric", style="cyan", no_wrap=True)
    summary_table.add_column("Count", style="magenta", justify="right")
    summary_table.add_column("Percentage", style="green", justify="right")
    
    total_unique = len(covered_texts) + len(missing_texts)
    
    summary_table.add_row(
        "Total Unique Texts", 
        f"{total_unique:,}", 
        "100.0%"
    )
    summary_table.add_row(
        "✅ Covered Texts", 
        f"{len(covered_texts):,}", 
        f"{len(covered_texts)/total_unique*100:.1f}%"
    )
    summary_table.add_row(
        "❌ Missing Texts", 
        f"{len(missing_texts):,}", 
        f"{len(missing_texts)/total_unique*100:.1f}%"
    )
    summary_table.add_row("", "", "")  # Separator
    summary_table.add_row(
        "Total Ads", 
        f"{total_ads:,}", 
        "100.0%"
    )
    summary_table.add_row(
        "✅ Covered Ads", 
        f"{covered_ad_count:,}", 
        f"{covered_ad_count/total_ads*100:.1f}%"
    )
    summary_table.add_row(
        "❌ Missing Coverage", 
        f"{missing_ad_count:,}", 
        f"{missing_ad_count/total_ads*100:.1f}%"
    )
    
    console.print(summary_table)
    
    # Create performance impact panel
    if missing_texts:
        impact_text = Text()
        impact_text.append("🚀 Performance Impact:\n", style="bold yellow")
        impact_text.append(f"• Computing embeddings for ", style="white")
        impact_text.append(f"{len(missing_texts):,}", style="bold red")
        impact_text.append(" unique texts\n", style="white")
        impact_text.append(f"• Will speed up processing for ", style="white")
        impact_text.append(f"{missing_ad_count:,}", style="bold green")
        impact_text.append(" ads\n", style="white")
        impact_text.append(f"• Estimated time savings: ", style="white")
        impact_text.append(f"{missing_ad_count * 0.1:.1f}", style="bold cyan")
        impact_text.append(" seconds per batch", style="white")
        
        console.print(Panel(impact_text, title="Impact Analysis", border_style="yellow"))

def generate_missing_embeddings(missing_texts: List[str], cache_file: str):
    """Generate embeddings for missing texts and save to cache."""
    console.print(f"\n[bold blue]🧠 Generating Missing Embeddings[/bold blue]")
    
    if not missing_texts:
        console.print("[green]✅ No missing embeddings to generate![/green]")
        return
    
    try:
        # Initialize embedder with the same model as configured
        from scripts.hybrid_classifier import M4OptimizedEmbedder
        
        embedder = M4OptimizedEmbedder(
            model_name='all-roberta-large-v1',
            cache_file=cache_file
        )
        
        console.print(f"[blue]📦 Initialized embedder with model: all-roberta-large-v1[/blue]")
        console.print(f"[blue]💾 Using cache file: {cache_file}[/blue]")
        
        # Generate embeddings in batches
        batch_size = 32
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            console=console
        ) as progress:
            task = progress.add_task("Generating embeddings...", total=len(missing_texts))
            
            for i in range(0, len(missing_texts), batch_size):
                batch = missing_texts[i:i + batch_size]
                
                # Generate embeddings for batch
                _ = embedder.encode(batch, batch_size=len(batch), show_progress=False)
                
                progress.update(task, completed=min(i + batch_size, len(missing_texts)))
        
        console.print(f"[green]✅ Successfully generated and cached {len(missing_texts):,} new embeddings![/green]")
        console.print(f"[blue]💾 Embeddings saved to: {cache_file}[/blue]")
        
    except Exception as e:
        console.print(f"[red]❌ Error generating embeddings: {e}[/red]")
        raise

def main():
    """Main execution function."""
    console.print(Panel.fit(
        "[bold blue]🧪 DynamoDB Embedding Coverage Analysis[/bold blue]\n"
        "[white]Analyzing FBAdArchive data for roberta-large-v1 embeddings[/white]",
        border_style="blue"
    ))
    
    try:
        # Step 1: Load DynamoDB data
        ads_data = load_dynamodb_data()
        if not ads_data:
            console.print("[red]❌ No data loaded. Exiting.[/red]")
            return
        
        # Step 2: Determine unique combinations
        unique_texts, text_counter = prepare_text_combinations(ads_data)
        
        # Step 3: Load existing embeddings from roberta cache
        cache_file = "roberta_embedding_cache.pkl"
        embeddings_cache = load_existing_embeddings(cache_file)
        
        # Step 4: Analyze coverage
        covered_texts, missing_texts, covered_ad_count, missing_ad_count, total_ads = analyze_coverage(
            unique_texts, embeddings_cache, text_counter
        )
        
        # Step 5: Display results
        display_coverage_summary(covered_texts, missing_texts, covered_ad_count, missing_ad_count, total_ads)
        
        # Step 6: Prompt user for embedding generation
        if missing_texts:
            console.print(f"\n[yellow]⚠️  Found {len(missing_texts):,} texts without embeddings[/yellow]")
            
            try:
                user_wants_generation = Confirm.ask(
                    f"[bold green]Generate embeddings for {len(missing_texts):,} missing texts?[/bold green]",
                    default=False
                )
            except (EOFError, KeyboardInterrupt):
                console.print("[yellow]⏭️  No input provided, skipping embedding generation[/yellow]")
                user_wants_generation = False
            
            if user_wants_generation:
                generate_missing_embeddings(list(missing_texts), cache_file)
                
                console.print(Panel.fit(
                    "[bold green]🎉 Embedding generation complete![/bold green]\n"
                    f"[white]Cache updated: {cache_file}[/white]\n"
                    f"[cyan]Ready for optimized classification of {total_ads:,} ads[/cyan]",
                    border_style="green"
                ))
            else:
                console.print("[yellow]⏭️  Skipping embedding generation[/yellow]")
        else:
            console.print(Panel.fit(
                "[bold green]🎉 Perfect Coverage![/bold green]\n"
                "[white]All texts already have embeddings cached[/white]\n"
                "[cyan]Ready for immediate optimized classification[/cyan]",
                border_style="green"
            ))
    
    except KeyboardInterrupt:
        console.print("\n[yellow]⚠️  Process interrupted by user[/yellow]")
    except Exception as e:
        console.print(f"\n[red]❌ Error: {e}[/red]")
        import traceback
        console.print("[red]" + traceback.format_exc() + "[/red]")

if __name__ == "__main__":
    main()