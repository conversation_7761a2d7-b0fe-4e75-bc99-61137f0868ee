#!/usr/bin/env python3
"""
Recover batch results from downloaded JSONL file
"""

import json
from pathlib import Path
from datetime import datetime

def recover_batch_results(jsonl_file, data_dir="data"):
    """Process batch results and create MD files."""
    
    # Read results
    with open(jsonl_file, 'r') as f:
        lines = f.readlines()
    
    print(f"Processing {len(lines)} results from {jsonl_file}")
    
    processed = 0
    failed = 0
    
    for line in lines:
        if not line.strip():
            continue
            
        try:
            result = json.loads(line)
            custom_id = result.get("custom_id", "")
            
            # Extract the filename from custom_id (format: "index_filename")
            if "_" in custom_id:
                parts = custom_id.split("_", 1)
                filename_part = parts[1]
                
                # Search for the PDF in data directories
                data_path = Path(data_dir)
                pdf_found = False
                
                for pdf_path in data_path.rglob(f"{filename_part}.pdf"):
                    # Skip backup directories
                    if "_bak" in str(pdf_path):
                        continue
                    
                    # Check if <PERSON> already exists
                    md_path = pdf_path.with_suffix(".md")
                    if md_path.exists():
                        print(f"Skipping {pdf_path.name} - MD already exists")
                        continue
                    
                    # Extract response
                    response = result.get("response", {})
                    if response.get("status_code") != 200:
                        print(f"Error response for {pdf_path.name}: {response.get('status_code')}")
                        failed += 1
                        continue
                    
                    body = response.get("body", {})
                    pages = body.get("pages", [])
                    
                    if not pages:
                        print(f"No pages for {pdf_path.name}")
                        failed += 1
                        continue
                    
                    # Extract text
                    extracted_text = ""
                    for page in pages:
                        page_num = page.get("index", 0) + 1
                        page_text = page.get("markdown", "")
                        if page_text:
                            extracted_text += f"\n--- Page {page_num} ---\n"
                            extracted_text += page_text
                            extracted_text += "\n"
                    
                    # Save MD file
                    with open(md_path, "w", encoding="utf-8") as f:
                        f.write(f"# OCR Extracted Text - {pdf_path.name}\n\n")
                        f.write(f"**Source PDF:** {pdf_path.name}\n")
                        f.write(f"**Processed:** {datetime.now().isoformat()}\n")
                        f.write(f"**Method:** Mistral Batch API (Recovery)\n")
                        f.write(f"**Batch File:** {jsonl_file}\n")
                        f.write(f"**Pages:** {len(pages)}\n\n")
                        f.write("---\n\n")
                        f.write(extracted_text)
                    
                    print(f"Created: {md_path}")
                    processed += 1
                    pdf_found = True
                    break
                
                if not pdf_found:
                    print(f"PDF not found for: {filename_part}")
                    
        except Exception as e:
            print(f"Error processing result: {e}")
            failed += 1
    
    print(f"\nSummary:")
    print(f"- Processed: {processed}")
    print(f"- Failed: {failed}")
    print(f"- Total: {len(lines)}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        jsonl_file = sys.argv[1]
    else:
        # Use the file from the error message
        jsonl_file = "batch_results_ae0ca6d4-87d3-4ed6-be0d-94e1fe820d55.jsonl"
    
    recover_batch_results(jsonl_file)