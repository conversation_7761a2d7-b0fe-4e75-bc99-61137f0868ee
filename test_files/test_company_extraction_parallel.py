#!/usr/bin/env python
"""Test company name extraction with parallel preprocessing"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import json
from src.scripts.hybrid_classifier import HybridClassifier

def test_company_extraction():
    """Test that company names are extracted correctly from ads"""
    
    # Initialize classifier
    classifier = HybridClassifier(
        campaign_config_path='src/config/fb_ad_categorizer/campaign_config.json',
        text_processing_fields=['ad_creative_bodies', 'page_name'],
        embedder_model_name='all-MiniLM-L6-v2',
        embedder_cache_file='test_company_cache.pkl',
        company_ner_model='en_core_web_sm',
        use_llm=False,
        ner_cache_file='test_company_ner_cache.pkl',
        force_parallel_preprocessing=True,
        log_level='DEBUG'
    )
    
    # Test ads with known companies
    test_ads = [
        {
            'id': 'test1',
            'page_name': 'Test Law Firm',
            'ad_creative_bodies': [{'text': 'Monsanto Roundup weed killer causing cancer? Contact us for legal help.'}],
            'ad_delivery_start_time': '2024-01-01'
        },
        {
            'id': 'test2',
            'page_name': 'Morgan & Morgan',  # This is a law firm, should be excluded
            'ad_creative_bodies': [{'text': 'Pfizer Oxbryta recall affecting sickle cell patients. Get compensation now.'}],
            'ad_delivery_start_time': '2024-01-02'
        },
        {
            'id': 'test3',
            'page_name': 'Legal Help Center',
            'ad_creative_bodies': [{'text': 'Abbott and Mead Johnson baby formula causing NEC in premature infants.'}],
            'ad_delivery_start_time': '2024-01-03'
        },
        {
            'id': 'test4',
            'page_name': 'Injury Lawyers',
            'ad_creative_bodies': [{'text': '3M AFFF firefighting foam contamination lawsuits. Military and firefighters affected.'}],
            'ad_delivery_start_time': '2024-01-04'
        },
        {
            'id': 'test5',
            'page_name': 'Class Action Center',
            'ad_creative_bodies': [{'text': 'Depo-Provera brain tumor litigation. Pfizer facing thousands of lawsuits.'}],
            'ad_delivery_start_time': '2024-01-05'
        }
    ]
    
    print("Testing company name extraction with parallel preprocessing...\n")
    
    # Classify ads
    results = classifier.classify_batch(test_ads)
    
    print("\nClassification Results:")
    print("=" * 80)
    
    for i, (ad, result) in enumerate(zip(test_ads, results)):
        print(f"\nAd {i+1}: {ad['page_name']}")
        print(f"Text: {ad['ad_creative_bodies'][0]['text'][:80]}...")
        print(f"Campaign: {result.campaign}")
        print(f"Company: {result.company or 'N/A'}")
        print(f"Original Company Name: {result.original_name or 'N/A'}")
        print(f"Method: {result.method}")
        print(f"Confidence: {result.confidence:.2f}")
        
        # Get company info from result details if available
        details = result.details or {}
        companies = details.get('company_names', [])
        normalized = result.company or ''
        
        # Check for invalid values
        invalid_values = ['NA', 'N/A', 'SKIPPED', 'None', 'null', 'Summary generation failed', 
                         'Unknown', 'Various', 'Multiple', 'TBD', 'Not Available']
        
        has_invalid = any(val.lower() in [c.lower() for c in companies] for val in invalid_values)
        has_invalid_normalized = normalized and normalized.lower() in [v.lower() for v in invalid_values]
        
        if has_invalid or has_invalid_normalized:
            print("❌ FAIL: Invalid company name detected!")
        elif not normalized and result.campaign != 'Other':
            print("⚠️  WARNING: No company names extracted")
        else:
            print("✅ PASS: Valid company names")
            
        # Check if law firms were properly excluded
        if normalized and 'Morgan & Morgan' in normalized:
            print("❌ FAIL: Law firm not excluded!")
        
        print("-" * 80)
    
    # Summary statistics
    print("\nSummary:")
    total_ads = len(results)
    ads_with_companies = sum(1 for r in results if r.company)
    ads_with_normalized = sum(1 for r in results if r.company)
    
    print(f"Total ads processed: {total_ads}")
    print(f"Ads with company names: {ads_with_companies}")
    print(f"Ads with normalized company: {ads_with_normalized}")
    
    # Clean up test cache files
    import os
    for cache_file in ['test_company_cache.pkl', 'test_company_ner_cache.pkl']:
        if os.path.exists(cache_file):
            os.remove(cache_file)
            print(f"Cleaned up: {cache_file}")

if __name__ == "__main__":
    test_company_extraction()