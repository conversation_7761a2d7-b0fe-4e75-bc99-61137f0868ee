#!/usr/bin/env python
"""
Fix for extract_company_info to also consider PRODUCT entities when extracting companies.
This is critical for product liability campaigns where the product name IS the company identifier.
"""

def extract_company_info_fixed(self, ad_data_item, campaign_name, all_extracted_entities, campaign_config_entry):
    """
    Fixed version that also considers PRODUCT entities
    """
    # FIRST: Check campaign config (same as original)
    if campaign_config_entry and campaign_config_entry.get('Company'):
        company_from_config = campaign_config_entry['Company']
        # ... validation logic ...
        return [company_from_config], [company_from_config]
    
    # SECOND: Look for company names in extracted entities
    # FIX: Also check PRODUCT entities for product liability campaigns
    candidate_names = []
    
    # Original entity types
    for entity_type in ['ORG', 'GPE', 'PERSON']:
        if entity_type in all_extracted_entities:
            candidate_names.extend(all_extracted_entities[entity_type])
    
    # NEW: Also check PRODUCT entities for product-based campaigns
    # This is critical for campaigns like "Trojan Condoms", "Roundup", etc.
    if 'PRODUCT' in all_extracted_entities:
        # Filter product names to likely company/brand names
        product_entities = all_extracted_entities['PRODUCT']
        for product in product_entities:
            # Skip generic terms
            if product.lower() not in ['product', 'products', 'investigation', 'liability']:
                # Extract brand name from product descriptions
                # E.g., "Trojan Condoms" -> "Trojan"
                words = product.split()
                if words:
                    # Take the first word as potential brand/company
                    brand = words[0]
                    if len(brand) > 2:  # Skip short words
                        candidate_names.append(brand)
                        # Also add full product name if it's short enough
                        if len(words) <= 3:
                            candidate_names.append(product)
    
    # Remove duplicates while preserving order
    seen = set()
    unique_candidates = []
    for name in candidate_names:
        if name not in seen:
            seen.add(name)
            unique_candidates.append(name)
    
    if unique_candidates:
        # Return the filtered candidates
        return unique_candidates[:3], unique_candidates[:3]  # Limit to top 3
    
    # If no entities found, return empty
    return [], []


# Test the fix
test_cases = [
    {
        "entities": {'PRODUCT': ['Trojan Condoms Product Liability Investigation', 'Trojan Ultra Thin Condoms']},
        "expected": ["Trojan"]
    },
    {
        "entities": {'PRODUCT': ['Roundup weed killer'], 'ORG': ['Monsanto']},
        "expected": ["Monsanto", "Roundup"]  # Both should be extracted
    },
    {
        "entities": {'PRODUCT': ['Mooka B-D02L Pro Air Purifier']},
        "expected": ["Mooka"]
    }
]

print("Testing PRODUCT entity extraction fix:\n")
for test in test_cases:
    print(f"Entities: {test['entities']}")
    print(f"Expected extraction: {test['expected']}")
    print("-" * 50)