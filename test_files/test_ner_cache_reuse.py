#!/usr/bin/env python
"""Test NER cache reuse"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.scripts.hybrid_classifier import HybridClassifier

# Use a fixed cache file name
CACHE_FILE = 'test_ner_reuse_cache.pkl'
EMBED_CACHE = 'test_embed_reuse_cache.pkl'

# Test ad
test_ad = {
    'id': 'test1',
    'page_name': 'Test Law Firm',
    'ad_creative_bodies': [{'text': 'Monsanto Roundup weed killer causing cancer? Contact us for legal help.'}],
    'ad_delivery_start_time': '2024-01-01'
}

print("=" * 60)
print("First run - should process NER:")
print("=" * 60)

# First run
classifier1 = HybridClassifier(
    campaign_config_path='src/config/fb_ad_categorizer/campaign_config.json',
    text_processing_fields=['ad_creative_bodies', 'page_name'],
    embedder_model_name='all-MiniLM-L6-v2',
    embedder_cache_file=EMBED_CACHE,
    company_ner_model='en_core_web_sm',
    use_llm=False,
    ner_cache_file=CACHE_FILE,
    log_level='INFO'
)

results1 = classifier1.classify_batch([test_ad])
print(f"Result: {results1[0].company}")

# Force save cache
if hasattr(classifier1, 'close'):
    classifier1.close()

print("\n" + "=" * 60)
print("Second run - should use cached NER:")
print("=" * 60)

# Second run with same cache file
classifier2 = HybridClassifier(
    campaign_config_path='src/config/fb_ad_categorizer/campaign_config.json',
    text_processing_fields=['ad_creative_bodies', 'page_name'],
    embedder_model_name='all-MiniLM-L6-v2',
    embedder_cache_file=EMBED_CACHE,
    company_ner_model='en_core_web_sm',
    use_llm=False,
    ner_cache_file=CACHE_FILE,
    log_level='INFO'
)

results2 = classifier2.classify_batch([test_ad])
print(f"Result: {results2[0].company}")

# Clean up
if hasattr(classifier2, 'close'):
    classifier2.close()