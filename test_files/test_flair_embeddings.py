#!/usr/bin/env python
"""Test available Flair embedding models"""

from flair.embeddings import DocumentPoolEmbeddings, WordEmbeddings, FlairEmbeddings, TransformerDocumentEmbeddings
from flair.data import Sentence
import torch

print("Testing Flair Document Embeddings...")
print(f"Device: {'mps' if torch.backends.mps.is_available() else 'cpu'}")

# Test text
text = "This is a test document about legal cases and litigation."

# Option 1: Document Pool Embeddings (combines word embeddings)
print("\n1. Testing DocumentPoolEmbeddings...")
try:
    doc_embeddings = DocumentPoolEmbeddings([
        WordEmbeddings('glove'),
        FlairEmbeddings('news-forward'),
        FlairEmbeddings('news-backward'),
    ])
    
    sentence = Sentence(text)
    doc_embeddings.embed(sentence)
    
    # Get document embedding
    embedding = sentence.embedding
    print(f"   Success! Embedding shape: {embedding.shape}")
    print(f"   Embedding dimension: {embedding.shape[0]}")
except Exception as e:
    print(f"   Error: {e}")

# Option 2: Transformer Document Embeddings (BERT-based)
print("\n2. Testing TransformerDocumentEmbeddings...")
try:
    # Use a smaller model for testing
    doc_embeddings = TransformerDocumentEmbeddings('bert-base-uncased')
    
    sentence = Sentence(text)
    doc_embeddings.embed(sentence)
    
    embedding = sentence.embedding
    print(f"   Success! Embedding shape: {embedding.shape}")
    print(f"   Embedding dimension: {embedding.shape[0]}")
except Exception as e:
    print(f"   Error: {e}")

# Option 3: Sentence Transformers via Flair
print("\n3. Testing SentenceTransformerDocumentEmbeddings...")
try:
    from flair.embeddings import SentenceTransformerDocumentEmbeddings
    
    # This uses sentence-transformers models
    doc_embeddings = SentenceTransformerDocumentEmbeddings('all-MiniLM-L6-v2')
    
    sentence = Sentence(text)
    doc_embeddings.embed(sentence)
    
    embedding = sentence.embedding
    print(f"   Success! Embedding shape: {embedding.shape}")
    print(f"   Embedding dimension: {embedding.shape[0]}")
except Exception as e:
    print(f"   Error: {e}")

print("\nRecommendation: Use SentenceTransformerDocumentEmbeddings for compatibility with existing embeddings.")