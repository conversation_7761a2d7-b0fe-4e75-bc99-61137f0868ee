#!/usr/bin/env python
"""
Test script to verify embedding similarity threshold is being applied correctly
"""

import sys
sys.path.append('/Users/<USER>/PycharmProjects/lexgenius')

# Test 1: Check if YAML config is parsed correctly
print("TEST 1: YAML Config Parsing")
print("-" * 50)

import yaml
config_path = "/Users/<USER>/PycharmProjects/lexgenius/src/config/fb_ad_categorizer/hybrid_classifier_flair_config.yml"

with open(config_path, 'r') as f:
    config = yaml.safe_load(f)

print(f"Full models config: {config.get('models', {})}")
print(f"Embedder config: {config.get('models', {}).get('embedder', {})}")
print(f"Similarity threshold from YAML: {config.get('models', {}).get('embedder', {}).get('similarity_threshold', 'NOT SET')}")

# Test 2: Check the yaml_config_to_args function
print("\n\nTEST 2: yaml_config_to_args Function")
print("-" * 50)

from src.scripts.hybrid_classifier import yaml_config_to_args
args_dict = yaml_config_to_args(config)
print(f"embedding_similarity_threshold in args dict: {args_dict.get('embedding_similarity_threshold', 'NOT SET')}")

# Test 3: Check the HybridClassifier initialization
print("\n\nTEST 3: HybridClassifier Initialization")
print("-" * 50)

# Create a minimal test to see what threshold is being used
test_config = {
    'campaign_config_path': 'src/config/fb_ad_categorizer/campaign_config.json',
    'text_fields': ['Title', 'Body'],
    'embedder_model': 'all-MiniLM-L6-v2',
    'ner_backend': 'flair',
    'embedding_similarity_threshold': 0.75  # Test with explicit value
}

print(f"Test config embedding_similarity_threshold: {test_config.get('embedding_similarity_threshold')}")

# Test 4: Check actual value used in classification
print("\n\nTEST 4: Example Classification Check")
print("-" * 50)

# Simulate what should happen
embedding_confidence = 0.60
threshold_050 = 0.50
threshold_065 = 0.65
threshold_070 = 0.70

print(f"Embedding confidence: {embedding_confidence}")
print(f"Would pass with threshold 0.50? {embedding_confidence > threshold_050}")
print(f"Would pass with threshold 0.65? {embedding_confidence > threshold_065}")
print(f"Would pass with threshold 0.70? {embedding_confidence > threshold_070}")