#!/usr/bin/env python3
"""
Mistral True Batch OCR Script

This script properly uses Mistral's batch API for OCR processing to get 50% discount pricing.
It creates batch jobs, uploads them, and processes results asynchronously.

Features:
- True batch API usage for 50% discount
- JSONL file creation and upload
- Batch job submission and monitoring
- Result download and MD file creation
- Check for missing processed files
- Download specific results by filename

Usage:
    # Process PDFs for a date range
    python mistral_true_batch_ocr.py --start-date 20250101 --end-date 20250131
    
    # Check dataset for missing MD files
    python mistral_true_batch_ocr.py --check-dataset
    
    # Download specific result
    python mistral_true_batch_ocr.py --download-missing paed_25_02781_BORCHICH_v_NORDISK_AS_et_al.pdf
"""

import argparse
import asyncio
import base64
import json
import logging
import os
import sys
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import tempfile

from mistralai import Mistra<PERSON>
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeRemainingColumn

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("Warning: python-dotenv package not installed. Environment variables must be set manually.")


class MistralTrueBatchOCR:
    """True batch OCR processor using Mistral's batch API for discounted pricing."""
    
    def __init__(self, data_dir: str = "data"):
        """Initialize the batch OCR processor."""
        self.console = Console()
        self.data_dir = Path(data_dir)
        
        # Initialize Mistral client
        api_key = os.environ.get("MISTRAL_API_KEY")
        if not api_key:
            self.console.print("[red]Error: MISTRAL_API_KEY environment variable not set[/red]")
            sys.exit(1)
        
        self.client = Mistral(api_key=api_key)
        self.logger = self._setup_logging()
        
    def _setup_logging(self) -> logging.Logger:
        """Setup logging configuration."""
        log_dir = self.data_dir / "logs"
        log_dir.mkdir(parents=True, exist_ok=True)
        
        logger = logging.getLogger("mistral_true_batch")
        logger.setLevel(logging.INFO)
        
        # File handler
        fh = logging.FileHandler(log_dir / "mistral_true_batch.log")
        fh.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        fh.setFormatter(formatter)
        logger.addHandler(fh)
        
        return logger
    
    def discover_pdfs_for_batch(self, date_dirs: List[str]) -> List[Tuple[Path, Path]]:
        """
        Discover PDF files that need processing, excluding _bak directories.
        
        Returns:
            List of tuples (pdf_path, md_path) for files that need processing
        """
        pdf_pairs = []
        
        for date_str in date_dirs:
            # Skip backup directories
            if "_bak" in date_str:
                continue
                
            date_dir = self.data_dir / date_str / "dockets"
            
            if not date_dir.exists():
                self.logger.warning(f"Directory does not exist: {date_dir}")
                continue
            
            # Process all PDFs in dockets directory and subdirectories
            for pdf_file in date_dir.rglob("*.pdf"):
                # Double check no _bak in path
                if "_bak" in str(pdf_file):
                    continue
                    
                md_file = pdf_file.with_suffix(".md")
                
                if not md_file.exists():
                    pdf_pairs.append((pdf_file, md_file))
                    self.logger.info(f"Found PDF to process: {pdf_file}")
        
        return pdf_pairs
    
    def create_batch_input_file(self, pdf_pairs: List[Tuple[Path, Path]]) -> Path:
        """
        Create JSONL file for batch processing.
        
        Returns:
            Path to the created JSONL file
        """
        batch_requests = []
        
        for idx, (pdf_path, _) in enumerate(pdf_pairs):
            # Read and encode PDF
            with open(pdf_path, "rb") as f:
                pdf_data = f.read()
            
            pdf_base64 = base64.b64encode(pdf_data).decode("utf-8")
            
            # Create batch request
            request = {
                "custom_id": f"{idx}_{pdf_path.stem}",
                "method": "POST",
                "url": "/v1/ocr",
                "body": {
                    "model": "mistral-ocr-latest",
                    "document": {
                        "type": "document_url",
                        "document_url": f"data:application/pdf;base64,{pdf_base64}"
                    },
                    "include_image_base64": False
                }
            }
            
            batch_requests.append(request)
        
        # Create temporary JSONL file
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.jsonl', delete=False)
        
        for request in batch_requests:
            json.dump(request, temp_file)
            temp_file.write('\n')
        
        temp_file.close()
        
        self.logger.info(f"Created batch input file with {len(batch_requests)} requests: {temp_file.name}")
        return Path(temp_file.name)
    
    def submit_batch_job(self, input_file_path: Path, pdf_pairs: List[Tuple[Path, Path]]) -> Optional[str]:
        """
        Upload file and submit batch job to Mistral.
        
        Returns:
            Job ID if successful, None otherwise
        """
        try:
            # Upload the file
            self.console.print("[blue]Uploading batch file to Mistral...[/blue]")
            
            batch_file = self.client.files.upload(
                file=input_file_path,
                purpose="batch"
            )
            
            self.logger.info(f"File uploaded: {batch_file.id}")
            
            # Create metadata mapping filenames to paths
            metadata = {
                "pdf_paths": {
                    f"{idx}_{pdf_path.stem}": str(pdf_path)
                    for idx, (pdf_path, _) in enumerate(pdf_pairs)
                },
                "created_at": datetime.now().isoformat(),
                "total_files": len(pdf_pairs)
            }
            
            # Submit batch job
            self.console.print("[blue]Submitting batch job...[/blue]")
            
            batch_job = self.client.batch.jobs.create(
                model="mistral-ocr-latest",
                input_files=[batch_file.id],
                endpoint="/v1/ocr",
                metadata=metadata
            )
            
            self.logger.info(f"Batch job created: {batch_job.id}")
            self.console.print(f"[green]Batch job submitted: {batch_job.id}[/green]")
            
            return batch_job.id
            
        except Exception as e:
            self.logger.error(f"Error submitting batch job: {e}")
            self.console.print(f"[red]Error submitting batch job: {e}[/red]")
            return None
        finally:
            # Clean up temp file
            if input_file_path.exists():
                input_file_path.unlink()
    
    def monitor_batch_job(self, job_id: str) -> bool:
        """
        Monitor batch job until completion.
        
        Returns:
            True if successful, False otherwise
        """
        self.console.print(f"[blue]Monitoring batch job: {job_id}[/blue]")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TimeRemainingColumn(),
            console=self.console
        ) as progress:
            
            task = progress.add_task("Processing batch job...", total=100)
            
            while True:
                try:
                    job = self.client.batch.jobs.get(job_id=job_id)
                    
                    status = job.status
                    total_requests = getattr(job, 'total_requests', 0)
                    completed_requests = getattr(job, 'completed_requests', 0)
                    failed_requests = getattr(job, 'failed_requests', 0)
                    
                    # Update progress
                    if total_requests > 0:
                        percentage = ((completed_requests + failed_requests) / total_requests) * 100
                        progress.update(task, completed=percentage)
                    
                    # Update description
                    desc = f"Status: {status} | Completed: {completed_requests}/{total_requests}"
                    if failed_requests > 0:
                        desc += f" | Failed: {failed_requests}"
                    progress.update(task, description=desc)
                    
                    if status == "SUCCESS":
                        progress.update(task, completed=100)
                        self.console.print(f"[green]Batch job completed successfully![/green]")
                        return True
                    elif status == "FAILED":
                        self.console.print(f"[red]Batch job failed![/red]")
                        return False
                    elif status in ["TIMEOUT_EXCEEDED", "CANCELLED"]:
                        self.console.print(f"[red]Batch job {status}[/red]")
                        return False
                    
                    time.sleep(5)  # Poll every 5 seconds
                    
                except Exception as e:
                    self.logger.error(f"Error monitoring job: {e}")
                    self.console.print(f"[red]Error monitoring job: {e}[/red]")
                    return False
    
    def download_and_process_results(self, job_id: str) -> int:
        """
        Download batch results and create MD files.
        
        Returns:
            Number of successfully processed files
        """
        try:
            # Get job details
            job = self.client.batch.jobs.get(job_id=job_id)
            
            if not hasattr(job, 'output_file') or not job.output_file:
                self.console.print("[red]No output file available[/red]")
                return 0
            
            # Download results
            self.console.print("[blue]Downloading results...[/blue]")
            output_stream = self.client.files.download(file_id=job.output_file)
            results_content = output_stream.read().decode("utf-8")
            
            # Save raw results
            results_file = Path(f"batch_results_{job_id}.jsonl")
            with open(results_file, "w", encoding="utf-8") as f:
                f.write(results_content)
            
            self.console.print(f"[green]Results saved to: {results_file}[/green]")
            
            # Get metadata to map back to original files
            metadata = getattr(job, 'metadata', {})
            pdf_paths = metadata.get('pdf_paths', {})
            
            # Process each result
            processed_count = 0
            for line in results_content.strip().split("\n"):
                if not line:
                    continue
                
                try:
                    result = json.loads(line)
                    custom_id = result.get("custom_id", "")
                    
                    # Get original PDF path from metadata
                    if custom_id in pdf_paths:
                        pdf_path = Path(pdf_paths[custom_id])
                        md_path = pdf_path.with_suffix(".md")
                        
                        # Extract response content
                        response = result.get("response", {})
                        body = response.get("body", {})
                        
                        # Extract text from all pages
                        extracted_text = ""
                        pages = body.get("pages", [])
                        
                        for page in pages:
                            page_num = page.get("index", 0) + 1
                            extracted_text += f"\n--- Page {page_num} ---\n"
                            extracted_text += page.get("markdown", "")
                            extracted_text += "\n"
                        
                        # Save to MD file
                        with open(md_path, "w", encoding="utf-8") as f:
                            f.write(f"# OCR Extracted Text - {pdf_path.name}\n\n")
                            f.write(f"**Source PDF:** {pdf_path.name}\n")
                            f.write(f"**Processed:** {datetime.now().isoformat()}\n")
                            f.write(f"**Method:** Mistral Batch API\n")
                            f.write(f"**Batch Job:** {job_id}\n")
                            f.write(f"**Pages:** {len(pages)}\n\n")
                            f.write("---\n\n")
                            f.write(extracted_text)
                        
                        processed_count += 1
                        self.logger.info(f"Created MD file: {md_path}")
                    else:
                        self.logger.warning(f"No path mapping for custom_id: {custom_id}")
                
                except Exception as e:
                    self.logger.error(f"Error processing result line: {e}")
            
            self.console.print(f"[green]Processed {processed_count} files[/green]")
            return processed_count
            
        except Exception as e:
            self.logger.error(f"Error downloading results: {e}")
            self.console.print(f"[red]Error downloading results: {e}[/red]")
            return 0
    
    def check_dataset_for_missing_md(self):
        """Check dataset for PDFs without corresponding MD files."""
        self.console.print("[blue]Checking dataset for missing MD files...[/blue]")
        
        # Get all batch jobs
        try:
            jobs = self.client.batch.jobs.list()
            
            missing_files = []
            
            # Create table for results
            table = Table(title="Missing MD Files Analysis", show_header=True)
            table.add_column("PDF Filename", style="cyan")
            table.add_column("Job ID", style="yellow")
            table.add_column("Job Status", style="magenta")
            table.add_column("PDF Path", style="dim")
            
            for job in jobs.data:
                if job.status != "SUCCESS":
                    continue
                
                metadata = getattr(job, 'metadata', {})
                pdf_paths = metadata.get('pdf_paths', {})
                
                for custom_id, pdf_path_str in pdf_paths.items():
                    pdf_path = Path(pdf_path_str)
                    pdf_name = pdf_path.name
                    
                    # Skip backup directories
                    if "_bak" in str(pdf_path):
                        continue
                    
                    # Check if PDF exists and MD doesn't
                    if pdf_path.exists():
                        md_path = pdf_path.with_suffix(".md")
                        if not md_path.exists():
                            missing_files.append({
                                'pdf_name': pdf_name,
                                'job_id': job.id,
                                'job_status': job.status,
                                'pdf_path': str(pdf_path)
                            })
                            
                            table.add_row(
                                pdf_name,
                                job.id[:12] + "...",
                                job.status,
                                str(pdf_path)
                            )
            
            self.console.print(table)
            self.console.print(f"\n[yellow]Found {len(missing_files)} PDFs without MD files[/yellow]")
            
            # Save missing files list
            if missing_files:
                missing_files_path = Path("missing_md_files.json")
                with open(missing_files_path, "w") as f:
                    json.dump(missing_files, f, indent=2)
                self.console.print(f"[green]Missing files list saved to: {missing_files_path}[/green]")
            
        except Exception as e:
            self.logger.error(f"Error checking dataset: {e}")
            self.console.print(f"[red]Error checking dataset: {e}[/red]")
    
    def download_missing_result(self, pdf_filename: str):
        """Download specific missing result by PDF filename."""
        self.console.print(f"[blue]Searching for {pdf_filename} in batch jobs...[/blue]")
        
        try:
            # Find the PDF in the filesystem (excluding _bak directories)
            pdf_path = None
            for path in self.data_dir.rglob(pdf_filename):
                if "_bak" not in str(path) and "/dockets/" in str(path):
                    pdf_path = path
                    break
            
            if not pdf_path:
                self.console.print(f"[red]PDF file not found in data directory: {pdf_filename}[/red]")
                return
            
            self.console.print(f"[green]Found PDF at: {pdf_path}[/green]")
            
            # Search through batch jobs
            jobs = self.client.batch.jobs.list()
            
            for job in jobs.data:
                if job.status != "SUCCESS":
                    continue
                
                metadata = getattr(job, 'metadata', {})
                pdf_paths = metadata.get('pdf_paths', {})
                
                # Find if this PDF was in this batch
                for custom_id, path_str in pdf_paths.items():
                    if Path(path_str).name == pdf_filename:
                        self.console.print(f"[green]Found in batch job: {job.id}[/green]")
                        
                        # Download and process just this file
                        output_stream = self.client.files.download(file_id=job.output_file)
                        results_content = output_stream.read().decode("utf-8")
                        
                        # Find the specific result
                        for line in results_content.strip().split("\n"):
                            if not line:
                                continue
                            
                            result = json.loads(line)
                            if result.get("custom_id") == custom_id:
                                # Process this result
                                response = result.get("response", {})
                                body = response.get("body", {})
                                
                                # Extract text
                                extracted_text = ""
                                pages = body.get("pages", [])
                                
                                for page in pages:
                                    page_num = page.get("index", 0) + 1
                                    extracted_text += f"\n--- Page {page_num} ---\n"
                                    extracted_text += page.get("markdown", "")
                                    extracted_text += "\n"
                                
                                # Save MD file
                                md_path = pdf_path.with_suffix(".md")
                                with open(md_path, "w", encoding="utf-8") as f:
                                    f.write(f"# OCR Extracted Text - {pdf_path.name}\n\n")
                                    f.write(f"**Source PDF:** {pdf_path.name}\n")
                                    f.write(f"**Processed:** {datetime.now().isoformat()}\n")
                                    f.write(f"**Method:** Mistral Batch API (Recovery)\n")
                                    f.write(f"**Batch Job:** {job.id}\n")
                                    f.write(f"**Pages:** {len(pages)}\n\n")
                                    f.write("---\n\n")
                                    f.write(extracted_text)
                                
                                self.console.print(f"[green]Successfully created: {md_path}[/green]")
                                return
            
            self.console.print(f"[red]PDF not found in any successful batch job[/red]")
            
        except Exception as e:
            self.logger.error(f"Error downloading missing result: {e}")
            self.console.print(f"[red]Error: {e}[/red]")
    
    def process_date_range(self, start_date: str, end_date: str):
        """Process PDFs for a date range using batch API."""
        # Generate date list
        dates = []
        try:
            start = datetime.strptime(start_date, "%Y%m%d")
            end = datetime.strptime(end_date, "%Y%m%d")
            
            current = start
            while current <= end:
                dates.append(current.strftime("%Y%m%d"))
                current += timedelta(days=1)
        except ValueError as e:
            self.console.print(f"[red]Error parsing dates: {e}[/red]")
            return
        
        # Discover PDFs
        self.console.print(f"[blue]Discovering PDFs in {len(dates)} date directories...[/blue]")
        pdf_pairs = self.discover_pdfs_for_batch(dates)
        
        if not pdf_pairs:
            self.console.print("[yellow]No PDFs need processing[/yellow]")
            return
        
        self.console.print(f"[green]Found {len(pdf_pairs)} PDFs to process[/green]")
        
        # Create batch input file
        input_file = self.create_batch_input_file(pdf_pairs)
        
        # Submit batch job
        job_id = self.submit_batch_job(input_file, pdf_pairs)
        if not job_id:
            return
        
        # Monitor job
        success = self.monitor_batch_job(job_id)
        if not success:
            return
        
        # Download and process results
        processed = self.download_and_process_results(job_id)
        
        self.console.print(f"[green]Batch processing complete! Processed {processed} files[/green]")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Mistral True Batch OCR - Uses batch API for 50% discount",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    # Date arguments
    parser.add_argument("--start-date", help="Start date (YYYYMMDD)")
    parser.add_argument("--end-date", help="End date (YYYYMMDD)")
    parser.add_argument("--date", help="Single date (YYYYMMDD)")
    
    # Operations
    parser.add_argument("--check-dataset", action="store_true",
                       help="Check dataset for PDFs without MD files")
    parser.add_argument("--download-missing", help="Download specific missing result by filename")
    
    args = parser.parse_args()
    
    processor = MistralTrueBatchOCR()
    
    if args.check_dataset:
        processor.check_dataset_for_missing_md()
    elif args.download_missing:
        processor.download_missing_result(args.download_missing)
    elif args.date:
        processor.process_date_range(args.date, args.date)
    elif args.start_date and args.end_date:
        processor.process_date_range(args.start_date, args.end_date)
    else:
        parser.print_help()


if __name__ == "__main__":
    main()