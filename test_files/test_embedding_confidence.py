#!/usr/bin/env python3
"""Simple test to check if embedding confidence values are being calculated."""

import pandas as pd
import numpy as np

# Read the CSV file
csv_file = "classified_ads_rules_embeddings_flair_dedup2.csv"
df = pd.read_csv(csv_file)

print("=== Embedding Confidence Analysis ===")
print(f"Total ads: {len(df)}")
print(f"\nEmbedding confidence statistics:")
print(f"  Non-zero values: {(df['embedding_confidence'] > 0).sum()}")
print(f"  Max value: {df['embedding_confidence'].max():.3f}")
print(f"  Mean value: {df['embedding_confidence'].mean():.3f}")
print(f"  Unique values: {df['embedding_confidence'].nunique()}")

# Check specific methods
print("\n=== Classification Methods ===")
print(df['method'].value_counts())

# Check if any ads have embedding confidence > 0
non_zero_embedding = df[df['embedding_confidence'] > 0]
if len(non_zero_embedding) > 0:
    print(f"\n=== Ads with non-zero embedding confidence ===")
    print(non_zero_embedding[['ad_id', 'campaign', 'method', 'embedding_confidence']].head(10))
else:
    print("\nWARNING: No ads have embedding confidence > 0!")
    
# Check DBSCAN clustering for "Other" ads
print("\n=== DBSCAN Clustering Analysis ===")
other_ads = df[df['campaign'] == 'Other']
print(f"Number of 'Other' ads: {len(other_ads)}")

if 'dbscan_cluster' in df.columns:
    clustered = other_ads[other_ads['dbscan_cluster'].notna()]
    print(f"'Other' ads with DBSCAN cluster assigned: {len(clustered)}")
    if len(clustered) > 0:
        print(f"Unique clusters: {sorted(clustered['dbscan_cluster'].unique())}")
        print("\nCluster distribution:")
        print(clustered['dbscan_cluster'].value_counts().sort_index())
else:
    print("ERROR: dbscan_cluster column not found!")
    
# Check if the issue might be with how embeddings are calculated
print("\n=== Debugging Hints ===")
print("1. Check if embedder is initialized properly")
print("2. Check if campaign_prototypes are generated")
print("3. Check if raw_text is available for embedding calculation")
print("4. For DBSCAN: Need at least 10 'Other' ads (current: {})".format(len(other_ads)))