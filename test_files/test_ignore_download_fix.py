#!/usr/bin/env python3
"""Test script to verify ignore_download fixes work correctly."""

import json
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.lib.data_transformer.law_firm_processor import LawFirmProcessor

def test_law_firm_processor():
    """Test that law firm processor preserves ignore_download values."""
    
    # Create test data that simulates what comes after reprocessing
    # where the law firm values have been reset to just the extracted values
    test_data = {
        "_processing_notes": "Matched ignore_download config.",
        "_reason_relevant": "Relevant due to MDL Number: 2738",
        "court_id": "njd",
        "attorney": [
            {
                "attorney_name": "MATTHEW STUBBS",
                "law_firm": "DUNCAN STUBBS"
            }
        ],
        "law_firm": "Duncan Stubbs",  # This got reset during processing
        "law_firms": ["<PERSON> Stubbs"],  # This got reset during processing
        "plaintiff": ["<PERSON><PERSON>"],
        "defendant": [
            "Johnson & Johnson",
            "Johnson & Johnson Consumer Inc",
            "Llt Management Llc",
            "Johnson & Johnson Holdco (Na) Inc.",
            "Janssen Pharmaceuticals Inc",
            "Kenvue Inc",
            "Red River Talc Llc"
        ],
        "versus": "Watts v. Johnson & Johnson et al."
    }
    
    # Create processor instance
    processor = LawFirmProcessor()
    
    # Process the data
    print("Before processing:")
    print(f"  law_firm: {test_data.get('law_firm')}")
    print(f"  law_firms: {test_data.get('law_firms')}")
    
    processor.process_law_firms(test_data)
    
    print("\nAfter processing:")
    print(f"  law_firm: {test_data.get('law_firm')}")
    print(f"  law_firms: {test_data.get('law_firms')}")
    
    # Verify the values were preserved
    expected_law_firm = "Duncan Stubbs ; OnderLaw LLC"
    expected_law_firms = ["Duncan Stubbs", "OnderLaw LLC"]
    
    if test_data.get('law_firm') == expected_law_firm:
        print("\n✓ law_firm preserved correctly!")
    else:
        print(f"\n✗ law_firm was changed! Expected: {expected_law_firm}, Got: {test_data.get('law_firm')}")
    
    if test_data.get('law_firms') == expected_law_firms:
        print("✓ law_firms preserved correctly!")
    else:
        print(f"✗ law_firms was changed! Expected: {expected_law_firms}, Got: {test_data.get('law_firms')}")

if __name__ == "__main__":
    test_law_firm_processor()