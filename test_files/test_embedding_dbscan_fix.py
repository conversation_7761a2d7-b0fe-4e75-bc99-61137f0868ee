#!/usr/bin/env python3
"""Test script to verify embedding confidence and DBSCAN clustering are working."""

import sys
import pandas as pd
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.scripts.hybrid_classifier import HybridClassifier

def main():
    # Initialize classifier with Flair backend
    classifier = HybridClassifier(
        campaign_config_path="src/config/fb_ad_categorizer/campaign_config.json",
        text_processing_fields=["TextUnified", "PageName"],
        ner_backend="flair",
        embedder_model_name="flair",
        use_llm=False,  # Disable LLM for testing
        improve_rules_active=False,  # Disable rule improvement
        skip_terms=["injured", "car crash", "free consultation", "no win no fee"]
    )
    
    # Test data - mix of different campaigns including "Other"
    test_ads = [
        {
            "AdArchiveID": "1",
            "TextUnified": "Have you been exposed to PFAS chemicals? You may be entitled to compensation.",
            "PageName": "Test Law Firm"
        },
        {
            "AdArchiveID": "2", 
            "TextUnified": "Roundup users diagnosed with cancer may qualify for significant compensation.",
            "PageName": "Legal Help Center"
        },
        {
            "AdArchiveID": "3",
            "TextUnified": "This is a random ad about something unrelated to any campaign.",
            "PageName": "Random Business"
        },
        {
            "AdArchiveID": "4",
            "TextUnified": "Another unrelated ad that should be classified as Other.",
            "PageName": "Another Business"
        },
        {
            "AdArchiveID": "5",
            "TextUnified": "Wells Fargo payment assistance investigation - were you denied help?",
            "PageName": "Consumer Protection"
        },
        {
            "AdArchiveID": "6",
            "TextUnified": "Join our investigation into unfair practices affecting consumers.",
            "PageName": "Legal Investigation"
        },
        {
            "AdArchiveID": "7",
            "TextUnified": "Generic legal services for all your needs.",
            "PageName": "Law Office"
        },
        {
            "AdArchiveID": "8",
            "TextUnified": "Have you been wronged? We can help with various legal matters.",
            "PageName": "Legal Aid"
        },
        {
            "AdArchiveID": "9",
            "TextUnified": "Consumer rights violation? Contact us for a free case review.",
            "PageName": "Consumer Law"
        },
        {
            "AdArchiveID": "10",
            "TextUnified": "Legal representation for complex litigation matters.",
            "PageName": "Litigation Group"
        },
        {
            "AdArchiveID": "11",
            "TextUnified": "Class action lawsuit investigation - multiple industries affected.",
            "PageName": "Class Action Center"
        },
        {
            "AdArchiveID": "12",
            "TextUnified": "Protect your rights with experienced legal counsel.",
            "PageName": "Rights Protection"
        }
    ]
    
    # Classify the ads
    output_file = "test_files/test_embedding_dbscan_results.csv"
    results = classifier.classify_batch(test_ads, output_csv=output_file)
    
    # Load and analyze the results
    df = pd.read_csv(output_file)
    
    print("\n=== Test Results Summary ===")
    print(f"Total ads processed: {len(df)}")
    print(f"\nCampaign distribution:")
    print(df['campaign'].value_counts())
    
    print("\n=== Embedding Confidence Analysis ===")
    print(f"Non-zero embedding confidence count: {(df['embedding_confidence'] > 0).sum()}")
    print(f"Embedding confidence statistics:")
    print(df['embedding_confidence'].describe())
    
    print("\n=== DBSCAN Clustering Analysis ===")
    other_ads = df[df['campaign'] == 'Other']
    print(f"Number of 'Other' ads: {len(other_ads)}")
    
    if 'dbscan_cluster' in df.columns:
        print(f"DBSCAN clusters assigned: {other_ads['dbscan_cluster'].notna().sum()}")
        if other_ads['dbscan_cluster'].notna().any():
            print(f"Unique clusters: {other_ads['dbscan_cluster'].dropna().unique()}")
    else:
        print("WARNING: dbscan_cluster column not found in output!")
    
    print("\n=== Sample Output (first 5 rows) ===")
    print(df[['ad_id', 'campaign', 'method', 'embedding_confidence', 'dbscan_cluster']].head())
    
    print("\n=== 'Other' Ads Details ===")
    if len(other_ads) > 0:
        print(other_ads[['ad_id', 'campaign', 'method', 'embedding_confidence', 'dbscan_cluster']])

if __name__ == "__main__":
    main()