#!/usr/bin/env python3
"""
Simple test for Company extraction logic in VectorClusterer.
Tests the requirement:
1. If campaign is matched by a rule and Company is NOT null, use that company
2. If Company is null, attempt to extract the company from the text of the ad
"""

import json
import logging
from typing import Dict, Any, List, Optional

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class MockAdCampaignMatcher:
    """Mock AdCampaignMatcher for testing."""
    
    def __init__(self, campaigns: List[Dict[str, Any]]):
        self.known_campaigns = campaigns
        self.logger = logger
        
    def match_ad_text(self, text: str) -> Optional[str]:
        """Simple text matching against campaign triggers."""
        text_lower = text.lower()
        
        for campaign in self.known_campaigns:
            triggers = campaign.get('triggers', [])
            for trigger in triggers:
                if trigger.lower() in text_lower:
                    return campaign['LitigationName']
        
        return None
    
    def get_campaign_company(self, litigation_name: str) -> Optional[str]:
        """Get the Company field for a given LitigationName."""
        for campaign in self.known_campaigns:
            if campaign.get('LitigationName') == litigation_name:
                return campaign.get('Company')
        return None


def mock_extract_company_with_ner(ad_data: Dict[str, Any]) -> Optional[str]:
    """
    Mock NER extraction that simulates finding companies in text.
    For testing purposes, it looks for known company names.
    """
    # Combine text fields
    text_parts = []
    for field in ['title', 'body', 'summary', 'ImageText', 'image_text']:
        value = ad_data.get(field, '')
        if value and isinstance(value, str):
            text_parts.append(value)
    
    combined_text = " ".join(text_parts).lower()
    
    # Mock company extraction - look for known company names
    known_companies = {
        'oakwood': 'Oakwood Corporation',
        'apple': 'Apple Inc.',
        'google': 'Google LLC',
        'johnson & johnson': 'Johnson & Johnson',
        'j&j': 'Johnson & Johnson',
        'amazon': 'Amazon.com Inc.',
        'microsoft': 'Microsoft Corporation'
    }
    
    for keyword, company_name in known_companies.items():
        if keyword in combined_text:
            return company_name
    
    return None


def process_ad_with_company_logic(ad_data: Dict[str, Any], matcher: MockAdCampaignMatcher) -> Dict[str, Any]:
    """
    Simulates the get_category_for_ad_data logic focusing on Company extraction.
    """
    result = ad_data.copy()
    
    # Prepare text for matching
    title = str(ad_data.get('title', ''))
    body = str(ad_data.get('body', ''))
    combined_text = f"{title} {body}".lower()
    
    # Try to match campaign
    matched_campaign = matcher.match_ad_text(combined_text)
    
    if matched_campaign:
        result['campaign'] = matched_campaign
        
        # Get Company from campaign rules
        campaign_company = matcher.get_campaign_company(matched_campaign)
        
        # Apply the company extraction logic
        if campaign_company is not None:
            result['Company'] = campaign_company
            logger.info(f"Using Company from campaign rule: {campaign_company}")
        else:
            # Extract company using NER
            extracted_company = mock_extract_company_with_ner(ad_data)
            result['Company'] = extracted_company
            if extracted_company:
                logger.info(f"Extracted Company using NER: {extracted_company}")
            else:
                logger.info("No Company found via NER extraction")
    else:
        result['campaign'] = 'Other'
        # For unmatched campaigns, still try to extract company
        extracted_company = mock_extract_company_with_ner(ad_data)
        result['Company'] = extracted_company
        if extracted_company:
            logger.info(f"Extracted Company using NER for 'Other' campaign: {extracted_company}")
    
    return result


def main():
    """Run the test scenarios."""
    
    # Test campaign configuration
    campaigns = [
        {
            "Company": "PowerSchool Holdings",
            "LitigationName": "Power School Holdings Data Breach Litigation",
            "triggers": ["powerschool", "data breach"]
        },
        {
            "Company": None,  # This will trigger NER extraction
            "LitigationName": "Juvenile Detention Abuse",
            "triggers": ["youth center", "juvenile detention", "detention center"]
        },
        {
            "Company": "Johnson & Johnson",
            "LitigationName": "J&J Baby Powder Litigation",
            "triggers": ["baby powder", "talcum", "j&j"]
        }
    ]
    
    # Test ads
    test_ads = [
        {
            "id": "test_001",
            "title": "PowerSchool Data Breach Class Action",
            "body": "Were you affected by the data breach?",
            "expected_campaign": "Power School Holdings Data Breach Litigation",
            "expected_company": "PowerSchool Holdings",
            "expected_source": "campaign rule"
        },
        {
            "id": "test_002",
            "title": "Youth Center Abuse Claims",
            "body": "If you suffered abuse at Oakwood Youth Center, contact us.",
            "expected_campaign": "Juvenile Detention Abuse",
            "expected_company": "Oakwood Corporation",
            "expected_source": "NER extraction"
        },
        {
            "id": "test_003",
            "title": "Apple iPhone Battery Lawsuit",
            "body": "Apple admitted to slowing down older iPhones.",
            "expected_campaign": "Other",
            "expected_company": "Apple Inc.",
            "expected_source": "NER extraction (no campaign match)"
        },
        {
            "id": "test_004",
            "title": "J&J Baby Powder Cancer Claims",
            "body": "Johnson & Johnson talcum powder linked to cancer.",
            "expected_campaign": "J&J Baby Powder Litigation",
            "expected_company": "Johnson & Johnson",
            "expected_source": "campaign rule"
        }
    ]
    
    # Create matcher
    matcher = MockAdCampaignMatcher(campaigns)
    
    print("\n" + "="*80)
    print("Testing Company Extraction Logic")
    print("="*80)
    
    # Run tests
    for test_ad in test_ads:
        print(f"\nTest Case: {test_ad['id']}")
        print(f"Title: {test_ad['title']}")
        print(f"Body: {test_ad['body']}")
        
        # Process the ad
        ad_data = {
            'title': test_ad['title'],
            'body': test_ad['body']
        }
        
        result = process_ad_with_company_logic(ad_data, matcher)
        
        # Check results
        print(f"\nResults:")
        print(f"  Campaign: {result.get('campaign')} (expected: {test_ad['expected_campaign']})")
        print(f"  Company: {result.get('Company')} (expected: {test_ad['expected_company']})")
        print(f"  Source: {test_ad['expected_source']}")
        
        # Verify
        campaign_match = result.get('campaign') == test_ad['expected_campaign']
        company_match = result.get('Company') == test_ad['expected_company']
        
        if campaign_match and company_match:
            print("  ✅ PASS")
        else:
            print("  ❌ FAIL")
            if not campaign_match:
                print(f"    - Campaign mismatch")
            if not company_match:
                print(f"    - Company mismatch")
        
        print("-" * 80)
    
    print("\nTest completed!")


if __name__ == "__main__":
    main()