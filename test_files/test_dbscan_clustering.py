#!/usr/bin/env python3
"""
Test script to verify DBSCAN clustering functionality for 'Other' campaigns
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.scripts.hybrid_classifier import HybridClassifier
import pandas as pd
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_data():
    """Create test data with some ads that will be classified as 'Other'"""
    test_ads = [
        # These should classify as "Other"
        {"AdArchiveID": "1", "ad_summary": "Generic product sale advertisement"},
        {"AdArchiveID": "2", "ad_summary": "Another generic product promotion"},
        {"AdArchiveID": "3", "ad_summary": "General services offered"},
        {"AdArchiveID": "4", "ad_summary": "Random business advertisement"},
        {"AdArchiveID": "5", "ad_summary": "Unrelated product marketing"},
        {"AdArchiveID": "6", "ad_summary": "Basic service announcement"},
        {"AdArchiveID": "7", "ad_summary": "Generic business promotion"},
        {"AdArchiveID": "8", "ad_summary": "Random service offering"},
        {"AdArchiveID": "9", "ad_summary": "General product description"},
        {"AdArchiveID": "10", "ad_summary": "Miscellaneous advertisement"},
        {"AdArchiveID": "11", "ad_summary": "Another generic ad"},
        {"AdArchiveID": "12", "ad_summary": "Basic product info"},
        {"AdArchiveID": "13", "ad_summary": "Random promotional content"},
        {"AdArchiveID": "14", "ad_summary": "General business info"},
        {"AdArchiveID": "15", "ad_summary": "Unspecified service details"},
        # Add a few that might match existing campaigns for comparison
        {"AdArchiveID": "16", "ad_summary": "Roundup weed killer lawsuit - call now for compensation"},
        {"AdArchiveID": "17", "ad_summary": "Talcum powder cancer lawsuit - Johnson & Johnson victims"},
    ]
    return test_ads

def main():
    logger.info("Starting DBSCAN clustering test")
    
    # Initialize classifier with improve_rules enabled
    classifier = HybridClassifier(
        campaign_config_path="src/config/fb_ad_categorizer/campaign_config.json",
        text_processing_fields=["ad_summary"],
        embedder_model="all-MiniLM-L6-v2",
        use_llm=False,  # Disable LLM for this test
        improve_rules=True  # Enable rule improvement to trigger DBSCAN clustering
    )
    
    # Create test data
    test_ads = create_test_data()
    logger.info(f"Created {len(test_ads)} test ads")
    
    # Classify ads
    output_csv = "test_files/dbscan_test_results.csv"
    results = classifier.classify_batch(
        ads_data_list=test_ads,
        output_csv=output_csv
    )
    
    # Check results
    df = pd.read_csv(output_csv)
    logger.info(f"\nClassification results saved to {output_csv}")
    
    # Filter for 'Other' campaigns with DBSCAN clusters
    other_df = df[df['campaign'] == 'Other']
    logger.info(f"\nFound {len(other_df)} ads classified as 'Other'")
    
    if 'DBSCAN_Cluster' in df.columns:
        clustered_df = other_df[other_df['DBSCAN_Cluster'].notna()]
        logger.info(f"Found {len(clustered_df)} 'Other' ads with DBSCAN clusters assigned")
        
        if not clustered_df.empty:
            logger.info("\nDBSCAN Cluster assignments:")
            cluster_counts = clustered_df['DBSCAN_Cluster'].value_counts().sort_index()
            for cluster_id, count in cluster_counts.items():
                logger.info(f"  Cluster {int(cluster_id)}: {count} ads")
                # Show sample ads from each cluster
                cluster_ads = clustered_df[clustered_df['DBSCAN_Cluster'] == cluster_id]
                for _, ad in cluster_ads.head(3).iterrows():
                    logger.info(f"    - Ad {ad['ad_id']}: {ad.get('raw_text_combined', '')[:60]}...")
    else:
        logger.warning("DBSCAN_Cluster column not found in results!")
    
    logger.info("\nTest complete!")

if __name__ == "__main__":
    main()