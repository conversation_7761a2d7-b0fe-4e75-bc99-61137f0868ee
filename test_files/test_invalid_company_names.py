#!/usr/bin/env python
"""Test that invalid company names are never returned"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.scripts.hybrid_classifier import CompanyNameNormalizer

def test_invalid_names():
    """Test that invalid names are filtered out"""
    
    normalizer = CompanyNameNormalizer()
    
    # Test invalid names that should be filtered
    invalid_names = [
        "NA",
        "N/A", 
        "n.a.",
        "SKIPPED",
        "skipped",
        "Summary generation failed",
        "summary generation failed",
        "Failed",
        "Error",
        "Unknown",
        "Various",
        "Multiple",
        "TBD",
        "Not Available",
        "undefined",
        "null",
        "None",
        "",
        "  ",  # Just whitespace
        "Summary generation failed for this ad"
    ]
    
    print("Testing invalid company name filtering...\n")
    
    for name in invalid_names:
        # Test normalization
        normalized = normalizer._normalize_company_name(name)
        
        # Test add_or_match
        canonical, original = normalizer.add_or_match_company(name)
        
        print(f"Input: '{name}'")
        print(f"Normalized: '{normalized}'")
        print(f"Add/Match result: canonical='{canonical}', original='{original}'")
        
        # Verify it's filtered out
        if canonical is None and original is None and normalized == "":
            print("✓ PASS - Correctly filtered out")
        else:
            print("✗ FAIL - Should have been filtered out!")
        print("-" * 50)
    
    # Test valid names that should NOT be filtered
    print("\nTesting valid company names...\n")
    
    valid_names = [
        "Apple Inc.",
        "Microsoft",
        "3M Company",
        "Johnson & Johnson",
        "CrowdStrike"
    ]
    
    for name in valid_names:
        normalized = normalizer._normalize_company_name(name)
        canonical, original = normalizer.add_or_match_company(name)
        
        print(f"Input: '{name}'")
        print(f"Normalized: '{normalized}'")
        print(f"Add/Match result: canonical='{canonical}', original='{original}'")
        
        if canonical is not None and original is not None:
            print("✓ PASS - Valid company preserved")
        else:
            print("✗ FAIL - Valid company was filtered!")
        print("-" * 50)

if __name__ == "__main__":
    test_invalid_names()