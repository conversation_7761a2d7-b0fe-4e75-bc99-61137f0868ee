#!/usr/bin/env python
"""Test Flair entity extraction directly"""
import sys
sys.path.append('src')

from flair.data import Sentence
from flair.models import SequenceTagger
import torch

# Test text with obvious entities
test_text = """
Apple Inc. announced that CEO <PERSON> will meet with President <PERSON><PERSON> 
at the White House in Washington DC next Monday to discuss technology 
regulations. Microsoft and Google representatives will also attend.
The meeting is scheduled for January 15th at 2:00 PM EST.
"""

print("Testing Flair NER extraction...")
print(f"Test text: {test_text[:100]}...")

# Set device
device = torch.device('mps' if torch.backends.mps.is_available() else 'cpu')
print(f"Using device: {device}")

# Load Flair NER model
print("\nLoading Flair NER model...")
tagger = SequenceTagger.load("flair/ner-english-large")
if device.type == 'mps':
    tagger = tagger.to(device)

# Create sentence and predict
print("\nProcessing text...")
sentence = Sentence(test_text)
tagger.predict(sentence)

# Extract entities
print("\nExtracted entities:")
entities_found = []
for entity in sentence.get_spans('ner'):
    label = entity.get_label('ner').value
    text = entity.text
    score = entity.get_label('ner').score
    entities_found.append({
        'text': text,
        'label': label,
        'score': score
    })
    print(f"  - {text} ({label}) [confidence: {score:.3f}]")

print(f"\nTotal entities found: {len(entities_found)}")

# Test the label mapping
label_mapping = {
    'PER': 'PERSON',
    'LOC': 'LOC',
    'ORG': 'ORG',
    'MISC': 'PRODUCT',
}

print("\nMapped entities:")
for ent in entities_found:
    mapped_label = label_mapping.get(ent['label'], ent['label'])
    print(f"  - {ent['text']} -> {mapped_label}")