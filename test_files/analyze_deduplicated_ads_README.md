# Deduplicated Ads Analysis Script

This script analyzes deduplicated Facebook ads CSV files to understand embedding similarities and clustering patterns.

## Features

### Analysis One: Identical Items Similarity
- Finds ads with identical PageName, Title, and Body
- Calculates cosine similarity between their `raw_text_combined` embeddings
- Adds columns to the CSV:
  - `identical_group_id`: Group identifier (e.g., G0001, G0002)
  - `identical_group_avg_similarity`: Average similarity within the group
- Outputs:
  - `identical_items_detailed_analysis.csv` - Detailed pairwise similarity analysis
  - `identical_items_similarity_distribution.png` - Histogram of similarity scores

### Analysis Two: Rule-Based Classification Sub-clustering
- Analyzes ads classified by rules to find sub-clusters within each campaign
- Uses DBSCAN clustering on embeddings to identify distinct groups
- Adds column to the CSV:
  - `rule_subcluster_id`: Cluster identifier (e.g., Campaign_C01, Campaign_NOISE)
- Outputs:
  - `rule_subclusters_detailed_analysis.csv` - Detailed cluster breakdown
  - `rule_subcluster_analysis/` directory containing:
    - Individual campaign cluster visualizations (PCA plots)
    - `cluster_summary_charts.png` - Summary charts showing cluster distribution
  - `{original_filename}_with_analysis.csv` - Updated CSV with all new columns

## Usage

```bash
# Using just the filename (will look in src/config/fb_ad_categorizer/)
python analyze_deduplicated_ads.py --config hybrid_classifier_config.yml

# Or using a full path
python analyze_deduplicated_ads.py --config /path/to/your/config.yml
```

### Arguments

- `--config`: Config filename or path. If just a filename is provided, the script looks in `src/config/fb_ad_categorizer/`

### Required Config Settings

The config YAML file must contain:
```yaml
output:
  deduplicated_csv_file: path/to/your/deduplicated.csv
```

### Optional Config Settings

The script also reads these optional settings from the config file:
```yaml
models:
  embedder:
    cache_file: embedding_cache.pkl  # Path to embedding cache

# DBSCAN parameters for clustering
dbscan_min_samples: 5    # Minimum samples for DBSCAN clustering
dbscan_eps: 0.3          # DBSCAN eps parameter for clustering distance
```

## Requirements

The script requires the following Python packages:
- pandas
- numpy
- scikit-learn
- matplotlib
- seaborn
- pyyaml

## Example Output

### Analysis One Output
```
=== Analysis One: Similarity of Identical Items ===
Found 245 pairwise comparisons across 89 unique groups
Average similarity: 0.982
Min similarity: 0.876
Max similarity: 1.000

12 pairs with similarity < 0.95:
  Similarity: 0.876 | Page: Smith Law Firm | IDs: 123456 vs 789012
  Similarity: 0.891 | Page: Johnson & Associates | IDs: 345678 vs 901234
  ...
```

### Analysis Two Output
```
=== Analysis Two: Sub-clustering Analysis for Rule-Classified Items ===
Found 1,234 rule-classified items
Found 15 unique campaigns with rule classifications

Analyzing Roundup: 156 items
  Found 3 clusters and 12 noise points

Analyzing 3M Earplug: 89 items
  Found 2 clusters and 5 noise points
  
=== Clustering Summary ===
Roundup:
  Total clusters: 3
  Cluster sizes: [45, 67, 32]
  
3M Earplug:
  Total clusters: 2
  Cluster sizes: [41, 43]
```

## Understanding the Results

### Similarity Analysis
- High similarity (>0.95) indicates the ads are essentially the same despite minor variations
- Low similarity (<0.95) may indicate:
  - Different law firms using the same ad template
  - Significant updates to ad content while keeping the same structure
  - Data quality issues in the deduplication process

### Clustering Analysis
- Multiple clusters within a campaign suggest different messaging strategies or law firms
- Large clusters indicate common templates or coordinated campaigns
- Noise points are ads that don't fit well into any cluster (unique messaging)

## Notes

- The script uses the same embedding cache format as hybrid_classifier.py
- Embeddings must be pre-computed and cached before running this analysis
- DBSCAN parameters may need tuning based on your data:
  - Increase `eps` for broader clusters
  - Increase `min-samples` for denser clusters