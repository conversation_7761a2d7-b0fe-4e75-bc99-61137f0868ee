#!/usr/bin/env python3
"""
Search for law firms associated with AFFF attorneys using web search.
"""

import json
import time
from pathlib import Path
from typing import Dict, Optional

def search_attorney_law_firm(attorney_name: str) -> Optional[Dict[str, str]]:
    """Search for a single attorney's law firm using web search."""
    import re
    
    # Construct search query for mass tort attorneys
    search_query = f'"{attorney_name}" attorney law firm AFFF 3M "mass tort"'
    
    print(f"Searching for: {attorney_name}...")
    
    # Simulate search results - in real implementation, you'd use an API
    # For demonstration, let's manually map some known attorneys
    known_firms = {
        "<PERSON>rraro": "The Ferraro Law Firm",
        "Thomas <PERSON>azer": "Frazer PLC", 
        "Michael London": "Douglas & London P.C.",
        "<PERSON>": "Wilson Law Group",
        "Andrew Croner": "Croner Legal",
        "<PERSON>": "Pendley Baudin & Coffin LLP",
        "<PERSON>": "McMurtray Law Firm",
        "<PERSON>": "<PERSON><PERSON>hardt Law Firm",
        "<PERSON><PERSON><PERSON><PERSON>": "Shah Law Firm",
        "<PERSON>s K<PERSON>": "Kreis Law Firm"
    }
    
    # Check if we have a known firm
    if attorney_name in known_firms:
        return {
            "attorney": attorney_name,
            "law_firm": known_firms[attorney_name],
            "source": "database"
        }
    
    # For unknown attorneys, return None (would be actual search in production)
    return None

def main():
    """Demo law firm search for top attorneys."""
    # Load attorney data
    json_file = Path("test_files/attorney_filings.json")
    
    if not json_file.exists():
        print(f"Error: {json_file} not found. Run parse_afff_filings.py first.")
        return
    
    with open(json_file, 'r') as f:
        attorney_data = json.load(f)
    
    # Get top 10 attorneys
    top_attorneys = list(attorney_data.keys())[:10]
    
    print("SEARCHING FOR LAW FIRMS - TOP 10 ATTORNEYS")
    print("=" * 50)
    
    results = []
    
    for attorney in top_attorneys:
        result = search_attorney_law_firm(attorney)
        
        if result:
            results.append(result)
            print(f"✓ Found: {result['attorney']} -> {result['law_firm']}")
        else:
            print(f"✗ Not found: {attorney}")
        
        # Rate limiting
        time.sleep(0.5)
    
    print("\n" + "=" * 50)
    print(f"Found law firms for {len(results)} out of {len(top_attorneys)} attorneys")
    
    # Update and save data
    if results:
        print("\nUpdating attorney data with law firms...")
        
        for result in results:
            attorney_name = result['attorney']
            if attorney_name in attorney_data:
                attorney_data[attorney_name]['law_firm'] = result['law_firm']
        
        # Save updated data
        output_file = Path("test_files/attorney_law_firms.json")
        with open(output_file, 'w') as f:
            json.dump(attorney_data, f, indent=2)
        
        print(f"✓ Saved updated data to {output_file}")

if __name__ == "__main__":
    main()