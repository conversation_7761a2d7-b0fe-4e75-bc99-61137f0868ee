#!/usr/bin/env python3
"""
Check if there's a new CSV file to process and run the appropriate scripts.
"""

import os
import sys
from pathlib import Path
from datetime import datetime

def check_for_new_csv():
    """Look for AFFF CSV files that need processing."""
    
    # Possible CSV file names
    csv_patterns = [
        "AFFF-Filings-Case-Shell.csv",
        "afff-filings-case-shell.csv",
        "AFFF_Filings_Case_Shell.csv",
        "*AFFF*Case*Shell*.csv"
    ]
    
    # Check current directory
    found_csv = None
    for pattern in csv_patterns:
        csv_path = Path(pattern)
        if csv_path.exists():
            found_csv = csv_path
            break
    
    # If not found, check parent directory
    if not found_csv:
        parent_dir = Path("..")
        for pattern in csv_patterns:
            csv_files = list(parent_dir.glob(pattern))
            if csv_files:
                found_csv = csv_files[0]
                break
    
    if found_csv:
        print(f"✓ Found CSV file: {found_csv}")
        
        # Check if it's newer than our parsed data
        parsed_csv = Path("parsed_afff_filings.csv")
        if parsed_csv.exists():
            csv_mtime = datetime.fromtimestamp(found_csv.stat().st_mtime)
            parsed_mtime = datetime.fromtimestamp(parsed_csv.stat().st_mtime)
            
            if csv_mtime > parsed_mtime:
                print(f"  → CSV is newer than parsed data (CSV: {csv_mtime.strftime('%Y-%m-%d %H:%M')}, Parsed: {parsed_mtime.strftime('%Y-%m-%d %H:%M')})")
                print("\nTo process this new CSV file, run:")
                print(f"  python parse_afff_filings.py")
                print(f"  python smart_merge.py")
                return True
            else:
                print(f"  → CSV has already been processed (last modified: {csv_mtime.strftime('%Y-%m-%d %H:%M')})")
                return False
        else:
            print("  → No parsed data found. This CSV needs to be processed.")
            print("\nTo process this CSV file, run:")
            print(f"  python parse_afff_filings.py")
            print(f"  python smart_merge.py")
            return True
    else:
        print("✗ No AFFF CSV file found in current or parent directory")
        print("\nLooking for files matching patterns:")
        for pattern in csv_patterns:
            print(f"  - {pattern}")
        return False

def main():
    """Main entry point."""
    print("AFFF CSV File Checker")
    print("="*50)
    
    needs_processing = check_for_new_csv()
    
    print("\nCurrent status:")
    print(f"  - attorney_law_firm_lookup.json: ", end="")
    if Path("attorney_law_firm_lookup.json").exists():
        print("✓ Found (authoritative source)")
    else:
        print("✗ Missing")
    
    print(f"  - attorney_filings.json: ", end="")
    if Path("attorney_filings.json").exists():
        print("✓ Found")
    else:
        print("✗ Missing")
    
    print(f"  - attorney_comprehensive_data.json: ", end="")
    if Path("attorney_comprehensive_data.json").exists():
        print("✓ Found")
    else:
        print("✗ Missing")
    
    return 0 if not needs_processing else 1

if __name__ == "__main__":
    sys.exit(main())