#!/usr/bin/env python3
"""
Create visualizations for filing analysis data.
"""

import json
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path

def create_visualizations():
    """Create charts for filing analysis."""
    
    # Load analysis data
    with open('filing_analysis.json', 'r') as f:
        data = json.load(f)
    
    # Set style
    plt.style.use('seaborn-v0_8-darkgrid')
    sns.set_palette("husl")
    
    # Create figure with subplots
    fig = plt.figure(figsize=(16, 12))
    
    # 1. Top Law Firms Bar Chart
    ax1 = plt.subplot(2, 2, 1)
    top_firms = data['filings_by_law_firm'][:10]
    firms = [f['law_firm'][:30] + '...' if len(f['law_firm']) > 30 else f['law_firm'] 
             for f in top_firms]
    counts = [f['total_filings'] for f in top_firms]
    
    bars = ax1.barh(firms, counts, color='skyblue', edgecolor='navy', alpha=0.7)
    ax1.set_xlabel('Number of Filings', fontsize=12)
    ax1.set_title('Top 10 Law Firms by Filing Count', fontsize=14, fontweight='bold')
    
    # Add value labels
    for i, (bar, count) in enumerate(zip(bars, counts)):
        ax1.text(bar.get_width() + 5, bar.get_y() + bar.get_height()/2, 
                f'{count:,}', va='center', fontsize=10)
    
    ax1.invert_yaxis()
    
    # 2. Monthly Filing Trend
    ax2 = plt.subplot(2, 2, 2)
    months = [item['month'].split(' ')[1][1:-1] for item in data['filings_by_month']]
    month_counts = [item['count'] for item in data['filings_by_month']]
    
    bars = ax2.bar(months, month_counts, color='lightcoral', edgecolor='darkred', alpha=0.7)
    ax2.set_xlabel('Month', fontsize=12)
    ax2.set_ylabel('Number of Filings', fontsize=12)
    ax2.set_title('Filings by Month', fontsize=14, fontweight='bold')
    
    # Add value labels
    for bar, count in zip(bars, month_counts):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 10,
                f'{count:,}', ha='center', va='bottom', fontsize=10)
    
    # 3. Daily Filing Distribution (last 30 days)
    ax3 = plt.subplot(2, 2, 3)
    recent_dates = data['filings_by_date'][-30:]
    dates = [datetime.strptime(item['date'], "%m/%d/%Y").strftime("%m/%d") 
             for item in recent_dates]
    daily_counts = [item['count'] for item in recent_dates]
    
    ax3.plot(dates, daily_counts, marker='o', linewidth=2, markersize=6, 
             color='darkgreen', markerfacecolor='lightgreen')
    ax3.set_xlabel('Date', fontsize=12)
    ax3.set_ylabel('Number of Filings', fontsize=12)
    ax3.set_title('Daily Filings (Last 30 Days)', fontsize=14, fontweight='bold')
    ax3.tick_params(axis='x', rotation=45)
    ax3.grid(True, alpha=0.3)
    
    # 4. Filing Attorneys per Firm Distribution
    ax4 = plt.subplot(2, 2, 4)
    
    # Count how many firms have 1, 2, 3+ filing attorneys
    attorney_distribution = {}
    for f in data['filings_by_law_firm']:
        count = f['attorney_count']
        if count >= 3:
            key = '3+ attorneys'
        else:
            key = f'{count} attorney' if count == 1 else f'{count} attorneys'
        
        if key not in attorney_distribution:
            attorney_distribution[key] = {'firms': 0, 'filings': 0}
        
        attorney_distribution[key]['firms'] += 1
        attorney_distribution[key]['filings'] += f['total_filings']
    
    # Create grouped bar chart
    categories = sorted(attorney_distribution.keys())
    firm_counts = [attorney_distribution[cat]['firms'] for cat in categories]
    filing_counts = [attorney_distribution[cat]['filings'] for cat in categories]
    
    x = range(len(categories))
    width = 0.35
    
    bars1 = ax4.bar([i - width/2 for i in x], firm_counts, width, 
                     label='Number of Firms', color='lightblue', edgecolor='navy')
    bars2 = ax4.bar([i + width/2 for i in x], [f/50 for f in filing_counts], width,
                     label='Total Filings (÷50)', color='lightgreen', edgecolor='darkgreen')
    
    ax4.set_xlabel('Filing Attorneys per Firm', fontsize=12)
    ax4.set_ylabel('Count', fontsize=12)
    ax4.set_title('Distribution of Filing Attorneys per Law Firm', fontsize=14, fontweight='bold')
    ax4.set_xticks(x)
    ax4.set_xticklabels(categories)
    ax4.legend()
    
    # Add value labels
    for bar in bars1:
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{int(height)}', ha='center', va='bottom', fontsize=9)
    
    for bar, filing_count in zip(bars2, filing_counts):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{filing_count:,}', ha='center', va='bottom', fontsize=9)
    
    plt.tight_layout()
    
    # Save the figure
    output_file = Path('filing_analysis_charts.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"✓ Charts saved to {output_file}")
    
    # Create summary statistics
    summary_stats = {
        "total_filings": data['summary'].get('total_filings_in_csv', data['summary']['total_filings']),
        "total_with_attorneys": data['summary']['total_filings'],
        "filings_without_attorneys": data['summary'].get('filings_without_attorneys', 0),
        "top_firm": {
            "name": data['filings_by_law_firm'][0]['law_firm'],
            "filings": data['filings_by_law_firm'][0]['total_filings'],
            "percentage": round(data['filings_by_law_firm'][0]['total_filings'] / 
                              data['summary']['total_filings'] * 100, 1)
        },
        "busiest_month": max(data['filings_by_month'], key=lambda x: x['count']),
        "busiest_day": max(data['filings_by_date'], key=lambda x: x['count']),
        "average_filings_per_attorney": round(data['summary']['total_filings'] / 
                                            data['summary']['total_attorneys'], 1),
        "average_filings_per_firm": round(data['summary']['total_filings'] / 
                                        data['summary']['total_law_firms'], 1)
    }
    
    # Save summary statistics
    stats_file = Path('filing_summary_stats.json')
    with open(stats_file, 'w') as f:
        json.dump(summary_stats, f, indent=2)
    print(f"✓ Summary statistics saved to {stats_file}")

if __name__ == "__main__":
    create_visualizations()