# AFFF Filings Analysis

This folder contains all scripts and data files for analyzing AFFF (Aqueous Film-Forming Foam) litigation filings.

## Files Overview

### Data Files
- `attorney_comprehensive_data.json` - Complete attorney data with law firms and all filing details
- `attorney_law_firm_lookup.json` - Law firm lookup table for all attorneys
- `attorney_filings.json` - Raw filing data by attorney
- `parsed_afff_filings.csv` - Parsed CSV data from original filings
- `filing_analysis.json` - Detailed analysis results
- `filing_summary_stats.json` - Summary statistics

### Scripts
- `parse_afff_filings.py` - Interactive parser for AFFF CSV data
- `merge_attorney_data.py` - Merges law firm data with filing information
- `analyze_filings.py` - Analyzes filings by law firm and date
- `visualize_filings.py` - Creates visualization charts
- `run_afff_analysis.py` - Main runner script with options
- `run_afff_analysis.sh` - Bash runner script

### Output Files
- `filing_analysis_charts.png` - Visualization charts

## Usage

### Run Complete Analysis
```bash
# From the AFFF folder
python run_afff_analysis.py

# Or use the bash script
./run_afff_analysis.sh
```

### Run Individual Steps
```bash
# Parse CSV data
python parse_afff_filings.py

# Merge attorney data
python merge_attorney_data.py

# Analyze filings
python analyze_filings.py

# Create visualizations
python visualize_filings.py
```

### Options for run_afff_analysis.py
```bash
# Skip CSV parsing (use existing data)
python run_afff_analysis.py --skip-parse

# Interactive mode
python run_afff_analysis.py --interactive

# Only run analysis
python run_afff_analysis.py --analyze-only
```

## Key Statistics
- Total Attorneys: 77
- Total Law Firms: 65
- Total Filings: 3,206
- Top Law Firm: The Ferraro Law Firm (547 filings)
- Date Range: January 2, 2025 to June 2, 2025

## Notes
- Attorney law firm data has been manually verified and updated
- Some attorneys' law firm affiliations remain unknown
- Filing data is from the AFFF MDL 2873 litigation