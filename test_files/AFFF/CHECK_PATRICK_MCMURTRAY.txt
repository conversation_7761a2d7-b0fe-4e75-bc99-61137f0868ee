PATRICK MCMURTRAY LAW FIRM VERIFICATION
======================================

The case-insensitive matching IS WORKING CORRECTLY!

Here's the proof:

1. ATTORNEY_FILINGS.JSON (Raw parsed data - NO law firms):
   "<PERSON> Mcmurtray": {
     "law_firm": "",  <-- This is SUPPOSED to be empty!
     "filing_count": 184
   }

2. ATTORNEY_LAW_FIRM_LOOKUP.JSON (Authoritative source):
   "<PERSON> McMurtray": {  <-- Note capital M
     "law_firm": "OnderLaw LLC"
   }

3. ATTORNEY_COMPREHENSIVE_DATA.J<PERSON><PERSON> (MERGED data - THIS IS WHAT YOU SHOULD USE!):
   "<PERSON> Mcmurtray": {
     "law_firm": "OnderLaw LLC",  <-- CORRECTLY MATCHED!
     "filing_count": 184
   }

IMPORTANT:
- attorney_filings.json = RAW data from CSV (no law firms)
- attorney_comprehensive_data.json = MERGED data with law firms

ALWAYS USE attorney_comprehensive_data.json FOR LAW FIRM DATA!

The smart_merge.py script successfully:
✓ Found "<PERSON> Mcmurtray" in the CSV
✓ Matched it case-insensitively to "Patrick McMurtray" in the lookup
✓ Applied "OnderLaw LLC" to the comprehensive data
✓ Reported the case mismatch for awareness