#!/usr/bin/env python3
"""Debug script to test if embeddings are being calculated."""

import sys
from pathlib import Path
import numpy as np

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Test if we can create embeddings directly
try:
    from flair.embeddings import SentenceTransformerDocumentEmbeddings
    from flair.data import Sentence
    
    print("=== Testing Flair Embeddings ===")
    
    # Initialize embedder with the model from config
    model_name = "all-roberta-large-v1"
    print(f"Initializing embedder with model: {model_name}")
    embedder = SentenceTransformerDocumentEmbeddings(model_name)
    
    # Test text
    test_text = "Have you been exposed to PFAS chemicals? You may be entitled to compensation."
    print(f"\nTest text: {test_text}")
    
    # Create Flair sentence and embed
    sentence = Sentence(test_text)
    embedder.embed(sentence)
    
    # Get embedding
    embedding = sentence.embedding.numpy()
    print(f"\nEmbedding shape: {embedding.shape}")
    print(f"Embedding norm: {np.linalg.norm(embedding):.4f}")
    print(f"First 10 values: {embedding[:10]}")
    
    # Test similarity calculation
    test_text2 = "PFAS contamination lawsuit investigation for water exposure victims."
    sentence2 = Sentence(test_text2)
    embedder.embed(sentence2)
    embedding2 = sentence2.embedding.numpy()
    
    # Calculate cosine similarity
    similarity = np.dot(embedding, embedding2) / (np.linalg.norm(embedding) * np.linalg.norm(embedding2))
    print(f"\nSimilarity between texts: {similarity:.4f}")
    
    print("\n✓ Embeddings are working correctly!")
    
except Exception as e:
    print(f"\n✗ Error testing embeddings: {e}")
    import traceback
    traceback.print_exc()

# Now test the FlairOptimizedEmbedder
print("\n=== Testing FlairOptimizedEmbedder ===")
try:
    from src.scripts.hybrid_classifier import FlairOptimizedEmbedder
    
    embedder = FlairOptimizedEmbedder(
        model_name="all-roberta-large-v1",
        cache_file="test_embeddings_cache.pkl"
    )
    
    # Test encoding
    texts = [
        "PFAS water contamination lawsuit",
        "Roundup cancer investigation", 
        "Random text about something else"
    ]
    
    print(f"Encoding {len(texts)} texts...")
    embeddings = embedder.encode(texts, show_progress=False)
    
    print(f"Embeddings shape: {embeddings.shape}")
    print(f"All embeddings non-zero: {np.all(np.any(embeddings != 0, axis=1))}")
    
    # Calculate similarities
    for i, text in enumerate(texts):
        norm = np.linalg.norm(embeddings[i])
        print(f"Text {i+1} embedding norm: {norm:.4f}")
    
    print("\n✓ FlairOptimizedEmbedder is working correctly!")
    
except Exception as e:
    print(f"\n✗ Error testing FlairOptimizedEmbedder: {e}")
    import traceback
    traceback.print_exc()