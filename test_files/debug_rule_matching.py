#!/usr/bin/env python
"""
Debug script to understand why rule matching is failing
"""

import re
import json

def normalize_text_for_matching(text: str) -> str:
    """Normalize text for rule matching"""
    if not isinstance(text, str):
        return ""
    text = text.lower()
    text = re.sub(r'[^\w\s]', ' ', text)  # Keep alphanumeric and spaces
    text = re.sub(r'\s+', ' ', text).strip()
    return text

def compile_term_pattern(terms: list, description: str) -> re.Pattern:
    """Compile regex pattern from terms"""
    if not terms:
        return None
    valid_terms = [term for term in terms if term]
    if not valid_terms:
        return None
    pattern_str = r'\b(?:' + '|'.join(re.escape(term) for term in valid_terms) + r')\b'
    return re.compile(pattern_str, re.IGNORECASE)

# Test cases that are failing
test_ads = [
    {
        "text": "Defective Spinal Cord Stimulator Lawsuit A nationwide lawsuit includes multiple defendants responsible for spinal stimulators",
        "expected": "Spinal Cord Stimulator Products Liability",
        "wrongly_matched_to": "Abiomed Impella Heart Pump Products Liability"
    },
    {
        "text": "Dreamland Baby Co. customer? You may be entitled to seek compensation. Have you purchased products from Dreamland Baby Co.",
        "expected": "Should create new campaign or go to Other",
        "wrongly_matched_to": "Bair Hugger Products Liability"
    },
    {
        "text": "Cord Blood Banking Compensation Click To Learn More Parents who invested in Cord Blood Banking may be entitled to compensation",
        "expected": "Should create new campaign or go to Other",
        "wrongly_matched_to": "Bio-Lab Chemical Plant Explosion Liability"
    }
]

# Load campaign config
config_path = "/Users/<USER>/PycharmProjects/lexgenius/src/config/fb_ad_categorizer/campaign_config.json"
with open(config_path, 'r') as f:
    campaigns = json.load(f)

print("Testing rule matching logic:\n")

# Find relevant campaigns
spinal_campaign = None
bair_hugger_campaign = None
biolab_campaign = None

for campaign in campaigns:
    if campaign.get('LitigationName') == 'Spinal Cord Stimulator Products Liability':
        spinal_campaign = campaign
    elif campaign.get('LitigationName') == 'Bair Hugger Products Liability':
        bair_hugger_campaign = campaign
    elif campaign.get('LitigationName') == 'Bio-Lab. Chemical Plan Explosion Liability':
        biolab_campaign = campaign

# Test spinal cord stimulator
print("=" * 80)
print("TEST 1: Spinal Cord Stimulator")
print("=" * 80)
if spinal_campaign:
    print(f"Campaign triggers: {spinal_campaign.get('triggers', [])}")
    normalized_text = normalize_text_for_matching(test_ads[0]['text'])
    print(f"\nNormalized text: {normalized_text}")
    
    # Test trigger matching
    normalized_triggers = [normalize_text_for_matching(t) for t in spinal_campaign.get('triggers', [])]
    print(f"\nNormalized triggers: {normalized_triggers}")
    
    pattern = compile_term_pattern(normalized_triggers, "spinal triggers")
    if pattern:
        matches = pattern.findall(normalized_text)
        print(f"Pattern matches: {matches}")
        print(f"Should match: {'YES' if matches else 'NO'}")
else:
    print("Spinal campaign not found!")

# Test Bair Hugger
print("\n" + "=" * 80)
print("TEST 2: Bair Hugger (should NOT match Dreamland Baby)")
print("=" * 80)
if bair_hugger_campaign:
    print(f"Campaign triggers: {bair_hugger_campaign.get('triggers', [])}")
    normalized_text = normalize_text_for_matching(test_ads[1]['text'])
    print(f"\nNormalized text snippet: {normalized_text[:100]}...")
    
    normalized_triggers = [normalize_text_for_matching(t) for t in bair_hugger_campaign.get('triggers', [])]
    print(f"\nNormalized triggers: {normalized_triggers}")
    
    pattern = compile_term_pattern(normalized_triggers, "bair hugger triggers")
    if pattern:
        matches = pattern.findall(normalized_text)
        print(f"Pattern matches: {matches}")
        print(f"Should match: {'NO - THIS IS WRONG!' if matches else 'NO - Correct'}")

# Test Bio-Lab
print("\n" + "=" * 80)
print("TEST 3: Bio-Lab (should NOT match Cord Blood Banking)")
print("=" * 80)
if biolab_campaign:
    print(f"Campaign triggers: {biolab_campaign.get('triggers', [])}")
    normalized_text = normalize_text_for_matching(test_ads[2]['text'])
    print(f"\nNormalized text snippet: {normalized_text[:100]}...")
    
    normalized_triggers = [normalize_text_for_matching(t) for t in biolab_campaign.get('triggers', [])]
    print(f"\nNormalized triggers: {normalized_triggers}")
    
    pattern = compile_term_pattern(normalized_triggers, "biolab triggers")
    if pattern:
        matches = pattern.findall(normalized_text)
        print(f"Pattern matches: {matches}")
        print(f"Should match: {'NO - THIS IS WRONG!' if matches else 'NO - Correct'}")

# Check embedding thresholds
print("\n" + "=" * 80)
print("EMBEDDING SIMILARITY ANALYSIS")
print("=" * 80)
print("All these ads were matched via embedding with ~0.51 similarity")
print("This is WAY too low a threshold!")
print("The embedding similarity of 0.51 means these ads are only 51% similar")
print("to the campaign prototypes - that's basically random!")