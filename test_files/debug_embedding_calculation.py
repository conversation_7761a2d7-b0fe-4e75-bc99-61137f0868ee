#!/usr/bin/env python3
"""Debug script to trace embedding calculation in hybrid classifier."""

import sys
from pathlib import Path
import logging

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Enable debug logging
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s - %(name)s - %(message)s')

from src.scripts.hybrid_classifier import HybridClassifier

def main():
    print("=== Debug Embedding Calculation ===")
    
    # Simple test data
    test_ads = [
        {
            "AdArchiveID": "test1",
            "TextUnified": "Have you been exposed to PFAS chemicals in drinking water? You may be entitled to compensation.",
            "PageName": "Legal Help Center"
        },
        {
            "AdArchiveID": "test2",
            "TextUnified": "Roundup weed killer linked to cancer. If you used Roundup and developed cancer, contact us.",
            "PageName": "Law Firm"
        },
        {
            "AdArchiveID": "test3",
            "TextUnified": "Generic legal services for all your needs.",
            "PageName": "General Law"
        }
    ]
    
    # Initialize classifier with debug logging
    classifier = HybridClassifier(
        campaign_config_path="src/config/fb_ad_categorizer/campaign_config.json",
        text_processing_fields=["TextUnified", "PageName"],
        embedder_model_name="all-roberta-large-v1",
        embedder_cache_file="test_debug_embeddings.pkl",
        ner_backend="flair",
        ner_cache_file="test_debug_ner.pkl",
        use_llm=False,
        improve_rules_active=False,
        log_level="DEBUG"
    )
    
    # Check if embedder is initialized
    print(f"\nEmbedder initialized: {classifier.embedder is not None}")
    if classifier.embedder:
        print(f"Embedder type: {type(classifier.embedder).__name__}")
    
    # Check campaign prototypes
    print(f"\nCampaign prototypes: {len(classifier.campaign_prototypes) if classifier.campaign_prototypes else 0}")
    if classifier.campaign_prototypes:
        print("Sample campaigns with prototypes:")
        for campaign, proto in list(classifier.campaign_prototypes.items())[:5]:
            print(f"  - {campaign}: embedding shape {proto.shape if hasattr(proto, 'shape') else 'unknown'}")
    
    # Classify with debug output
    print("\n\nClassifying ads...")
    results = classifier.classify_batch(test_ads, output_csv="test_files/debug_embeddings.csv")
    
    # Check results
    print("\n=== Classification Results ===")
    for result in results:
        print(f"\nAd ID: {result.ad_id}")
        print(f"  Campaign: {result.campaign}")
        print(f"  Method: {result.method}")
        print(f"  Rule confidence: {result.rule_confidence:.3f}")
        print(f"  Embedding confidence: {result.embedding_confidence:.3f}")
        print(f"  LLM confidence: {result.llm_confidence:.3f}")

if __name__ == "__main__":
    main()