# Cache Analysis and Structure Documentation

## Overview
This document describes the cache key generation, structure, and analysis results for the FBAdArchive processing system.

## Field Processing Methods

### Lowercase Field Processing (Current Classifier Logic)
```python
YAML_FIELDS = ["Title", "Body", "Summary", "LinkDescription", "PageName"]

# Step 1: Convert field names to lowercase
text_processing_fields_lc = [field.lower() for field in YAML_FIELDS]
# Result: ["title", "body", "summary", "linkdescription", "pagename"]

# Step 2: Convert ad data keys to lowercase for matching
ad_data_lower_keys = {k.lower(): v for k, v in ad.items()}

# Step 3: Extract values using lowercase field names
text_parts = []
for field_name_lc in text_processing_fields_lc:
    value = ad_data_lower_keys.get(field_name_lc)
    if value is not None and str(value).strip():
        text_parts.append(str(value).strip())

# Step 4: Join text parts
combined_text = ' '.join(text_parts)
```

### Uppercase Field Processing (Original YAML Capitalization)
```python
YAML_FIELDS = ["Title", "Body", "Summary", "LinkDescription", "PageName"]

# Step 1: Use field names as-is (with original capitalization)
# Step 2: Extract values using exact field names
text_parts = []
for field_name in YAML_FIELDS:
    value = ad.get(field_name)
    if value is not None and str(value).strip():
        text_parts.append(str(value).strip())

# Step 3: Join text parts
combined_text = ' '.join(text_parts)
```

## Cache Key Generation

### NER Cache Keys

#### Composite Key Format
```python
ad_id = ad.get('AdArchiveID', ad.get('ad_id', 'unknown'))
text_hash = hashlib.md5(combined_text.encode()).hexdigest()
composite_key = f"ner_{ad_id}_{text_hash}"
```

**Example:**
- Ad ID: `614697441055080`
- Combined text: `"FREE and EASY - sign up in 1 minute! People who used Roundup..."`
- Text hash: `54a5560afbfb501ab5db7d765907c962`
- Composite key: `ner_614697441055080_54a5560afbfb501ab5db7d765907c962`

#### Raw Text Key Format
```python
raw_text_key = combined_text
```

**Example:**
- Raw text key: `"FREE and EASY - sign up in 1 minute! People who used Roundup products may be eligible..."`

### Embedding Cache Keys

#### Hash Key Format (Current)
```python
text_hash = hashlib.md5(combined_text.encode()).hexdigest()
cache_key = text_hash
```

**Example:**
- Combined text: `"FREE and EASY - sign up in 1 minute! People who used Roundup..."`
- Cache key: `54a5560afbfb501ab5db7d765907c962`

## Analysis Results

### NER Cache Analysis
- **Original cache size**: 145924 entries
- **Lowercase matches**: 898 entries (0.87% of FBAdArchive)
- **Uppercase matches**: 898 entries (0.87% of FBAdArchive)

#### Lowercase Processing Stats
- Total ads processed: 102,951
- Ads with text: 102,951
- Composite key hits: 900
- Raw text hits: 0
- Total hits: 900
- Misses: 102,051
- **Hit rate: 0.87%**

#### Uppercase Processing Stats
- Total ads processed: 102,951
- Ads with text: 102,951
- Composite key hits: 900
- Raw text hits: 0
- Total hits: 900
- Misses: 102,051
- **Hit rate: 0.87%**

### Embedding Cache Analysis
- **Original cache size**: 45695 entries
- **Cache key format**: hash
- **Lowercase matches**: 727 entries (22.96% of FBAdArchive)
- **Uppercase matches**: 727 entries (22.96% of FBAdArchive)

#### Lowercase Processing Stats
- Total ads processed: 102,951
- Ads with text: 102,951
- Hits: 23,640
- Misses: 79,311
- **Hit rate: 22.96%**

#### Uppercase Processing Stats
- Total ads processed: 102,951
- Ads with text: 102,951
- Hits: 23,640
- Misses: 79,311
- **Hit rate: 22.96%**

## Key Differences Between Processing Methods

### Text Extraction Differences
1. **Field Name Casing**:
   - Lowercase: Uses `["title", "body", "summary", "linkdescription", "pagename"]`
   - Uppercase: Uses `["Title", "Body", "Summary", "LinkDescription", "PageName"]`

2. **Data Key Conversion**:
   - Lowercase: Converts all DynamoDB field names to lowercase before lookup
   - Uppercase: Uses original DynamoDB field names (capitalized)

3. **Text Processing**:
   - Both methods use identical string processing (strip, join with spaces)
   - Both use the same MD5 hashing algorithm

### Impact on Cache Keys
- Different text extraction → Different combined text → Different MD5 hash → Different cache key
- This explains why the same logical content can have different cache entries

## Recommendations

### For Consistent Cache Usage
1. **Choose one processing method** and stick with it consistently
2. **Regenerate caches** if switching methods to avoid duplicate entries
3. **Document the chosen method** clearly for future development

### For Current State
- **Lowercase processing**: 0.9% NER coverage, 23.0% embedding coverage
- **Uppercase processing**: 0.9% NER coverage, 23.0% embedding coverage

### LLM Cache Analysis

#### LLM Cache Key Generation
```python
# Cache key generation from hybrid_classifier.py
def _get_cache_key(text: str, candidate_labels: List[str], context: Optional[str] = None, enhanced: bool = False) -> str:
    max_text_len = 1000
    text_to_classify = text[:max_text_len] if len(text) > max_text_len else text
    
    key_data = {
        'text': text_to_classify,
        'labels': sorted(candidate_labels),  # Campaign names from campaign_config.json
        'context': context,
        'enhanced': enhanced,
        'model': self.model_path,            # e.g., "deepseek-r1:8b-0528-qwen3-q8_0"
        'backend': self.backend,             # e.g., "ollama"
        'type': 'classification'
    }
    return hashlib.md5(json.dumps(key_data, sort_keys=True).encode()).hexdigest()
```

#### Old vs New LLM Cache Processing

**Old Processing (.bak logic):**
```python
# Text extraction with .bak logic
text_processing_fields = ["title", "body", "summary", "linkdescription", "pagename"]  # lowercase
ad_data_lower_keys = {k.lower(): v for k, v in ad_data.items()}

raw_parts = []
for field_name_config in text_processing_fields:
    value = ad_data_lower_keys.get(field_name_config)
    if value and str(value).strip():
        # Special summary validation
        if field_name_config == 'summary':
            normalized_summary_content = str(value).lower()
            if normalized_summary_content in invalid_summary_strings:
                continue
        raw_parts.append(str(value).strip())

old_text = " | ".join(raw_parts)  # Pipe separator
```

**New Processing (YAML fields):**
```python
# Text extraction with exact YAML fields
YAML_FIELDS = ["Title", "Body", "Summary", "LinkDescription", "PageName"]  # exact capitalization

text_parts = []
for field_name in YAML_FIELDS:
    value = ad_data.get(field_name)  # Direct access with exact capitalization
    if value and str(value).strip():
        text_parts.append(str(value).strip())

new_text = " ".join(text_parts)  # Space separator
```

#### LLM Cache Structure Analysis
- **Original cache file**: `llm_response_cache.pkl`
- **New cache file**: `llm_response_cache_new.pkl`
- **Total entries**: 14,800
- **Entry format**: `{cache_key: (campaign_name, confidence_score)}`

**Cache Entry Breakdown:**
- **Successful classifications**: 20 entries (specific campaign names found)
- **Failed classifications**: 14,780 entries (`(None, 0.0)` - for retry with new logic)
- **Total processing investment**: 14,800 LLM classification attempts

**Example Cache Entries:**
```python
# Successful classification
"ef9bd32046a7399481df406256b39cd3": ("Glyhphosate Type 2 Diabetes Products Liability", 0.7)

# Failed classification (for retry)
"1059a716a10881af96bf5171d71a9379": (None, 0.0)
```

#### Migration Strategy
- **Preserve ALL entries**: Complete copy from old to new cache file
- **Successful entries**: 20 entries provide immediate cache hits
- **Failed entries**: 14,780 entries preserved for retry with improved YAML text processing
- **Zero processing loss**: All LLM attempts preserved for optimal M4 Mac performance

## File Outputs

### NER Cache Files
- `ner_results_cache.pkl`: Original NER cache (145,924 entries)
- `ner_cache_new.pkl`: New NER cache with YAML field processing (102,674 entries, 99.94% coverage)

### Embedding Cache Files  
- `embedding_cache_roberta-large-v1.pkl`: Original embedding cache (45,695 entries)
- `embedding_roberta.pkl`: New embedding cache with YAML field processing (22,062 entries, 99.97% coverage)

### LLM Cache Files
- `llm_response_cache.pkl`: Original LLM cache (14,800 entries, old .bak text processing)
- `llm_response_cache_new.pkl`: New LLM cache (14,800 entries, preserved for YAML text processing)
