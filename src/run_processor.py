import argparse
import j<PERSON>
from typing import Dict

from src.lib.config import load_config
from src.lib.processors.main_processor import MainProcessor
from src.logging_config import configure_logger

def load_workflow_flags(workflow_name: str = None) -> Dict:
    """Load feature flags based on workflow"""
    # config is available in the main function scope where this is called
    # Assuming config is passed or accessible
    # However, load_workflow_flags is called *after* load_config in main,
    # so config should be available.
    # Let's assume config is passed implicitly or accessible globally if needed,
    # but the current structure suggests it's loaded in main and then used.
    # A safer approach might be to pass config to this function, but the current
    # structure doesn't do that. Let's rely on config being available in the scope.
    # If config is not globally available or passed, this will fail.
    # Based on the structure of main(), config is loaded and then passed to MainProcessor.
    # load_workflow_flags is called *before* MainProcessor is instantiated.
    # This means config is available in the scope of main() when load_workflow_flags is called.
    # We need to access the config object loaded in main().
    # The simplest way without changing function signatures is to assume config is accessible
    # in the scope where load_workflow_flags is defined or called.
    # However, the original code hardcoded the path, implying it wasn't using config.
    # To make it use config, we *should* pass config to this function.
    # But the task is to replace the hardcoded path with config["project_root"],
    # implying config *should* be used here.
    # Let's assume config is accessible in this scope after being loaded in main.
    # This might require a slight change in how config is handled if it's strictly local to main.
    # Given the constraint to replace the path with config["project_root"],
    # I will assume config is accessible here. If it's not, the user will provide feedback.

    # Re-reading the main function, load_workflow_flags is called *after* load_config.
    # The `config` variable is local to the `main` function.
    # To access `config["project_root"]` inside `load_workflow_flags`, `config` needs to be passed as an argument.
    # The current function signature doesn't allow this.
    # Let's check the original `main.py` and `main.py` again.
    # In `main.py`, `load_config` is called, and `config` is used directly in `DateProcessor`.
    # In `main.py`, `load_config` is called, and `initial_config` is used in `setup_rich_logging` and passed to `MainProcessor`.
    # `load_workflow_flags` is only in `run_processor.py`.
    # The `main` function in `run_processor.py` loads `config` and then calls `load_workflow_flags`.
    # The `config` variable is local to `main` in `run_processor.py`.
    # To use `config["project_root"]` in `load_workflow_flags`, I must change the function signature of `load_workflow_flags` to accept `config`.
    # This is a necessary change to fulfill the user's request correctly.
    # I will modify `load_workflow_flags` to accept `config` and then modify the call in `main`.

    # Plan:
    # 1. Modify `load_workflow_flags` signature to accept `config`.
    # 2. Modify the call to `load_workflow_flags` in `main` to pass `config`.
    # 3. Replace the hardcoded path in `load_workflow_flags` with `os.path.join(config["project_root"], "src", "config")`.

    # Step 1 & 2: Modify function signature and call.
    # This requires two `replace_in_file` calls or one complex one.
    # Let's do it in one go with multiple SEARCH/REPLACE blocks.

    # First, modify the function definition.
    # Second, modify the call site in main.
    # Third, modify the base_path assignment inside the function.

    # Let's refine the plan:
    # 1. Modify the function definition `load_workflow_flags` to accept `config`.
    # 2. Modify the call `load_workflow_flags(args.workflow)` to `load_workflow_flags(args.workflow, config)`.
    # 3. Modify the line `base_path = '/Users/<USER>/PycharmProjects/lexgenius/src/config'` to `base_path = os.path.join(config["project_root"], "src", "config")`.

    # This requires three SEARCH/REPLACE blocks in `src/run_processor.py`.

    base_path = os.path.join(config["project_root"], "src", "config")

    # Load base feature flags
    with open(f'{base_path}/feature_flags.json', 'r') as f:
        flags = json.load(f)
    
    # If workflow specified, override with workflow-specific flags
    if workflow_name:
        try:
            with open(f'{base_path}/workflows/{workflow_name}.json', 'r') as f:
                workflow_flags = json.load(f)
                # Deep merge the workflow flags with base flags
                for category, settings in workflow_flags.items():
                    if category in flags:
                        flags[category].update(settings)
                    else:
                        flags[category] = settings
        except FileNotFoundError:
            pass
    
    return flags

def main():
    parser = argparse.ArgumentParser(description='Run the processor with specific workflow')
    parser.add_argument('--workflow', type=str, help='Workflow configuration name')
    parser.add_argument('--date', type=str, help='Processing date (MM/DD/YY)')
    parser.add_argument('--start-date', type=str, help='Start date for range processing')
    parser.add_argument('--end-date', type=str, help='End date for range processing')
    parser.add_argument('--court-id', type=str, help='Specific court ID to process')
    parser.add_argument('--docket-num', type=str, help='Specific docket number to process')
    
    args = parser.parse_args()
    
    # Load base configuration
    config = load_config(args.date or args.end_date)
    
    # Load workflow-specific feature flags
    feature_flags = load_workflow_flags(args.workflow, config) # Modified call

    # Configure logging
    configure_logger(config)
    
    # Prepare parameters
    params = {
        'date': args.date,
        'start_date': args.start_date,
        'end_date': args.end_date,
        'process_single_court': [args.court_id] if args.court_id else [],
        'docket_num': args.docket_num,
    }
    
    # Initialize and run processor
    processor = MainProcessor(params, config)
    processor.run()

if __name__ == '__main__':
    main()
