html, body {
    font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: white;
    max-width: 100%;
    font-size: 16px;
}

.container {
    width: 100%;
    margin: 0 auto;
    padding: 1.25rem;
    background: white;
    box-shadow: 0 .125rem .25rem rgba(0, 0, 0, 0.1);
}

.container table {
    width: 100%;
    margin: 0 auto 1.25rem auto;
}

@media (min-width: 768px) {
    .container {
        width: 100%;
    }
}

p {
    margin: 0 1rem 1rem 1rem; /* Aligns <p> tag with the tables */
    font-size: 1rem; /* Adjust font size as necessary */
}

h2, h3 {
    font-size: 1.5rem;
    color: #333;
    margin-bottom: 1.25rem; /* Spacing after the h2 tag */
    text-align: center;
}

h2 {
    margin: 2rem;
    font-size: 2rem;
}

table {
    border-collapse: collapse;
    margin: 1.25rem auto;
    width: calc(100% - 2.5rem);
    font-size: 1rem;
}

th, td {
    padding: 0.5rem;
    border: none;
    text-align: left;
    font-family: sans-serif;
    font-size: 1rem;
}

th {
    background-color: #f2f2f2;
}

.title-row th {
    background-color: #5585b5;
    color: white;
    font-weight: normal;
    text-align: left;
    border-right: none;
}

.secondary-title {
    padding-left: 2rem !important;
    background-color: #53a8b6 !important;
    font-weight: normal !important;
}

.right-align {
    text-align: right;
    border-left: none;
}

.total-row {
    background-color: #79c2d0;
    font-weight: normal;
}

.law-firm, .versus {
    padding-left: 1rem;
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
    font-family: sans-serif;
    font-size: 1rem;
}

.filings {
    text-align: right;
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
    font-family: sans-serif;
}

.no-border-left {
    border-left: none;
}

.no-border-right {
    border-right: none;
}

.cases-filed-row {
    background-color: #79c2d0;
    font-weight: normal;
}

tr.light-border {
    border: none;
}

tr.light-border td, tr.light-border th {
    border: none;
}

.allegations-row {
    background-color: #e0e0e0 !important; /* Light gray background */
    color: #333; /* Optional: sets text color */
}

a {
    color: inherit;
    text-decoration: underline;
}

a:hover {
    color: #FF0000;
    text-decoration: none;
}

.empty-link {
    text-decoration: none; /* Removes underline */
    color: inherit; /* Inherits color from parent, you can set this as needed */
    pointer-events: none; /* Disables the link from being clickable */
    cursor: default; /* Changes cursor to default instead of pointer */
}

.view-individual-filings {
    background-color: white;
}

.law-firm-header {
    background-color: #5585b5;
    color: white;
    font-weight: bold;
    text-align: left;
}

.ad-header th, .ad-header td {
    background-color: #79c2d0;
    color: black; /* White text */
    padding: 0.5rem;
    text-align: left;
    border: none;
}

.title-cell {
    width: 70%;
    font-weight: normal;
}

.other-cell {
    width: 15%;
    font-weight: normal;
}
