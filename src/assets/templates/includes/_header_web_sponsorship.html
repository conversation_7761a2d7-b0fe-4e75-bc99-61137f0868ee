{# Header Sponsorship Web (Extracted from original) #}
{# Assumes 'sponsorships' dict is available in context #}
{% if sponsorships and sponsorships.header_sponsorship_data and sponsorships.header_sponsorship_data.display %}
    {% set sponsor_data = sponsorships.header_sponsorship_data %}
    {# Remove all classes and use only inline styles #}
    <div style="margin-bottom: 2rem; padding: 0.75rem; border-radius: 0.5rem; box-shadow: 0 1px 3px rgba(0,0,0,0.12); border: 1px solid #e5e7eb; overflow: hidden; background-color: {% if sponsor_data.background_color %}{{ sponsor_data.background_color }}{% else %}white{% endif %} !important;">
        {# Optional: Add the "Sponsored By" text if desired #}
        {# <p class="text-center text-sm text-gray-600 font-medium mb-4"><strong>LexGenius</strong> is sponsored by <b><PERSON><PERSON> & Luxenberg PC</b></p> #}

        {# Responsive Grid Layout using inline styles #}
        <div style="display: flex; flex-wrap: wrap; align-items: center; gap: 1rem;">
            {# Logo Area - Responsive size constraints with proper aspect ratio #}
            <div style="flex: 1; min-width: 150px; display: flex; justify-content: center; align-items: center;">
                {% if sponsor_data.logo_svg_link %}
                <div style="position: relative; width: 100%; max-width: 150px; height: 70px; border-radius: 4px; padding: 5px;">
                    <img src="{{ sponsor_data.logo_svg_link }}" alt="Sponsor Logo"
                         style="height: 100%; width: auto; max-height: 100%; max-width: 100%; object-fit: contain; margin-left: auto; margin-right: auto; display: block;">
                </div>
                {% endif %}
            </div>

            {# Text Area - Responsive text alignment #}
            <div style="flex: 2; min-width: 200px; text-align: center; font-size: 1.125rem; color: #374151; padding: 0.25rem 0;">
                {{ sponsor_data.web_ad_copy|safe if sponsor_data.web_ad_copy else '' }}
            </div>

            {# Button Area - Responsive alignment #}
            <div style="flex: 1; min-width: 150px; display: flex; justify-content: center;">
                {% if sponsor_data.cta_link %}
                <a href="{{ sponsor_data.cta_link }}" target="_blank"
                   style="display: inline-flex; align-items: center; padding: 0.5rem 1rem; border: none; border-radius: 0.375rem; font-size: 0.875rem; font-weight: 500; color: white !important; background-color: #2563eb; box-shadow: 0 1px 2px rgba(0,0,0,0.05); white-space: nowrap; text-decoration: none;">
                    <i class="fas fa-calendar-alt" style="margin-right: 0.5rem; color: white;"></i>
                    <span style="color: white;">Book Meeting</span>
                </a>
                {% endif %}
            </div>
        </div>
    </div>
{% endif %}