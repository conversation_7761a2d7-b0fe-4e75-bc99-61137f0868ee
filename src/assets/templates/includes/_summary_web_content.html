{# Web Summary Section Content (Extracted from original) #}
{# Assumes num_torts, num_ads, summary_date, chart_labels, chart_data, is_weekly_report are in context #}
<div class="bg-white rounded-xl shadow-md overflow-hidden"> {# Removed mb-12, handled by wrapper #}
    <div class="p-6">
        {# Title moved to wrapper template #}
        {# <h2 class="text-2xl font-bold text-gray-800 mb-6 flex items-center"><i class="fas fa-chart-line text-blue-600 mr-3"></i> Report Summary </h2> #}
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8 summary-stats-grid">
            <a href="#litigation-report" class="stat-card stat-card-primary p-5">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-50 text-blue-600 mr-4"><i class="fas fa-gavel text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">New Tort Claims Filed*</p>
                        <p class="text-2xl font-bold text-gray-800">{{ num_torts | default('N/A') }}</p>
                    </div>
                </div>
            </a>
            <a href="#adspy-report" class="stat-card stat-card-secondary p-5">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-sky-50 text-sky-600 mr-4"><i class="fas fa-ad text-xl"></i></div>
                    <div>
                        <p class="text-sm text-gray-500">New Legal Ads ({{ "7 days" if is_weekly_report else "14 days" }})</p>
                        <p class="text-2xl font-bold text-gray-800">{{ num_ads | default('N/A') }}</p>
                    </div>
                </div>
            </a>
        </div>
        <div class="bg-white p-5 rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="flex items-center gap-2 mb-3">
                <div class="bg-blue-50 p-2 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                    </svg>
                </div>
                <h3 class="font-semibold text-gray-900">Summary</h3>
            </div>
            <p class="text-gray-700 mb-3">
                {% if is_weekly_report %}For the week
                    {{ summary_date | default('this week') }}{% else %}On
                    {{ summary_date | default('this date') }}{% endif %}, there were <span
                    class="font-bold text-blue-600">{{ num_torts | default('N/A') }}</span> new claims filed* and <span
                    class="font-bold text-blue-600">{{ num_ads | default('N/A') }}</span> new legal ads detected.
            </p>
            <p class="text-gray-500 text-sm mb-2">* Claims count reflects total plaintiffs for AFFF & Suboxone filings,
                individual filings otherwise.</p>
        </div>
        <div class="bg-gray-50 rounded-lg p-4 h-96 md:h-[500px] flex items-center justify-center mt-8 relative"> {# Added relative positioning for absolute fallback #}
            {# Always render canvas and fallback; JS will handle visibility #}
            <canvas id="tort-chart"></canvas> {# Canvas is always here #}
            <p id="chart-fallback" class="text-center text-gray-500 hidden absolute inset-0 flex items-center justify-center">
                {# Default message, JS might update it #}
                Chart data is loading or unavailable.
            </p>
        </div>
    </div>
</div>
