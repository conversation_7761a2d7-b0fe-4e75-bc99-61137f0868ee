{# File: _litigation_report_content.html (Updated for Web Margin) #}

{# --- START OF _litigation_report_content.html --- #}
<!-- DEBUG: START _litigation_report_content.html -->
{# Assumes context variables like grouped, is_web, s3_prod, iso_date, sponsorships, #}
{# litigation_sponsorships, docket_df, report_processor are available #}
{# Assumes macros object is available (imported in base.html) #}

{# Removed outer space-y. Will add margin-bottom to category divs below for web. Email uses its own spacing. #}
<div class="{{ '' if is_web else 'space-y-6-email' }}">
    {% if grouped %} {# Access directly now #}
        <!-- DEBUG: Litigation Content - Found grouped data -->
        {% set CAT_MDL = 'Multidistrict Litigation' %}
        {% set CAT_DATA_BREACH = 'Data Breach' %}
        {% set CAT_PRIVACY = 'Data Privacy' %}
        {% set CAT_CLASS_ACTION = 'Class Action' %}
        {% set CAT_OTHER = 'Other Litigation' %}
        {% set categorized_torts = { CAT_MDL: [], CAT_DATA_BREACH: [], CAT_PRIVACY: [], CAT_CLASS_ACTION: [], CAT_OTHER: [] } %}

        {# Categorization Loop #}
        {% for title, litigation_data_for_title in grouped.items() %} {# Use the pre-processed dict directly #}
            <!-- DEBUG: Litigation Content - Processing title: {{ title }} -->
            <!-- DEBUG: Title exact: "{{ title }}" -->
            {% if litigation_data_for_title is defined %}
                {% set current_mdl_num = litigation_data_for_title.mdl_num if litigation_data_for_title.mdl_num else '' %}
                {% set lower_title = title|lower %}
                {% set category = CAT_OTHER %}

                <!-- DEBUG: Title: "{{ title }}", MDL: "{{ current_mdl_num }}" -->
                
                {# Check MDL first as requested #}
                {% if current_mdl_num %}
                    {% set mdl_num_for_check = current_mdl_num|string|trim %}
                    {% if ((mdl_num_for_check|length >= 4 or mdl_num_for_check == '25') and mdl_num_for_check != '9000') %}
                        {# Special handling for certain MDLs that should be categorized differently #}
                        {% if 'privacy' in lower_title or 'video privacy' in lower_title %}
                            {% set category = CAT_PRIVACY %}
                            <!-- DEBUG: MDL {{ mdl_num_for_check }} but categorized as Data Privacy due to privacy in title -->
                        {% elif 'data breach' in lower_title or 'data privacy' in lower_title %}
                            {% set category = CAT_DATA_BREACH %}
                            <!-- DEBUG: MDL {{ mdl_num_for_check }} but categorized as Data Breach due to data breach in title -->
                        {% else %}
                            {% set category = CAT_MDL %}
                            <!-- DEBUG: Categorized as MDL {{ mdl_num_for_check }} -->
                        {% endif %}
                    {% endif %}
                {% endif %}

                {# Only check other categories if not already categorized #}
                {% if category == CAT_OTHER %}
                    {# Check for data breach and privacy first, even if it's a class action #}
                    {% if 'data breach' in lower_title or 'data privacy' in lower_title %} 
                        {% set category = CAT_DATA_BREACH %}
                        <!-- DEBUG: Categorized as Data Breach -->
                    {% elif 'privacy' in lower_title or 'video privacy' in lower_title %}
                        {% set category = CAT_PRIVACY %}
                        <!-- DEBUG: Categorized as Data Privacy (matched privacy term) -->
                    {% elif 'class action' in lower_title %} 
                        {% set category = CAT_CLASS_ACTION %}
                        <!-- DEBUG: Categorized as Class Action -->
                    {% endif %}
                    <!-- DEBUG: Final category: {{ category }} -->
                {% endif %}

                {% set item_filings = litigation_data_for_title.total_filings if litigation_data_for_title.total_filings is defined else 0 %}

                {# Call processor's method directly using objects from context #}
                {% set allegations = report_processor.get_allegations_and_causes(title, current_mdl_num, docket_df) %}

                {% set _ = categorized_torts[category].append((item_filings, title, litigation_data_for_title, allegations)) %}
            {% endif %}
        {% endfor %}
        <!-- DEBUG: Litigation Content - Categorization done -->

        {# --- Render MDL Category --- #}
        {% if categorized_torts[CAT_MDL] %}
             {# Add mb-8 class for web only to create space below this category block #}
            <div class="{{ 'mb-8' if is_web else '' }}">
                <!-- DEBUG: Litigation Content - Rendering MDL Category -->
                {# Category Header: Add web classes alongside email classes #}
                <h3 class="text-xl-email font-semibold-email text-gray-700-email pb-2-email border-b-email border-gray-300-email mb-4-email
                           {{ 'text-xl font-semibold text-gray-700 pb-2 border-b border-gray-300 mb-4' if is_web else '' }}"
                    style="font-size: 20px; font-weight: 600; color: #374151; padding-bottom: 8px; border-bottom: 1px solid #d1d5db; margin-bottom: 16px;">{{ CAT_MDL }}</h3>
                {# Item Container: Add web spacing class alongside email class #}
                {# This controls space INSIDE a category, between items #}
                <div class="space-y-4-email {{ 'space-y-4' if is_web else '' }}">
                    {% set sorted_mdl_items = categorized_torts[CAT_MDL] | sort(reverse=True, attribute=0) %}
                    {% for _, title, litigation_data_item, allegations in sorted_mdl_items %}
                        <!-- DEBUG: Litigation Content - About to call render_litigation_table for MDL: {{ title }} -->
                        {# The macro handles email styling internally now #}
                        {{ macros.render_litigation_table(
                            title=title,
                            allegations=allegations,
                            litigation_data=litigation_data_item,
                            is_web=is_web,
                            s3_prod=s3_prod,
                            iso_date=iso_date,
                            general_sponsorships=sponsorships,
                            litigation_sponsorships=litigation_sponsorships,
                            is_weekly_report=is_weekly_report
                        ) }}
                        <!-- DEBUG: Litigation Content - Finished call render_litigation_table for MDL: {{ title }} -->
                    {% endfor %}
                </div>
                <!-- DEBUG: Litigation Content - Finished Rendering MDL Category -->
            </div>
        {% endif %}

        {# --- Prepare and Render Other Categories --- #}
        {% set other_categories_data = [] %}
        {% for cat_name, cat_items_list in categorized_torts.items() %}
            {% if cat_name != CAT_MDL and cat_items_list %}
                {% set category_total = cat_items_list | sum(attribute=0) %}
                {% set _ = other_categories_data.append({'total': category_total, 'name': cat_name, 'item_list': cat_items_list}) %}
            {% endif %}
        {% endfor %}
        {% set sorted_other_categories = other_categories_data | sort(reverse=True, attribute='total') %}

        {# --- Render Other Categories Loop --- #}
        {% for category_data in sorted_other_categories %}
             {# Add mb-8 class for web only to create space below this category block #}
            <div class="{{ 'mb-8' if is_web else '' }}">
                <!-- DEBUG: Litigation Content - Rendering Other Category: {{ category_data.name }} -->
                 {# Category Header: Add web classes alongside email classes #}
                <h3 class="text-xl-email font-semibold-email text-gray-700-email pb-2-email border-b-email border-gray-300-email mb-4-email
                     {{ 'text-xl font-semibold text-gray-700 pb-2 border-b border-gray-300 mb-4' if is_web else '' }}"
                     style="font-size: 20px; font-weight: 600; color: #374151; padding-bottom: 8px; border-bottom: 1px solid #d1d5db; margin-bottom: 16px;">{{ category_data.name }}</h3>
                 {# Item Container: Add web spacing class alongside email class #}
                 {# This controls space INSIDE a category, between items #}
                <div class="space-y-4-email {{ 'space-y-4' if is_web else '' }}">
                    {% set sorted_cat_items = category_data.item_list | sort(reverse=True, attribute=0) %}
                    {% for _, title, litigation_data_item, allegations in sorted_cat_items %}
                        <!-- DEBUG: Litigation Content - About to call render_litigation_table for Other: {{ title }} -->
                        {# The macro handles email styling internally now #}
                        {{ macros.render_litigation_table(
                                title=title,
                                allegations=allegations,
                                litigation_data=litigation_data_item,
                                is_web=is_web,
                                s3_prod=s3_prod,
                                iso_date=iso_date,
                                general_sponsorships=sponsorships,
                                litigation_sponsorships=litigation_sponsorships,
                                is_weekly_report=is_weekly_report
                            ) }}
                        <!-- DEBUG: Litigation Content - Finished call render_litigation_table for Other: {{ title }} -->
                    {% endfor %}
                </div>
                <!-- DEBUG: Litigation Content - Finished Rendering Other Category: {{ category_data.name }} -->
            </div>
        {% endfor %}

    {% else %}
        <!-- DEBUG: Litigation Content - grouped data is empty or missing -->
         {# Use email text classes #}
        <p class="text-sm-email text-gray-500-email" style="font-size: 14px; color: #6b7280;">No litigation data available.</p>
    {% endif %}
</div>
<!-- DEBUG: END _litigation_report_content.html -->