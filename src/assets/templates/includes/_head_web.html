
{# Web only assets and styles - Included by base.html when is_web is true #}

{# Web only assets - MATCHING index.html versions AND PLUGINS #}
<script src="https://cdn.tailwindcss.com?plugins=typography"></script> {# Matched plugins #}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"> {# Matched version #}
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.7.1/chart.min.js"></script> {# Matched version #}
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"></script> {# Matched version #}

{# Web only styles - COPIED DIRECTLY FROM index.html's SECOND <style> block #}
<style>
    body {
        font-family: 'Inter', sans-serif;
        background-color: #f8fafc;
    }

    .gradient-header {
        background: linear-gradient(135deg, #115197 0%, #39BEFA 100%);
    }

    .highlight-box {
        background: linear-gradient(135deg, rgba(17, 81, 151, 0.1) 0%, rgba(57, 190, 250, 0.1) 100%);
        border-left: 4px solid #115197;
    }

    .tort-card, .firm-card, .expandable-section-hover {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        background-color: #ffffff;
        border-radius: 0.75rem;
        overflow: hidden;
        border: 1px solid #e5e7eb;
        position: relative;
    }

    /* Default Hover (Desktop) */
    .tort-card:hover, .firm-card:hover, .expandable-section-hover:not(#footer *):hover {
        transform: scale(1.03);
        box-shadow: 0 12px 28px rgba(0, 0, 0, 0.12);
        z-index: 10;
    }

    /* --- START: Web Hover Reset CSS (Mobile Only via Media Query) --- */
    @media (max-width: 768px) {
        /* When the 'no-hover-effect' class is present (added by JS on collapse), disable hover styles */
        .tort-card.no-hover-effect:hover,
        .firm-card.no-hover-effect:hover,
        .expandable-section-hover.no-hover-effect:hover {
            transform: none !important; /* Prevent scaling */
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05) !important; /* Revert to base shadow */
            z-index: auto !important; /* Revert z-index */
        }
    }
    /* --- END: Web Hover Reset CSS --- */

    #footer > div.expandable-section-hover {
        background-color: inherit;
    }
    /* Keep footer dark */
    #footer > div.expandable-section-hover:hover {
        transform: none;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        z-index: auto;
    }
    /* Prevent footer hover effect */


    .nav-link {
        position: relative;
        padding-bottom: 4px;
    }

    .nav-link:after {
        content: '';
        position: absolute;
        width: 0;
        height: 2px;
        bottom: 0;
        left: 0;
        background-color: white;
        transition: width 0.3s ease;
    }

    .nav-link:hover:after {
        width: 100%;
    }

    .stat-card {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        display: block;
        text-decoration: none;
        color: inherit;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .stat-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
    }

    .stat-card-primary {
        border-top: 4px solid #115197;
    }

    .stat-card-secondary {
        border-top: 4px solid #39BEFA;
    }

    .toggle-content {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.5s ease-in-out;
    }

    .toggle-content.active {
        max-height: 5000px; /* Adjust as needed */
    }

    .toggle-icon {
        transition: transform 0.3s ease;
    }

    .toggle-icon.rotate-180 {
        transform: rotate(180deg);
    }

    .back-to-top {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 99;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background-color: #115197;
        color: white;
        text-align: center;
        line-height: 50px;
        font-size: 20px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        transition: all 0.3s ease;
        display: none;
    }

    .back-to-top:hover {
        background-color: #39BEFA;
        transform: translateY(-3px);
    }

    .table-row-hover:hover {
        background-color: #f1f5f9;
    }

    #litigation-report a, #detailed-filings a, #adspy-report a, #upcoming-hearings a, #special-reports a, #news a {
        color: #2563eb;
        text-decoration: none;
    }
    #litigation-report a:hover, #detailed-filings a:hover, #adspy-report a:hover, #upcoming-hearings a:hover, #special-reports a:hover, #news a:hover {
        text-decoration: underline;
    }
    #summary .stat-card a { text-decoration: none; }
    #detailed-filings .firm-card-content a { color: #2563eb; }
    #detailed-filings .tort-subtitle a { color: #1e40af; font-weight: 500; }
    #detailed-filings .tort-subtitle a:hover { text-decoration: underline; }
    #detailed-filings .case-link a { color: #2563eb; }

    .list-item-link {
        display: flex; align-items: center; padding: 10px 15px;
        border: 1px solid #e5e7eb; border-radius: 6px;
        text-decoration: none; transition: background-color 0.2s ease, border-color 0.2s ease;
        margin-bottom: 10px; background-color: #fff;
    }
    .list-item-link:hover { background-color: #f9fafb; border-color: #d1d5db; }
    .list-item-icon { color: #4b5563; margin-right: 12px; width: 18px; text-align: center; }
    .list-item-text { color: #1f2937; font-weight: 500; font-size: 14px; line-height: 1.4; }
    .list-item-link:hover .list-item-text { color: #115197; }

    section[id] {
        scroll-margin-top: 90px;
    }

    @keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }
    @keyframes slideUp { from { transform: translateY(20px); opacity: 0; } to { transform: translateY(0); opacity: 1; } }

    .animate-on-scroll {
        opacity: 0; transform: translateY(20px);
        transition: opacity 0.6s ease-out, transform 0.6s ease-out;
        will-change: opacity, transform;
    }
    .animate-on-scroll.visible { opacity: 1; transform: translateY(0); }

    /* --- START: Arrow Alignment Style (Web Only) --- */
    .collapsible-header { display: flex; align-items: center; justify-content: space-between; }
    .collapsible-header-group { display: flex; align-items: center; min-width: 0; flex-shrink: 1; margin-right: 0.5rem; }
    .collapsible-header-title { overflow: hidden; text-overflow: ellipsis; white-space: nowrap; margin-right: 0.5rem; }
    .collapsible-header > span.toggle-icon { flex-shrink: 0; }
    .collapsible-header-group > span.toggle-icon { flex-shrink: 0; }
    /* --- END: Arrow Alignment Style --- */

    @media (max-width: 768px) {
        section[id] { scroll-margin-top: 70px; }
        .list-item-text { font-size: 13px; }
        .firm-stats { flex-wrap: wrap; justify-content: flex-end; }

        /* Sponsorships */
        .litigation-sponsor-web .md\:w-1\/4 { flex-basis: 20%; }
        .litigation-sponsor-web .md\:w-1\/2 { flex-basis: 55%; }
        .litigation-sponsor-web .md\:w-1\/4:last-child { flex-basis: 25%; }
        .litigation-sponsor-web img { height: 2.5rem; }
        .summary-stats-grid { grid-template-columns: 1fr; }
        .header-sponsorship-web .md\:flex-row { flex-direction: column; }
        .header-sponsorship-web .md\:items-center { align-items: center; }
        .header-sponsorship-web .md\:w-1\/4, .header-sponsorship-web .md\:w-1\/2 { width: 100%; }
        .header-sponsorship-web .md\:justify-start { justify-content: center; }
        .header-sponsorship-web .md\:text-left { text-align: center; }
        .header-sponsorship-web .md\:justify-end { justify-content: center; }
        .header-sponsorship-web .md\:space-x-4 > :not([hidden]) ~ :not([hidden]) { margin-left: 0; margin-right: 0; }
        .header-sponsorship-web .md\:mt-0 { margin-top: 0.75rem; }
        .header-sponsorship-web .md\:pr-2 { padding-right: 0; }
        .header-sponsorship-web .md\:w-1\/4:last-child { margin-top: 1rem; }

        /* Spacing */
        .space-y-6 > :not([hidden]) ~ :not([hidden]) { margin-top: 1rem; margin-bottom: 0; }
        .space-y-4 > :not([hidden]) ~ :not([hidden]) { margin-top: 0.75rem; margin-bottom: 0; }
    }
</style>

<script>
// Note: Enhanced handleFromParameter function is now in main.js and _scripts_web.html
// This placeholder ensures compatibility if scripts load in different order
</script>
