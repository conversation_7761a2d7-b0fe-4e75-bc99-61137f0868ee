{# File: _special_reports_content.html (Updated for Email) #}

{# Special Reports Section Content #}
{# Assumes 'special_reports' list is available in context #}
{% if special_reports %}
     {# Use email spacing class if needed #}
    <div class="space-y-3-email"> {# Assuming space-y-3-email defined if needed #}
        {% for report in special_reports %}
             {# Use list-item-link-email class (needs definition in _head_email.html) #}
             {# Fallback inline styles provided #}
            <a href="{{ report.link }}" target="_blank" class="list-item-link-email"
               style="display: block; padding: 10px 15px; border: 1px solid #e5e7eb; border-radius: 6px; text-decoration: none; margin-bottom: 10px; background-color: #fff;">
                {# Use entity or text for icon #}
                <span class="list-item-icon-email" style="color: #4b5563; margin-right: 12px; width: 18px; display: inline-block; text-align: center;">→</span> {# Or → #}
                {# Use email text classes #}
                <span class="list-item-text-email text-sm-email font-medium-email text-gray-800-email"
                      style="color: #1f2937; font-weight: 500; font-size: 14px; line-height: 1.4;">{{ report.title }}</span>
            </a>
        {% endfor %}
    </div>
{% else %}
    {# Use email text classes #}
    <p class="text-sm-email text-gray-500-email" style="font-size: 14px; color: #6b7280;">No special reports available currently.</p>
{% endif %}