{# File: _afff_stats_content.html (Updated for Email) #}

{# AFFF Stats Section Content #}
{# Assumes 'afff_stats' dict is available in context #}
{% if afff_stats %}
    {# Removed overflow-x-auto for email #}
    <div>
        {# Use email-table class for consistent styling #}
        <table class="email-table" width="100%" cellpadding="0" cellspacing="0" border="0" style="min-width: 100%; border-collapse: collapse; margin-bottom: 15px;">
            {# Use email bg class for thead #}
            <thead class="bg-gray-100-email">
            <tr>
                 {# Use email text classes for th, remove tracking #}
                <th scope="col" class="px-3-email py-2-email text-left-email text-xs-email font-medium-email text-gray-500-email uppercase-email" {# Removed tracking-wider #}
                    style="padding: 8px 12px; text-align: left; font-size: 12px; font-weight: 500; color: #6b7280; text-transform: uppercase; border: 1px solid #e5e7eb;">
                    Metric
                </th>
                <th scope="col" class="px-3-email py-2-email text-right-email text-xs-email font-medium-email text-gray-500-email uppercase-email" {# Removed tracking-wider #}
                    style="padding: 8px 12px; text-align: right; font-size: 12px; font-weight: 500; color: #6b7280; text-transform: uppercase; border: 1px solid #e5e7eb;">
                    Value
                </th>
            </tr>
            </thead>
            {# Use email bg class for tbody, remove divide #}
            <tbody class="bg-white-email">
            {# Removed table-row-hover (no hover in email) #}
            <tr>
                 {# Use email text classes for td, remove whitespace #}
                <td class="px-3-email py-2-email text-sm-email text-gray-700-email" {# Removed whitespace-nowrap #}
                    style="padding: 8px 12px; font-size: 14px; color: #374151; border: 1px solid #e5e7eb;">PACER filings this period (pending CTO)
                </td>
                <td class="px-3-email py-2-email text-sm-email font-medium-email text-blue-600-email text-right-email" {# Removed whitespace-nowrap #}
                    style="padding: 8px 12px; font-size: 14px; font-weight: 500; color: #2563eb; text-align: right; border: 1px solid #e5e7eb;">
                    {{ afff_stats.pacer_cases_period }} {{ 'case' if afff_stats.pacer_cases_period == 1 else 'cases' }}
                    {% if afff_stats.pacer_plaintiffs_period is defined and afff_stats.pacer_plaintiffs_period > 0 %}
                         {# Use email font/color classes #}
                        <span class="font-semibold-email text-gray-700-email" style="font-weight: 600; color: #374151;"> ({{ afff_stats.pacer_plaintiffs_period }} {{ 'plaintiff' if afff_stats.pacer_plaintiffs_period == 1 else 'plaintiffs' }})</span>
                    {% endif %}
                </td>
            </tr>
            <tr>
                <td class="px-3-email py-2-email text-sm-email text-gray-700-email" style="padding: 8px 12px; font-size: 14px; color: #374151; border: 1px solid #e5e7eb;">Cases transferred into MDL this period
                </td>
                 <td class="px-3-email py-2-email text-sm-email font-medium-email text-blue-600-email text-right-email" style="padding: 8px 12px; font-size: 14px; font-weight: 500; color: #2563eb; text-align: right; border: 1px solid #e5e7eb;">
                    {{ afff_stats.transferred_in_period }} {{ 'case' if afff_stats.transferred_in_period == 1 else 'cases' }}
                    {% if afff_stats.transferred_in_plaintiffs_period is defined and afff_stats.transferred_in_plaintiffs_period > 0 %}
                        <span class="font-semibold-email text-gray-700-email" style="font-weight: 600; color: #374151;"> ({{ afff_stats.transferred_in_plaintiffs_period }} {{ 'plaintiff' if afff_stats.transferred_in_plaintiffs_period == 1 else 'plaintiffs' }})</span>
                    {% endif %}
                </td>
            </tr>
             <tr>
                <td class="px-3-email py-2-email text-sm-email text-gray-700-email" style="padding: 8px 12px; font-size: 14px; color: #374151; border: 1px solid #e5e7eb;">Direct filings this period (est.)</td>
                <td class="px-3-email py-2-email text-sm-email font-medium-email text-blue-600-email text-right-email" style="padding: 8px 12px; font-size: 14px; font-weight: 500; color: #2563eb; text-align: right; border: 1px solid #e5e7eb;">{{ afff_stats.direct_filings_placeholder }}
                    filings
                </td>
            </tr>
            <tr>
                <td class="px-3-email py-2-email text-sm-email text-gray-700-email" style="padding: 8px 12px; font-size: 14px; color: #374151; border: 1px solid #e5e7eb;">Direct filings - past 30 days (est.)</td>
                 <td class="px-3-email py-2-email text-sm-email font-medium-email text-blue-600-email text-right-email" style="padding: 8px 12px; font-size: 14px; font-weight: 500; color: #2563eb; text-align: right; border: 1px solid #e5e7eb;">{{ afff_stats.direct_filings_30_days_placeholder }}
                    filings
                </td>
            </tr>
            <tr>
                <td class="px-3-email py-2-email text-sm-email text-gray-700-email" style="padding: 8px 12px; font-size: 14px; color: #374151; border: 1px solid #e5e7eb;">Total Filings YTD (est.)</td>
                <td class="px-3-email py-2-email text-sm-email font-medium-email text-blue-600-email text-right-email" style="padding: 8px 12px; font-size: 14px; font-weight: 500; color: #2563eb; text-align: right; border: 1px solid #e5e7eb;">{{ afff_stats.filings_ytd_placeholder }}
                    filings
                </td>
            </tr>
            </tbody>
        </table>
    </div>
    {# Use email text classes for note #}
    <p class="text-sm-email text-gray-500-email italic-email mt-4-email" style="font-size: 14px; color: #6b7280; font-style: italic; margin-top: 16px;">Note: There is a 3-week delay between filing and appearance in the
        transferee court PACER docket, which is why individual filings do not appear on the daily report.</p>
{% else %}
     {# Use email text classes for fallback #}
    <p class="text-sm-email text-gray-500-email" style="font-size: 14px; color: #6b7280;">AFFF statistics are currently unavailable.</p>
{% endif %}