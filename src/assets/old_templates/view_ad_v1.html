<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LexGenius AdSpy</title>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-9CREQVVWNF"></script>
    <script>
        window.dataLayer = window.dataLayer || [];

        function gtag() {
            dataLayer.push(arguments);
        }

        gtag('js', new Date());

        gtag('config', 'G-9CREQVVWNF');
    </script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f0f2f5;
        }

        .container {
            max-width: 500px;
            margin: 20px auto;
            background-color: #fff;
            border: 1px solid #dddfe2;
            border-radius: 8px;
            overflow: hidden;
        }

        .top-row {
            background-color: #5585b5;
            padding: 10px;
            text-align: center;
            font-size: 20px;
            font-weight: bold;
            color: white;
        }

        .ad-info {
            padding: 20px;
            background-color: #f8f9fa;
            border-bottom: 1px solid #dddfe2;
        }

        .field {
            display: flex;
            margin-bottom: 10px;
        }

        .field label {
            flex: 0 0 100px;
            font-weight: bold;
        }

        .field div {
            flex: 1;
            word-wrap: break-word;
        }

        .platforms svg {
            width: 20px;
            height: 20px;
            margin-right: 5px;
        }

        .fb-ad {
            padding: 12px;
        }

        .ad-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }

        .logo {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 10px;
        }

        .header-text {
            display: flex;
            flex-direction: column;
        }

        .law-firm {
            font-weight: 600;
            font-size: 14px;
            color: #050505;
        }

        .sponsored {
            font-size: 12px;
            color: #65676b;
        }

        .ad-body-container {
            position: relative;
        }

        .ad-body {
            font-size: 15px;
            line-height: 1.3333;
            color: #050505;
            margin-bottom: 12px;
            white-space: pre-wrap;
            overflow: hidden;
        }

        .ad-body.truncated::after {
            content: '...';
            position: absolute;
            bottom: 0;
            right: 0;
            padding-left: 40px;
            background: linear-gradient(to right, transparent, white 50%);
        }

        .see-more {
            color: #65676b;
            font-weight: bold;
            cursor: pointer;
            display: none;
        }

        .truncated + .see-more {
            display: inline;
        }

        .ad-image {
            width: 100%;
            height: auto;
            object-fit: cover;
            margin-bottom: 12px;
            border-radius: 8px;
        }

        .ad-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #f0f2f5;
            padding: 12px;
            border-radius: 8px;
        }

        .ad-title {
            font-size: 15px;
            font-weight: 500;
            color: #050505;
            margin-bottom: 4px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            max-height: 3em;
            line-height: 1.5em;
            cursor: pointer;
            transition: max-height 0.3s ease-out;
        }

        .ad-title.expanded {
            -webkit-line-clamp: unset;
            max-height: none;
        }

        .ad-description {
            font-size: 14px;
            color: #65676b;
            margin-bottom: 8px;
            overflow: hidden;
            cursor: pointer;
        }

        .ad-description.truncate {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            text-overflow: ellipsis;
        }

        .ad-caption {
            font-size: 12px;
            color: #65676b;
            margin-bottom: 4px;
        }

        .ad-details {
            flex-grow: 1;
            min-width: 0;
        }

        .ad-button {
            background-color: #e4e6eb;
            border: none;
            padding: 10px 12px;
            border-radius: 6px;
            font-weight: 600;
            font-size: 14px;
            text-align: center;
            display: inline-block;
            white-space: nowrap;
            cursor: pointer;
            margin-left: auto;
            align-self: center;
            text-decoration: none;
            color: black;
        }

        .ad-caption, .ad-title, .ad-description {
            word-wrap: break-word;
            white-space: normal;
        }

        .center {
            text-align: center;
        }


        .landing-page a {
            word-break: break-all;
            color: #000;
        }

        @media (max-width: 768px) {
            .ad-body.truncated {
                max-height: 10em; /* Approximately 125 characters */
            }
        }

        @media (min-width: 769px) {
            .ad-body.truncated {
                max-height: 11.4em; /* Approximately 130 characters */
            }
        }

        @media (max-width: 600px) {
            .container {
                margin: 0;
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
<div class="container">
    <div class="top-row">
        <h1>LexGenius AdSpy</h1>
    </div>
    <div class="ad-info">
        <h2 class="center">Ad Information
            <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" height="1em"
                 width="1em" xmlns="http://www.w3.org/2000/svg" data-tooltip="Facebook">
                <path d="M256 8C119.043 8 8 119.083 8 256c0 136.997 111.043 248 248 248s248-111.003 248-248C504 119.083 392.957 8 256 8zm0 110c23.196 0 42 18.804 42 42s-18.804 42-42 42-42-18.804-42-42 18.804-42 42-42zm56 254c0 6.627-5.373 12-12 12h-88c-6.627 0-12-5.373-12-12v-24c0-6.627 5.373-12 12-12h12v-64h-12c-6.627 0-12-5.373-12-12v-24c0-6.627 5.373-12 12-12h64c6.627 0 12 5.373 12 12v100h12c6.627 0 12 5.373 12 12v24z"></path>
            </svg>
        </h2>
        <div class="field">
            <label>Title</label>
            <div>{{ data.title }}</div>
        </div>
        <div class="field">
            <label>Status</label>
            <div>{{ data.is_active }}</div>
        </div>
        <div class="field">
            <label>Page Name</label>
            <div>{{ data.page_name }}</div>
        </div>
        <div class="field">
            <label>Started</label>
            <div>{{ data.start_date }}</div>
        </div>
        <div class="field">
            <label>Ends</label>
            <div>{{ data.end_date }}</div>
        </div>
        <div class="field">
            <label>Platforms</label>
            <div class="platforms">
                {% if 'facebook' in data.publisher_platform|join(',')|lower %}
                <svg class="tooltip-icon" data-tooltip="Facebook" fill="blue" viewBox="0 0 512 512">
                    <path d="M504 256C504 119 393 8 256 8S8 119 8 256c0 123.78 90.69 226.38 209.25 245V327.69h-63V256h63v-54.64c0-62.15 37-96.48 93.67-96.48 27.14 0 55.52 4.84 55.52 4.84v61h-31.28c-30.8 0-40.41 19.12-40.41 38.73V256h68.78l-11 71.69h-57.78V501C413.31 482.38 504 379.78 504 256z"></path>
                </svg>
                {% endif %}
                {% if 'instagram' in data.publisher_platform|join(',')|lower %}
                <svg class="tooltip-icon" data-tooltip="Instagram" fill="green" viewBox="0 0 448 512">
                    <path d="M224.1 141c-63.6 0-114.9 51.3-114.9 114.9s51.3 114.9 114.9 114.9S339 319.5 339 255.9 287.7 141 224.1 141zm0 189.6c-41.1 0-74.7-33.5-74.7-74.7s33.5-74.7 74.7-74.7 74.7 33.5 74.7 74.7-33.6 74.7-74.7 74.7zm146.4-194.3c0 14.9-12 26.8-26.8 26.8-14.9 0-26.8-12-26.8-26.8s12-26.8 26.8-26.8 26.8 12 26.8 26.8zm76.1 27.2c-1.7-35.9-9.9-67.7-36.2-93.9-26.2-26.2-58-34.4-93.9-36.2-37-2.1-147.9-2.1-184.9 0-35.8 1.7-67.6 9.9-93.9 36.1s-34.4 58-36.2 93.9c-2.1 37-2.1 147.9 0 184.9 1.7 35.9 9.9 67.7 36.2 93.9s58 34.4 93.9 36.2c37 2.1 147.9 2.1 184.9 0 35.9-1.7 67.7-9.9 93.9-36.2 26.2-26.2 34.4-58 36.2-93.9 2.1-37 2.1-147.8 0-184.8zM398.8 388c-7.8 19.6-22.9 34.7-42.6 42.6-29.5 11.7-99.5 9-132.1 9s-102.7 2.6-132.1-9c-19.6-7.8-34.7-22.9-42.6-42.6-11.7-29.5-9-99.5-9-132.1s-2.6-102.7 9-132.1c7.8-19.6 22.9-34.7 42.6-42.6 29.5-11.7 99.5-9 132.1-9s102.7-2.6 132.1 9c19.6 7.8 34.7 22.9 42.6 42.6 11.7 29.5 9 99.5 9 132.1s2.7 102.7-9 132.1z"></path>
                </svg>
                {% endif %}
                {% if 'audience_network' in data.publisher_platform|join(',')|lower %}
                <svg class="tooltip-icon" data-tooltip="Audience Network" fill="red" viewBox="0 0 512 512">
                    <path d="M256 288c79.5 0 144-64.5 144-144S335.5 0 256 0 112 64.5 112 144s64.5 144 144 144zm128 32h-55.1c-22.2 10.2-46.9 16-72.9 16s-50.6-5.8-72.9-16H128C57.3 320 0 377.3 0 448v16c0 26.5 21.5 48 48 48h416c26.5 0 48-21.5 48-48v-16c0-70.7-57.3-128-128-128z"></path>
                </svg>
                {% endif %}
                {% if 'messenger' in data.publisher_platform|join(',')|lower %}
                <svg class="tooltip-icon" data-tooltip="Messenger" fill="purple" viewBox="0 0 512 512">
                    <path d="M256.55 8C116.52 8 8 110.34 8 248.57c0 72.3 29.71 134.78 78.07 177.94 8.35 7.51 6.63 11.86 8.05 58.23A19.92 19.92 0 0 0 122 502.31c52.91-23.3 53.59-25.14 62.56-22.7C337.85 521.8 504 423.7 504 248.57 504 110.34 396.59 8 256.55 8zm149.24 185.13l-73 115.57a37.37 37.37 0 0 1-53.91 9.93l-58.08-43.47a15 15 0 0 0-18 0l-78.37 59.44c-10.46 7.93-24.16-4.6-17.11-15.67l73-115.57a37.36 37.36 0 0 1 53.91-9.93l58.06 43.46a15 15 0 0 0 18 0l78.41-59.38c10.44-7.98 24.14 4.54 17.09 15.62z"></path>
                </svg>
                {% endif %}
            </div>
        </div>
        <div class="field">
            <label>Landing Page</label>
            <div class="landing-page">
                {% if data.landing_page == 'http://fb.me/' or data.landing_page == 'https://fb.me/' %}
                FB Lead Form
                {% else %}
                <a href="{{ data.landing_page }}">{{ data.landing_page }}</a>
                {% endif %}
            </div>
        </div>
    </div>
    <div class="fb-ad">
        <div class="ad-header">
            <img src="{{ data.logo_uri }}" class="logo" alt="{{ data.page_name }} logo">
            <div class="header-text">
                <div class="law-firm">{{ data.page_name }}</div>
                <div class="sponsored">Sponsored</div>
            </div>
        </div>
        {% if data.body and data.body != 'nan' %}
        <div class="ad-body-container">
            <div class="ad-body" id="adBody">{{ data.body | convert_newlines }}</div>
            <span class="see-more" onclick="toggleAdBody()">See more</span>
        </div>
        {% endif %}
        {% if data.image_uri and data.image_uri != 'nan' %}
        <img src="{{ data.image_uri }}" class="ad-image" alt="Ad image">
        {% endif %}
        {% if (data.caption and data.caption != 'nan') or
        (data.title and data.title != 'nan') or
        (data.link_description and data.link_description != 'nan') or
        (data.cta_text and data.cta_text != 'nan' and data.cta_text != '' and data.cta_text != 'No button') %}
        <div class="ad-footer">
            <div class="ad-details">
                {% if data.caption and data.caption != 'nan' %}
                <div class="ad-caption">{{ data.caption }}</div>
                {% endif %}
                {% if data.title and data.title != 'nan' %}
                <div class="ad-title expandable-title">{{ data.title }}</div>
                {% endif %}
                {% if data.link_description and data.link_description != 'nan' %}
                <div class="ad-description truncate" onclick="toggleDescription(this)">{{ data.link_description }}</div>
                {% endif %}
            </div>
            {% if data.cta_text and data.cta_text != 'nan' and data.cta_text != '' and data.cta_text != 'No button' %}
            <div class="ad-button">
                {% if data.link_url == 'http://fb.me/' or data.link_url == 'https://fb.me/' %}
                <span class="ad-button-text">{{ data.cta_text }}</span>
                {% else %}
                <a href="{{ data.link_url }}" class="ad-button-text" style="text-decoration: none; color: black;">{{
                    data.cta_text }}</a>
                {% endif %}
            </div>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        var expandableTitles = document.querySelectorAll('.expandable-title');

        expandableTitles.forEach(function (title) {
            title.addEventListener('click', function () {
                this.classList.toggle('expanded');
            });
        });
    });
</script>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip';
        tooltip.style.position = 'absolute';
        tooltip.style.display = 'none';
        tooltip.style.padding = '5px';
        tooltip.style.background = 'rgba(0,0,0,0.8)';
        tooltip.style.color = 'white';
        tooltip.style.borderRadius = '3px';
        tooltip.style.zIndex = '1000';
        document.body.appendChild(tooltip);

        document.querySelectorAll('.tooltip-icon').forEach(function (element) {
            element.addEventListener('mouseenter', function (e) {
                tooltip.textContent = e.target.getAttribute('data-tooltip');
                tooltip.style.display = 'block';
                positionTooltip(e);
            });

            element.addEventListener('mousemove', positionTooltip);

            element.addEventListener('mouseleave', function () {
                tooltip.style.display = 'none';
            });
        });

        function positionTooltip(e) {
            const rect = e.target.getBoundingClientRect();
            const tooltipRect = tooltip.getBoundingClientRect();
            const top = rect.top + window.scrollY - tooltipRect.height - 10;
            const left = rect.left + window.scrollX + (rect.width - tooltipRect.width) / 2;

            tooltip.style.top = `${top}px`;
            tooltip.style.left = `${left}px`;
        }
    });
</script>
<script>
    function toggleDescription(element) {
        element.classList.toggle('truncate');
        if (element.classList.contains('truncate')) {
            element.style.webkitLineClamp = '2';
            element.style.maxHeight = '2.6em';  // Approximately 2 lines of text
        } else {
            element.style.webkitLineClamp = 'unset';
            element.style.maxHeight = 'none';
        }
    }
</script>
<script>
    function truncateAdBody() {
        const adBody = document.getElementById('adBody');
        const seeMore = document.querySelector('.see-more');
        const maxHeight = window.innerWidth <= 768 ? '5em' : '5.2em';

        if (adBody.scrollHeight > adBody.clientHeight) {
            adBody.classList.add('truncated');
            adBody.style.maxHeight = maxHeight;
            seeMore.style.display = 'inline';
        } else {
            adBody.classList.remove('truncated');
            seeMore.style.display = 'none';
        }
    }

    function toggleAdBody() {
        const adBody = document.getElementById('adBody');
        const seeMore = document.querySelector('.see-more');

        if (adBody.classList.contains('truncated')) {
            adBody.classList.remove('truncated');
            adBody.style.maxHeight = 'none';
            seeMore.textContent = 'See less';
        } else {
            adBody.classList.add('truncated');
            adBody.style.maxHeight = window.innerWidth <= 768 ? '5em' : '5.2em';
            seeMore.textContent = 'See more';
        }
    }

    document.addEventListener('DOMContentLoaded', function () {
        truncateAdBody();
        window.addEventListener('resize', truncateAdBody);
    });
</script>
</body>
</html>