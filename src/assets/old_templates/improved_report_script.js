// Toggle visibility of sections
function toggleVisibility(id) {
    const elem = document.getElementById(id);
    const indicator = document.getElementById(id + '_indicator');
    if (elem.style.display === 'none') {
        elem.style.display = 'table-row';
        indicator.innerHTML = '-';
    } else {
        elem.style.display = 'none';
        indicator.innerHTML = '+';
    }
}

// Back to Top button functionality
window.onscroll = function () {
    if (document.body.scrollTop > 20 || document.documentElement.scrollTop > 20) {
        document.getElementById("back-to-top").style.display = "block";
    } else {
        document.getElementById("back-to-top").style.display = "none";
    }
};

document.getElementById("back-to-top").onclick = function () {
    document.body.scrollTop = 0; // For Safari
    document.documentElement.scrollTop = 0; // For Chrome, Firefox, IE and Opera
};

// Lazy loading for images
document.addEventListener("DOMContentLoaded", function () {
    var lazyImages = [].slice.call(document.querySelectorAll("img.lazy"));

    if ("IntersectionObserver" in window) {
        let lazyImageObserver = new IntersectionObserver(function (entries, observer) {
            entries.forEach(function (entry) {
                if (entry.isIntersecting) {
                    let lazyImage = entry.target;
                    lazyImage.src = lazyImage.dataset.src;
                    lazyImage.classList.remove("lazy");
                    lazyImageObserver.unobserve(lazyImage);
                }
            });
        });

        lazyImages.forEach(function (lazyImage) {
            lazyImageObserver.observe(lazyImage);
        });
    }
});

// Chart.js implementation for tort filings visualization
const ctx = document.getElementById('tort-chart').getContext('2d');
new Chart(ctx, {
    type: 'bar',
    data: {
        labels: ['Tort 1', 'Tort 2', 'Tort 3', 'Tort 4', 'Tort 5'],
        datasets: [{
            label: '# of Filings',
            data: [12, 19, 3, 5, 2],
            backgroundColor: [
                'rgba(255, 99, 132, 0.2)',
                'rgba(54, 162, 235, 0.2)',
                'rgba(255, 206, 86, 0.2)',
                'rgba(75, 192, 192, 0.2)',
                'rgba(153, 102, 255, 0.2)'
            ],
            borderColor: [
                'rgba(255, 99, 132, 1)',
                'rgba(54, 162, 235, 1)',
                'rgba(255, 206, 86, 1)',
                'rgba(75, 192, 192, 1)',
                'rgba(153, 102, 255, 1)'
            ],
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Search functionality
document.getElementById('search-input').addEventListener('input', function (e) {
    const searchTerm = e.target.value.toLowerCase();
    const tables = document.querySelectorAll('table');

    tables.forEach(table => {
        const rows = table.querySelectorAll('tr');
        let tableVisible = false;

        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
                row.style.display = '';
                tableVisible = true;
            } else {
                row.style.display = 'none';
            }
        });

        table.style.display = tableVisible ? '' : 'none';
    });
});


function toggleAllegations(id) {
    var element = document.getElementById(id);
    var indicator = document.getElementById(id + '_indicator');
    if (element.style.display === "none" || element.style.display === "") {
        element.style.display = "table-row";
        indicator.innerHTML = "-";
    } else {
        element.style.display = "none";
        indicator.innerHTML = "+";
    }
}
