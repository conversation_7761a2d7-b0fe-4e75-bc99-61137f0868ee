import re
import unicodedata
from urllib.parse import quote, unquote, urljoin

import requests
from bs4 import BeautifulSoup

from color_logging import LoggerSetup
from config import load_config
from pdf_extractor import PDFExtractor  # Assuming you have a PDFExtractor class
from color_logging import LoggerSetup
from config import load_config
from pprint import pformat

class GANDScraper:
    def __init__(self, config, url, mdl_id):
        self.url = url
        self.mdl_num = mdl_id
        log_setup = LoggerSetup(config, 'GANDScraperLogger', 'GANDScraper.log')
        self.logger = log_setup.get_logger()
        self.pdf_extractor = PDFExtractor(config,'')

    def fetch_content(self, url):
        try:
            response = requests.get(url)
            response.raise_for_status()
            return BeautifulSoup(response.content, 'html.parser')
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Error fetching the URL: {e}")
            return None

    def parse_docs(self, soup):
        # Find the <h1> tag with the specific ID and then find the next <ul> tag
        h1_tag = soup.find('h1', id='page-title')
        if not h1_tag:
            self.logger.info("No <h1 id='page-title'> tag found.")
            return []

        ul_tag = h1_tag.find_next('ul')
        if not ul_tag:
            self.logger.info("No <ul> tag found after the <h1> tag.")
            return []

        results = []
        li_tags = ul_tag.find_all('li')
        for li in li_tags:
            a_tag = li.find('a', href=True)
            if a_tag:
                href = a_tag['href'].strip()
                url = urljoin(self.url, quote(unquote(href)))

                # Extract the doc_num from the text or URL
                doc_num_match = re.search(r'(Doc\.?\s*(\d+))', a_tag.get_text(strip=True))

                if doc_num_match:
                    doc_num = doc_num_match.group(2)
                else:
                    doc_num_url_match = re.search(r'_doc(\d+)\.pdf', url)
                    doc_num = doc_num_url_match.group(1) if doc_num_url_match else None

                # Assign the doc_title and clean it
                doc_title = re.sub(r'\(?Doc\.?\s*\d+\)?', '', a_tag.get_text(strip=True)).strip().replace('\xa0', ' ').strip()

                # Look for the filing date after the anchor tag
                filing_date = 'NA'
                additional_text = a_tag.find_next_sibling(text=True)
                if additional_text:
                    date_match = re.search(r'–\s*(\w+\s+\d{1,2},\s+\d{4})', additional_text)
                    if date_match:
                        filing_date_raw = date_match.group(1)
                        filing_date_obj = re.search(r'(\w+)\s+(\d{1,2}),\s+(\d{4})', filing_date_raw)
                        if filing_date_obj:
                            month_str = filing_date_obj.group(1)
                            day = filing_date_obj.group(2)
                            year = filing_date_obj.group(3)
                            # Convert month name to month number
                            month_num = {
                                "January": "01", "February": "02", "March": "03", "April": "04",
                                "May": "05", "June": "06", "July": "07", "August": "08",
                                "September": "09", "October": "10", "November": "11", "December": "12"
                            }.get(month_str, "01")
                            filing_date = f"{month_num}/{day.zfill(2)}/{year}"

                result = {
                    "mdl_num": self.mdl_num,
                    "url": url,
                    "doc_title": doc_title,
                    "doc_num": doc_num,
                    "filing_date": filing_date
                }
                self.logger.info(pformat(result))
                # Extract missing doc_num or filing_date from PDF if necessary
                self.extract_doc_nums_and_dates(result)

                # self.logger.info(f"Extracted URL: {url}, Document Title: {doc_title}, Document Number: {doc_num}, Filing Date: {filing_date}")
                results.append(result)

        return results

    def extract_doc_nums_and_dates(self, result):
        if 'pdf' in result['url']:
            pdf_text = self.pdf_extractor.extract_text_from_pdf_pymupdf(result['url'])
            normalized_text = unicodedata.normalize('NFKD', pdf_text)
            cleaned_text = ''.join(c for c in normalized_text if not unicodedata.category(c).startswith('C'))

            # Extract the document number using the specified regex
            if not result.get('doc_num') or result.get('doc_num') == 'NA':
                doc_num_match = re.search(r'\s+[Dd]ocument\s+(\d+)\s+', cleaned_text)
                if doc_num_match:
                    doc_num = doc_num_match.group(1)
                    result['doc_num'] = doc_num.replace('()', '')
                    if doc_num != 'NA':
                        self.logger.info(f"Extracted Doc Num: {doc_num} from PDF at URL: {result['url']}")

            # Extract the filing date
            if not result.get('filing_date') or result['filing_date'] == 'NA':
                filing_date = self.pdf_extractor.extract_filing_date(cleaned_text)
                if filing_date:
                    result['filing_date'] = filing_date
                    if filing_date != 'NA':
                        self.logger.info(f"Extracted Filing Date: {filing_date} from PDF at URL: {result['url']}")

            return result

    def run(self):
        all_results = []

        # Fetch and parse the content from the orders page
        soup = self.fetch_content(self.url)
        if soup:
            results = self.parse_docs(soup)
            all_results.extend(results)
            self.logger.info(pformat(results))

        # Handle the two specific cases for mdl_num == '2974'
        if self.mdl_num == '2974':
            additional_docs = [
                {
                    "mdl_num": "2974",
                    "doc_num": "NA",
                    "filing_date": "NA",
                    "doc_title": "Short Form Complaint",
                    "url": "https://www.gand.uscourts.gov/sites/gand/files/LMM2974_SFC.pdf"
                },
                {
                    "mdl_num": "2974",
                    "doc_num": "NA",
                    "filing_date": "NA",
                    "doc_title": "Plaintiff Fact Sheet",
                    "url": "https://www.gand.uscourts.gov/sites/gand/files/LMM2974_pfs.pdf"
                }
            ]
            all_results.extend(additional_docs)
            self.logger.info(pformat(additional_docs))

        return all_results


if __name__ == "__main__":
    config = load_config('01/01/1970')
    mdl_info = [
        ('gand', 'https://www.gand.uscourts.gov/17md2782/case-specific-forms', '2782'),
        ('gand', 'https://www.gand.uscourts.gov/17md2782/practice-procedure-orders', '2782'),
        ('gand', 'https://www.gand.uscourts.gov/17md2782/orders-and-opinions', '2782'),
        ('gand', 'https://www.gand.uscourts.gov/17md2782/misc-orders', '2782'),
        ('gand', 'https://www.gand.uscourts.gov/20md2974/practice-procedure-orders', '2974'),
        ('gand', 'https://www.gand.uscourts.gov/20md2974/orders-and-opinions', '2974')
    ]

    # TODO: Add https://www.gand.uscourts.gov/sites/gand/files/LMM2974_SFC.pdf
    # TODO: Add https://www.gand.uscourts.gov/sites/gand/files/LMM2974_pfs.pdf

    for mdl in mdl_info:
        court_id, url, mdl_num = mdl
        scraper = GANDScraper(config, url, mdl_num)
        results = scraper.run()
        # if results:
        #     for result in results:
        #         print(result)
        # else:
        #     print("No results were scraped.")
