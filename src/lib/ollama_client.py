# --- START OF FILE src/lib/ollama_text.py ---
import asyncio
import json
import os
import logging # Import logging

import aiohttp
import requests
from colorama import Fore
from tenacity import retry, wait_exponential, stop_after_attempt, before_sleep_log

# Logging should be configured in the main script
logger = logging.getLogger(__name__)

# Module-level constant for Ollama concurrency
OLLAMA_CONCURRENCY = 2 # Example concurrency limit

class OllamaTextGenerator:
    """
    Generates text using a local Ollama text generation model.

    Handles asynchronous requests, connection checking, and retries for robustness.
    Uses a shared aiohttp session for efficiency.
    Supports Metal optimizations for GPU acceleration via num_gpu_layers.
    """

    def __init__(self,
                 ollama_base_url="http://localhost:11434",
                 model_name="mistral:7b", # Example text model
                 request_timeout=180,
                 default_temperature=0.7, # Often higher for creative text gen
                 num_gpu_layers=-1):
        """
        Initializes the text generator.

        Args:
            ollama_base_url (str): Base URL of the Ollama API service.
            model_name (str): Name of the text generation model to use (e.g., 'mistral:7b', 'llama3:8b').
            request_timeout (int): Timeout in seconds for requests to Ollama.
            default_temperature (float): Default sampling temperature for generation.
            num_gpu_layers (int): Number of GPU layers to offload (-1 for auto/default).
        """
        self.ollama_generate_url = f"{ollama_base_url.rstrip('/')}/api/generate"
        self.model_name = model_name
        self.timeout = request_timeout
        self.default_temperature = default_temperature
        self.num_gpu_layers = num_gpu_layers
        self.session = None # Initialize session attribute
        self._check_ollama_connection() # Initial synchronous check

    async def _ensure_session(self):
        """Ensures an active aiohttp session exists."""
        if self.session is None or self.session.closed:
            logger.debug("Creating new aiohttp session.")
            # Limit concurrent connections to Ollama host
            connector = aiohttp.TCPConnector(limit_per_host=OLLAMA_CONCURRENCY)
            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=aiohttp.ClientTimeout(total=self.timeout)
            )

    async def close_session(self):
        """Closes the aiohttp session if it exists."""
        if self.session:
            logger.debug("Closing aiohttp session.")
            await self.session.close()
            self.session = None # Set back to None after closing

    def _check_ollama_connection(self):
        """Checks if the Ollama service is reachable (synchronous)."""
        try:
            base_url = self.ollama_generate_url.replace('/api/generate', '/')
            response = requests.get(base_url, timeout=5)
            logger.debug(f"Ollama connection check Status Code: {response.status_code}")
            logger.debug(f"Ollama connection check Raw Response Text: {response.text}")
            response.raise_for_status()
            logger.info(f"Successfully connected to Ollama at {base_url}")
        except requests.exceptions.RequestException as e:
            logger.error(f"Error connecting to Ollama: {e}")

    # Keep retry on Ollama query
    @retry(
        wait=wait_exponential(multiplier=1, min=5, max=60),
        stop=stop_after_attempt(3),
        retry_error_callback=lambda retry_state: logger.error(
            f"{Fore.RED}Ollama query failed after multiple retries: {retry_state.outcome.exception()}"),
        before_sleep=before_sleep_log(logger, logging.WARNING)
    )
    async def _query_ollama(self, prompt: str, temperature: float) -> str:
        """
        Queries the Ollama text generation model, handles retries for network/server errors.

        Args:
            prompt (str): The input prompt for the text generation model.
            temperature (float): The sampling temperature for this specific request.

        Returns:
            str: The generated text content.

        Raises:
            aiohttp.ClientError: For unrecoverable client-side errors during the request.
            asyncio.TimeoutError: If the request times out.
            json.JSONDecodeError: If the response from Ollama is not valid JSON.
            Exception: For other unexpected errors during the query process.
        """
        await self._ensure_session()
        logger.info(f"{Fore.CYAN}Querying Ollama model '{self.model_name}' for text generation...")
        payload = {
            "model": self.model_name,
            "prompt": prompt,
            # "images": [base64_image], # REMOVED - Not needed for text models
            "stream": False,
            "options": {
                "temperature": 0.1,
                "request_timeout": self.timeout,
                "num_gpu": self.num_gpu_layers,
                "num_ctx": 8192 # Context window size, adjust if needed
            }
        }
        # Log payload without potentially huge prompt for brevity if needed
        log_payload = payload.copy()
        if len(log_payload.get("prompt", "")) > 200:
             log_payload["prompt"] = log_payload["prompt"][:100] + "..." + log_payload["prompt"][-100:]
        logger.debug(f"Sending Payload: {json.dumps(log_payload)}")

        # Let retry decorator handle exceptions from this block
        async with self.session.post(
                self.ollama_generate_url, json=payload,
                headers={'Content-Type': 'application/json'},
                timeout=self.timeout
        ) as response:
            logger.debug(f"Received Status Code: {response.status}")
            raw_text = await response.text()
            logger.debug(f"Raw Response Text Length: {len(raw_text)}")
            response.raise_for_status() # Raise ClientResponseError for 4xx/5xx

            try:
                response_data = json.loads(raw_text)
                logger.debug(f"Parsed response_data type: {type(response_data)}")
                generated_text = response_data.get('response', '').strip()
                logger.debug(f"Generated text exists: {bool(generated_text)}")
                return generated_text
            except json.JSONDecodeError as json_err:
                logger.error(f"{Fore.RED}Failed to decode JSON response from Ollama: {json_err}")
                logger.debug(f"Raw text that failed decoding: {raw_text[:500]}...")
                raise # Re-raise for tenacity retry or final failure

    async def generate_text(self,
                            prompt: str,
                            temperature: float | None = None) -> str:
        """
        Generates text based on a given prompt using the configured Ollama model.
        Handles Ollama query retries internally.

        Args:
            prompt (str): The input prompt to send to the language model.
            temperature (float | None): Override the default temperature for this request.
                                       If None, uses the instance's default_temperature.

        Returns:
            str: The generated text from the model. Returns an empty string if the
                 query fails after all retries or if the model returns an empty response.
        """
        logger.debug(f"Attempting text generation for prompt starting with: '{prompt[:100]}...'")
        if temperature is None:
            temperature = self.default_temperature

        try:
            # Query Ollama (has own retry)
            generated_text = await self._query_ollama(prompt, temperature)

            # Check if text is empty *after* successful query (and retries)
            if not generated_text:
                logger.warning(f"{Fore.YELLOW}Ollama query returned empty response for prompt after potential retries.")
                return "" # Return empty string if Ollama responded successfully but with no content

            logger.info(f"{Fore.GREEN}Ollama text generation successful.")
            return generated_text

        except Exception as e:
            # This catches:
            # 1. Final failure of _query_ollama after its retries.
            # 2. Other unexpected errors in this function's scope.
            error_type = type(e).__name__
            logger.error(
                f"{Fore.RED}Error during Ollama text generation pipeline ({error_type}): {e} (final attempt or unexpected).",
                exc_info=False)
            return "" # Return empty string on final failure

# --- END OF FILE src/lib/ollama_text.py ---