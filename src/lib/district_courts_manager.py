from typing import Dict, Any

from boto3.dynamodb.conditions import Key
from botocore.exceptions import ClientError

try:
    from .dynamodb_base_manager import DynamoDbBaseManager
except ImportError:
    from src.lib.dynamodb_base_manager import DynamoDbBaseManager


class DistrictCourtsManager(DynamoDbBaseManager):
    def __init__(self, config: Dict[str, Any], use_local: bool = False, local_port: int = 8000) -> None:
        try:
            super().__init__(config, 'DistrictCourts', use_local=use_local, local_port=local_port)
            # Check if filter_record exists immediately after super().__init__
            if hasattr(self, 'filter_record'):
                print(f"--- DEBUG: self.filter_record FOUND after super init ---") # ADDED
            else:
                print(f"--- DEBUG: self.filter_record NOT FOUND after super init ---") # ADDED
        except Exception as e:
            print(f"--- DEBUG: ERROR during DistrictCourtsManager super().__init__(): {e} ---") # ADDED
            # It's useful to see if super() itself is erroring silently
            raise # Re-raise the exception

    def filter_record(self, record: Dict[str, Any], no_filter=False) -> Dict[str, Any]:
        """
        Filters a DistrictCourts record.
        Currently, no specific filtering is applied beyond the base class logic
        (which handles include/exclude attributes if defined, but DistrictCourts
         doesn't have them defined in the base manager config).
        This method primarily exists to satisfy the base class scan_table requirement.
        """
        # For DistrictCourts, we generally want all attributes.
        # The base class filter_record handles the is_local check and potential
        # filtering based on self.current_attributes/self.exclude_attributes
        # if they were configured for 'DistrictCourts'. Since they aren't,
        # calling the base implementation effectively acts as a pass-through here.
        # If specific filtering WAS needed for DistrictCourts, it would go here.
        return super().filter_record(record, no_filter=no_filter)
        # Or, even simpler if no base filtering applies:
        # return record

    # --- END ADDED METHOD ---

    def add_or_update_record(self, record):
        record = self.snake_or_camel_to_pascal_case(record)
        record = self.sanitize_record(record)
        record = {k: str(v) if not isinstance(v, bool) else v for k, v in record.items() if v is not None}

        try:
            key = {'CourtId': record['CourtId'], 'MdlNum': record['MdlNum']}
            existing_item = self.table.get_item(Key=key).get('Item')
            if existing_item:
                update_data = {k: v for k, v in record.items() if v != existing_item.get(k)}
                if update_data:
                    self.update_item(key, update_data)
            else:
                self.table.put_item(Item=record)
                self.logger.info(f"New record added for {key}")
        except ClientError as e:
            self.logger.error(f"Error updating or adding record in DynamoDB: {e.response['Error']['Message']}")
        except KeyError as e:
            self.logger.error(f"Key error: {e}")
        except ValueError as e:
            self.logger.error(f"Value error: {e}")

    def query_by_transferee_court_id_and_docket_num(self, transferee_court_id, transferee_docket_num):
        try:
            response = self.table.query(
                IndexName='TransfereeCourtId-TransfereeDocketNum-index',
                KeyConditionExpression=Key('TransfereeCourtId').eq(transferee_court_id) & Key('TransfereeDocketNum').eq(
                    transferee_docket_num)
            )
            items = response.get('Items', [])
            if items:
                self.logger.info(
                    f"Found item(s) for TransfereeCourtId: {transferee_court_id}, TransfereeDocketNum: {transferee_docket_num}")
                return items
            else:
                self.logger.info(
                    f"No item found for TransfereeCourtId: {transferee_court_id}, TransfereeDocketNum: {transferee_docket_num}")
                return []
        except ClientError as e:
            self.logger.error(
                f"Failed to query by TransfereeCourtId {transferee_court_id} and TransfereeDocketNum {transferee_docket_num}: {e.response['Error']['Message']}")
            return []
        except Exception as e:
            self.logger.error(
                f"An unexpected error occurred while querying by TransfereeCourtId and TransfereeDocketNum: {str(e)}")
            return []
