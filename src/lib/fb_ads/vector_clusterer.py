# --- START OF FILE vector_classifier.py ---

import argparse
import asyncio
import json
import logging
import os
import re
import sys
import time
from collections import Counter
from pathlib import Path
from typing import List, Tuple, Dict, Optional, Any, Pattern

import aiohttp
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sns
import torch
import umap  # For UMAP
# Rich integration
from rich.console import Console
from rich.logging import RichHandler
from rich.panel import Panel
from rich.table import Table
from tqdm import tqdm
from tqdm.asyncio import tqdm_asyncio

# Optional spaCy import for NER
try:
    import spacy
except ImportError:
    spacy = None

# Project imports (adjust paths as necessary)
try:
    from ..fb_archive_manager import FBAdArchiveManager
except ImportError:
    print(
        "Error: Could not import FBAdArchiveManager. Ensure vector_classifier.py is positioned correctly relative to fb_archive_manager.py")
    # Attempt import assuming script is run from project root and src is in path
    try:
        from src.lib.fb_archive_manager import FBAdArchiveManager
    except ImportError:
        sys.exit("Failed to import FBAdArchiveManager from multiple paths. Check structure and PYTHONPATH.")

# --- Logger setup with Rich ---
# Configure root logger or specific logger
# Using a specific logger for this class
logger = logging.getLogger("VectorClusterer")
if not logger.handlers:  # Avoid adding handlers multiple times if imported elsewhere
    # Use RichHandler for console output
    log_handler = RichHandler(rich_tracebacks=True, show_time=True, show_level=True, show_path=False)
    log_formatter = logging.Formatter('%(name)s - %(message)s')  # RichHandler handles the rest
    log_handler.setFormatter(log_formatter)
    logger.addHandler(log_handler)
    logger.setLevel(logging.INFO)  # Set default level (can be overridden by config/args)
    logger.propagate = False  # Prevent double logging if root logger is configured


class AdCampaignMatcher:
    """
    Handles matching of ad text against a predefined set of campaign rules.
    """

    def __init__(self, known_campaigns: List[Dict[str, Any]], logger_instance: logging.Logger):
        self.known_campaigns = known_campaigns
        self.logger = logger_instance

        # Pre-segregate rules for efficiency, mirroring logic from _rule_match_campaigns
        self.data_breach_rules = [c for c in self.known_campaigns if c['LitigationName'] == "Data Breach"]
        self.etsy_privacy_rules = [c for c in self.known_campaigns if
                                   c['LitigationName'] == "Etsy Privacy Litigation"]
        self.general_privacy_rules = [c for c in self.known_campaigns if c['LitigationName'] == "Privacy Violation"]

        other_rule_litigation_names = ["Data Breach", "Etsy Privacy Litigation", "Privacy Violation"]
        self.remaining_rules_for_dynamic_check = sorted(
            [c for c in self.known_campaigns if c['LitigationName'] not in other_rule_litigation_names],
            key=lambda x: (len(x.get('triggers', [])), len(x.get('include', [])), len(x.get('exclude', []))),
            reverse=True
        )

        # This specific structure is for match_ad_text's loop
        self.prioritized_rule_sets_for_match = [
            ("Data Breach Rules", self.data_breach_rules),
            ("Etsy Privacy Rules", self.etsy_privacy_rules),
            ("General Privacy Rules", self.general_privacy_rules),
            ("Other Campaign Rules (Dynamic Selection)", self.remaining_rules_for_dynamic_check)
        ]

    def _matches_rules_for_campaign(self, campaign_config_entry: Dict[str, Any], text_to_match: str) -> bool:
        """
        Checks if the given text matches all rules for a single campaign configuration.
        Assumes text_to_match is already normalized.
        """
        trigger_pattern: Optional[Pattern] = campaign_config_entry.get('trigger_pattern')
        include_or_pattern: Optional[Pattern] = campaign_config_entry.get('include_or_pattern')
        exclude_pattern: Optional[Pattern] = campaign_config_entry.get('exclude_pattern')

        # 1. Check Triggers (Must match at least one trigger)
        if not trigger_pattern or not trigger_pattern.search(text_to_match):
            return False

        # 2. Check Includes (If an include_or_pattern exists, it must match at least one include term)
        if include_or_pattern and not include_or_pattern.search(text_to_match):
            return False

        # 3. Check Excludes (If an exclude_pattern exists, it must NOT match any exclude term)
        if exclude_pattern and exclude_pattern.search(text_to_match):
            return False

        return True

    def _calculate_specificity_score(self, campaign_config_entry: Dict[str, Any], text_to_match: str) -> int:
        """
        Calculates a specificity score for a match.
        Assumes text_to_match is already normalized.
        """
        trigger_pattern: Optional[Pattern] = campaign_config_entry.get('trigger_pattern')
        include_or_pattern: Optional[Pattern] = campaign_config_entry.get('include_or_pattern')

        matched_trigger_terms_list = trigger_pattern.findall(text_to_match) if trigger_pattern else []
        matched_include_terms_list = include_or_pattern.findall(text_to_match) if include_or_pattern else []

        specificity_score = 0
        if include_or_pattern and matched_include_terms_list:
            # High bonus for rules with includes, if they matched
            specificity_score += 1000

        specificity_score += len(set(matched_trigger_terms_list))
        specificity_score += len(set(matched_include_terms_list))
        return specificity_score

    def match_ad_text(self, text_to_match: str, do_verbose_log: bool = False, ad_id_for_log: str = "N/A") -> Optional[
        str]:
        """
        Attempts to match the given normalized text against campaign rules.
        Returns the LitigationName of the matched campaign, or None.
        """
        matched_campaign_name: Optional[str] = None

        if not text_to_match.strip():
            if do_verbose_log: self.logger.debug(
                f"AD_ID: {ad_id_for_log} - AdCampaignMatcher: Empty text_to_match provided.")
            return None
    
    def get_campaign_company(self, litigation_name: str) -> Optional[str]:
        """
        Gets the Company field for a given LitigationName from the campaign rules.
        Returns None if the campaign is not found or has no Company field.
        """
        for campaign in self.known_campaigns:
            if campaign.get('LitigationName') == litigation_name:
                return campaign.get('Company')
        return None

        for pass_name, current_rule_set in self.prioritized_rule_sets_for_match:
            if matched_campaign_name:  # Already found a match in a higher priority set
                break

            if do_verbose_log: self.logger.debug(
                f"  AD_ID: {ad_id_for_log} - AdCampaignMatcher attempting pass: {pass_name}")

            if pass_name == "Other Campaign Rules (Dynamic Selection)":
                potential_matches: List[Tuple[int, str]] = []  # (specificity_score, campaign_name)
                for campaign_config_entry in current_rule_set:
                    campaign_name_from_config = campaign_config_entry['LitigationName']
                    if do_verbose_log: self.logger.debug(
                        f"    AD_ID: {ad_id_for_log} - Candidate rule in '{pass_name}': '{campaign_name_from_config}'")

                    if self._matches_rules_for_campaign(campaign_config_entry, text_to_match):
                        score = self._calculate_specificity_score(campaign_config_entry, text_to_match)
                        potential_matches.append((score, campaign_name_from_config))
                        if do_verbose_log: self.logger.debug(
                            f"      Rule '{campaign_name_from_config}': POTENTIAL MATCH (Score: {score}).")

                if potential_matches:
                    potential_matches.sort(key=lambda x: x[0], reverse=True)  # Highest score first
                    matched_campaign_name = potential_matches[0][1]
                    if do_verbose_log: self.logger.debug(
                        f"  AD_ID: {ad_id_for_log} - *** BEST MATCH IN '{pass_name}': {matched_campaign_name} (Score: {potential_matches[0][0]}) ***")
                    break  # Found best match in this pass
            else:  # Fixed-priority passes (Data Breach, Etsy, General Privacy)
                for campaign_config_entry in current_rule_set:
                    campaign_name_from_config = campaign_config_entry['LitigationName']
                    if do_verbose_log: self.logger.debug(
                        f"    AD_ID: {ad_id_for_log} - Checking rule in '{pass_name}': '{campaign_name_from_config}'")

                    if self._matches_rules_for_campaign(campaign_config_entry, text_to_match):
                        matched_campaign_name = campaign_name_from_config
                        if do_verbose_log: self.logger.debug(
                            f"    AD_ID: {ad_id_for_log} - *** SUCCESSFULLY EXACT MATCHED TO CAMPAIGN: {campaign_name_from_config} (Pass: {pass_name}) ***")
                        break  # Matched in this fixed-priority rule set

            if matched_campaign_name:  # if matched in the inner loop (fixed-priority)
                break

        if do_verbose_log and not matched_campaign_name:
            self.logger.debug(
                f"  AD_ID: {ad_id_for_log} - AdCampaignMatcher: No campaign rule matched this ad after all passes.")

        return matched_campaign_name


# Helper method

def simplify_name(name):
    """Basic simplification for comparison/grouping."""
    if not name or not isinstance(name, str):
        return "Unknown"
    # Lowercase, remove punctuation/excess whitespace, common lawsuit suffixes
    name = name.lower()
    name = re.sub(r'[^\w\s-]', '', name)  # Keep alphanumeric, whitespace, hyphen
    suffixes_to_remove = [
        'litigation', 'lawsuit', 'claims', 'investigation', 'settlement', 'products liability',
        'case', 'mdl', 'class action', 'mass tort', 'attorney', 'lawyer', 'legal'
    ]
    # Remove suffixes as whole words
    for suffix in suffixes_to_remove:
        name = re.sub(r'\b' + re.escape(suffix) + r'\b', '', name)
    name = re.sub(r'\s+', ' ', name).strip()  # Consolidate whitespace
    return name if name else "Unknown"


def _normalize_text_for_matching(text: str) -> str:
    """
    Normalizes text for consistent rule matching: lowercases, removes punctuation,
    and consolidates whitespace.
    """
    if not isinstance(text, str):
        return ""
    text = text.lower()
    # Remove punctuation and replace with a single space to avoid merging words
    text = re.sub(r'[^\w\s]', ' ', text)
    text = re.sub(r'\s+', ' ', text).strip()  # Consolidate spaces and strip leading/trailing
    return text


# --- VectorClusterer Class ---
class VectorClusterer:
    def __init__(self, config: Dict[str, Any], session: aiohttp.ClientSession,
                 embedding_fields: List[str] = ['Title', 'Body', 'Summary', 'ImageText'],
                 combined_text_col: str = 'CombinedEmbeddingText',
                 embedding_stop_terms_path: Optional[str] = None,
                 campaign_skip_terms_path: Optional[str] = None,
                 campaign_config_path: Optional[str] = None,  # Path to campaign_config.json
                 use_local: bool = False, scan_workers: Optional[int] = None,
                 rules_only: bool = False,
                 use_dbscan: bool = False,  # New parameter for DBSCAN
                 dbscan_eps: float = 0.5,  # New parameter for DBSCAN
                 dbscan_min_samples: int = 5):  # New parameter for DBSCAN
        """
        Initialize the VectorClusterer.
        Args:
            # ... (other args) ...
            campaign_config_path (Optional[str]): Path to JSON file with known campaign definitions.
            rules_only (bool): If True, run only rule-based matching and skip vector processing.
            use_dbscan (bool): If True, use DBSCAN for clustering remainder ads instead of K-Means.
            dbscan_eps (float): Epsilon parameter for DBSCAN.
            dbscan_min_samples (int): Min_samples parameter for DBSCAN.
        """
        self.config = config
        self.session = session
        self.embedding_fields = embedding_fields
        self.combined_text_col = combined_text_col
        self.use_local = use_local
        self.scan_workers = scan_workers if scan_workers is not None else (os.cpu_count() or 4)
        self.rules_only_mode = rules_only
        self.use_dbscan_mode = use_dbscan
        self.dbscan_eps = dbscan_eps
        self.dbscan_min_samples = dbscan_min_samples

        self.console = Console()
        self.all_data_df = None
        # Note: logger is defined at module level. If VectorClusterer needs its own or configured one:
        self.logger = logging.getLogger("VectorClusterer")  # Use the module-level logger

        try:
            self.ad_archive = FBAdArchiveManager(config, use_local=self.use_local, no_filter=True)
            self.logger.info(
                f"FBAdArchiveManager initialized for table '{self.ad_archive.table_name}' (local={self.use_local})")
        except Exception as e:
            self.logger.error(f"FBAdArchiveManager initialization failed: {e}", exc_info=True)
            self.console.print(
                f"[bold red]Error:[/bold red] Failed to initialize FBAdArchiveManager: {e}. Check config and DynamoDB connection.")
            raise

        if not self.rules_only_mode:
            self.embedding_model = self._get_embedding_model()
        else:
            self.embedding_model = None
            self.logger.info("Running in rules-only mode. Embedding model will not be loaded.")

        self.embedding_stop_terms = self._load_json_term_list(embedding_stop_terms_path, "embedding stop terms")
        self.embedding_stop_terms_pattern = self._compile_term_pattern(self.embedding_stop_terms,
                                                                       "embedding stop terms")

        self.campaign_skip_terms = self._load_json_term_list(campaign_skip_terms_path, "campaign skip terms")
        self.campaign_skip_terms_pattern = self._compile_term_pattern(self.campaign_skip_terms, "campaign skip terms")

        self.known_campaigns = self._load_and_process_campaign_config(campaign_config_path)

        # Initialize AdCampaignMatcher
        if self.known_campaigns:
            self.ad_campaign_matcher = AdCampaignMatcher(self.known_campaigns, self.logger)
            self.logger.info(f"AdCampaignMatcher initialized with {len(self.known_campaigns)} processed campaigns.")
        else:
            self.ad_campaign_matcher = None  # No rules to match with
            self.logger.warning("AdCampaignMatcher not initialized as no known_campaigns were loaded.")

        self.faiss_index = None
        self.index_to_ad_id_map = None
        self.embeddings_array_full = None
        self.ad_ids_full = None
        self.ad_id_to_full_idx_map = None
        self.representative_to_all_originals_map: Optional[Dict[str, List[str]]] = None

        if self.use_dbscan_mode:
            self.logger.info(
                f"DBSCAN clustering enabled with eps={self.dbscan_eps}, min_samples={self.dbscan_min_samples}")
            self.console.print(
                f"[yellow]DBSCAN clustering will be used for remainder ads (eps={self.dbscan_eps}, min_samples={self.dbscan_min_samples}).[/]")
        else:
            self.logger.info("K-Means clustering will be used for remainder ads.")

    def _load_campaign_config_file(self, file_path: Optional[str]) -> List[Dict[str, Any]]:
        """Loads the campaign configuration file (list of dictionaries)."""
        if not file_path:
            logger.info("No campaign config file path provided.")
            return []

        path = Path(file_path).resolve()
        logger.info(f"Loading campaign configuration from: {path}")
        self.console.print(f"[cyan]Loading campaign configuration from: {path}...[/]")
        try:
            with open(path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                if isinstance(data, list) and all(isinstance(item, dict) for item in data):
                    logger.info(f"Loaded {len(data)} campaign config entries.")
                    self.console.print(f"[green]✓ Loaded {len(data)} campaign config entries.[/]")
                    return data
                else:
                    logger.error(f"Invalid format in {path}. Expected a JSON list of campaign objects (dictionaries).")
                    self.console.print(
                        f"[bold red]Error:[/bold red] Invalid format in campaign config file {path}. Expected list of objects.")
                    return []
        except FileNotFoundError:
            logger.error(f"Campaign config file not found: {path}")
            self.console.print(f"[bold red]Error:[/bold red] Campaign config file not found: {path}")
            return []
        except json.JSONDecodeError as e:
            logger.error(f"Error decoding JSON from campaign config {path}: {e}")
            self.console.print(f"[bold red]Error:[/bold red] Failed to decode JSON from campaign config {path}: {e}")
            return []
        except Exception as e:
            logger.error(f"Error loading campaign config from {path}: {e}", exc_info=True)
            self.console.print(f"[bold red]Error:[/bold red] Failed to load campaign config: {e}")
            return []

    def _load_and_process_campaign_config(self, file_path: Optional[str]) -> List[Dict[str, Any]]:
        """Loads campaign_config.json and pre-compiles regex for triggers, includes, and excludes
        using OR logic for each group, ensuring terms are normalized (lowercased, no punctuation)."""
        if not file_path:
            logger.warning(
                "No campaign_config.json path provided. Rule-based matching to known campaigns will be skipped.")
            return []

        campaign_data_list = self._load_campaign_config_file(file_path)

        processed_campaigns = []
        if not isinstance(campaign_data_list, list):
            logger.error(
                f"Campaign configuration data from '{file_path}' is not a list as expected. Skipping processing.")
            return []

        for campaign_dict in campaign_data_list:
            current_campaign_data = campaign_dict.copy()

            if "LitigationName" not in current_campaign_data or "triggers" not in current_campaign_data:
                logger.warning(
                    f"Skipping invalid campaign entry (missing LitigationName or triggers): {current_campaign_data.get('LitigationName', 'N/A')}")
                continue

            litigation_name = current_campaign_data['LitigationName']

            # Process Triggers (OR logic)
            triggers_raw = current_campaign_data.get("triggers", [])
            if not triggers_raw or not isinstance(triggers_raw, list) or not all(
                    isinstance(t, str) for t in triggers_raw):
                logger.warning(
                    f"Campaign '{litigation_name}' has invalid or missing triggers (must be a list of strings). Skipping.")
                continue

            # Normalize trigger terms BEFORE compiling
            processed_trigger_terms = [_normalize_text_for_matching(t) for t in triggers_raw if t.strip()]
            if not processed_trigger_terms:
                logger.warning(f"Campaign '{litigation_name}' has no valid trigger terms after processing. Skipping.")
                continue
            current_campaign_data['trigger_pattern'] = self._compile_term_pattern(
                processed_trigger_terms, f"triggers for {litigation_name}"
            )
            if not current_campaign_data['trigger_pattern']:
                logger.warning(f"Failed to compile trigger pattern for {litigation_name}. Skipping this campaign.")
                continue

            # Process Includes (OR logic)
            includes_raw = current_campaign_data.get("include", [])

            # Normalize include terms BEFORE compiling
            processed_include_terms = [_normalize_text_for_matching(i) for i in includes_raw if i.strip()]

            if processed_include_terms:
                current_campaign_data['include_or_pattern'] = self._compile_term_pattern(
                    processed_include_terms, f"includes for {litigation_name}"
                )
            else:
                current_campaign_data['include_or_pattern'] = None

            # Process Excludes (OR logic)
            excludes_raw = current_campaign_data.get("exclude", [])

            # Normalize exclude terms BEFORE compiling
            processed_exclude_terms = [_normalize_text_for_matching(e) for e in excludes_raw if e.strip()]

            if processed_exclude_terms:
                current_campaign_data['exclude_pattern'] = self._compile_term_pattern(
                    processed_exclude_terms, f"excludes for {litigation_name}"
                )
            else:
                current_campaign_data['exclude_pattern'] = None

            processed_campaigns.append(current_campaign_data)

        if processed_campaigns:
            logger.info(
                f"Successfully processed {len(processed_campaigns)} known campaigns from config with OR-logic regex patterns.")
            self.console.print(f"[green]✓ Processed {len(processed_campaigns)} known campaigns with regex patterns.[/]")
        else:
            logger.warning(
                "No valid known campaigns were processed from the configuration. Rule-matching will be ineffective.")
            self.console.print(
                "[yellow]Warning: No valid known campaigns were processed. Check campaign_config.json format and content.[/]")
        return processed_campaigns

    def _load_json_term_list(self, file_path: Optional[str], term_type_description: str) -> List[str]:
        """Loads a list of terms from a JSON file."""
        # Unique message to see if this is called for campaign_config.json
        if "known campaign configuration" in term_type_description.lower():
            self.console.print(
                f"[bold red]DEBUG: _load_json_term_list CALLED FOR '{term_type_description}' with path {file_path}[/bold red]")
            logger.error(f"DEBUG: _load_json_term_list CALLED FOR '{term_type_description}' with path {file_path}")

        if not file_path:
            logger.info(f"No {term_type_description} file provided. Skipping.")
            return []
        # ... (rest of the method as before) ...
        path = Path(file_path).resolve()
        logger.info(f"Loading {term_type_description} from: {path}")
        self.console.print(f"[cyan]Loading {term_type_description} from: {path}...[/]")
        try:
            with open(path, 'r', encoding='utf-8') as f:
                terms = json.load(f)
                if isinstance(terms, list) and all(isinstance(t, str) for t in terms):
                    processed_terms = [term.strip().lower() for term in terms if term.strip()]
                    logger.info(f"Loaded {len(processed_terms)} {term_type_description}.")
                    self.console.print(f"[green]✓ Loaded {len(processed_terms)} {term_type_description}.[/]")
                    return processed_terms
                else:
                    logger.error(
                        f"Invalid format in {path}. Expected a JSON list of strings for {term_type_description}.")
                    self.console.print(
                        f"[bold red]Error:[/bold red] Invalid format in {term_type_description} file {path}. Expected list of strings.")
                    return []
        except FileNotFoundError:
            logger.error(f"{term_type_description.capitalize()} file not found: {path}")
            self.console.print(
                f"[bold red]Error:[/bold red] {term_type_description.capitalize()} file not found: {path}")
            return []
        except json.JSONDecodeError as e:
            logger.error(f"Error decoding JSON from {path} ({term_type_description}): {e}")
            self.console.print(
                f"[bold red]Error:[/bold red] Failed to decode JSON from {path} ({term_type_description}): {e}")
            return []
        except Exception as e:
            logger.error(f"Error loading {term_type_description} from {path}: {e}", exc_info=True)
            self.console.print(f"[bold red]Error:[/bold red] Failed to load {term_type_description}: {e}")
            return []

    def _load_embedding_stop_terms(self, file_path: Optional[str]) -> List[str]:
        """Loads stop terms from a JSON file."""
        if not file_path:
            logger.info("No embedding stop terms file provided. Skipping removal.")
            return []

        path = Path(file_path).resolve()
        logger.info(f"Loading embedding stop terms from: {path}")
        self.console.print(f"[cyan]Loading embedding stop terms from: {path}...[/]")
        try:
            with open(path, 'r', encoding='utf-8') as f:
                terms = json.load(f)
                if isinstance(terms, list) and all(isinstance(t, str) for t in terms):
                    # Convert to lowercase for case-insensitive matching later
                    processed_terms = [term.strip().lower() for term in terms if term.strip()]
                    logger.info(f"Loaded {len(processed_terms)} embedding stop terms.")
                    self.console.print(f"[green]✓ Loaded {len(processed_terms)} embedding stop terms.[/]")
                    return processed_terms
                else:
                    logger.error(f"Invalid format in {path}. Expected a JSON list of strings.")
                    self.console.print(
                        f"[bold red]Error:[/bold red] Invalid format in stop terms file {path}. Expected list of strings.")
                    return []
        except FileNotFoundError:
            logger.error(f"Embedding stop terms file not found: {path}")
            self.console.print(f"[bold red]Error:[/bold red] Embedding stop terms file not found: {path}")
            return []
        except json.JSONDecodeError as e:
            logger.error(f"Error decoding JSON from {path}: {e}")
            self.console.print(f"[bold red]Error:[/bold red] Failed to decode JSON from {path}: {e}")
            return []
        except Exception as e:
            logger.error(f"Error loading embedding stop terms from {path}: {e}", exc_info=True)
            self.console.print(f"[bold red]Error:[/bold red] Failed to load embedding stop terms: {e}")
            return []

    def _compile_term_pattern(self, terms: List[str], term_type_description: str) -> Optional[re.Pattern]:
        """
        Compiles a regex pattern from a list of terms.
        Assumes input 'terms' are already normalized (lowercased, no punctuation).
        Uses \b for whole-word matching.
        """
        if not terms:
            return None
        try:
            # Join terms with '|' for OR logic, escape for regex special chars,
            # and wrap in \b for whole-word match
            # Note: terms here should already be clean (no punctuation)
            pattern_str = r'\b(?:' + '|'.join(re.escape(term) for term in terms) + r')\b'
            pattern = re.compile(pattern_str, re.IGNORECASE)
            logger.info(f"Compiled regex pattern for {len(terms)} {term_type_description}.")
            return pattern
        except Exception as e:
            logger.error(f"Failed to compile regex for {term_type_description}: {e}", exc_info=True)
            self.console.print(f"[bold red]Error:[/bold red] Failed to compile regex for {term_type_description}.")
            return None

    def _prepare_combined_text_for_embedding(self):
        """
        Creates the final combined text for embedding (self.combined_text_col)
        for ALL ads not categorized as 'General'.
        This version specifically uses ONLY 'Title' and 'Body' for the embedding text source,
        regardless of the broader self.embedding_fields list.
        Removes embedding_stop_terms.
        """
        if self.all_data_df is None or self.all_data_df.empty:
            logger.error("DataFrame not loaded. Cannot prepare combined text.")
            return False

        self.all_data_df[self.combined_text_col] = ""  # Initialize for all

        needs_embedding_text_mask = (self.all_data_df['PreClusterCategory'] != 'General')
        df_subset_for_embedding_text = self.all_data_df[needs_embedding_text_mask].copy()

        if df_subset_for_embedding_text.empty:
            logger.info("No non-General ads require preparation of combined text for embedding.")
            return True

        # Define the specific fields to be used as the source for the embedding text column
        fields_for_embedding_col_source = ['Title', 'Body']

        logger.info(
            f"Preparing '{self.combined_text_col}' for embedding for {len(df_subset_for_embedding_text)} non-General ads. "
            f"Source fields for this column are hardcoded to: {fields_for_embedding_col_source}. "
            f"(Note: self.embedding_fields from CLI/config was: {self.embedding_fields}, "
            f"which may be used for data loading or other non-embedding related reporting/processing)."
        )
        self.console.print(
            f"  [dim]Combined text for embedding ({self.combined_text_col}) will be sourced ONLY from: {', '.join(fields_for_embedding_col_source)}.[/]"
        )

        if self.embedding_stop_terms_pattern:
            self.console.print(
                f"  [dim]Will remove {len(self.embedding_stop_terms)} embedding stop terms from this text.[/]")

        present_fields_for_embedding_col = [field for field in fields_for_embedding_col_source
                                            if field in df_subset_for_embedding_text.columns]

        if not present_fields_for_embedding_col:
            logger.error(
                f"None of the designated embedding source fields {fields_for_embedding_col_source} are present in the DataFrame subset. "
                f"Cannot create '{self.combined_text_col}'.")
            self.console.print(
                f"[bold red]Error:[/bold red] Required fields ({', '.join(fields_for_embedding_col_source)}) for embedding text are missing.")
            return False

        def combine_and_clean_for_embedding(row):
            # Use only the hardcoded 'Title' and 'Body' (if present) for this specific combined text
            combined = " | ".join(
                str(row.get(field, '')).lower()
                for field in present_fields_for_embedding_col if str(row.get(field, '')).strip()
            )
            cleaned = combined
            if self.embedding_stop_terms_pattern and combined:
                cleaned = self.embedding_stop_terms_pattern.sub('', combined)
                cleaned = re.sub(r'\s+', ' ', cleaned).strip()
                cleaned = re.sub(r'(\s*\|\s*){2,}', ' | ', cleaned).strip(' | ')
            return cleaned

        self.all_data_df.loc[needs_embedding_text_mask, self.combined_text_col] = \
            df_subset_for_embedding_text.apply(combine_and_clean_for_embedding, axis=1)

        empty_after_clean_count = (self.all_data_df.loc[needs_embedding_text_mask, self.combined_text_col] == '').sum()
        if empty_after_clean_count > 0:
            logger.warning(
                f"{empty_after_clean_count} non-General ads have empty '{self.combined_text_col}' after cleaning, using only {fields_for_embedding_col_source}.")

        self.console.print(
            f"[green]✓ '{self.combined_text_col}' prepared for non-General ads (sourced from Title, Body).[/]")
        return True

    def _filter_general_ads(self):
        if self.all_data_df is None or self.all_data_df.empty: return False

        # Initialize PreClusterCategory for ALL ads first
        self.all_data_df['PreClusterCategory'] = 'NeedsProcessing'  # Default

        if not self.campaign_skip_terms_pattern:
            logger.info("No campaign skip terms loaded. No ads pre-filtered as 'General'.")
            return True

        if '_RawCombinedTextForRuleMatch' not in self.all_data_df.columns:
            logger.error("'_RawCombinedTextForRuleMatch' column missing. Cannot filter general ads.")
            return False

        self.console.print(
            f"[cyan]Filtering general ads (for those not rule-matched) using {len(self.campaign_skip_terms)} skip terms...[/]")

        # IMPORTANT: Only apply general skip terms to ads NOT already matched to a specific campaign
        unmatched_by_rule_mask = self.all_data_df['RuleMatchedCampaign'].isna()

        # Get the raw text for these unmatched ads
        text_series_for_skip_check = self.all_data_df.loc[
            unmatched_by_rule_mask, '_RawCombinedTextForRuleMatch'].astype(str)

        if text_series_for_skip_check.empty:
            logger.info("No ads remaining to check for general skip terms after rule matching.")
            return True

        general_mask_on_subset = text_series_for_skip_check.str.contains(
            self.campaign_skip_terms_pattern,
            regex=True,
            na=False
        )

        # Get the original DataFrame indices for ads that matched skip terms from the subset
        original_indices_for_general = text_series_for_skip_check[general_mask_on_subset].index

        num_general_ads = len(original_indices_for_general)
        if num_general_ads > 0:
            self.all_data_df.loc[original_indices_for_general, 'PreClusterCategory'] = 'General'

        logger.info(
            f"Marked {num_general_ads} ads (from non-rule-matched set) as 'General' based on campaign skip terms.")
        self.console.print(f"[green]✓ Marked {num_general_ads} non-rule-matched ads as 'General' (pre-filter).[/]")

        # ... (Debugging block for high general count can remain, but ensure it uses text_series_for_skip_check) ...
        return True

    def _get_embedding_model(self):
        # Initialize and return your chosen sentence transformer or other model
        from sentence_transformers import SentenceTransformer
        logger.info("Loading embedding model...")
        self.console.print("[cyan]Loading embedding model (all-MiniLM-L6-v2)...[/]")
        if torch.backends.mps.is_available() and torch.backends.mps.is_built():
            device = 'mps'
        elif torch.cuda.is_available():
            device = 'cuda'
        else:
            device = 'cpu'
        logger.info(f"Using device: {device} for embedding model.")
        self.console.print(f"  [dim]Using device: {device}[/]")
        try:
            model = SentenceTransformer('all-MiniLM-L6-v2', device=device)
            self.console.print("[green]✓ Embedding model loaded.[/]")
            logger.info("Embedding model loaded.")
            return model
        except Exception as e:
            logger.error(f"Failed to load SentenceTransformer model: {e}", exc_info=True)
            self.console.print(f"[bold red]Error:[/bold red] Failed to load embedding model: {e}")
            raise

    def _rule_match_campaigns(self):
        if self.all_data_df is None or self.all_data_df.empty:
            self.logger.error("DataFrame not loaded. Cannot perform rule matching.")
            self.console.print("[bold red]Error: DataFrame not loaded for rule matching.[/]")
            return
        if not self.ad_campaign_matcher:  # Checks if known_campaigns were loaded and matcher initialized
            self.logger.warning(
                "AdCampaignMatcher not available (no known campaigns loaded). Skipping rule-based matching.")
            self.console.print(
                "[yellow]Warning: No known campaigns loaded/AdCampaignMatcher not available. Skipping rule-based matching.[/]")
            self.all_data_df['RuleMatchedCampaign'] = pd.NA
            return

        self.console.print("[cyan]Attempting prioritized exact rule-based matching (dynamic specificity)...[/]")
        self.all_data_df['RuleMatchedCampaign'] = pd.NA

        if '_RawCombinedTextForRuleMatch' not in self.all_data_df.columns:
            self.logger.error("'_RawCombinedTextForRuleMatch' column missing. Cannot perform rule matching.")
            self.console.print("[bold red]Error: _RawCombinedTextForRuleMatch missing for rule matching.[/]")
            return

        match_count = 0
        DEBUG_AD_ID_TARGET = None  # Or set an AdArchiveID string for specific debugging
        ads_debugged_this_run = 0
        MAX_DEBUG_ADS = 5  # Keep low for general runs, increase for specific debugging

        for idx in tqdm(self.all_data_df.index, desc="Prioritized Exact Rule Matching", unit="ad", ncols=100):
            row_series = self.all_data_df.loc[idx]
            current_ad_id = str(row_series['AdArchiveID'])
            # text_to_match is already normalized when _RawCombinedTextForRuleMatch was created
            text_to_match = str(row_series.get('_RawCombinedTextForRuleMatch', ''))

            do_verbose_log = (DEBUG_AD_ID_TARGET and current_ad_id == DEBUG_AD_ID_TARGET) or \
                             (
                                     DEBUG_AD_ID_TARGET is None and ads_debugged_this_run < MAX_DEBUG_ADS and self.logger.isEnabledFor(
                                 logging.DEBUG))

            if not text_to_match.strip():
                if do_verbose_log: self.logger.debug(
                    f"AD_ID: {current_ad_id} - Text to match is empty. Skipping rule checks.")
                continue

            if do_verbose_log: self.logger.debug(
                f"AD_ID: {current_ad_id} - Processing rule matching with text: '{text_to_match[:300]}...'")

            matched_campaign_name = self.ad_campaign_matcher.match_ad_text(
                text_to_match,
                do_verbose_log=do_verbose_log,
                ad_id_for_log=current_ad_id
            )

            if matched_campaign_name:
                self.all_data_df.loc[idx, 'RuleMatchedCampaign'] = matched_campaign_name
                match_count += 1
                if do_verbose_log and DEBUG_AD_ID_TARGET is None:
                    ads_debugged_this_run += 1
            elif do_verbose_log and DEBUG_AD_ID_TARGET is None:  # No match, still count towards debug limit
                ads_debugged_this_run += 1

        self.logger.info(
            f"Prioritized exact rule-based matching complete. {match_count} ads matched to known campaigns.")
        self.console.print(
            f"[green]✓ Prioritized exact rule-based matching: {match_count} ads assigned to known campaigns.[/]")

    async def load_data_from_dynamodb(self):
        self.console.print(
            Panel(f"[bold cyan]Loading Data from DynamoDB ({'Local' if self.use_local else 'AWS'})...[/]",
                  border_style="cyan"))
        logger.info("Starting data load from DynamoDB...")
        items = []
        start_time = time.time()
        initial_count_raw = 0

        # Ensure 'Summary' is always projected as it's used in deduplication and output logic later
        fields_to_project_set = {'AdArchiveID', 'LitigationName', 'Title', 'Body', 'Summary'}
        # Add fields specified for embedding if not already present
        fields_to_project_set.update(self.embedding_fields)
        # Also ensure StartDate is projected if available, for tie-breaking in deduplication
        # However, since StartDate might not always exist or be consistently named,
        # we handle its absence in the deduplication logic itself.
        # For now, only explicitly add fields known to be essential for core processing or fixed output columns.
        # If 'StartDate' is crucial, it should be added here and handled like other essential fields.

        projection_expression = ", ".join(list(fields_to_project_set))
        logger.info(f"DynamoDB ProjectionExpression: {projection_expression}")

        try:
            if self.use_local:
                logger.info(f"Starting parallel scan with {self.scan_workers} workers...")
                total_segments = self.scan_workers

                async def scan_segment_local(segment, total_segments_val, projection_expr_val):
                    segment_items_list = []
                    last_evaluated_key = None
                    try:
                        while True:
                            if not self.ad_archive.table: return []
                            scan_params = {'Segment': segment, 'TotalSegments': total_segments_val,
                                           'ProjectionExpression': projection_expr_val}
                            if last_evaluated_key: scan_params['ExclusiveStartKey'] = last_evaluated_key
                            response = await asyncio.to_thread(self.ad_archive.table.scan, **scan_params)
                            segment_items_list.extend(response.get('Items', []))
                            last_evaluated_key = response.get('LastEvaluatedKey')
                            if not last_evaluated_key: break
                        return segment_items_list
                    except Exception as e_scan:
                        logger.error(f"Worker {segment}: Scan Error: {e_scan}", exc_info=True)
                        return []

                scan_tasks = [scan_segment_local(i, total_segments, projection_expression) for i in
                              range(total_segments)]
                with self.console.status("[bold green]Scanning DynamoDB segments...", spinner="dots"):
                    segment_results = await tqdm_asyncio.gather(*scan_tasks, desc="Scanning Table Segments",
                                                                disable=True)  # TQDM disabled here for cleaner status
                for result_list_val in segment_results: items.extend(result_list_val)
            else:  # AWS
                with self.console.status("[bold green]Scanning AWS DynamoDB table...", spinner="dots"):
                    items = await asyncio.to_thread(self.ad_archive.scan_table,
                                                    projection_expression=projection_expression)

            initial_count_raw = len(items)
            scan_duration = time.time() - start_time
            self.console.print(f"[green]✓ Scan finished in {scan_duration:.2f}s, found {initial_count_raw} items.[/]")

            if not items:
                self.console.print("[yellow]No items found in DynamoDB scan.[/]")
                self.all_data_df = pd.DataFrame()
                return False

            self.all_data_df = pd.DataFrame(items)
            if 'AdArchiveID' not in self.all_data_df.columns:
                logger.error("Critical: 'AdArchiveID' column not found in loaded data.")
                self.console.print("[bold red]Error: 'AdArchiveID' missing from loaded data.[/]")
                return False
            self.all_data_df['AdArchiveID'] = self.all_data_df['AdArchiveID'].astype(str)

            # Ensure all projected fields exist and are string type
            # This includes 'Summary' due to the modification above.
            for col in list(fields_to_project_set):
                if col not in self.all_data_df.columns: self.all_data_df[col] = ''  # Initialize if missing from items
                self.all_data_df[col] = self.all_data_df[col].fillna('').astype(str)

            count_before_dedup = len(self.all_data_df)
            self.all_data_df.drop_duplicates(subset=['AdArchiveID'], keep='first', inplace=True)
            if len(self.all_data_df) < count_before_dedup:
                self.console.print(
                    f"[yellow]Removed {count_before_dedup - len(self.all_data_df)} duplicate AdArchiveIDs.[/]")

            self.console.print(
                f"[cyan]Preparing raw combined text for rule matching & skip terms (using Title & Body only)...[/]")

            def get_raw_combined_text(row):
                fields_for_rule_match = ['Title', 'Body']
                combined = " | ".join(
                    str(row.get(field, '')).lower()
                    for field in fields_for_rule_match
                    if str(row.get(field, '')).strip()
                )
                return _normalize_text_for_matching(combined)

            self.all_data_df['_RawCombinedTextForRuleMatch'] = self.all_data_df.apply(get_raw_combined_text, axis=1)
            logger.info("Created '_RawCombinedTextForRuleMatch' column using Title and Body.")

            self._rule_match_campaigns()
            self._filter_general_ads()

            if not self.rules_only_mode:
                if not self._prepare_combined_text_for_embedding():  # Uses self.embedding_fields
                    return False
            else:
                logger.info("Skipping embedding text preparation in rules-only mode.")
                self.console.print("[yellow]Skipping embedding text preparation (rules-only mode).[/]")

            if 'LitigationName' in self.all_data_df.columns:
                original_lit_name_counts = self.all_data_df['LitigationName'].value_counts(dropna=False)
                if not original_lit_name_counts.empty:
                    logger.debug(f"Original LitigationName distribution:\n{original_lit_name_counts.to_string()}")
                else:
                    logger.info("No 'LitigationName' values found in the loaded data.")

            self.console.print(f"[green]✓ Data loaded and pre-processed. {len(self.all_data_df)} ads ready.[/]")
            return True

        except Exception as e:
            logger.error(f"Error during data load from DynamoDB: {e}", exc_info=True)
            self.all_data_df = pd.DataFrame()
            return False

    async def generate_and_index_embeddings(self, id_field='AdArchiveID'):
        """
        Generates embeddings for unique creatives (based on Title and Body) among the true remainder ads.
        Populates self.embeddings_array_full, self.ad_ids_full (with representative AdArchiveIDs),
        self.ad_id_to_full_idx_map, and self.representative_to_all_originals_map.
        """
        if self.all_data_df is None or self.all_data_df.empty:
            logger.error("DataFrame not loaded. Cannot generate embeddings.")
            return False

        # Embed only ads that are "true remainders":
        # - Not matched by any rule (RuleMatchedCampaign is NA).
        # - PreClusterCategory is 'NeedsProcessing'.
        embedding_target_mask = (self.all_data_df['RuleMatchedCampaign'].isna()) & \
                                (self.all_data_df['PreClusterCategory'] == 'NeedsProcessing')

        df_eligible_for_embedding = self.all_data_df[embedding_target_mask].copy()

        if df_eligible_for_embedding.empty:
            self.console.print(
                "[cyan]No ads require embedding for clustering (all rule-matched, general, or other).[/]")
            self.embeddings_array_full = np.array([])
            self.ad_ids_full = []
            self.ad_id_to_full_idx_map = {}
            self.representative_to_all_originals_map = {}
            return True

        # Ensure necessary columns for deduplication and embedding text are present
        required_cols = [id_field, self.combined_text_col, 'Title', 'Body']
        if 'StartDate' not in df_eligible_for_embedding.columns:  # Add for sorting if not present
            df_eligible_for_embedding['StartDate'] = pd.NaT

        for col in required_cols:
            if col not in df_eligible_for_embedding.columns:
                self.console.print(
                    f"[bold red]Error:[/bold red] Required column '{col}' not found in DataFrame for embedding targets.")
                return False

        # Prepare Title and Body for deduplication key
        df_eligible_for_embedding['Title_dedup_key'] = df_eligible_for_embedding['Title'].fillna('').astype(
            str).str.strip()
        df_eligible_for_embedding['Body_dedup_key'] = df_eligible_for_embedding['Body'].fillna('').astype(
            str).str.strip()

        # Sort to pick a consistent representative (e.g., earliest by StartDate, then by AdArchiveID)
        df_eligible_for_embedding['StartDate_dt'] = pd.to_datetime(df_eligible_for_embedding['StartDate'],
                                                                   errors='coerce')
        df_eligible_for_embedding.sort_values(
            by=['Title_dedup_key', 'Body_dedup_key', 'StartDate_dt', id_field],
            ascending=[True, True, True, True],
            na_position='last',
            inplace=True
        )

        # Identify unique creatives and their representatives
        df_unique_creatives = df_eligible_for_embedding.drop_duplicates(
            subset=['Title_dedup_key', 'Body_dedup_key'],
            keep='first'
        ).copy()

        num_original_eligible = len(df_eligible_for_embedding)
        num_unique_creatives = len(df_unique_creatives)
        logger.info(
            f"Identified {num_unique_creatives} unique creatives (Title/Body) from {num_original_eligible} eligible remainder ads.")
        self.console.print(
            f"[cyan]Identified {num_unique_creatives} unique creatives from {num_original_eligible} remainder ads for embedding.[/]")

        if df_unique_creatives.empty:
            self.console.print("[cyan]No unique creatives to embed for clustering.[/]")
            self.embeddings_array_full = np.array([])
            self.ad_ids_full = []
            self.ad_id_to_full_idx_map = {}
            self.representative_to_all_originals_map = {}
            return True

        # Populate self.representative_to_all_originals_map
        self.representative_to_all_originals_map = {}
        for _, group in df_eligible_for_embedding.groupby(['Title_dedup_key', 'Body_dedup_key']):
            representative_id = group.iloc[0][
                id_field]  # Due to sort & drop_duplicates, this is the chosen representative
            all_ids_for_creative = group[id_field].tolist()
            self.representative_to_all_originals_map[representative_id] = all_ids_for_creative

        texts_to_embed = df_unique_creatives[self.combined_text_col].fillna('').astype(str).tolist()
        self.ad_ids_full = df_unique_creatives[id_field].tolist()  # These are the representative AdArchiveIDs

        logger.info(f"Generating embeddings for {len(self.ad_ids_full)} unique creatives...")
        self.console.print(
            Panel(f"[bold cyan]Generating Embeddings for {len(self.ad_ids_full)} Unique Remainder Creatives...[/]",
                  border_style="cyan"))

        batch_size = 64
        all_embeddings_list = []
        loop = asyncio.get_running_loop()

        if self.embedding_model is None:
            logger.error("Embedding model not loaded. Cannot generate embeddings.")
            self.console.print("[bold red]Error: Embedding model is not loaded.[/bold red]")
            if not self.rules_only_mode:  # Should always be false if we reach here
                logger.info("Attempting to load embedding model now...")
                self.embedding_model = self._get_embedding_model()
                if self.embedding_model is None: return False
            else:
                return False

        with tqdm(total=len(texts_to_embed), desc="Generating Embeddings (Unique Creatives)", unit="creative",
                  ncols=100) as pbar:
            for i in range(0, len(texts_to_embed), batch_size):
                batch_texts = texts_to_embed[i:i + batch_size]
                try:
                    batch_embeddings = await loop.run_in_executor(None, lambda: self.embedding_model.encode(batch_texts,
                                                                                                            convert_to_numpy=True,
                                                                                                            show_progress_bar=False))
                    all_embeddings_list.extend(batch_embeddings)
                except Exception as e:
                    logger.error(f"Error encoding batch {i // batch_size} of unique creatives: {e}", exc_info=True)
                pbar.update(len(batch_texts))

        if not all_embeddings_list:
            self.embeddings_array_full = np.array([])
            # self.ad_ids_full is already set or empty
            self.ad_id_to_full_idx_map = {}
            # self.representative_to_all_originals_map already set or empty
            logger.warning("No embeddings were generated for unique creatives.")
            self.console.print("[yellow]Warning: No embeddings generated for unique creatives.[/]")
            return True  # Technically successful if there was nothing to embed.

        self.embeddings_array_full = np.array(all_embeddings_list).astype(np.float32)
        if len(self.ad_ids_full) != len(self.embeddings_array_full):
            logger.error(
                f"Mismatch: Representative Ad IDs ({len(self.ad_ids_full)}) vs Embeddings ({len(self.embeddings_array_full)}). This should not happen.")
            return False

        self.ad_id_to_full_idx_map = {ad_id: i for i, ad_id in enumerate(self.ad_ids_full)}
        logger.info(
            f"Generated {len(self.embeddings_array_full)} embeddings for {len(self.ad_ids_full)} unique creatives.")
        self.console.print(f"[green]✓ Generated {len(self.embeddings_array_full)} embeddings for unique creatives.[/]")

        # Clean up temporary columns from all_data_df if they were added widely (they weren't, only to a copy)
        # df_eligible_for_embedding.drop(columns=['Title_dedup_key', 'Body_dedup_key', 'StartDate_dt'], inplace=True, errors='ignore')
        return True

    def run_faiss_kmeans(self, num_clusters: int, niter: int = 20, verbose: bool = False, random_seed: int = 42,
                         embeddings_input: Optional[np.ndarray] = None) -> Tuple[
        Optional[np.ndarray], Optional[np.ndarray]]:
        """Runs FAISS K-Means on the stored or provided embeddings."""
        current_embeddings = embeddings_input if embeddings_input is not None else self.embeddings_array_full

        if current_embeddings is None or current_embeddings.ndim != 2 or current_embeddings.shape[0] == 0:
            logger.error("Invalid or missing embeddings for FAISS K-Means (or embeddings_array_full is empty/None).")
            return None, None

        num_samples, dimension = current_embeddings.shape
        if num_samples < num_clusters:
            logger.error(
                f"Number of samples ({num_samples}) is less than number of clusters ({num_clusters}). Cannot apply K-Means.")
            return None, None

        try:
            import faiss
            logger.debug(
                f"Running FAISS K-Means: k={num_clusters}, niter={niter}, samples={num_samples}, dim={dimension}")
            self.console.print(f"[cyan]Running FAISS K-Means (k={num_clusters})...[/]")

            if current_embeddings.dtype != np.float32:
                embeddings_float32 = current_embeddings.astype(np.float32)
            else:
                embeddings_float32 = current_embeddings

            kmeans = faiss.Kmeans(d=dimension, k=num_clusters, niter=niter, verbose=verbose, seed=random_seed,
                                  gpu=False)  # Ensure GPU is explicitly False or handled by config
            kmeans.train(embeddings_float32)
            centroids = kmeans.centroids

            _distances, labels_matrix = kmeans.index.search(embeddings_float32, 1)
            labels = labels_matrix.flatten()

            logger.debug(
                f"FAISS K-Means completed for k={num_clusters}. Found {len(np.unique(labels))} unique cluster labels.")
            self.console.print(f"[green]✓ FAISS K-Means complete (k={num_clusters}).[/]")
            return centroids, labels

        except ImportError:
            logger.error("FAISS library not installed for K-Means. `pip install faiss-cpu` or `faiss-gpu`")
            self.console.print("[bold red]Error:[/bold red] FAISS library not installed for K-Means.")
            return None, None
        except Exception as e:
            logger.error(f"Error during FAISS K-Means (k={num_clusters}): {e}", exc_info=True)
            self.console.print(f"[bold red]Error:[/bold red] Failed during FAISS K-Means (k={num_clusters}): {e}")
            return None, None

    def _calculate_silhouette_score(self, embeddings: np.ndarray, labels: np.ndarray) -> Optional[float]:
        """Calculates silhouette score for given embeddings and labels."""
        # Ensure embeddings and labels are valid and compatible
        if embeddings is None or labels is None or len(embeddings) != len(labels):
            logger.warning("Invalid input for silhouette score calculation (None or length mismatch).")
            return None

        unique_labels = np.unique(labels)
        num_labels = len(unique_labels)

        if num_labels < 2 or num_labels >= len(embeddings):
            # Silhouette score is not defined if number of labels is 1 or >= n_samples
            logger.warning(f"Silhouette score undefined for {num_labels} labels and {len(embeddings)} samples.")
            return None

        try:
            from sklearn.metrics import silhouette_score

            # Limit sample size for performance if dataset is very large
            sample_size = 50000
            if len(embeddings) > sample_size:
                logger.debug(f"Sampling {sample_size} points for Silhouette calculation.")
                indices = np.random.choice(len(embeddings), sample_size, replace=False)
                embeddings_sample, labels_sample = embeddings[indices], labels[indices]
            else:
                embeddings_sample, labels_sample = embeddings, labels

            # Ensure the sample still has enough clusters
            if len(np.unique(labels_sample)) < 2:
                logger.warning("Sample for silhouette score has fewer than 2 unique labels.")
                return None

            # Use cosine distance since embeddings represent semantic similarity
            score = silhouette_score(embeddings_sample, labels_sample, metric='cosine')
            return score
        except ImportError:
            logger.error("scikit-learn not installed. Cannot calculate silhouette score. `pip install scikit-learn`")
            self.console.print(
                "[bold red]Error:[/bold red] scikit-learn not installed. Cannot calculate silhouette score.")
            return None
        except ValueError as ve:
            # Handle specific sklearn errors if needed
            logger.warning(f"Could not calculate silhouette score: {ve}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error calculating silhouette score: {e}", exc_info=True)
            return None

    def find_optimal_k_silhouette(self, embeddings: np.ndarray, min_k: int = 10, max_k: int = 100, k_step: int = 5,
                                  kmeans_niter: int = 10, kmeans_verbose: bool = False, kmeans_seed: int = 42) -> \
            Optional[int]:
        """
        Estimates optimal k for a given set of embeddings using Silhouette Analysis.
        """
        if embeddings is None or embeddings.size == 0:  # Check if embeddings array is empty
            logger.error("Embeddings not provided or empty. Cannot estimate optimal k.")
            return None

        num_samples = embeddings.shape[0]
        if num_samples <= 1:  # K-Means requires at least 1 sample, Silhouette requires at least 2 unique labels.
            logger.warning(
                f"Not enough samples ({num_samples}) to perform Silhouette analysis. Need at least 2 for distinct clusters.")
            return None

        self.console.print(
            Panel(
                f"[bold cyan]Estimating Optimal k (Silhouette) for {num_samples} ads (Range: {min_k}-{max_k}, Step: {k_step})...[/]",
                border_style="cyan"))

        best_k = None
        highest_silhouette_score = -1.1

        # Adjust max_k if it's greater than number of samples - 1 (for silhouette)
        # K-Means k must be <= num_samples. Silhouette needs at least 2 clusters.
        # So k can go up to num_samples-1 if num_samples > 1.
        # If num_samples is very small, this loop might not run.
        safe_max_k = min(max_k, num_samples - 1 if num_samples > 1 else 1)
        if min_k >= safe_max_k and num_samples > 1:  # If min_k is too high for the sample size
            logger.warning(
                f"Min_k ({min_k}) is too high for {num_samples} samples. Adjusting min_k to {max(1, safe_max_k // 2)} or skipping.")
            # min_k = max(1, safe_max_k // 2 if safe_max_k > 1 else 1) # Try to adjust, or skip if still problematic
            if min_k > safe_max_k:  # If min_k still too high
                logger.warning("Skipping Silhouette: not enough samples for the k range.")
                return 1 if num_samples > 0 else None  # Default to 1 cluster if cannot determine

        k_values = list(range(min_k, safe_max_k + 1, k_step))

        if not k_values:
            logger.warning("No valid k values to test in the given range for Silhouette.")
            return 1 if num_samples > 0 else None  # Default to 1 cluster if no k values to test

        for k_candidate in tqdm(k_values, desc="Testing k (Silhouette)", unit="k", ncols=100):
            if k_candidate <= 0: continue  # k must be > 0
            if k_candidate > num_samples:  # Should be caught by safe_max_k but double check
                logger.warning(f"Skipping k={k_candidate}, as it's > number of samples ({num_samples})")
                continue

            _, labels = self.run_faiss_kmeans(num_clusters=k_candidate,
                                              niter=kmeans_niter,
                                              verbose=kmeans_verbose,
                                              random_seed=kmeans_seed,
                                              embeddings_input=embeddings)  # Pass current embeddings
            if labels is not None:
                # Silhouette score requires at least 2 distinct labels.
                if len(np.unique(labels)) < 2:
                    logger.info(f"k = {k_candidate}, Only 1 cluster found. Silhouette undefined.")
                    silhouette_avg = -1  # Assign a low score
                else:
                    silhouette_avg = self._calculate_silhouette_score(embeddings, labels)

                if silhouette_avg is not None:
                    if silhouette_avg > highest_silhouette_score:
                        highest_silhouette_score = silhouette_avg
                        best_k = k_candidate
            else:  # K-Means failed
                pass  # Logged in run_faiss_kmeans

        if best_k is not None:
            self.console.print(
                f"[green]✓ Optimal k for this subset: {best_k} (Silhouette Score: {highest_silhouette_score:.4f})[/]")
        else:
            self.console.print(
                "[yellow]Could not determine optimal k for this subset. Defaulting to a small k or 1.[/]")
            best_k = min(5, num_samples) if num_samples > 0 else 1  # Fallback for small sets
            if best_k == 0 and num_samples > 0: best_k = 1

        return best_k if best_k is not None and best_k > 0 else (1 if num_samples > 0 else None)

    def run_sklearn_dbscan(self, embeddings_input: np.ndarray, eps: float, min_samples: int) -> Optional[np.ndarray]:
        """
        Runs scikit-learn DBSCAN on the provided embeddings.
        Args:
            embeddings_input (np.ndarray): The embeddings to cluster.
            eps (float): The maximum distance between two samples for one to be considered as in the neighborhood of the other.
            min_samples (int): The number of samples in a neighborhood for a point to be considered as a core point.
        Returns:
            Optional[np.ndarray]: Cluster labels for each point. Noise points are labeled -1. Returns None on failure.
        """
        if embeddings_input is None or embeddings_input.ndim != 2 or embeddings_input.shape[0] == 0:
            logger.error("Invalid or empty embeddings for DBSCAN.")
            return None

        num_samples, dimension = embeddings_input.shape
        if num_samples < min_samples:
            logger.warning(
                f"Number of samples ({num_samples}) is less than min_samples ({min_samples}) for DBSCAN. "
                f"All points might be classified as noise or result in few/no clusters."
            )
            # DBSCAN can still run, but results might not be meaningful.

        try:
            from sklearn.cluster import DBSCAN
            logger.info(
                f"Running scikit-learn DBSCAN: eps={eps}, min_samples={min_samples}, samples={num_samples}, dim={dimension}")
            self.console.print(f"[cyan]Running DBSCAN (eps={eps}, min_samples={min_samples})...[/]")

            if embeddings_input.dtype != np.float32:
                embeddings_float32 = embeddings_input.astype(np.float32)
            else:
                embeddings_float32 = embeddings_input

            # Using 'cosine' distance as embeddings are high-dimensional and semantic
            dbscan = DBSCAN(eps=eps, min_samples=min_samples, metric='cosine', n_jobs=-1)
            labels = dbscan.fit_predict(embeddings_float32)

            num_clusters_found = len(np.unique(labels)) - (
                1 if -1 in labels else 0)  # Count actual clusters, excluding noise
            num_noise_points = np.sum(labels == -1)

            logger.info(
                f"DBSCAN completed. Found {num_clusters_found} clusters and {num_noise_points} noise points.")
            self.console.print(
                f"[green]✓ DBSCAN complete. Found {num_clusters_found} clusters, {num_noise_points} noise points.[/]")
            return labels

        except ImportError:
            logger.error("scikit-learn not installed for DBSCAN. `pip install scikit-learn`")
            self.console.print("[bold red]Error:[/bold red] scikit-learn not installed for DBSCAN.")
            return None
        except Exception as e:
            logger.error(f"Error during DBSCAN: {e}", exc_info=True)
            self.console.print(f"[bold red]Error:[/bold red] Failed during DBSCAN: {e}")
            return None

    def run_faiss_kmeans(self, num_clusters: int, niter: int = 20, verbose: bool = False, random_seed: int = 42,
                         embeddings_input: Optional[np.ndarray] = None) -> Tuple[
        Optional[np.ndarray], Optional[np.ndarray]]:
        """Runs FAISS K-Means on the stored or provided embeddings."""
        current_embeddings = embeddings_input if embeddings_input is not None else self.embeddings_array

        if current_embeddings is None or current_embeddings.ndim != 2:
            logger.error("Invalid or missing embeddings for FAISS K-Means.")
            return None, None

        num_samples, dimension = current_embeddings.shape
        if num_samples < num_clusters:
            logger.error(
                f"Number of samples ({num_samples}) is less than number of clusters ({num_clusters}). Cannot apply K-Means.")
            return None, None

        try:
            import faiss
            logger.debug(
                f"Running FAISS K-Means: k={num_clusters}, niter={niter}, samples={num_samples}, dim={dimension}")
            self.console.print(f"[cyan]Running FAISS K-Means (k={num_clusters})...[/]")

            if current_embeddings.dtype != np.float32:
                embeddings_float32 = current_embeddings.astype(np.float32)
            else:
                embeddings_float32 = current_embeddings

            kmeans = faiss.Kmeans(d=dimension, k=num_clusters, niter=niter, verbose=verbose, seed=random_seed,
                                  gpu=False)
            kmeans.train(embeddings_float32)
            centroids = kmeans.centroids

            _distances, labels_matrix = kmeans.index.search(embeddings_float32, 1)
            labels = labels_matrix.flatten()

            logger.debug(
                f"FAISS K-Means completed for k={num_clusters}. Found {len(np.unique(labels))} unique cluster labels.")
            self.console.print(f"[green]✓ FAISS K-Means complete (k={num_clusters}).[/]")
            return centroids, labels

        except ImportError:
            logger.error("FAISS library not installed for K-Means. `pip install faiss-cpu`")
            self.console.print("[bold red]Error:[/bold red] FAISS library not installed for K-Means.")
            return None, None
        except Exception as e:
            logger.error(f"Error during FAISS K-Means (k={num_clusters}): {e}", exc_info=True)
            self.console.print(f"[bold red]Error:[/bold red] Failed during FAISS K-Means (k={num_clusters}): {e}")
            return None, None

    def name_clusters(self,
                      cluster_labels: np.ndarray,
                      ad_ids_for_these_labels: List[str],
                      llm_category_field: str = 'LitigationName',
                      use_simplified_names: bool = True,
                      dominance_threshold: float = 0.3,
                      min_members_for_llm_name: int = 5,
                      top_n_keywords: int = 3,
                      clustering_method_name: str = "KMeans") -> Dict[
        int, Dict[str, Any]]:  # Added clustering_method_name
        """
        Assigns a name and gathers details for each cluster.
        Handles DBSCAN noise label (-1) by assigning a specific name.
        Args:
            cluster_labels (np.ndarray): Array of cluster labels. Can include -1 for DBSCAN noise.
            ad_ids_for_these_labels (List[str]): List of AdArchiveIDs corresponding to cluster_labels.
            # ... other args
            clustering_method_name (str): "KMeans" or "DBSCAN", used for default prefix.
        """
        unique_labels = sorted(np.unique(cluster_labels))

        # Determine prefix based on the method used for *this specific clustering run*
        default_cluster_prefix = "DBSCAN_Cluster" if clustering_method_name.lower() == "dbscan" else "Cluster"

        default_result = {
            label: {'name': f"{default_cluster_prefix} {label}", 'dominant_llm_cat': 'N/A',
                    'dominant_simplified_cat': 'N/A',
                    'llm_cat_count': 0, 'member_count': 0, 'naming_method': 'Default'} for label in unique_labels}

        if self.all_data_df is None or self.all_data_df.empty:
            logger.warning("Original data (self.all_data_df) not available for naming clusters.")
            return default_result

        if not ad_ids_for_these_labels or cluster_labels is None or len(ad_ids_for_these_labels) != len(cluster_labels):
            logger.error("Mismatch or missing ad_ids_for_these_labels/cluster_labels. Cannot reliably name clusters.")
            return default_result

        self.console.print(Panel(f"[bold cyan]Naming {clustering_method_name} Clusters...[/]", border_style="cyan"))

        temp_df = pd.DataFrame({
            'AdArchiveID': ad_ids_for_these_labels,
            'VectorCluster': cluster_labels
        })

        merge_cols = ['AdArchiveID']
        if llm_category_field in self.all_data_df.columns:
            merge_cols.append(llm_category_field)
        else:
            logger.warning(f"LLM field '{llm_category_field}' missing for naming.")

        if self.combined_text_col in self.all_data_df.columns:
            merge_cols.append(self.combined_text_col)
        else:
            logger.warning(f"Combined text field '{self.combined_text_col}' missing. Keyword naming will fail.")

        if 'AdArchiveID' not in self.all_data_df.columns:
            logger.error("AdArchiveID missing from self.all_data_df for merge in name_clusters.")
            return default_result

        # Ensure AdArchiveID is string for merge compatibility
        # Make a copy for safe modification if dtypes are different
        all_data_df_for_merge = self.all_data_df.copy()
        all_data_df_for_merge['AdArchiveID'] = all_data_df_for_merge['AdArchiveID'].astype(str)
        temp_df['AdArchiveID'] = temp_df['AdArchiveID'].astype(str)

        cols_to_merge = list(dict.fromkeys([col for col in merge_cols if col in all_data_df_for_merge.columns]))
        merged_df = pd.merge(temp_df, all_data_df_for_merge[cols_to_merge], on='AdArchiveID', how='left')

        cluster_details = {}
        for label in tqdm(unique_labels, desc=f"Naming {clustering_method_name} Clusters", unit="cluster", ncols=100):
            ads_in_cluster_df = merged_df[merged_df['VectorCluster'] == label]
            member_count = len(ads_in_cluster_df)
            # Use clustering_method_name to check if it's DBSCAN for noise handling
            is_dbscan_noise = (clustering_method_name.lower() == "dbscan" and label == -1)

            cluster_name_suffix = f"(VC {label})" if not is_dbscan_noise else "(Noise)"
            cluster_name = f"{default_cluster_prefix} {label} {cluster_name_suffix}"
            dominant_llm_cat_original = "N/A"
            dominant_simplified_cat = "N/A"
            llm_cat_count = 0
            naming_method = 'Default'

            if is_dbscan_noise:
                cluster_name = "DBSCAN Noise Points"  # Generic, as it could be from various DBSCAN runs
                naming_method = "DBSCAN Noise"
            elif member_count >= min_members_for_llm_name and llm_category_field in ads_in_cluster_df.columns:
                llm_categories_raw = ads_in_cluster_df[llm_category_field].fillna("Unknown").astype(str).tolist()
                simplified_categories = [simplify_name(cat) for cat in llm_categories_raw]
                valid_simplified = [cat for cat in simplified_categories if cat != "Unknown"]
                if valid_simplified:
                    simplified_counts = Counter(valid_simplified)
                    if simplified_counts:
                        most_common_simplified, most_common_count = simplified_counts.most_common(1)[0]
                        llm_cat_count = most_common_count
                        dominant_simplified_cat = most_common_simplified
                        original_names_for_dominant_simplified = [orig for orig, simp in
                                                                  zip(llm_categories_raw, simplified_categories) if
                                                                  simp == most_common_simplified]
                        if original_names_for_dominant_simplified:
                            dominant_llm_cat_original = \
                                Counter(original_names_for_dominant_simplified).most_common(1)[0][0]
                        if most_common_count >= member_count * dominance_threshold:
                            cluster_name = f"{dominant_llm_cat_original} {cluster_name_suffix}"
                            naming_method = 'LLM Dominance'

            if naming_method == 'Default' and not is_dbscan_noise:
                if self.combined_text_col in ads_in_cluster_df.columns:
                    all_text_in_cluster = " ".join(
                        ads_in_cluster_df[self.combined_text_col].fillna('').astype(str).tolist())
                    if all_text_in_cluster.strip():
                        words = re.findall(r'\b\w{4,}\b', all_text_in_cluster.lower())
                        stopwords = set(
                            ["this", "that", "with", "your", "from", "their", "text", "summary", "title", "body",
                             "page", "name", "image", "com", "www", "about", "claim", "legal", "attorney", "lawyer",
                             "contact", "call", "click", "learn", "more", "free", "info", "link", "visit"])
                        filtered_words = [word for word in words if word not in stopwords]
                        if filtered_words:
                            word_counts = Counter(filtered_words)
                            top_keywords = [kw for kw, count in word_counts.most_common(top_n_keywords)]
                            if top_keywords:
                                cluster_name = f"Keywords: {', '.join(top_keywords)} {cluster_name_suffix}"
                                naming_method = 'Keywords'
                else:
                    logger.debug(
                        f"Combined text column '{self.combined_text_col}' missing for Cluster {label} in merged_df, cannot use keyword naming.")

            cluster_details[label] = {
                'name': cluster_name,
                'dominant_llm_cat': dominant_llm_cat_original,
                'dominant_simplified_cat': dominant_simplified_cat,
                'llm_cat_count': llm_cat_count,
                'member_count': member_count,
                'naming_method': naming_method
            }
        logger.info(f"{clustering_method_name} cluster naming attempt complete.")
        self.console.print(f"[green]✓ {clustering_method_name} cluster naming complete.[/]")
        return cluster_details

    async def _run_dbscan_on_subset(self,
                                    subset_mask: pd.Series,
                                    subset_name_for_logging: str,
                                    id_field: str = 'AdArchiveID'
                                    ) -> Optional[
        Tuple[np.ndarray, List[str], Dict[str, List[str]], Dict[int, Dict[str, Any]]]]:
        """
        Runs DBSCAN clustering on a specified subset of self.all_data_df.
        Identifies unique creatives within the subset, generates embeddings, runs DBSCAN, and names clusters.
        Returns:
            - DBSCAN labels for unique creatives.
            - List of representative AdArchiveIDs for these unique creatives.
            - Mapping from representative AdArchiveID to all original AdArchiveIDs it represents within this subset.
            - Named cluster details.
            Returns None if the process fails or the subset is empty/unsuitable.
        """
        if self.all_data_df is None or self.all_data_df.empty:
            logger.error(f"Main DataFrame not loaded. Cannot run DBSCAN for subset '{subset_name_for_logging}'.")
            return None

        df_subset = self.all_data_df[subset_mask].copy()

        if df_subset.empty:
            self.console.print(f"[yellow]Subset '{subset_name_for_logging}' is empty. Skipping DBSCAN.[/]")
            return None

        logger.info(f"Processing DBSCAN for subset '{subset_name_for_logging}' with {len(df_subset)} ads.")
        self.console.print(
            Panel(f"[bold cyan]Running DBSCAN for Subset: {subset_name_for_logging} ({len(df_subset)} ads)[/]",
                  border_style="cyan"))

        # Ensure combined_text_col is present and ready (it should be from _prepare_combined_text_for_embedding)
        if self.combined_text_col not in df_subset.columns or df_subset[self.combined_text_col].isnull().all():
            logger.error(
                f"Column '{self.combined_text_col}' is missing or all null for subset '{subset_name_for_logging}'. Cannot embed.")
            # Attempt to prepare it just for this subset if it seems uninitialized
            # This is a fallback; ideally _prepare_combined_text_for_embedding handles all relevant ads.
            needs_text_prep_mask_subset = df_subset[self.combined_text_col].isnull() & (
                    df_subset['PreClusterCategory'] != 'General')
            if needs_text_prep_mask_subset.any():
                logger.warning(
                    f"Attempting to prepare '{self.combined_text_col}' for {needs_text_prep_mask_subset.sum()} ads in '{subset_name_for_logging}' subset.")

                # Simplified text prep for subset - assumes Title/Body
                def combine_and_clean_for_embedding_subset(row):
                    combined = " | ".join(
                        str(row.get(field, '')).lower()
                        for field in ['Title', 'Body'] if str(row.get(field, '')).strip()
                    )
                    cleaned = combined
                    if self.embedding_stop_terms_pattern and combined:
                        cleaned = self.embedding_stop_terms_pattern.sub('', combined)
                        cleaned = re.sub(r'\s+', ' ', cleaned).strip()
                        cleaned = re.sub(r'(\s*\|\s*){2,}', ' | ', cleaned).strip(' | ')
                    return cleaned

                df_subset.loc[needs_text_prep_mask_subset, self.combined_text_col] = \
                    df_subset[needs_text_prep_mask_subset].apply(combine_and_clean_for_embedding_subset, axis=1)

            if df_subset[self.combined_text_col].isnull().all():  # Check again
                self.console.print(
                    f"[bold red]Error: '{self.combined_text_col}' still missing/all null for '{subset_name_for_logging}'.[/]")
                return None

        # --- Identify Unique Creatives (Title/Body) within the subset ---
        required_cols_for_dedup = [id_field, 'Title', 'Body']
        if 'StartDate' not in df_subset.columns: df_subset['StartDate'] = pd.NaT

        for col in required_cols_for_dedup:
            if col not in df_subset.columns:
                self.console.print(
                    f"[bold red]Error:[/bold red] Subset '{subset_name_for_logging}': Required column '{col}' not found for unique creative identification.")
                return None

        df_subset['Title_dedup_key'] = df_subset['Title'].fillna('').astype(str).str.strip()
        df_subset['Body_dedup_key'] = df_subset['Body'].fillna('').astype(str).str.strip()
        df_subset['StartDate_dt'] = pd.to_datetime(df_subset['StartDate'], errors='coerce')

        df_subset.sort_values(
            by=['Title_dedup_key', 'Body_dedup_key', 'StartDate_dt', id_field],
            ascending=[True, True, True, True], na_position='last', inplace=True
        )
        df_unique_creatives_subset = df_subset.drop_duplicates(
            subset=['Title_dedup_key', 'Body_dedup_key'], keep='first'
        ).copy()

        num_original_subset = len(df_subset)
        num_unique_subset = len(df_unique_creatives_subset)
        logger.info(
            f"Subset '{subset_name_for_logging}': Identified {num_unique_subset} unique creatives from {num_original_subset} ads.")
        self.console.print(
            f"[cyan]Subset '{subset_name_for_logging}': {num_unique_subset} unique creatives for DBSCAN.[/]")

        if df_unique_creatives_subset.empty:
            self.console.print(
                f"[yellow]Subset '{subset_name_for_logging}': No unique creatives to embed for DBSCAN.[/]")
            return None  # Or return empty arrays/dicts to signify no clustering happened

        representative_to_all_originals_map_subset = {}
        for _, group in df_subset.groupby(['Title_dedup_key', 'Body_dedup_key']):
            representative_id = group.iloc[0][id_field]
            all_ids_for_creative = group[id_field].tolist()
            representative_to_all_originals_map_subset[representative_id] = all_ids_for_creative

        texts_to_embed = df_unique_creatives_subset[self.combined_text_col].fillna('').astype(str).tolist()
        representative_ids_subset = df_unique_creatives_subset[id_field].tolist()

        # --- Generate Embeddings for these Unique Creatives ---
        if self.embedding_model is None:
            logger.error(f"Subset '{subset_name_for_logging}': Embedding model not loaded.")
            self.console.print(f"[bold red]Error ({subset_name_for_logging}): Embedding model not loaded.[/]")
            return None

        logger.info(
            f"Subset '{subset_name_for_logging}': Generating embeddings for {len(representative_ids_subset)} unique creatives...")
        batch_size = 64
        all_embeddings_list_subset = []
        loop = asyncio.get_running_loop()

        with tqdm(total=len(texts_to_embed), desc=f"Embedding for {subset_name_for_logging}", unit="creative",
                  ncols=100) as pbar:
            for i in range(0, len(texts_to_embed), batch_size):
                batch_texts = texts_to_embed[i:i + batch_size]
                try:
                    batch_embeddings = await loop.run_in_executor(None, lambda: self.embedding_model.encode(batch_texts,
                                                                                                            convert_to_numpy=True,
                                                                                                            show_progress_bar=False))
                    all_embeddings_list_subset.extend(batch_embeddings)
                except Exception as e:
                    logger.error(f"Subset '{subset_name_for_logging}': Error encoding batch {i // batch_size}: {e}",
                                 exc_info=True)
                pbar.update(len(batch_texts))

        if not all_embeddings_list_subset:
            logger.warning(f"Subset '{subset_name_for_logging}': No embeddings generated.")
            self.console.print(f"[yellow]Warning ({subset_name_for_logging}): No embeddings generated.[/]")
            return None

        embeddings_array_subset = np.array(all_embeddings_list_subset).astype(np.float32)
        if len(representative_ids_subset) != len(embeddings_array_subset):
            logger.error(
                f"Subset '{subset_name_for_logging}': Mismatch representative IDs ({len(representative_ids_subset)}) vs Embeddings ({len(embeddings_array_subset)}).")
            return None

        # --- Run DBSCAN ---
        dbscan_labels_subset = self.run_sklearn_dbscan(
            embeddings_input=embeddings_array_subset,
            eps=self.dbscan_eps,
            min_samples=self.dbscan_min_samples
        )

        if dbscan_labels_subset is None:
            self.console.print(f"[yellow]Subset '{subset_name_for_logging}': DBSCAN clustering failed.[/]")
            return None

        # --- Name Clusters ---
        # Pass "dbscan" as method name for correct prefixing by name_clusters
        cluster_details_subset = self.name_clusters(
            cluster_labels=dbscan_labels_subset,
            ad_ids_for_these_labels=representative_ids_subset,  # Representative IDs
            llm_category_field='LitigationName',  # Standard field for naming context
            clustering_method_name="dbscan"
        )

        df_unique_creatives_subset.drop(columns=['Title_dedup_key', 'Body_dedup_key', 'StartDate_dt'], inplace=True,
                                        errors='ignore')

        logger.info(f"Subset '{subset_name_for_logging}': DBSCAN processing complete.")
        return dbscan_labels_subset, representative_ids_subset, representative_to_all_originals_map_subset, cluster_details_subset

    def find_similar_ads(self, query_embedding: np.ndarray, k: int = 5) -> List[Tuple[str, float]]:
        """Finds k most similar ads to the query_embedding using the built FAISS index."""
        if self.faiss_index is None or query_embedding is None or self.index_to_ad_id_map is None:
            logger.warning("FAISS index not built or query embedding missing. Cannot find similar ads.")
            self.console.print("[yellow]Warning:[/yellow] FAISS index/mapping not available for similarity search.")
            return []
        try:
            import faiss
            query_embedding_normalized = query_embedding.reshape(1, -1).astype(np.float32)
            faiss.normalize_L2(query_embedding_normalized)

            distances, indices = self.faiss_index.search(query_embedding_normalized, k)
            results = []
            for i, idx in enumerate(indices[0]):
                if idx == -1: continue
                ad_id = self.index_to_ad_id_map.get(idx)
                similarity = distances[0][i]
                if ad_id:
                    results.append((ad_id, similarity))
            return results
        except ImportError:
            logger.error("FAISS library not installed for similarity search.")
            self.console.print("[bold red]Error:[/bold red] FAISS library not installed for similarity search.")
            return []
        except Exception as e:
            logger.error(f"Error during similarity search: {e}", exc_info=True)
            self.console.print(f"[bold red]Error:[/bold red] Failed during similarity search: {e}")
            return []

    async def get_embedding_for_text(self, text: str) -> Optional[np.ndarray]:
        """Generates an embedding for a single piece of text."""
        if not text or not isinstance(text, str):
            logger.warning("Invalid text provided for embedding.")
            return None
        try:
            loop = asyncio.get_running_loop()
            embedding = await loop.run_in_executor(
                None,
                lambda: self.embedding_model.encode(text, convert_to_numpy=True, show_progress_bar=False)
            )
            return embedding.astype(np.float32)
        except Exception as e:
            logger.error(f"Error generating embedding for single text: {e}", exc_info=True)
            return None

    async def run_clustering_pipeline(self,
                                      min_k_remainder: int = 100,
                                      max_k_remainder: int = 1000,
                                      k_step_remainder: int = 50,
                                      final_k_remainder: Optional[int] = None,
                                      output_dir: str = "vector_clustering_outputs",
                                      text_truncate_length: int = 100):
        """
        Orchestrates the full clustering pipeline including new DBSCAN stages.
        """
        self.console.print(
            Panel("[bold green]Starting Enhanced Vector Clustering Pipeline[/]", border_style="green"))
        # ... (initial console prints for modes) ...
        if self.use_dbscan_mode:
            self.console.print(
                "[yellow]Note: DBSCAN mode is active for REMAINDER ads (original pipeline path).[/yellow]")
        else:
            self.console.print(
                "[yellow]Note: K-Means mode is active for REMAINDER ads (original pipeline path).[/yellow]")

        base_output_path = Path(output_dir).resolve()
        base_output_path.mkdir(parents=True, exist_ok=True)
        logger.info(f"All output files will be saved to: {base_output_path}")

        pipeline_success = False
        # Initialize new columns in all_data_df
        if self.all_data_df is not None:  # Should be loaded by load_data_from_dynamodb
            self.all_data_df['RuleOnlyDBSCAN_CategoryName'] = pd.NA
            self.all_data_df['CombinedRulesRemainderDBSCAN_CategoryName'] = pd.NA
        else:  # Pre-initialize if all_data_df might not exist yet (though it should)
            # This is more of a safeguard. Actual initialization happens after load.
            # self.all_data_df will be created in load_data_from_dynamodb
            pass

        try:
            # --- 1. Load Data and Perform Initial Rule/General Processing ---
            if not await self.load_data_from_dynamodb():
                self.console.print("[bold red]Pipeline aborted: Failed to load and pre-process data.[/]")
                return

            # Ensure new columns are present after data load
            self.all_data_df['RuleOnlyDBSCAN_CategoryName'] = pd.NA
            self.all_data_df['CombinedRulesRemainderDBSCAN_CategoryName'] = pd.NA

            # --- 2. Generate Embeddings for Unique Creatives of TRUE REMAINDERS (for original pipeline path) ---
            if not self.rules_only_mode:
                if not await self.generate_and_index_embeddings(id_field='AdArchiveID'):
                    self.console.print(
                        "[bold red]Pipeline (original path): Failed to generate embeddings for unique remainder creatives.[/]")
                    # Continue if possible for new paths, but original path might be impacted
                else:
                    self.console.print(
                        "[green]Pipeline (original path): Embeddings for unique remainder creatives generated.[/]")
            else:
                self.console.print(
                    "[yellow]Skipping embedding generation for original remainder path (rules-only mode).[/]")
                self.embeddings_array_full = np.array([])
                self.ad_ids_full = []
                self.ad_id_to_full_idx_map = {}
                self.representative_to_all_originals_map = {}

            # --- 3. Initialize FinalVectorCategory (for original pipeline path) and Assign Based on Pre-processing ---
            self.all_data_df['FinalVectorCategory'] = pd.NA
            general_mask = self.all_data_df['PreClusterCategory'] == 'General'
            self.all_data_df.loc[general_mask, 'FinalVectorCategory'] = "PREFILTERED_GENERAL"
            rule_matched_mask = self.all_data_df['RuleMatchedCampaign'].notna()
            if rule_matched_mask.any():
                self.all_data_df.loc[rule_matched_mask, 'FinalVectorCategory'] = \
                    "RULE_MATCH:" + self.all_data_df.loc[rule_matched_mask, 'RuleMatchedCampaign'].astype(str)

            # --- NEW STAGE A: DBSCAN on Rule-Matched Ads Only ---
            self.console.print(
                Panel("[bold blue]Starting DBSCAN Clustering for Rule-Matched Ads Only[/]", border_style="blue"))
            if not self.rules_only_mode:  # Requires embeddings
                rule_only_dbscan_results = await self._run_dbscan_on_subset(
                    subset_mask=rule_matched_mask,  # use the same mask as above
                    subset_name_for_logging="RuleMatchedOnly"
                )
                if rule_only_dbscan_results:
                    labels, rep_ids, rep_to_orig_map, details = rule_only_dbscan_results

                    # Propagate named clusters to all_data_df
                    for rep_id_val, orig_ids_list in rep_to_orig_map.items():
                        # Find the label for this representative ID
                        try:
                            idx_in_rep_ids = rep_ids.index(rep_id_val)
                            cluster_label_val = labels[idx_in_rep_ids]
                            named_category = details.get(cluster_label_val, {}).get('name',
                                                                                    f"RULE_DBSCAN_Unknown_{cluster_label_val}")

                            mask_originals_to_update_ro = self.all_data_df['AdArchiveID'].isin(orig_ids_list)
                            self.all_data_df.loc[
                                mask_originals_to_update_ro, 'RuleOnlyDBSCAN_CategoryName'] = named_category
                        except ValueError:
                            logger.warning(
                                f"Representative ID {rep_id_val} not found in DBSCAN results for RuleMatchedOnly set.")

                    self.console.print(f"[green]✓ DBSCAN results for Rule-Matched Ads processed and propagated.[/]")
                    # Reporting for Rule-Only DBSCAN (unique creatives)
                    rule_only_report_table = Table(
                        title="DBSCAN Cluster Details for Rule-Matched Ads (Unique Creatives)")
                    rule_only_report_table.add_column("DBSCAN Cluster ID", style="cyan")
                    rule_only_report_table.add_column("Generated Name", style="magenta", max_width=60, overflow="fold")
                    rule_only_report_table.add_column("Size (Unique Creatives)", style="green")
                    rule_only_report_table.add_column("Naming Method", style="dim")
                    for lbl, det in sorted(details.items()):
                        display_lbl = "NOISE (-1)" if lbl == -1 else str(lbl)
                        rule_only_report_table.add_row(display_lbl, det['name'], str(det['member_count']),
                                                       det['naming_method'])
                    self.console.print(rule_only_report_table)

                    # Save Rule-Only DBSCAN results (all original ads with their new category)
                    df_rules_dbscan_output = self.all_data_df[
                        self.all_data_df['RuleOnlyDBSCAN_CategoryName'].notna()].copy()
                    self._save_dataframe_to_csv(
                        df_rules_dbscan_output,
                        columns_to_include=['AdArchiveID', 'Summary', 'Title', 'Body', 'RuleMatchedCampaign',
                                            'RuleOnlyDBSCAN_CategoryName', self.combined_text_col, 'LitigationName'],
                        output_filename=str(base_output_path / "5_rules_only_dbscan_clusters.csv"),
                        text_truncate_length=text_truncate_length
                    )
                else:
                    self.console.print("[yellow]DBSCAN clustering for Rule-Matched Ads was skipped or failed.[/]")
            else:
                self.console.print("[yellow]Skipping DBSCAN on Rule-Matched ads (rules-only mode).[/]")

            # --- NEW STAGE B: DBSCAN on Combined Rule-Matched + True Remainder Ads ---
            self.console.print(
                Panel("[bold blue]Starting DBSCAN Clustering for Combined (Rule-Matched + True Remainder) Ads[/]",
                      border_style="blue"))
            if not self.rules_only_mode:  # Requires embeddings
                # True remainder: Not rule-matched AND PreClusterCategory is 'NeedsProcessing'
                true_remainder_mask = (self.all_data_df['RuleMatchedCampaign'].isna()) & \
                                      (self.all_data_df['PreClusterCategory'] == 'NeedsProcessing')
                combined_mask_for_dbscan = rule_matched_mask | true_remainder_mask

                combined_dbscan_results = await self._run_dbscan_on_subset(
                    subset_mask=combined_mask_for_dbscan,
                    subset_name_for_logging="CombinedRulesRemainder"
                )
                if combined_dbscan_results:
                    labels_c, rep_ids_c, rep_to_orig_map_c, details_c = combined_dbscan_results

                    # Propagate named clusters to all_data_df
                    for rep_id_val_c, orig_ids_list_c in rep_to_orig_map_c.items():
                        try:
                            idx_in_rep_ids_c = rep_ids_c.index(rep_id_val_c)
                            cluster_label_val_c = labels_c[idx_in_rep_ids_c]
                            named_category_c = details_c.get(cluster_label_val_c, {}).get('name',
                                                                                          f"COMBINED_DBSCAN_Unknown_{cluster_label_val_c}")

                            mask_originals_to_update_c = self.all_data_df['AdArchiveID'].isin(orig_ids_list_c)
                            # Be careful not to overwrite if an ad is part of multiple propagations, though masks should be distinct here.
                            self.all_data_df.loc[
                                mask_originals_to_update_c, 'CombinedRulesRemainderDBSCAN_CategoryName'] = named_category_c
                        except ValueError:
                            logger.warning(
                                f"Representative ID {rep_id_val_c} not found in DBSCAN results for CombinedRulesRemainder set.")

                    self.console.print(
                        f"[green]✓ DBSCAN results for Combined (Rules+Remainder) Ads processed and propagated.[/]")
                    # Reporting for Combined DBSCAN (unique creatives)
                    combined_report_table = Table(title="DBSCAN Cluster Details for Combined Ads (Unique Creatives)")
                    combined_report_table.add_column("DBSCAN Cluster ID", style="cyan")
                    combined_report_table.add_column("Generated Name", style="magenta", max_width=60, overflow="fold")
                    combined_report_table.add_column("Size (Unique Creatives)", style="green")
                    combined_report_table.add_column("Naming Method", style="dim")
                    for lbl, det in sorted(details_c.items()):
                        display_lbl = "NOISE (-1)" if lbl == -1 else str(lbl)
                        combined_report_table.add_row(display_lbl, det['name'], str(det['member_count']),
                                                      det['naming_method'])
                    self.console.print(combined_report_table)

                    # Save Combined DBSCAN results
                    df_combined_dbscan_output = self.all_data_df[
                        self.all_data_df['CombinedRulesRemainderDBSCAN_CategoryName'].notna()].copy()
                    self._save_dataframe_to_csv(
                        df_combined_dbscan_output,
                        columns_to_include=['AdArchiveID', 'Summary', 'Title', 'Body', 'RuleMatchedCampaign',
                                            'PreClusterCategory', 'CombinedRulesRemainderDBSCAN_CategoryName',
                                            self.combined_text_col, 'LitigationName'],
                        output_filename=str(base_output_path / "6_combined_rules_remainder_dbscan_clusters.csv"),
                        text_truncate_length=text_truncate_length
                    )
                else:
                    self.console.print(
                        "[yellow]DBSCAN clustering for Combined (Rules+Remainder) Ads was skipped or failed.[/]")
            else:
                self.console.print("[yellow]Skipping DBSCAN on Combined ads (rules-only mode).[/]")

            # --- 4. ORIGINAL REMAINDER Clustering (K-Means or DBSCAN on self.embeddings_array_full) ---
            # This section remains largely the same, operating on `self.embeddings_array_full` (true remainders)
            # and populating `FinalVectorCategory`.
            # ... (existing code for this section from original run_clustering_pipeline) ...
            cluster_details_remainder = {}  # For the original remainder clustering
            remainder_cluster_labels_orig = None
            num_remainder_clusters_orig = 0

            if not self.rules_only_mode:
                if self.embeddings_array_full is not None and self.embeddings_array_full.size > 0:
                    num_unique_creatives_to_cluster_orig = len(self.embeddings_array_full)
                    self.console.print(
                        Panel(
                            f"[bold cyan]Original Pipeline: Clustering {num_unique_creatives_to_cluster_orig} Unique TRUE REMAINDER Creatives...[/]",
                            border_style="cyan"))

                    if num_unique_creatives_to_cluster_orig > 0:
                        category_prefix_orig = ""
                        clustering_method_for_remainder_naming = "kmeans"  # default

                        if self.use_dbscan_mode:  # DBSCAN for original remainder path
                            clustering_method_for_remainder_naming = "dbscan"
                            self.console.print(
                                f"[cyan]Original Pipeline (DBSCAN on true remainders): eps={self.dbscan_eps}, min_samples={self.dbscan_min_samples}...[/]")
                            remainder_cluster_labels_orig = self.run_sklearn_dbscan(
                                embeddings_input=self.embeddings_array_full,
                                eps=self.dbscan_eps,
                                min_samples=self.dbscan_min_samples
                            )
                            if remainder_cluster_labels_orig is not None:
                                unique_dbscan_labels = np.unique(remainder_cluster_labels_orig)
                                num_remainder_clusters_orig = len(unique_dbscan_labels) - (
                                    1 if -1 in unique_dbscan_labels else 0)
                                num_noise_dbscan = np.sum(remainder_cluster_labels_orig == -1)
                                self.console.print(
                                    f"[green]✓ Original Pipeline (DBSCAN on true remainders): {num_remainder_clusters_orig} clusters, {num_noise_dbscan} noise points.[/]")
                                category_prefix_orig = "DBSCAN"
                            else:
                                self.console.print(
                                    "[yellow]Original Pipeline: DBSCAN clustering failed for true remainders.[/]")
                        else:  # K-Means for original remainder path
                            clustering_method_for_remainder_naming = "kmeans"
                            self.console.print(f"[cyan]Original Pipeline (K-Means on true remainders)...[/]")
                            effective_k_remainder_orig = 0
                            # ... (K-Means k selection logic - unchanged from original)
                            if final_k_remainder is None:
                                actual_min_k = max(2,
                                                   min_k_remainder) if num_unique_creatives_to_cluster_orig >= 2 else 1
                                actual_max_k = min(max_k_remainder,
                                                   num_unique_creatives_to_cluster_orig - 1 if num_unique_creatives_to_cluster_orig > 1 else 1)
                                if actual_min_k > actual_max_k or num_unique_creatives_to_cluster_orig < 2:
                                    effective_k_remainder_orig = 1 if num_unique_creatives_to_cluster_orig > 0 else 0
                                else:
                                    effective_k_remainder_orig = self.find_optimal_k_silhouette(
                                        embeddings=self.embeddings_array_full,
                                        min_k=actual_min_k, max_k=actual_max_k, k_step=k_step_remainder
                                    )
                                if effective_k_remainder_orig is None or effective_k_remainder_orig == 0:
                                    effective_k_remainder_orig = min(20,
                                                                     num_unique_creatives_to_cluster_orig) if num_unique_creatives_to_cluster_orig > 0 else 0
                            else:
                                effective_k_remainder_orig = min(final_k_remainder,
                                                                 num_unique_creatives_to_cluster_orig) if num_unique_creatives_to_cluster_orig > 0 else 0

                            num_remainder_clusters_orig = effective_k_remainder_orig
                            if effective_k_remainder_orig > 0 and num_unique_creatives_to_cluster_orig >= effective_k_remainder_orig:
                                _, remainder_cluster_labels_orig = self.run_faiss_kmeans(
                                    num_clusters=effective_k_remainder_orig,
                                    embeddings_input=self.embeddings_array_full
                                )
                                category_prefix_orig = "KMEANS"
                                if remainder_cluster_labels_orig is None:
                                    self.console.print(
                                        "[yellow]Original Pipeline: K-Means clustering failed for true remainders.[/]")
                            else:
                                self.console.print(
                                    f"[cyan]Original Pipeline: No K-Means for true remainders (k={effective_k_remainder_orig}, samples={num_unique_creatives_to_cluster_orig}).[/]")

                        # Propagate original remainder cluster labels to FinalVectorCategory
                        if remainder_cluster_labels_orig is not None and category_prefix_orig and self.representative_to_all_originals_map:
                            # Naming these original remainder clusters
                            cluster_details_remainder = self.name_clusters(
                                cluster_labels=remainder_cluster_labels_orig,
                                ad_ids_for_these_labels=self.ad_ids_full,  # Representative IDs for true remainders
                                clustering_method_name=clustering_method_for_remainder_naming
                            )

                            propagated_count_orig = 0
                            for representative_id, original_ids_list in self.representative_to_all_originals_map.items():
                                # Find the label for this representative ID (from self.ad_ids_full and remainder_cluster_labels_orig)
                                try:
                                    idx_in_full_ids = self.ad_ids_full.index(representative_id)
                                    cluster_label_val_orig = remainder_cluster_labels_orig[idx_in_full_ids]
                                    # Get the named category from cluster_details_remainder
                                    final_cat_name_orig = cluster_details_remainder.get(cluster_label_val_orig, {}).get(
                                        'name', f"{category_prefix_orig.upper()}_Unknown_{cluster_label_val_orig}")

                                    mask_originals_to_update_orig = (
                                            self.all_data_df['AdArchiveID'].isin(original_ids_list) &
                                            self.all_data_df['FinalVectorCategory'].isna()
                                        # Only update if not rule/general
                                    )
                                    self.all_data_df.loc[
                                        mask_originals_to_update_orig, 'FinalVectorCategory'] = final_cat_name_orig
                                    propagated_count_orig += mask_originals_to_update_orig.sum()
                                except ValueError:
                                    logger.warning(
                                        f"Representative ID {representative_id} (from original remainder set) not found in its cluster results.")

                            logger.info(
                                f"Original Pipeline: Propagated remainder cluster labels to {propagated_count_orig} original ad instances in 'FinalVectorCategory'.")
                            self.console.print(
                                f"[green]✓ Original Pipeline: Propagated remainder cluster labels to {propagated_count_orig} original ad instances.[/]")

                        elif remainder_cluster_labels_orig is None and category_prefix_orig:  # Clustering failed
                            ids_that_should_have_been_clustered_orig = []
                            if self.representative_to_all_originals_map:
                                for orig_ids in self.representative_to_all_originals_map.values():
                                    ids_that_should_have_been_clustered_orig.extend(orig_ids)
                            fail_mask_orig = (
                                    self.all_data_df['AdArchiveID'].isin(ids_that_should_have_been_clustered_orig) &
                                    self.all_data_df['FinalVectorCategory'].isna()
                            )
                            fail_cat_name_orig = f"{category_prefix_orig.upper()}:CLUSTER_FAIL_PROPAGATION"
                            self.all_data_df.loc[fail_mask_orig, 'FinalVectorCategory'] = fail_cat_name_orig
                else:
                    self.console.print(
                        "[cyan]Original Pipeline: No embeddings for true remainders. Skipping their clustering.[/]")
            else:  # rules_only_mode is True
                self.console.print("[yellow]Original Pipeline: Skipping remainder clustering (rules-only mode).[/]")

            # Fallback for any ads that are still NA in FinalVectorCategory (original pipeline)
            unassigned_mask = self.all_data_df['FinalVectorCategory'].isna()
            if unassigned_mask.any():
                originally_needs_processing_unassigned_mask = unassigned_mask & (
                        self.all_data_df['PreClusterCategory'] == 'NeedsProcessing')
                self.all_data_df.loc[
                    originally_needs_processing_unassigned_mask, 'FinalVectorCategory'] = "REMAINDER:UNCLUSTERED_OR_PROP_FAIL"
                self.all_data_df['FinalVectorCategory'] = self.all_data_df['FinalVectorCategory'].fillna(
                    "UNASSIGNED_OTHER")

            # --- Save Output Files (existing files 1-4, 3.5, and 0) ---
            # File 1 & 2 (Rules - based on RuleMatchedCampaign and FinalVectorCategory)
            df_rules_categorized_full_source = self.all_data_df[
                self.all_data_df['RuleMatchedCampaign'].notna()
            ].copy()  # This definition is fine
            self._save_dataframe_to_csv(
                df_rules_categorized_full_source,
                columns_to_include=['AdArchiveID', 'Summary', 'Title', 'Body', 'RuleMatchedCampaign',
                                    'FinalVectorCategory',  # Original remainder category or RULE_MATCH:*
                                    'RuleOnlyDBSCAN_CategoryName',  # New category
                                    'CombinedRulesRemainderDBSCAN_CategoryName',  # New category
                                    'LitigationName', self.combined_text_col],
                output_filename=str(base_output_path / "1_rules_categorized_full.csv"),
                text_truncate_length=text_truncate_length
            )
            # ... (deduplication for file 2 - ensure new columns are carried if desired)
            # For brevity, I'll assume file 2's column list is similar to file 1 for now.
            if not df_rules_categorized_full_source.empty:
                df_to_dedup_rules = df_rules_categorized_full_source.copy()
                # ... (standard dedup logic on Title_for_dedup, Body_for_dedup)
                if 'Title' not in df_to_dedup_rules.columns: df_to_dedup_rules['Title'] = ''
                if 'Body' not in df_to_dedup_rules.columns: df_to_dedup_rules['Body'] = ''
                df_to_dedup_rules['Title_for_dedup'] = df_to_dedup_rules['Title'].fillna('').astype(str).str.strip()
                df_to_dedup_rules['Body_for_dedup'] = df_to_dedup_rules['Body'].fillna('').astype(str).str.strip()
                if 'StartDate' not in df_to_dedup_rules.columns:
                    df_to_dedup_rules['StartDate_dt'] = pd.NaT
                else:
                    df_to_dedup_rules['StartDate_dt'] = pd.to_datetime(df_to_dedup_rules['StartDate'], errors='coerce')
                df_to_dedup_rules.sort_values(['Title_for_dedup', 'Body_for_dedup', 'StartDate_dt', 'AdArchiveID'],
                                              ascending=[True, True, True, True], na_position='last', inplace=True)
                df_rules_categorized_deduped = df_to_dedup_rules.drop_duplicates(
                    subset=['Title_for_dedup', 'Body_for_dedup'], keep='first')
                df_rules_categorized_deduped = df_rules_categorized_deduped.drop(
                    columns=['Title_for_dedup', 'Body_for_dedup', 'StartDate_dt'], errors='ignore')
                self._save_dataframe_to_csv(
                    df_rules_categorized_deduped,
                    columns_to_include=['AdArchiveID', 'Summary', 'Title', 'Body', 'RuleMatchedCampaign',
                                        'FinalVectorCategory', 'RuleOnlyDBSCAN_CategoryName',
                                        'CombinedRulesRemainderDBSCAN_CategoryName',
                                        'LitigationName', self.combined_text_col],
                    output_filename=str(base_output_path / "2_rules_categorized_deduped.csv"),
                    text_truncate_length=text_truncate_length
                )

            # File 3 & 4 (Vector - based on FinalVectorCategory from original remainder clustering)
            # This selects ads clustered by the original remainder pipeline (KMeans or DBSCAN)
            clustering_prefix_filter_orig = "DBSCAN:" if self.use_dbscan_mode else "KMEANS:"  # Check FinalVectorCategory
            # Ensure we are checking FinalVectorCategory which holds the original pipeline's vector cluster names
            file3_mask = self.all_data_df['FinalVectorCategory'].astype(str).str.contains(
                f"^{clustering_prefix_filter_orig}", regex=True) & \
                         (~self.all_data_df['FinalVectorCategory'].astype(str).str.contains(
                             "CLUSTER_FAIL|UNCLUSTERED|DBSCAN_Unknown", case=False, regex=True)) & \
                         (~self.all_data_df['FinalVectorCategory'].astype(str).str.endswith(
                             "Noise Points"))  # Exclude DBSCAN noise from this file specifically

            df_vector_categorized_full_source = self.all_data_df[file3_mask].copy()
            self._save_dataframe_to_csv(
                df_vector_categorized_full_source,
                columns_to_include=['AdArchiveID', 'Summary', 'Title', 'Body', 'FinalVectorCategory',
                                    'RuleOnlyDBSCAN_CategoryName',
                                    'CombinedRulesRemainderDBSCAN_CategoryName',
                                    self.combined_text_col, 'LitigationName'],
                output_filename=str(base_output_path / "3_vector_categorized_full.csv"),
                text_truncate_length=text_truncate_length
            )

            if self.use_dbscan_mode:  # DBSCAN Noise points from the original remainder clustering
                # Check FinalVectorCategory for the specific noise name generated by name_clusters
                file3_5_mask = self.all_data_df['FinalVectorCategory'].astype(str).str.contains("DBSCAN Noise Points",
                                                                                                regex=False)
                df_dbscan_noise_orig = self.all_data_df[file3_5_mask].copy()
                self._save_dataframe_to_csv(
                    df_dbscan_noise_orig,
                    columns_to_include=['AdArchiveID', 'Summary', 'Title', 'Body', 'FinalVectorCategory',
                                        'RuleOnlyDBSCAN_CategoryName',
                                        'CombinedRulesRemainderDBSCAN_CategoryName',
                                        self.combined_text_col, 'LitigationName'],
                    output_filename=str(base_output_path / "3_5_dbscan_remainder_noise_points.csv"),
                    text_truncate_length=text_truncate_length
                )

            # ... (deduplication for file 4 from df_vector_categorized_full_source - similar to file 2 dedup)
            if not df_vector_categorized_full_source.empty:
                df_to_dedup_vector = df_vector_categorized_full_source.copy()
                if 'Title' not in df_to_dedup_vector.columns: df_to_dedup_vector['Title'] = ''
                if 'Body' not in df_to_dedup_vector.columns: df_to_dedup_vector['Body'] = ''
                df_to_dedup_vector['Title_for_dedup'] = df_to_dedup_vector['Title'].fillna('').astype(str).str.strip()
                df_to_dedup_vector['Body_for_dedup'] = df_to_dedup_vector['Body'].fillna('').astype(str).str.strip()
                if 'StartDate' not in df_to_dedup_vector.columns:
                    df_to_dedup_vector['StartDate_dt'] = pd.NaT
                else:
                    df_to_dedup_vector['StartDate_dt'] = pd.to_datetime(df_to_dedup_vector['StartDate'],
                                                                        errors='coerce')
                df_to_dedup_vector.sort_values(['Title_for_dedup', 'Body_for_dedup', 'StartDate_dt', 'AdArchiveID'],
                                               ascending=[True, True, True, True], na_position='last', inplace=True)
                df_vector_categorized_deduped = df_to_dedup_vector.drop_duplicates(
                    subset=['Title_for_dedup', 'Body_for_dedup'], keep='first')
                df_vector_categorized_deduped = df_vector_categorized_deduped.drop(
                    columns=['Title_for_dedup', 'Body_for_dedup', 'StartDate_dt'], errors='ignore')
                self._save_dataframe_to_csv(
                    df_vector_categorized_deduped,
                    columns_to_include=['AdArchiveID', 'Summary', 'Title', 'Body', 'FinalVectorCategory',
                                        'RuleOnlyDBSCAN_CategoryName',
                                        'CombinedRulesRemainderDBSCAN_CategoryName',
                                        self.combined_text_col, 'LitigationName'],
                    output_filename=str(base_output_path / "4_vector_categorized_deduped.csv"),
                    text_truncate_length=text_truncate_length
                )

            # --- Reporting (Overall Summary including new categories) ---
            self.console.print(Panel(f"[bold green]Final Overall Categorization Summary[/]", border_style="green"))
            self.console.print(f"Total Ads Processed (in self.all_data_df): {len(self.all_data_df)}")

            # Report for FinalVectorCategory (Original Pipeline)
            final_overall_counts = self.all_data_df['FinalVectorCategory'].value_counts(dropna=False).sort_index()
            overall_summary_table = Table(
                title="Overall 'FinalVectorCategory' Distribution (Original Pipeline Ad Counts)")
            overall_summary_table.add_column("FinalVectorCategory", style="magenta", max_width=70, overflow="fold")
            overall_summary_table.add_column("Count", justify="right", style="green")
            for cat_name, cat_count in final_overall_counts.items():
                overall_summary_table.add_row(str(cat_name), str(cat_count))
            self.console.print(overall_summary_table)

            # Report for RuleOnlyDBSCAN_CategoryName
            rule_only_dbscan_counts = self.all_data_df['RuleOnlyDBSCAN_CategoryName'].value_counts(
                dropna=False).sort_index()
            ro_summary_table = Table(title="Overall 'RuleOnlyDBSCAN_CategoryName' Distribution (Ad Counts)")
            ro_summary_table.add_column("RuleOnlyDBSCAN_CategoryName", style="cyan", max_width=70, overflow="fold")
            ro_summary_table.add_column("Count", justify="right", style="green")
            for cat_name, cat_count in rule_only_dbscan_counts.items():
                ro_summary_table.add_row(str(cat_name), str(cat_count))
            self.console.print(ro_summary_table)

            # Report for CombinedRulesRemainderDBSCAN_CategoryName
            combined_dbscan_counts = self.all_data_df['CombinedRulesRemainderDBSCAN_CategoryName'].value_counts(
                dropna=False).sort_index()
            crr_summary_table = Table(
                title="Overall 'CombinedRulesRemainderDBSCAN_CategoryName' Distribution (Ad Counts)")
            crr_summary_table.add_column("CombinedRulesRemainderDBSCAN_CategoryName", style="yellow", max_width=70,
                                         overflow="fold")
            crr_summary_table.add_column("Count", justify="right", style="green")
            for cat_name, cat_count in combined_dbscan_counts.items():
                crr_summary_table.add_row(str(cat_name), str(cat_count))
            self.console.print(crr_summary_table)

            if cluster_details_remainder:  # From original remainder clustering
                clustering_method_name_disp = "DBSCAN" if self.use_dbscan_mode else "K-Means"
                report_title_rem = f"Original Remainder {clustering_method_name_disp} Cluster Details (Unique True Remainder Creatives)"
                cluster_report_table_rem = Table(title=report_title_rem)
                cluster_report_table_rem.add_column(f"{clustering_method_name_disp} Cluster ID", style="cyan")
                cluster_report_table_rem.add_column("Generated Name", style="magenta", max_width=60, overflow="fold")
                cluster_report_table_rem.add_column("Size (Unique Creatives)", style="green")
                cluster_report_table_rem.add_column("Naming Method", style="dim")
                for label_val, details_val in sorted(cluster_details_remainder.items()):
                    cluster_id_display = str(label_val)
                    if self.use_dbscan_mode and label_val == -1: cluster_id_display = "NOISE (-1)"
                    cluster_report_table_rem.add_row(cluster_id_display, details_val['name'],
                                                     str(details_val['member_count']), details_val['naming_method'])
                self.console.print(
                    "\n[bold]Generated Names for Original Remainder Clusters (Unique True Remainder Creatives):[/]")
                self.console.print(cluster_report_table_rem)

            # Comprehensive output including new columns
            comprehensive_cols = ['AdArchiveID', 'Summary', 'Title', 'Body', self.combined_text_col,
                                  'RuleMatchedCampaign', 'PreClusterCategory',
                                  'FinalVectorCategory',  # Original pipeline category
                                  'RuleOnlyDBSCAN_CategoryName',  # New
                                  'CombinedRulesRemainderDBSCAN_CategoryName',  # New
                                  'LitigationName']
            # ... (add embedding fields to comprehensive_cols as before) ...
            for embed_field in self.embedding_fields:
                if embed_field not in comprehensive_cols:
                    comprehensive_cols.append(embed_field)

            self._save_dataframe_to_csv(
                self.all_data_df,
                columns_to_include=[col for col in comprehensive_cols if col in self.all_data_df.columns],
                output_filename=str(base_output_path / "0_comprehensive_results.csv"),
                text_truncate_length=text_truncate_length
            )
            pipeline_success = True

        except Exception as e:
            logger.critical(f"Unhandled error in pipeline: {e}", exc_info=True)
            self.console.print(f"[bold red]Critical Pipeline Error: {e}[/]")
            # ... (error dump logic as before, ensure new columns are included if relevant) ...
            if self.all_data_df is not None and not self.all_data_df.empty:
                try:
                    error_dump_path = base_output_path / "ERROR_DUMP_all_data_df.csv"
                    cols_to_dump_onerror = ['AdArchiveID', 'LitigationName', 'RuleMatchedCampaign',
                                            'PreClusterCategory', 'FinalVectorCategory',
                                            'RuleOnlyDBSCAN_CategoryName', 'CombinedRulesRemainderDBSCAN_CategoryName',
                                            self.combined_text_col, 'Title', 'Body']
                    # ... (extend with embedding_fields and save)
                    cols_to_dump_onerror.extend(self.embedding_fields)
                    actual_cols_to_dump = [col for col in cols_to_dump_onerror if col in self.all_data_df.columns]
                    if 'AdArchiveID' in actual_cols_to_dump:
                        self.all_data_df[actual_cols_to_dump].to_csv(error_dump_path, index=False)
                        logger.info(f"Saved partial data to {error_dump_path} due to error.")
                        self.console.print(f"[yellow]Saved partial data to {error_dump_path} due to error.[/yellow]")

                except Exception as dump_e:
                    logger.error(f"Failed to save error dump: {dump_e}", exc_info=True)

        finally:
            if pipeline_success:
                self.console.print(
                    Panel("[bold green]Enhanced Vector Clustering Pipeline Finished Successfully[/]",
                          border_style="green"))
            else:
                self.console.print(
                    Panel(
                        "[bold yellow]Enhanced Vector Clustering Pipeline Finished (with errors or incomplete results)[/]",
                        border_style="yellow"))

    async def _save_classification_output(self, df_to_save: pd.DataFrame,
                                          base_output_path: Path,
                                          report_category_col: str):
        """
        Helper method to add snippets, save full CSV, and save deduplicated CSV.
        Args:
            df_to_save: DataFrame containing the data to save.
            base_output_path: Path object for the output file (without .csv extension).
            report_category_col: Name of the column containing the final category for this report.
        """
        if df_to_save.empty:
            logger.info(f"No data to save for report: {base_output_path.name}. Creating empty/header file.")
            # Create empty files or files with headers only
            output_file_full = base_output_path.with_suffix(".csv")
            output_file_dedup = base_output_path.with_name(base_output_path.name + "_dedup").with_suffix(".csv")

            # Define expected columns for empty files (base + snippets + category)
            header_cols = ['AdArchiveID']
            if report_category_col not in header_cols: header_cols.append(report_category_col)
            for field in self.embedding_fields: header_cols.append(f"{field}_snippet")
            # Add other common columns if desired for empty files
            other_common_cols = ['LitigationName', 'RuleMatchedCampaign', 'PreClusterCategory']
            for col in other_common_cols:
                if col not in header_cols: header_cols.append(col)

            pd.DataFrame(columns=header_cols).to_csv(output_file_full, index=False, encoding='utf-8')
            pd.DataFrame(columns=header_cols).to_csv(output_file_dedup, index=False, encoding='utf-8')
            self.console.print(
                f"[yellow]Saved empty/header files for {base_output_path.name} as no data was processed for it.[/]")
            return

        output_df_full = df_to_save.copy()

        # Ensure AdArchiveID is present (should be guaranteed by upstream logic)
        if 'AdArchiveID' not in output_df_full.columns:
            logger.error(f"CRITICAL: AdArchiveID missing in df_to_save for {base_output_path.name}. Cannot save.")
            self.console.print(f"[bold red]Error: AdArchiveID missing for {base_output_path.name}, CSV not saved.[/]")
            return

        snippet_length = 50
        snippet_col_names = []

        # Add common contextual columns if they exist in the source df_to_save
        base_context_cols = ['LitigationName', 'RuleMatchedCampaign', 'PreClusterCategory']
        cols_for_output = ['AdArchiveID']  # AdArchiveID must be first or prominent

        # Add the specific report_category_col
        if report_category_col in output_df_full.columns:
            if report_category_col not in cols_for_output:
                cols_for_output.append(report_category_col)
        else:
            logger.warning(
                f"Report category column '{report_category_col}' not found in DataFrame for {base_output_path.name}.")
            # Add a placeholder to avoid breaking deduplication if it's a key
            output_df_full[report_category_col] = "CATEGORY_COL_MISSING"
            if report_category_col not in cols_for_output:
                cols_for_output.append(report_category_col)

        for col in base_context_cols:
            if col in output_df_full.columns and col not in cols_for_output:
                cols_for_output.append(col)

        # Prepare snippet columns
        for field in self.embedding_fields:  # ['Title', 'Body', 'Summary', 'ImageText']
            snippet_col_name = f"{field}_snippet"
            snippet_col_names.append(snippet_col_name)
            if field in self.all_data_df.columns:  # Get full text from self.all_data_df using AdArchiveID
                # Merge full text to ensure snippets are from original, not potentially modified df_to_save
                merged_snippets = pd.merge(output_df_full[['AdArchiveID']],
                                           self.all_data_df[['AdArchiveID', field]],
                                           on='AdArchiveID', how='left')
                output_df_full[snippet_col_name] = merged_snippets[field].fillna('').astype(str).str.slice(0,
                                                                                                           snippet_length)
            elif field in output_df_full.columns:  # Fallback if field somehow only in df_to_save
                output_df_full[snippet_col_name] = output_df_full[field].fillna('').astype(str).str.slice(0,
                                                                                                          snippet_length)
            else:
                output_df_full[snippet_col_name] = ""  # Placeholder if field not found

            if snippet_col_name not in cols_for_output:
                cols_for_output.append(snippet_col_name)

        # Reorder to ensure key columns are followed by snippets, then others
        final_output_df_full = output_df_full[
            cols_for_output + [col for col in output_df_full.columns if col not in cols_for_output]]

        # Save the full (potentially non-deduplicated by content) CSV
        output_file_full_path = base_output_path.with_suffix(".csv")
        output_file_full_path.parent.mkdir(parents=True, exist_ok=True)
        final_output_df_full.to_csv(output_file_full_path, index=False, encoding='utf-8')
        self.console.print(
            f"\n[green]✓ Full report ({len(final_output_df_full)} rows) saved to:[/green] {output_file_full_path}")

        # Deduplicate based on snippets and the specific report_category_col
        deduplication_key_cols = snippet_col_names + [report_category_col]
        valid_deduplication_key_cols = [col for col in deduplication_key_cols if col in final_output_df_full.columns]

        if not valid_deduplication_key_cols or not any(
                s_col in valid_deduplication_key_cols for s_col in snippet_col_names):
            logger.warning(
                f"Not enough valid key columns (need snippets) for content-based deduplication of {base_output_path.name}. Saving non-deduplicated version as deduped.")
            output_df_deduplicated = final_output_df_full.copy()
        else:
            rows_before_dedup = len(final_output_df_full)
            # Keep the instance with the 'first' AdArchiveID encountered (original df order)
            output_df_deduplicated = final_output_df_full.drop_duplicates(subset=valid_deduplication_key_cols,
                                                                          keep='first')
            rows_after_dedup = len(output_df_deduplicated)
            if rows_before_dedup > rows_after_dedup:
                self.console.print(
                    f"  [yellow]Deduplicated {rows_before_dedup - rows_after_dedup} rows based on content snippets & '{report_category_col}'.[/yellow]")

        output_file_dedup_path = base_output_path.with_name(base_output_path.name + "_dedup").with_suffix(".csv")
        output_df_deduplicated.to_csv(output_file_dedup_path, index=False, encoding='utf-8')
        self.console.print(
            f"  [green]✓ Deduplicated report ({len(output_df_deduplicated)} rows) saved to:[/green] {output_file_dedup_path}")

    def _save_dataframe_to_csv(self, df_to_save: pd.DataFrame,
                               columns_to_include: List[str],
                               output_filename: str,
                               text_truncate_length: int):
        """
        Saves a DataFrame to CSV, selecting specific columns and truncating text fields.
        """
        if df_to_save is None or df_to_save.empty:
            logger.warning(f"DataFrame for '{output_filename}' is empty. Skipping save.")
            self.console.print(f"[yellow]Warning: No data to save for {output_filename}.[/]")
            return False

        # Ensure AdArchiveID is always present if it exists in the df
        if 'AdArchiveID' not in columns_to_include and 'AdArchiveID' in df_to_save.columns:
            columns_to_include.insert(0, 'AdArchiveID')

        final_cols_to_save = [col for col in columns_to_include if col in df_to_save.columns]
        missing_cols = set(columns_to_include) - set(final_cols_to_save)
        if missing_cols:
            logger.warning(f"Columns missing for CSV '{output_filename}': {missing_cols}. They will be excluded.")

        if not final_cols_to_save or 'AdArchiveID' not in final_cols_to_save:
            logger.error(
                f"Cannot save CSV '{output_filename}': Essential columns like 'AdArchiveID' are missing from selection or DataFrame.")
            self.console.print(f"[bold red]Error: Essential columns missing for {output_filename}.[/]")
            return False

        output_df = df_to_save[final_cols_to_save].copy()

        # Truncate text fields (Summary and CombinedEmbeddingText by default, can be expanded)
        # Identify text-like columns to truncate (heuristic: object dtype, not AdArchiveID or cluster labels)
        cols_to_truncate = ['Summary', self.combined_text_col]  # Explicitly list known long text fields
        # Add other text fields from self.embedding_fields if they are in final_cols_to_save
        for embed_field in self.embedding_fields:
            if embed_field in final_cols_to_save and embed_field not in cols_to_truncate:
                cols_to_truncate.append(embed_field)

        for txt_col in cols_to_truncate:
            if txt_col in output_df.columns:
                output_df[txt_col] = output_df[txt_col].astype(str).str.slice(0, text_truncate_length)

        try:
            output_file_path = Path(output_filename).resolve()
            output_file_path.parent.mkdir(parents=True, exist_ok=True)
            output_df.to_csv(output_file_path, index=False, encoding='utf-8')
            logger.info(f"Saved data to {output_file_path} ({len(output_df)} rows)")
            self.console.print(
                f"\n[green]✓ Results ({len(output_df)} rows, truncated) saved to:[/green] {output_file_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to save results to CSV '{output_filename}': {e}", exc_info=True)
            self.console.print(f"[bold red]Error:[/bold red] Failed to save {output_filename}: {e}")
            return False

    async def _create_and_save_umap_plot(self,
                                         df_subset: pd.DataFrame,
                                         text_column: str,
                                         category_column: str,
                                         plot_title: str,
                                         output_filename: str,
                                         max_samples: int,
                                         umap_n_neighbors: int,
                                         umap_min_dist: float,
                                         umap_random_state: int,
                                         max_legend_categories: int = 20):  # New parameter
        """Helper to generate embeddings, UMAP, and plot for a subset of data."""

        if df_subset.empty:
            logger.info(f"DataFrame subset for '{plot_title}' is empty. Skipping plot.")
            self.console.print(f"[yellow]No data for plot: {plot_title}[/yellow]")
            return

        if text_column not in df_subset.columns:
            logger.error(f"Text column '{text_column}' not found for plot '{plot_title}'.")
            self.console.print(f"[bold red]Error: Text column '{text_column}' missing for '{plot_title}'.[/]")
            return

        df_plot_source = df_subset[
            df_subset[text_column].notna() & (df_subset[text_column].str.strip() != '')].copy()  # Use .copy()
        if df_plot_source.empty:
            logger.info(f"DataFrame subset for '{plot_title}' is empty after filtering empty texts. Skipping plot.")
            self.console.print(f"[yellow]No non-empty text data for plot: {plot_title}[/yellow]")
            return

        if category_column not in df_plot_source.columns:
            logger.error(f"Category column '{category_column}' not found for plot '{plot_title}'.")
            self.console.print(f"[bold red]Error: Category column '{category_column}' missing for '{plot_title}'.[/]")
            return

        df_plot_source[category_column] = df_plot_source[category_column].fillna("NA_Category").astype(str)

        if len(df_plot_source) > max_samples:
            logger.info(f"Sampling {max_samples} from {len(df_plot_source)} for plot '{plot_title}'.")
            df_plot = df_plot_source.sample(n=max_samples, random_state=umap_random_state).copy()
        else:
            df_plot = df_plot_source.copy()

        texts_to_embed = df_plot[text_column].tolist()

        if not texts_to_embed:
            logger.info(f"No texts to embed for plot '{plot_title}'.")
            self.console.print(f"[yellow]No text data to embed for plot: {plot_title}[/yellow]")
            return

        logger.info(f"Generating embeddings for {len(texts_to_embed)} samples for '{plot_title}'...")
        loop = asyncio.get_running_loop()
        try:
            embeddings = await loop.run_in_executor(
                None,
                lambda: self.embedding_model.encode(texts_to_embed, convert_to_numpy=True, show_progress_bar=False)
            )
        except Exception as e:
            logger.error(f"Failed to generate embeddings for '{plot_title}': {e}", exc_info=True)
            self.console.print(f"[bold red]Error generating embeddings for '{plot_title}': {e}[/]")
            return

        embeddings = embeddings.astype(np.float32)

        logger.info(f"Running UMAP for '{plot_title}' (n_neighbors={umap_n_neighbors}, min_dist={umap_min_dist})...")
        try:
            reducer = umap.UMAP(
                n_neighbors=umap_n_neighbors,
                min_dist=umap_min_dist,
                n_components=2,
                metric='cosine',
                random_state=umap_random_state
            )
            embedding_2d = reducer.fit_transform(embeddings)
        except Exception as e:
            logger.error(f"UMAP failed for '{plot_title}': {e}", exc_info=True)
            self.console.print(f"[bold red]Error during UMAP for '{plot_title}': {e}[/]")
            return

        # --- Category handling for legend ---
        original_categories_series = df_plot[category_column]
        category_counts = original_categories_series.value_counts()

        plot_category_column_name = category_column  # Will be reassigned if "Other" grouping happens
        legend_title_actual = category_column
        hue_order = None  # For controlling legend order and color assignment

        if len(category_counts) > max_legend_categories:
            self.console.print(
                f"[yellow]Plot '{plot_title}': Too many categories ({len(category_counts)}). Grouping into Top {max_legend_categories - 1} + '_Other_'.[/yellow]")
            top_n_minus_1 = category_counts.nlargest(max_legend_categories - 1).index.tolist()

            # Create a new column for plotting categories
            plot_category_column_name = f"{category_column}_PlotGroup"
            df_plot[plot_category_column_name] = original_categories_series.apply(
                lambda x: x if x in top_n_minus_1 else "_Other_")

            categories_for_plot = df_plot[plot_category_column_name]
            hue_order = top_n_minus_1 + ["_Other_"]  # Ensure consistent order
            legend_title_actual = f"{category_column} (Top {max_legend_categories - 1} + _Other_)"
            num_colors_needed = len(hue_order)
        else:
            categories_for_plot = original_categories_series
            hue_order = sorted(category_counts.index.tolist())
            legend_title_actual = category_column
            num_colors_needed = len(hue_order)

        # Select a palette
        if num_colors_needed <= 10:
            palette = sns.color_palette("Paired", n_colors=num_colors_needed)
        elif num_colors_needed <= 20:
            palette = sns.color_palette("tab20", n_colors=num_colors_needed)
        else:  # Should only happen if max_legend_categories > 20, or not many unique cats
            # This case might occur if max_legend_categories is high. Fallback to a visually distinct set if possible.
            # For very high numbers (even after "Other"), a continuous map might be unavoidable,
            # but we try to provide distinct colors for the "Top N + Other".
            palette = sns.color_palette("husl", n_colors=num_colors_needed)

        plt.figure(figsize=(18, 12))  # Slightly wider for legend

        sns.scatterplot(
            x=embedding_2d[:, 0],
            y=embedding_2d[:, 1],
            hue=categories_for_plot,  # Use the potentially modified categories
            hue_order=hue_order,  # Control legend order
            palette=palette,
            alpha=0.7,
            s=15,  # marker size
            legend="full"  # Request full legend
        )

        plt.title(plot_title, fontsize=16)
        plt.xlabel("UMAP Dimension 1", fontsize=12)
        plt.ylabel("UMAP Dimension 2", fontsize=12)
        plt.gca().set_aspect('equal', 'datalim')

        # Adjust legend position to be outside the plot
        plt.legend(title=legend_title_actual, bbox_to_anchor=(1.02, 1), loc='upper left', borderaxespad=0.)
        plt.tight_layout(rect=[0, 0, 0.85, 1])  # Adjust layout to make space for external legend

        try:
            plt.savefig(output_filename, dpi=150, bbox_inches='tight')
            logger.info(f"Saved plot to {output_filename}")
            self.console.print(f"[green]✓ Plot saved:[/green] {output_filename}")
        except Exception as e:
            logger.error(f"Failed to save plot {output_filename}: {e}", exc_info=True)
            self.console.print(f"[bold red]Error saving plot {output_filename}: {e}[/]")
        finally:
            plt.close()

    async def generate_visual_analysis_charts(self,
                                              input_csv_path: str,
                                              output_dir: str = "visualization_charts",
                                              max_samples_for_plot: int = 5000,
                                              umap_n_neighbors: int = 15,
                                              umap_min_dist: float = 0.1,
                                              umap_random_state: int = 42,
                                              max_legend_categories: int = 15):  # New param, default to 15 for readability
        """
        Generates 2D UMAP visualizations for different clustering/categorization stages.
        Assumes '0_comprehensive_results.csv' (or similar) from run_clustering_pipeline is provided.
        """
        self.console.print(Panel("[bold green]Generating Visual Analysis Charts[/]", border_style="green"))

        if self.embedding_model is None and not self.rules_only_mode:
            logger.error("Embedding model not loaded. Cannot generate embeddings for visualization.")
            self.console.print("[bold red]Error: Embedding model not loaded. Visualization aborted.[/]")
            if not self.rules_only_mode:
                logger.info("Attempting to load embedding model for visualization...")
                self.embedding_model = self._get_embedding_model()
                if self.embedding_model is None:
                    self.console.print("[bold red]Failed to load embedding model. Aborting visualization.[/]")
                    return
            else:
                self.console.print("[bold red]In rules_only_mode but trying to visualize embeddings. Aborting.[/]")
                return

        input_path = Path(input_csv_path)
        if not input_path.exists():
            logger.error(f"Input CSV for visualization not found: {input_path}")
            self.console.print(f"[bold red]Error: Input CSV not found at {input_path}.[/]")
            return

        try:
            df_full = pd.read_csv(input_path)
            logger.info(f"Loaded {len(df_full)} rows from {input_path} for visualization.")
        except Exception as e:
            logger.error(f"Failed to load CSV {input_path}: {e}", exc_info=True)
            self.console.print(f"[bold red]Error: Failed to load CSV {input_path}: {e}[/]")
            return

        viz_output_path = Path(output_dir).resolve()
        viz_output_path.mkdir(parents=True, exist_ok=True)
        logger.info(f"Visualization charts will be saved to: {viz_output_path}")

        # --- Chart 1: DBSCAN clusters of Rule-Matched Ads ---
        self.console.print("\n[cyan]Generating Chart 1: DBSCAN on Rule-Matched Ads (colored by DBSCAN clusters)...[/]")
        chart1_df = df_full[
            df_full['RuleMatchedCampaign'].notna() & df_full['RuleOnlyDBSCAN_CategoryName'].notna()].copy()
        if not chart1_df.empty:
            await self._create_and_save_umap_plot(
                df_subset=chart1_df,
                text_column=self.combined_text_col,
                category_column='RuleOnlyDBSCAN_CategoryName',
                plot_title="UMAP of Rule-Matched Ads (Colored by their DBSCAN Clusters)",
                output_filename=str(viz_output_path / "1_rule_matched_dbscan_clusters.png"),
                max_samples=max_samples_for_plot,
                umap_n_neighbors=umap_n_neighbors,
                umap_min_dist=umap_min_dist,
                umap_random_state=umap_random_state,
                max_legend_categories=max_legend_categories  # Pass new param
            )
        else:
            self.console.print(
                "[yellow]Skipped Chart 1: No data with RuleMatchedCampaign and RuleOnlyDBSCAN_CategoryName.[/]")

        # --- Chart 1b: Rule-Matched Ads (colored by Original RuleMatchedCampaign) ---
        self.console.print("\n[cyan]Generating Chart 1b: Rule-Matched Ads (colored by Original Rule Name)...[/]")
        chart1b_df = df_full[df_full['RuleMatchedCampaign'].notna()].copy()
        if not chart1b_df.empty:
            await self._create_and_save_umap_plot(
                df_subset=chart1b_df,
                text_column=self.combined_text_col,
                category_column='RuleMatchedCampaign',
                plot_title="UMAP of Rule-Matched Ads (Colored by Original RuleMatchedCampaign)",
                output_filename=str(viz_output_path / "1b_rule_matched_original_rules.png"),
                max_samples=max_samples_for_plot,
                umap_n_neighbors=umap_n_neighbors,
                umap_min_dist=umap_min_dist,
                umap_random_state=umap_random_state,
                max_legend_categories=max_legend_categories  # Pass new param
            )
        else:
            self.console.print("[yellow]Skipped Chart 1b: No data with RuleMatchedCampaign.[/]")

        # --- Chart 2: Combined Rule-Matched + Remainder Ads (colored by their DBSCAN clusters) ---
        self.console.print("\n[cyan]Generating Chart 2: Combined Ads (colored by their Combined DBSCAN Clusters)...[/]")
        chart2_df = df_full[df_full['CombinedRulesRemainderDBSCAN_CategoryName'].notna()].copy()
        if not chart2_df.empty:
            await self._create_and_save_umap_plot(
                df_subset=chart2_df,
                text_column=self.combined_text_col,
                category_column='CombinedRulesRemainderDBSCAN_CategoryName',
                plot_title="UMAP of Combined (Rule+Remainder) Ads (Colored by their DBSCAN Clusters)",
                output_filename=str(viz_output_path / "2_combined_rules_remainder_dbscan_clusters.png"),
                max_samples=max_samples_for_plot,
                umap_n_neighbors=umap_n_neighbors,
                umap_min_dist=umap_min_dist,
                umap_random_state=umap_random_state,
                max_legend_categories=max_legend_categories  # Pass new param
            )
        else:
            self.console.print("[yellow]Skipped Chart 2: No data with CombinedRulesRemainderDBSCAN_CategoryName.[/]")

        # --- Chart 2b: Combined Rule-Matched + Remainder Ads (colored by original type: Rule vs Remainder) ---
        # This chart naturally has few categories, so max_legend_categories might not be as critical, but passing it is consistent.
        self.console.print(
            "\n[cyan]Generating Chart 2b: Combined Ads (colored by Original Type: Rule-Matched vs Remainder)...[/]")
        chart2b_df_source = df_full[df_full['CombinedRulesRemainderDBSCAN_CategoryName'].notna()].copy()
        if not chart2b_df_source.empty:
            chart2b_df_source['OriginalType'] = 'Remainder'
            chart2b_df_source.loc[chart2b_df_source['RuleMatchedCampaign'].notna(), 'OriginalType'] = 'Rule-Matched'

            await self._create_and_save_umap_plot(
                df_subset=chart2b_df_source,
                text_column=self.combined_text_col,
                category_column='OriginalType',
                plot_title="UMAP of Combined (Rule+Remainder) Ads (Colored by Original Type)",
                output_filename=str(viz_output_path / "2b_combined_original_type.png"),
                max_samples=max_samples_for_plot,
                umap_n_neighbors=umap_n_neighbors,
                umap_min_dist=umap_min_dist,
                umap_random_state=umap_random_state,
                max_legend_categories=max_legend_categories  # Pass new param (will likely be < max, so no "Other")
            )
        else:
            self.console.print("[yellow]Skipped Chart 2b: No data for combined original type visualization.[/]")

        # --- Chart 3: Original Remainder Clustering (colored by FinalVectorCategory) ---
        self.console.print(
            "\n[cyan]Generating Chart 3: True Remainder Ads (colored by their FinalVectorCategory cluster)...[/]")
        chart3_df = df_full[
            df_full['FinalVectorCategory'].notna() & \
            (~df_full['FinalVectorCategory'].astype(str).str.startswith("RULE_MATCH:")) & \
            (~df_full['FinalVectorCategory'].astype(str).str.startswith("PREFILTERED_GENERAL")) & \
            (~df_full['FinalVectorCategory'].astype(str).str.startswith("UNASSIGNED_OTHER")) & \
            (~df_full['FinalVectorCategory'].astype(str).str.startswith("REMAINDER:UNCLUSTERED"))
            ].copy()
        if not chart3_df.empty:
            await self._create_and_save_umap_plot(
                df_subset=chart3_df,
                text_column=self.combined_text_col,
                category_column='FinalVectorCategory',
                plot_title="UMAP of True Remainder Ads (Colored by FinalVectorCategory Clusters)",
                output_filename=str(viz_output_path / "3_true_remainder_final_vector_clusters.png"),
                max_samples=max_samples_for_plot,
                umap_n_neighbors=umap_n_neighbors,
                umap_min_dist=umap_min_dist,
                umap_random_state=umap_random_state,
                max_legend_categories=max_legend_categories  # Pass new param
            )
        else:
            self.console.print(
                "[yellow]Skipped Chart 3: No suitable data for FinalVectorCategory clusters visualization.[/]")

        self.console.print(Panel("[bold green]Visual Analysis Chart Generation Complete[/]", border_style="green"))

    def get_category_for_ad_data(self, ad_data: Dict[str, Any]) -> Optional[str]:
        """
        Matches a single ad (provided as a dictionary with snake_cased keys)
        against the loaded campaign rules using AdCampaignMatcher.
        It sets the 'campaign' field in ad_data to the matched LitigationName or 'Other',
        updates the 'summary' field if the original summary indicates a failed/skipped generation,
        and handles Company extraction based on campaign rules or NER.

        Args:
            ad_data (Dict[str, Any]): A dictionary containing the ad's data.
                                      Expected to have snake_cased keys like 'title', 'body', 'summary'.
                                      This dictionary WILL be modified in-place.

        Returns:
            Optional[str]: The LitigationName of the matched campaign, or None if no rule matches.
        """
        if not self.ad_campaign_matcher:
            self.logger.warning(
                "AdCampaignMatcher not available. Cannot categorize ad_data. Setting campaign to 'Other'.")
            ad_data['campaign'] = 'Other'
            return None

        title_str = str(ad_data.get('title', ''))
        body_str = str(ad_data.get('body', ''))

        # Prepare text from title and body for rule matching
        parts = []
        if title_str.strip(): parts.append(title_str.lower())
        if body_str.strip(): parts.append(body_str.lower())
        raw_combined_text_tb = " | ".join(parts)
        text_to_match_tb = _normalize_text_for_matching(raw_combined_text_tb)

        matched_campaign_name: Optional[str] = None

        # Attempt match with Title & Body
        if text_to_match_tb.strip():
            matched_campaign_name = self.ad_campaign_matcher.match_ad_text(text_to_match_tb)

        # If no match with Title/Body, try ImageText
        if not matched_campaign_name:
            image_text_raw = ad_data.get('image_text', '') or ad_data.get('ImageText', '')  # Handle both potential keys
            if image_text_raw and isinstance(image_text_raw, str) and image_text_raw.strip():
                self.logger.info(f"No match with title/body for ad_data. Attempting match with ImageText.")
                text_to_match_img = _normalize_text_for_matching(image_text_raw.lower())
                if text_to_match_img.strip():
                    matched_campaign_name_img = self.ad_campaign_matcher.match_ad_text(text_to_match_img)
                    if matched_campaign_name_img:
                        matched_campaign_name = matched_campaign_name_img
                        self.logger.info(f"Input ad_data matched to campaign via ImageText: {matched_campaign_name}")

        if matched_campaign_name:
            self.logger.info(
                f"Input ad_data (keys: {list(ad_data.keys())}) matched to campaign: {matched_campaign_name}")
            ad_data['campaign'] = matched_campaign_name

            # Get Company from campaign rules
            campaign_company = self.ad_campaign_matcher.get_campaign_company(matched_campaign_name)
            
            # Apply the company extraction logic:
            # 1. If campaign rule has a Company, use it
            # 2. If Company is null, attempt NER extraction
            if campaign_company is not None:
                ad_data['Company'] = campaign_company
                self.logger.info(f"Using Company from campaign rule: {campaign_company}")
            else:
                # Extract company using NER
                extracted_company = self._extract_company_with_ner(ad_data)
                ad_data['Company'] = extracted_company
                if extracted_company:
                    self.logger.info(f"Extracted Company using NER: {extracted_company}")
                else:
                    self.logger.info("No Company found via NER extraction")

            # Update summary if it's a placeholder
            if 'summary' in ad_data:
                current_summary_value = ad_data.get('summary')
                current_summary_str = str(
                    current_summary_value if current_summary_value is not None else '').strip().lower()
                # Define placeholder summaries that indicate a need for replacement
                summaries_to_replace = ["na", "skipped", "summary generation failed", "failed to generate summary"]

                # More robust check: if any placeholder is a substring of the current summary
                if any(placeholder in current_summary_str for placeholder in summaries_to_replace):
                    ad_data['summary'] = matched_campaign_name  # Replace summary
                    self.logger.info(
                        f"Updated ad_data summary to '{matched_campaign_name}' (original was '{current_summary_value}')")
            else:
                self.logger.debug(
                    "Input ad_data does not have a 'summary' field. Cannot check for summary replacement.")
        else:
            self.logger.info(
                f"Input ad_data (keys: {list(ad_data.keys())}) did not match any campaign rules. Setting campaign to 'Other'.")
            ad_data['campaign'] = 'Other'
            # For unmatched campaigns, still try to extract company
            extracted_company = self._extract_company_with_ner(ad_data)
            ad_data['Company'] = extracted_company
            if extracted_company:
                self.logger.info(f"Extracted Company using NER for 'Other' campaign: {extracted_company}")

        return matched_campaign_name

    def _extract_company_with_ner(self, ad_data: Dict[str, Any]) -> Optional[str]:
        """
        Extracts company name from ad text using spaCy NER.
        Searches for ORG entities in title, body, summary, and ImageText fields.
        
        Args:
            ad_data: Dictionary containing ad fields
            
        Returns:
            The first organization name found, or None if no organizations detected
        """
        try:
            # Check if spaCy is available
            if not hasattr(self, '_nlp'):
                if spacy is None:
                    self.logger.warning("spaCy not available. Company NER extraction disabled.")
                    return None
                
                # Load a lightweight model for NER
                try:
                    self._nlp = spacy.load("en_core_web_sm")
                except OSError:
                    try:
                        # Try loading the larger model if small is not available
                        self._nlp = spacy.load("en_core_web_lg")
                    except OSError:
                        self.logger.warning("No spaCy model found. Company NER extraction disabled.")
                        return None
            
            # Combine text from multiple fields for NER processing
            text_parts = []
            
            # Add title
            title = ad_data.get('title', '')
            if title and isinstance(title, str):
                text_parts.append(title)
            
            # Add body (limit to first 500 chars for performance)
            body = ad_data.get('body', '')
            if body and isinstance(body, str):
                text_parts.append(body[:500])
            
            # Add summary
            summary = ad_data.get('summary', '')
            if summary and isinstance(summary, str) and summary.lower() not in ["na", "skipped", "summary generation failed"]:
                text_parts.append(summary)
            
            # Add ImageText
            image_text = ad_data.get('ImageText', '') or ad_data.get('image_text', '')
            if image_text and isinstance(image_text, str):
                text_parts.append(image_text[:300])
            
            if not text_parts:
                return None
            
            # Combine all text
            combined_text = " ".join(text_parts)
            
            # Process with spaCy
            doc = self._nlp(combined_text)
            
            # Extract ORG entities
            organizations = []
            for ent in doc.ents:
                if ent.label_ == "ORG":
                    # Clean up the organization name
                    org_name = ent.text.strip()
                    # Skip common non-company entities
                    skip_patterns = [
                        "lawsuit", "litigation", "case", "court", "class action",
                        "attorney", "law firm", "lawyer", "legal", "victims",
                        "injured", "compensation", "settlement"
                    ]
                    if not any(pattern in org_name.lower() for pattern in skip_patterns):
                        organizations.append(org_name)
            
            # Return the first valid organization found
            if organizations:
                # Prefer longer organization names (more specific)
                organizations.sort(key=len, reverse=True)
                return organizations[0]
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error during NER company extraction: {e}")
            return None


async def main_async(args):
    # ... (config loading as before) ...
    config = {}
    try:
        script_dir = Path(__file__).resolve().parent
        project_root = script_dir.parents[2]
        if str(project_root) not in sys.path: sys.path.insert(0, str(project_root))
        from src.lib.config import load_config
        config = load_config(args.config_date)
        if args.local and "dynamodb_endpoint_url" not in config:
            config["dynamodb_endpoint_url"] = os.getenv("DYNAMODB_ENDPOINT_URL", "http://localhost:8000")
    except Exception as e:
        logger.error(f"Config load failed: {e}. Using basic environment variables.", exc_info=True)
        config = {
            "aws_access_key_id": os.getenv("AWS_ACCESS_KEY_ID"),
            "aws_secret_access_key": os.getenv("AWS_SECRET_ACCESS_KEY"),
            "region_name": os.getenv("AWS_REGION", "us-east-1"),
            "dynamodb_endpoint_url": os.getenv("DYNAMODB_ENDPOINT_URL", "http://localhost:8000") if args.local else None
        }

    async with aiohttp.ClientSession() as session:
        try:
            clusterer = VectorClusterer(
                config=config,
                session=session,
                embedding_fields=args.embed_fields,
                embedding_stop_terms_path=args.embedding_stop_terms_file,
                campaign_skip_terms_path=args.campaign_skip_terms_file,
                campaign_config_path=args.campaign_config_file,
                use_local=args.local,
                scan_workers=args.scan_workers,
                rules_only=args.rules_only,
                use_dbscan=args.use_dbscan,
                dbscan_eps=args.dbscan_eps,
                dbscan_min_samples=args.dbscan_min_samples
            )

            if not args.visualize_only:
                await clusterer.run_clustering_pipeline(
                    min_k_remainder=args.min_k_remainder,
                    max_k_remainder=args.max_k_remainder,
                    k_step_remainder=args.k_step_remainder,
                    final_k_remainder=args.final_k_remainder,
                    output_dir=args.output_dir,
                    text_truncate_length=args.text_truncate_length
                )

            if args.run_visualization or args.visualize_only:
                input_csv_for_viz = Path(args.output_dir) / "0_comprehensive_results.csv"
                if not input_csv_for_viz.exists() and (args.visualize_only or args.run_visualization):
                    clusterer.console.print(
                        f"[bold red]Comprehensive results CSV not found at {input_csv_for_viz}. Cannot run visualization.[/]")
                    clusterer.console.print(
                        f"[yellow]Please run the full pipeline first, or ensure the path is correct.[/]")
                elif clusterer.rules_only_mode and (args.visualize_only or args.run_visualization):
                    clusterer.console.print(
                        f"[yellow]Visualization involves embeddings. Skipping as clusterer is in rules-only mode.[/]")
                else:
                    await clusterer.generate_visual_analysis_charts(
                        input_csv_path=str(input_csv_for_viz),
                        output_dir=str(Path(args.output_dir) / "visualization_charts"),
                        max_samples_for_plot=args.max_samples_for_plot,
                        umap_n_neighbors=args.umap_n_neighbors,
                        umap_min_dist=args.umap_min_dist,
                        max_legend_categories=args.max_legend_categories  # Pass new arg
                    )
        except Exception as e:
            logger.critical(f"Critical error during execution: {e}", exc_info=True)
            print(f"\n--- CRITICAL ERROR: {e} ---")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Seeded Clustering and Visualization of FB Ads.")
    # ... (all previous parser arguments) ...
    parser.add_argument('--local', action='store_true', default=True, help="Use local DynamoDB (default: True).")
    parser.add_argument('--aws', action='store_false', dest='local', help="Use AWS DynamoDB.")
    parser.add_argument('--scan-workers', type=int, default=None, help="Parallel workers for local scan.")
    parser.add_argument('--config-date', type=str, default='01/01/70', help="Date for loading config (MM/DD/YY).")
    parser.add_argument('--embed-fields', nargs='+', default=['Title', 'Body', 'Summary', 'ImageText'],
                        help="List of fields to combine for embeddings (e.g., --embed-fields Title Body ImageText)")
    # ... (file path arguments as before)
    script_dir_for_defaults = Path(__file__).resolve().parent
    try:
        src_root_for_defaults = script_dir_for_defaults
        while src_root_for_defaults.name != 'src' and src_root_for_defaults.parent != src_root_for_defaults:
            src_root_for_defaults = src_root_for_defaults.parent
        if src_root_for_defaults.name != 'src':  # Fallback if 'src' not found in parent chain
            project_root_for_defaults = script_dir_for_defaults
            # Try to find project root by looking for a common marker like '.git' or 'pyproject.toml'
            # This is a heuristic and might need adjustment based on your project structure.
            # For now, let's assume it's two levels up if 'src' isn't directly found.
            for _ in range(3):  # Check up to 3 levels
                if (project_root_for_defaults / '.git').exists() or \
                        (project_root_for_defaults / 'pyproject.toml').exists() or \
                        (project_root_for_defaults / 'src').is_dir():
                    break
                project_root_for_defaults = project_root_for_defaults.parent
            src_root_for_defaults = project_root_for_defaults / "src"
            if not (src_root_for_defaults).is_dir():  # Ultimate fallback
                src_root_for_defaults = script_dir_for_defaults.parents[2]

        base_config_path_for_defaults = src_root_for_defaults / "config" / "fb_ad_categorizer"
    except Exception:  # Broad exception if path logic fails
        base_config_path_for_defaults = Path(".") / "src" / "config" / "fb_ad_categorizer"  # Relative fallback

    default_embed_stop_terms = base_config_path_for_defaults / "embedding_stop_terms.json"
    parser.add_argument('--embedding-stop-terms-file', type=str, default=str(default_embed_stop_terms),
                        help="Path to JSON file with terms to remove from text before embedding.")

    default_campaign_skip_terms_path = base_config_path_for_defaults / "campaign_skip_terms.json"
    parser.add_argument('--campaign-skip-terms-file', type=str, default=str(default_campaign_skip_terms_path),
                        help="Path to JSON file containing terms to mark ads as 'General' and skip clustering.")

    default_campaign_config_path = base_config_path_for_defaults / "campaign_config.json"
    parser.add_argument('--campaign-config-file', type=str, default=str(default_campaign_config_path),
                        help="Path to JSON file with known campaign definitions (triggers, includes, etc.).")

    parser.add_argument('--use-dbscan', action='store_true', default=False,
                        help="Use DBSCAN for remainder clustering instead of K-Means.")
    kmeans_group = parser.add_argument_group('K-Means Parameters (if not using DBSCAN)')
    kmeans_group.add_argument('--min-k-remainder', type=int, default=5, help="Min k for Silhouette on remainder ads.")
    kmeans_group.add_argument('--max-k-remainder', type=int, default=50, help="Max k for Silhouette on remainder ads.")
    kmeans_group.add_argument('--k-step-remainder', type=int, default=5, help="Step for k testing on remainder ads.")
    kmeans_group.add_argument('--final-k-remainder', type=int, default=None,
                              help="Manually specify final k for remainder ads.")
    dbscan_group = parser.add_argument_group('DBSCAN Parameters (if using --use-dbscan or for new DBSCAN stages)')
    dbscan_group.add_argument('--dbscan-eps', type=float, default=0.5, help="Epsilon parameter for DBSCAN.")
    dbscan_group.add_argument('--dbscan-min-samples', type=int, default=5, help="Min_samples parameter for DBSCAN.")
    parser.add_argument('--output-dir', type=str, default="vector_clustering_outputs",
                        help="Directory to save all output CSV files and visualization charts.")
    parser.add_argument('--text-truncate-length', type=int, default=100,
                        help="Max length for text fields in CSV output.")
    parser.add_argument('--log-level', type=str, default='INFO',
                        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
                        help='Set logging level.')
    parser.add_argument('--rules-only', action='store_true', default=False,
                        help="Run only rule-based matching and pre-filtering, skip vector processing and clustering (and visualization).")

    # Visualization specific arguments
    viz_group = parser.add_argument_group('Visualization Parameters')
    viz_group.add_argument('--run-visualization', action='store_true', default=False,
                           help="Run visualization step after the main pipeline.")
    viz_group.add_argument('--visualize-only', action='store_true', default=False,
                           help="Only run the visualization step, assuming pipeline has already produced '0_comprehensive_results.csv'.")
    viz_group.add_argument('--max-samples-for-plot', type=int, default=5000,
                           help="Maximum number of samples to use for UMAP plots (for performance).")
    viz_group.add_argument('--umap-n-neighbors', type=int, default=15, help="UMAP: Number of neighbors.")
    viz_group.add_argument('--umap-min-dist', type=float, default=0.1, help="UMAP: Minimum distance.")

    args = parser.parse_args()

    # Logic for --visualize-only implies --run-visualization effectively
    if args.visualize_only:
        args.run_visualization = True  # Ensure visualization runs if visualize_only is set

    if args.rules_only and (args.run_visualization or args.visualize_only):
        print("[WARNING] --rules-only is set. Visualization requires embeddings and will be skipped.")
        args.run_visualization = False
        args.visualize_only = False

    logger.setLevel(getattr(logging, args.log_level.upper()))
    if logger.handlers:  # Ensure handler exists before trying to set its level
        # Check if RichHandler is used and if so, if it has a level attribute directly
        # RichHandler's level is often set at initialization or via the logger it's attached to.
        # The logger.setLevel above should be sufficient.
        # If specific handler level adjustment is needed:
        for handler in logger.handlers:
            handler.setLevel(getattr(logging, args.log_level.upper()))

    if sys.platform == 'win32':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

    asyncio.run(main_async(args))
