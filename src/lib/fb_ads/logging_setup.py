"""Logging configuration for Facebook Ads module.

This module provides a centralized logging configuration for the Facebook Ads module,
ensuring logs are saved to the specified location with rich formatting.
"""

import logging
import os
from typing import Dict, Any, Optional

from rich.console import Console
from rich.logging import RichHandler


class FBAdsLogger:
    """Configures and provides logging for the Facebook Ads module."""
    
    @staticmethod
    def setup_logging(config: Dict[str, Any]) -> logging.Logger:
        """Set up logging for the Facebook Ads module.
        
        Args:
            config: The application configuration dictionary
            
        Returns:
            A configured logger instance
        """
        # Get the iso_date from config
        iso_date = config.get('iso_date', 'default')
        
        # Determine log directory
        log_dir = config.get('directories', {}).get(
            'log_dir',
            os.path.join(config.get('DATA_DIR', os.path.join(os.getcwd(), 'data')), f"{iso_date}/logs")
        )
        
        # Ensure log directory exists
        os.makedirs(log_dir, exist_ok=True)
        
        # Set up log file path
        log_file = os.path.join(log_dir, 'fb_ads.log')
        
        # Create console for rich formatting
        console = Console()
        
        # Get the fb_ads logger
        logger = logging.getLogger('src.lib.fb_ads')
        logger.setLevel(logging.DEBUG)
        logger.propagate = False  # Prevent propagation to root logger
        
        # Remove any existing handlers
        for handler in logger.handlers[:]:  
            logger.removeHandler(handler)
        
        # File handler with standard formatting
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.DEBUG)
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(module)s.%(funcName)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)
        
        # Console handler with rich formatting
        console_handler = RichHandler(rich_tracebacks=True, console=console, show_path=False)
        console_handler.setLevel(logging.INFO)
        logger.addHandler(console_handler)
        
        logger.info(f"Facebook Ads logging configured. Log file: {log_file}")
        return logger