#!/usr/bin/env python3
import json
import os
from decimal import Decimal
from typing import Dict, Any, Optional, List

from tqdm import tqdm

import logging
from ..deepseek_interface import DeepSeek
from ..s3_manager import S3Manager


class DecimalEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, Decimal):
            return str(obj)
        return super(DecimalEncoder, self).default(obj)


class LegalAdAnalyzer:
    def __init__(self, config: Dict[str, Any], base_path: str):
        self.config = config
        self.deepeseek = DeepSeek(self.config, 'deepseek-chat')
        self.logger = logging.getLogger(__name__)
        self.base_path = base_path
        self.s3_prod = 'https://cdn.lexgenius.ai'
        self.s3 = S3Manager(config, 'lexgenius-dockets')

    def dedup_ads(self, ads: List[Dict[str, str]]) -> (List[Dict[str, str]], List[Dict[str, str]]):
        unique_ads = []
        duplicates = []
        seen_ads = set()

        for ad in ads:
            title = ad.get('Title', '')
            summary = ad.get('Summary', '')
            body = ad.get('Body', '')
            ad_signature = (title, summary, body)

            if ad_signature not in seen_ads:
                seen_ads.add(ad_signature)
                unique_ads.append(ad)
            else:
                duplicates.append(ad)

        return unique_ads, duplicates

    def analyze_ad(self, ad: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        try:
            summary = ad.get('Summary', '')
            if not summary or summary in ['', 'NA', 'None']:
                return None

            body = ad.get('Body', '')
            title = ad.get('Title', '')
            image_txt = ad.get('image_txt', '')
            ad_info = f"Summary: {summary}\nTitle: {title}\nBody: {body}\nImage Text: {image_txt}"
            response = self.deepseek.extract_summary(ad_info, prompt_type='classify_ad', expect_json=False)
            ad_with_data = ad.copy()
            ad_with_data['Campaign'] = response
            return ad_with_data
        except Exception as e:
            self.logger.error(f"Error analyzing ad: {e}")
            return None

    def process_ads(self):
        try:
            raw_file_path = os.path.join(self.base_path, 'fb_ads_raw.json')
            with open(raw_file_path, 'r') as f:
                ads = json.load(f)
            if not ads:
                self.logger.warning("No ads found in file")
                return None

            unique_ads, duplicate_ads = self.dedup_ads(ads)
            duplicates_filename = os.path.join(self.base_path, 'fb_ads_duplicates.json')
            uniques_filename = os.path.join(self.base_path, 'fb_ads_uniques.json')
            with open(duplicates_filename, 'w') as f:
                json.dump(duplicate_ads, f, indent=2)
            with open(uniques_filename, 'w') as f:
                json.dump(unique_ads, f, indent=2)

            analyzed_ads = []
            for ad in tqdm(unique_ads, desc="Analyzing ads", leave=True):
                analyzed_ad = self.analyze_ad(ad)
                if analyzed_ad:
                    analyzed_ads.append(analyzed_ad)

            processed_filename = os.path.join(self.base_path, 'fb_ads_processed.json')
            with open(processed_filename, 'w') as f:
                json.dump(analyzed_ads, f, indent=2)

            return {
                'processed_ads': len(analyzed_ads),
                'unique_ads': len(unique_ads),
                'duplicate_ads': len(duplicate_ads),
                'duplicates_file': duplicates_filename,
                'uniques_file': uniques_filename,
                'processed_file': processed_filename
            }
        except Exception as e:
            self.logger.error(f"Error processing ads: {e}", exc_info=True)
            raise

    def process_selected_ads(self, ad_ids: List[str], input_file: str, output_file: str) -> Dict[str, int]:
        """
        Process a selection of ads based on a list of AdArchiveIDs, analyze them,
        and save the results back to the JSON file.

        Args:
            ad_ids (List[str]): List of AdArchiveIDs to be processed.
            input_file (str): Path to the input JSON file containing all ads.
            output_file (str): Path to the output JSON file for saving processed results.

        Returns:
            Dict[str, int]: Processing statistics.
        """
        try:
            self.logger.info(f"Loading ads from file: {input_file}")

            # Load all ads from the input file
            with open(input_file, 'r') as f:
                ads_data = json.load(f)

            # Filter ads to process only those with matching AdArchiveIDs
            ads_to_process = [ad for ad in ads_data if ad.get('AdArchiveID') in ad_ids]
            stats = {
                'total_selected_ads': len(ads_to_process),
                'processed_ads': 0,
                'failed_ads': 0
            }

            # Analyze each selected ad
            processed_ads = []
            for ad in tqdm(ads_to_process, desc="Processing selected ads"):
                analyzed_ad = self.analyze_ad(ad)
                if analyzed_ad:
                    processed_ads.append(analyzed_ad)
                    stats['processed_ads'] += 1
                else:
                    stats['failed_ads'] += 1

            # Update original ads data with the processed information
            for i, ad in enumerate(ads_data):
                if ad.get('AdArchiveID') in ad_ids:
                    ads_data[i] = next((processed_ad for processed_ad in processed_ads if
                                        processed_ad['AdArchiveID'] == ad['AdArchiveID']), ad)

            # Save the updated ads data back to the output file
            with open(output_file, 'w') as f:
                json.dump(ads_data, f, indent=2, cls=DecimalEncoder)

            self.logger.info(f"Completed processing selected ads. Stats: {stats}")
            return stats

        except Exception as e:
            self.logger.error(f"Error in process_selected_ads: {e}", exc_info=True)
            raise


if __name__ == "__main__":
    from config import load_config

    base_path = "/Users/<USER>/PycharmProjects/mt_competitive_analysis/src/data/fb_ad_archive"
    config = load_config('01/01/70')
    analyzer = LegalAdAnalyzer(config, base_path)
    # results = analyzer.process_ads()
    # if results:
    #     print(f"Processed {results['processed_ads']} unique ads")
    #     print(f"Duplicates saved to: {results['duplicates_file']}")
    #     print(f"Uniques saved to: {results['uniques_file']}")
    #     print(f"Processed ads saved to: {results['processed_file']}")
    process_list = []
    analyzer.process_selected_ads(process_list)
    # PROCESS ADS
    # file_path = os.path.join(base_path, 'fb_ads_processed.json')
    # with open(file_path, 'r') as f:
    #     data = json.load(f)
    #
    # df = pd.DataFrame(data)
    # summary = df['Campaign'].value_counts().reset_index()
    # summary.columns = ['Campaign', 'Count']
    # summary = summary.sort_values(by='Count', ascending=False)
    # print(summary)

# mapping_dict = {
#     ["Depo-Provera", "Injectable Birth Control"]: "Depo-Provera",
#     ["NEC", "enfamil", "similac"]: "NEC",
#     ["AFFF", "firefighting", "fire fighter", "navy"]: "AFFF",
#     ["Suboxone"]: "Suboxone",
#     ["RoundUp", "non-hodgkins", "lymphoma"]: "RoundUp",
#     ["Juvenile Detention Center", "Juvenile Center", "Youth Center"]: "Juvenile Detention Center Abuse",
#     ["Paraquat", "Parkinsons"]: "Paraquat",
#     ["Hair Relaxer", "Hair Straightener"]: "Hair Relaxer",
#     ["Talc", "Talcum Powder", "Baby Powder"]: "Talc",
#     ["P. Diddy"]: "P.Diddy Sex Abuse",
#     ["Oxbryta"]: "Oxbryta",
#     ["Asbestos", "Mesothelioma", "Lung Cancer"]: "Asbestos",
#     ["hernia mesh"]: "hernia mesh",
#     ["port catheter", "port", "port-a-cath", "catheter"]: "Bard/Angiodynamics",
#     ["Zantac"]: "Zantac",
#     ["Dacthal", "DCPA"]: "Dacthal",
#     ["Ethylene Oxide", "eto", "sterigenics"]: "Ethylene Oxide",
#     ["conyers", "biolab"]: "Conyers Biolab",
#     ["bair hugger", "warming blanket"]: "Bair Hugger",
#     ["toxic baby food", "heavy metals"]: "Toxic Baby Food",
#     ["zimmer biomat"]: "Zimmer Hip",
#     ["ozempic", 'glp-1", "glp-1a", "semaglutide", "wegovy", "mounjaro", "rybelsus", "trulicity']: "Ozempic",
#     ["PFAS drinking water", "contaminated water"]: "PFAS Drinking Water Contamination",
#     ["LDS", "Latter-day Saints"]: "LDS Sex Abuse",
#     ["clergy abuse", "catholic church"]: "Clergy Abuse",
#     ["Dr."]: "Doctor Abuse",
#     ["Abiomed Impella", "Abiomed Heart Pump"]: "Abiomed Impella Heart Pump",
#     ["maui wildfire"]: "Maui Wildfire",
#     ["spinal cord stimulator", "spinal stimulator"]: "spinal",
#     ["school district"]: "School District Sex Abuse",
#     ["Cooper Surgical", "CooperSurgical", "IVF"]: "CooperSurgical Defective IVF Medium",
#     ["Google Incognito", "Google Privacy"]: "Google Incognito",
# }
