#!/usr/bin/env python3
from __future__ import annotations

# --- Imports needed for main_async ---
import argparse
import json
import logging
import random
import sys
import warnings
from datetime import datetime
from typing import List, Dict, Any, Optional

# For VectorClusterer
import aiohttp  # Required by VectorClusterer
# Ensure requests is imported if not already globally
# Third-party Libraries
from boto3.dynamodb.conditions import Key
from requests.exceptions import RequestException  # Keep RequestException import
from rich.console import Console
from rich.logging import RichHandler
from rich.panel import Panel
from rich.table import Table

from src.lib.pdf_extractor import PDFExtractor

try:
    from ..vector_clusterer import VectorClusterer  # Adjust path as necessary
except ImportError:
    # Fallback if orchestrator.py is in src.lib.fb_ads and vector_clusterer in src.lib
    from src.lib.fb_ads.vector_clusterer import VectorClusterer

from .api_client import FacebookAPIClient
from .image_handler import ImageHandler  # Ensure ImageHandler is imported
from .processing_tracker import ProcessingTracker
from .session_manager import FacebookSessionManager  # Use relative import
from .bandwidth_logger import BandwidthLogger
# Local Application/Library Specific Imports
# Adjust imports based on the new src/lib/fb_ads location
from ..config import load_config
from ..utils.date import DateUtils
from ..fb_archive_manager import FBAdArchiveManager  # Ensure FBAdArchiveManager is imported
from ..law_firms_manager import LawFirmsManager
from ..s3_manager import S3Manager

# Need to import S3Manager and ClientError if used in ImageHandler
# Assuming S3Manager is correctly defined elsewhere
# from src.lib.s3_manager import S3Manager

# --- Make sure SSLAdapter related imports are present ---

# Need FBAdArchiveManager if used in AdProcessor/AIIntegrator
# from src.lib.fb_archive_manager import FBAdArchiveManager

# Need requests.exceptions if used

# Ensure json is imported

# Ensure os is imported

# --- Configuration & Globals ---
warnings.filterwarnings("ignore", category=RuntimeWarning)

# Initialize rich console
console = Console()

# Configure root logger for RichHandler
# Set level higher initially, command-line args can lower it
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
    datefmt="[%X]",
    handlers=[RichHandler(rich_tracebacks=True, console=console, show_path=False)]  # Show path=False for cleaner logs
)
# Set library loggers to a higher level unless debug is enabled
logging.getLogger("urllib3").setLevel(logging.WARNING)
logging.getLogger("botocore").setLevel(logging.WARNING)
logging.getLogger("boto3").setLevel(logging.WARNING)
logging.getLogger("PIL").setLevel(logging.WARNING)
logging.getLogger("requests").setLevel(logging.WARNING)  # Quiet requests library too

# Import our custom logging setup
from .logging_setup import FBAdsLogger


class FacebookAdsOrchestrator:
    """Orchestrates the Facebook Ads scraping process."""
    KEYS_TO_REMOVE_BEFORE_SAVE = [
        'original_image_url', 'resized_image_url', 'video_preview_image_url',
        'video_hd_url', 'video_sd_url', '_temp_source_image_url', '_s3_check_result',
        'ImageText'  # Remove ImageText to save space - it's now stored in FBImageHash table
    ]

    def __init__(self, config: Dict[str, Any], session: aiohttp.ClientSession):  # Add aiohttp.ClientSession
        """Initializes the FacebookAdsOrchestrator with all components."""
        self.config = config
        self.aiohttp_session = session  # Store the session
        self._logger = FBAdsLogger.setup_logging(config)
        self.console = console
        self._logger.info(f"Facebook Ads Orchestrator initialized with iso_date: {config.get('iso_date', 'default')}")
        # ... (rest of the initializations as before: ignore_firm_data, dates, managers) ...
        self.ignore_firm_data: Dict[str, str] = self._load_ignore_firms()
        self.end_date_iso = config.get('iso_date')
        if not self.end_date_iso: raise ValueError("Config missing 'iso_date'.")
        try:
            datetime.strptime(self.end_date_iso, '%Y%m%d')
        except ValueError:
            raise ValueError(f"Invalid 'iso_date': {self.end_date_iso}.")
        self.start_date_iso = DateUtils.get_date_before_n_days(
            self.config.get('default_date_range_days', 14), self.end_date_iso
        )
        self.law_firm_db = LawFirmsManager(config)
        self.fb_ad_db = FBAdArchiveManager(config, use_local=config.get('use_local', False))
        self.s3_manager = S3Manager(config, config.get('bucket_name'))
        self.processing_tracker = ProcessingTracker(config.get('processing_results_dir', './processing_results'))
        self.session_manager = FacebookSessionManager(config)
        self.api_client = FacebookAPIClient(self.session_manager, config)
        self.image_handler = ImageHandler(config, self.s3_manager, self.session_manager)

        bandwidth_config = config.copy()
        bandwidth_config['disable_bandwidth_periodic_logging'] = bandwidth_config.get(
            'disable_bandwidth_periodic_logging', True)
        if hasattr(self.session_manager, 'bandwidth_logger'):
            self.bandwidth_logger = self.session_manager.bandwidth_logger
        else:
            self.bandwidth_logger = BandwidthLogger(bandwidth_config)

        self.pdf_extractor_instance = PDFExtractor(config, s3_link='https://cdn.lexgenius.ai/20250501/dockets/mdd_25_01388_DiToto_v_Nevro_Corporation.pdf')  # Keep for AIIntegrator if it uses it

        # --- Initialize AI Components (GPT, DeepSeek, LLaVA) ---
        gpt_instance = None  # ... (as before)
        if not config.get('disable_gpt'):
            try:
                from ..gpt4_interface import GPT4
                gpt_instance = GPT4(config=self.config)
                self._logger.info("GPT4 initialized.")
            except Exception as gpt_e:
                self._logger.error(f"Failed to initialize GPT4: {gpt_e}")

        deepseek_instance = None  # ... (as before)
        if not config.get('disable_deepseek'):
            try:
                from ..deepseek_interface import DeepSeek
                deepseek_instance = DeepSeek(config=self.config)
                self._logger.info("DeepSeek initialized.")
            except Exception as ds_e:
                self._logger.error(f"Failed to initialize DeepSeek: {ds_e}")

        self.llava_extractor = None
        # Skip LLaVA initialization if we're deferring image processing
        if not config.get('disable_llava') and not config.get('defer_image_processing'):
            try:
                from ..llava_vision import LlavaImageExtractor
                
                # Get model name from config, default to llava:7b
                llava_model = config.get('llava_model_name', "llava:7b")
                
                # Initialize with all parameters
                self.llava_extractor = LlavaImageExtractor(
                    ollama_base_url=config.get('ollama_base_url', "http://localhost:11434"),
                    model_name=llava_model,
                    request_timeout=config.get('llava_timeout', 1200),
                    default_temperature=config.get('llava_temperature', 0.3),
                    num_gpu_layers=config.get('llava_num_gpu_layers', -1),
                    use_client_semaphore=config.get('llava_use_semaphore', True),
                    client_semaphore_count=config.get('llava_semaphore_count', 3)
                )
                self._logger.info(f"LlavaImageExtractor initialized with model: {llava_model}")
            except ValueError as ve:
                self._logger.error(f"Invalid LLaVA model configuration: {ve}")
                self.llava_extractor = None
            except Exception as e:
                self._logger.error(f"Failed to initialize LlavaImageExtractor: {e}")
                self.llava_extractor = None

        # --- Initialize AIIntegrator ---
        from src.lib.ai_integrator import AIIntegrator  # Ensure import
        self.ai_integrator = AIIntegrator(
            config=self.config, logger=self._logger,
            pdf_extractor=self.pdf_extractor_instance,  # Pass PDFExtractor
            s3_manager=self.s3_manager, fb_ad_db=self.fb_ad_db,
            gpt=gpt_instance, deepseek=deepseek_instance, llava_extractor=self.llava_extractor
        )
        self._logger.info("AIIntegrator initialized.")

        # --- Initialize VectorClusterer ---
        # VectorClusterer needs campaign_config.json path etc.
        # These should come from the main config file or have defaults.
        # Default paths for VectorClusterer configs, assuming orchestrator.py is in src/lib/fb_ads
        vc_script_dir = Path(__file__).resolve().parent
        vc_src_root = vc_script_dir.parent.parent  # src/
        vc_base_config_path = vc_src_root / "config" / "fb_ad_categorizer"

        self.vector_clusterer_instance = VectorClusterer(
            config=self.config,  # Pass main config
            session=self.aiohttp_session,  # Pass the shared aiohttp session
            embedding_fields=self.config.get('vector_clusterer_embed_fields', ['Title', 'Body']),  # Example default
            embedding_stop_terms_path=str(
                self.config.get('embedding_stop_terms_file', vc_base_config_path / "embedding_stop_terms.json")),
            campaign_skip_terms_path=str(
                self.config.get('campaign_skip_terms_file', vc_base_config_path / "campaign_skip_terms.json")),
            campaign_config_path=str(
                self.config.get('campaign_config_file', vc_base_config_path / "campaign_config.json")),
            rules_only=True  # Initialize in rules_only mode as we only need get_category_for_ad_data
        )
        self._logger.info("VectorClusterer initialized for rule-based categorization (rules_only=True).")

        # --- Initialize AdProcessor with VectorClusterer ---
        from .processor import AdProcessor  # Ensure import
        self.ad_processor = AdProcessor(
            config=self.config,
            image_handler=self.image_handler,
            ai_integrator=self.ai_integrator,
            vector_clusterer=self.vector_clusterer_instance,  # Pass the instance
            current_process_date=self.end_date_iso
        )
        self._logger.info("AdProcessor initialized with VectorClusterer instance.")

        # ... (rest of __init__ as before: state, progress bar) ...
        self.error_firms: List[Dict[str, str]] = []
        self.law_firm_dir = config.get('law_firm_data_dir', './data/law_firms')
        self.download_dir = self._setup_download_dir()
        self.use_rich_progress = sys.stdout.isatty()
        if not self.use_rich_progress: self._logger.info("Not a TTY, disabling Rich progress bar.")
        from rich.progress import (BarColumn, Progress, SpinnerColumn, TextColumn,
                                   TimeRemainingColumn, MofNCompleteColumn)
        self.progress = Progress(
            SpinnerColumn(), TextColumn("[progress.description]{task.description}"), BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"), MofNCompleteColumn(),
            TimeRemainingColumn(), console=self.console, transient=True, disable=not self.use_rich_progress
        )
        self.session_refresh_interval = config.get('session_refresh_interval', 25)
        self._logger.info(f"Proactive session refresh interval set to {self.session_refresh_interval} firms.")
        self._logger.info("FacebookAdsOrchestrator initialized successfully.")

    # ... (all other methods of FacebookAdsOrchestrator like run_full_scrape, _process_single_firm, etc. remain unchanged)
    async def cleanup(self):
        """Closes sessions and performs other cleanup."""
        self._logger.info("Performing cleanup...")
        # ... (existing cleanup for bandwidth_logger, llava_extractor, session_manager.reqs, progress) ...
        if hasattr(self, 'bandwidth_logger'):
            self.bandwidth_logger.log_summary()
            # ... log stats ...
        if self.llava_extractor:
            try:
                await self.llava_extractor.close_session()
            except Exception as e:
                self._logger.error(f"Error closing LLaVA session: {e}")
        if self.session_manager and self.session_manager.reqs:
            try:
                self.session_manager.reqs.close()
            except Exception as e:
                self._logger.error(f"Error closing requests session: {e}")
        if hasattr(self, 'progress') and self.progress.live.is_started:
            try:
                self.progress.stop()
            except Exception as e:
                self._logger.error(f"Error stopping progress bar: {e}")

        # The shared aiohttp session is managed by main_async and closed there.
        # Orchestrator does not own it, so it should not close it.
        self._logger.info("Cleanup complete.")

    def get_bandwidth_usage(self):
        """Returns the current bandwidth usage statistics."""
        if hasattr(self, 'bandwidth_logger'):
            stats = self.bandwidth_logger.get_total_bandwidth()
            stats['downloaded_mb'] = stats['downloaded'] / (1024 * 1024)
            stats['uploaded_mb'] = stats['uploaded'] / (1024 * 1024)
            stats['images_mb'] = stats['images'] / (1024 * 1024)
            stats['html_mb'] = stats['html'] / (1024 * 1024)
            stats['api_mb'] = stats['api'] / (1024 * 1024)
            stats['other_mb'] = stats['other'] / (1024 * 1024)
            return stats
        return None

    def _load_ignore_firms(self) -> Dict[str, str]:
        """Loads the ignore firms data from a JSON file."""
        ignore_file_path = Path(__file__).parent.parent.parent / 'config/fb_ads/ignore_firms.json'
        self._logger.info(f"Attempting to load ignored firms from: {ignore_file_path}")
        try:
            with open(ignore_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            if not isinstance(data, dict):
                self._logger.error(f"Invalid format in {ignore_file_path}: Expected dict.")
                return {}
            self._logger.info(f"Successfully loaded {len(data)} firms from {ignore_file_path}")
            return data
        except FileNotFoundError:
            self._logger.error(f"Ignore firms file not found: {ignore_file_path}.")
            return {}
        except json.JSONDecodeError as e:
            self._logger.error(f"Error decoding JSON: {e}.")
            return {}
        except Exception as e:
            self._logger.error(f"Unexpected error loading {ignore_file_path}: {e}", exc_info=True);
            return {}

    def _setup_download_dir(self) -> str:
        """Sets up the base download directory for the current date."""
        base_data_path = Path(self.config.get('base_data_dir', Path(__file__).parent.parent.parent / 'data')).resolve()
        date_path = base_data_path / self.end_date_iso
        temp_image_path = Path(self.image_handler.temp_download_dir).resolve()
        try:
            date_path.mkdir(parents=True, exist_ok=True)
            temp_image_path.mkdir(parents=True, exist_ok=True)
            return str(date_path)
        except OSError as e:
            self._logger.error(f"Failed to create dirs ({date_path} or {temp_image_path}): {e}");
            raise

    def _create_directories(self):
        """Creates necessary local directories."""
        law_firm_img_dir = Path(self.law_firm_dir) / 'img'
        try:
            law_firm_img_dir.mkdir(parents=True, exist_ok=True)
        except OSError as e:
            self._logger.error(f"Failed to create law firm image directory {law_firm_img_dir}: {e}")

    async def run_full_scrape(self):
        """Runs the ad scraping process for all relevant law firms."""
        self._logger.info(f"Starting full Facebook Ads scrape for date: {self.end_date_iso}")
        self.error_firms = []
        if not await self.session_manager.create_new_session():
            self._logger.error("FATAL: Could not establish initial session. Aborting run.")
            return
        law_firms_to_process = self._get_filtered_law_firms()
        if not law_firms_to_process:
            self._logger.info("No firms eligible for processing.")
            self._summarize_errors()
            return
        firm_counter_for_refresh = 0
        with self.progress:
            task_id = self.progress.add_task("[cyan]Processing firms...", total=len(law_firms_to_process))
            for firm_data in law_firms_to_process:
                firm_counter_for_refresh += 1
                if self.session_refresh_interval > 0 and firm_counter_for_refresh > 1 and \
                        (firm_counter_for_refresh - 1) % self.session_refresh_interval == 0:
                    self._logger.info(f"Proactively refreshing session...")
                    if not await self.session_manager.create_new_session():
                        self._logger.error("Proactive session refresh failed! Continuing...")
                    else:
                        self._logger.info("Proactive session refresh successful.")
                await self._process_single_firm(firm_data, task_id)
                self.progress.update(task_id, advance=1)
                use_long_pause = random.random() < 0.05
                await self.session_manager.random_sleep(long_pause=use_long_pause)
                if self.session_manager.use_proxy and self.config.get('rotate_proxy_between_firms', True):
                    self.session_manager.rotate_proxy()
        self._summarize_errors()
        self._logger.info("Facebook Ads scrape finished.")

    async def run_single_firm_scrape(self, firm_id: str):
        """Runs the ad scraping process for a single specified law firm ID."""
        self._logger.info(f"Starting single firm scrape for ID: {firm_id}")
        self.error_firms = []
        
        # Stop and reset progress bar to ensure clean state
        if hasattr(self, 'progress') and self.progress.live.is_started:
            try:
                self.progress.stop()
            except Exception as e:
                self._logger.error(f"Error stopping lingering progress bar: {e}")
        
        # Create a fresh progress bar for single firm processing to avoid old task display
        from rich.progress import (BarColumn, Progress, SpinnerColumn, TextColumn,
                                   TimeRemainingColumn, MofNCompleteColumn)
        self.progress = Progress(
            SpinnerColumn(), TextColumn("[progress.description]{task.description}"), BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"), MofNCompleteColumn(),
            TimeRemainingColumn(), console=self.console, transient=True, disable=not self.use_rich_progress
        )
        
        if not await self.session_manager.create_new_session():  # Changed to await
            self._logger.error(f"FATAL: Could not establish session for single firm {firm_id}. Aborting.")
            return
        firm_data = self._get_single_firm_data(firm_id)
        if not firm_data:
            self._logger.error(f"Could not find or fetch data for firm ID {firm_id}.")
            self._summarize_errors()
            return
        original_start_date = self.start_date_iso
        single_run_days = self.config.get('single_firm_date_range_days', 30)
        self.start_date_iso = DateUtils.get_date_before_n_days(single_run_days, self.end_date_iso)
        self._logger.info(f"Using date range for single firm: {self.start_date_iso} to {self.end_date_iso}")
        
        with self.progress:
            firm_name_display = firm_data.get('Name', firm_id)[:40]
            task_id = self.progress.add_task(f"[cyan]Processing {firm_name_display}...", total=1)
            await self._process_single_firm(firm_data, task_id, update_db=True)
            
        self.start_date_iso = original_start_date
        self._summarize_errors()
        self._logger.info(f"Single firm scrape finished for {firm_id}.")

    async def run_ignore_list_processing(self):
        """Processes only the firms listed in the loaded ignore_firm_data."""
        self._logger.info("Starting processing for firms in the ignore list.")
        self.error_firms = []
        if not self.ignore_firm_data: self._logger.info("Ignore list is empty."); return
        if not await self.session_manager.create_new_session():  # Changed to await
            self._logger.error("FATAL: Could not establish session for ignore list. Aborting.")
            return
        ignore_list_firms_data = []
        ignore_firm_ids = list(self.ignore_firm_data.keys())
        self._logger.info(f"Fetching data for {len(ignore_firm_ids)} firms from ignore list...")
        with self.progress:
            task_fetch = self.progress.add_task("[yellow]Fetching ignore firms...", total=len(ignore_firm_ids))
            for firm_id in ignore_firm_ids:
                firm_data = self._get_single_firm_data(firm_id)
                if firm_data:
                    ignore_list_firms_data.append(firm_data)
                else:
                    self._logger.warning(f"Firm ID {firm_id} from ignore list not found in DB.")
                self.progress.update(task_fetch, advance=1)
        if not ignore_list_firms_data:
            self._logger.info("No firms from ignore list found in DB.")
            self._summarize_errors()
            return
        original_start_date = self.start_date_iso
        ignore_list_days = self.config.get('ignore_list_date_range_days', 30)
        self.start_date_iso = DateUtils.get_date_before_n_days(ignore_list_days, self.end_date_iso)
        self._logger.info(f"Using date range for ignore list: {self.start_date_iso} to {self.end_date_iso}")
        with self.progress:
            task_process = self.progress.add_task("[cyan]Processing ignore list...", total=len(ignore_list_firms_data))
            for firm_data in ignore_list_firms_data:
                await self._process_single_firm(firm_data, task_process, update_db=True)
                await self.session_manager.random_sleep(short=False)  # Changed to await
                self.progress.update(task_process, advance=1)
                if self.session_manager.use_proxy and self.config.get('rotate_proxy_between_firms', True):
                    self.session_manager.rotate_proxy()
        self.start_date_iso = original_start_date
        self._summarize_errors()
        self._logger.info("Ignore list processing finished.")

    def _get_filtered_law_firms(self) -> List[Dict[str, Any]]:
        """Gets all law firms and filters them."""
        all_firms = self._get_all_law_firms()
        if not all_firms: return []
        tracker_skip_ids = self.processing_tracker.get_skip_list()
        loaded_ignore_ids = set(self.ignore_firm_data.keys())
        combined_skip_ids = loaded_ignore_ids.union(tracker_skip_ids)
        filtered_firms = []
        skipped_ignored, skipped_tracker, skipped_updated = 0, 0, 0
        check_last_updated = self.config.get('skip_firms_updated_today', True)
        for firm in all_firms:
            firm_id = firm.get('ID')
            if not firm_id: continue
            if firm_id in combined_skip_ids:
                if firm_id in loaded_ignore_ids:
                    skipped_ignored += 1
                else:
                    skipped_tracker += 1
                continue
            if check_last_updated and firm.get('AdArchiveLastUpdated') == self.end_date_iso:
                skipped_updated += 1
                continue
            filtered_firms.append(firm)
        self._logger.info(f"Processing {len(filtered_firms)} firms. Skipped: {skipped_ignored} (ignored), "
                          f"{skipped_tracker} (tracker), {skipped_updated} (updated today).")
        if filtered_firms and self.config.get('shuffle_firm_order', True): random.shuffle(filtered_firms)
        return filtered_firms

    def _get_all_law_firms(self) -> List[Dict[str, Any]]:
        """Fetches all law firm records from the database."""
        try:
            projection = "ID, #nm, AdArchiveLastUpdated, NumAds"
            expression_names = {"#nm": "Name"}
            all_firms = self.law_firm_db.get_all_records(projection_expression=projection,
                                                         expression_attribute_names=expression_names)
            self._logger.info(f"Fetched {len(all_firms)} total law firm records.")
            return all_firms
        except Exception as e:
            self._logger.error(f"Failed to get law firms: {e}", exc_info=self.config.get('verbose', False));
            return []

    def _get_single_firm_data(self, firm_id: str) -> Optional[Dict[str, Any]]:
        """Fetches data for a single law firm by ID."""
        if not firm_id or not isinstance(firm_id, str): return None
        try:
            projection = "ID, #nm, AdArchiveLastUpdated, NumAds, pageAlias, category, imageURI"
            expression_names = {"#nm": "Name"}
            response = self.law_firm_db.table.query(KeyConditionExpression=Key('ID').eq(firm_id),
                                                    ProjectionExpression=projection,
                                                    ExpressionAttributeNames=expression_names)
            items = response.get('Items', [])
            if items:
                return items[0]
            else:
                self._logger.debug(f"Firm ID {firm_id} not found.");
                return None
        except Exception as e:
            self._logger.error(f"Error fetching firm {firm_id}: {e}",
                               exc_info=self.config.get('verbose', False));
            return None


    async def _process_single_firm(self, firm_data: Dict[str, Any], progress_task_id, update_db: bool = True):
        """Processes ads for one firm, including fetching, AI enhancement, and DB operations."""
        firm_id = firm_data.get('ID')
        name = firm_data.get('Name')
        if not firm_id or not name: self._logger.error(f"Skipping firm due to missing ID/Name: {firm_data}"); return
        if hasattr(self, 'progress') and self.progress and progress_task_id is not None:
            try:
                # Only update if this is the main progress instance (not single firm mode)
                self.progress.update(progress_task_id, description=f"[cyan]Processing: {name[:35].ljust(35)}")
            except:
                pass  # Ignore progress update errors
        self._logger.info(f"Processing firm: {name} (ID: {firm_id})")
        processed_ads_list: Optional[List[Dict[str, Any]]] = None
        success_flag = True
        error_msg = "Unknown processing error"
        ads_fetched_count = 0
        try:
            raw_ad_groups = await self._fetch_all_ad_payloads(firm_id)
            ads_fetched_count = len(raw_ad_groups) if raw_ad_groups is not None else 0
            if raw_ad_groups is None: raise RuntimeError(
                f"Failed to fetch ad payloads ({getattr(self.api_client, 'last_error_type', 'Fetch Error')})")
            if not raw_ad_groups:
                processed_ads_list = []
            else:
                processed_ads_list = await self.ad_processor.process_raw_ads(raw_ad_groups, name)
                if processed_ads_list is None:
                    error_msg = "AdProcessor returned None";
                    success_flag = False;
                    processed_ads_list = []
                elif not isinstance(processed_ads_list, list):
                    error_msg = f"AdProcessor returned non-list ({type(processed_ads_list).__name__})"
                    success_flag = False
                    processed_ads_list = []
                else:
                    valid_ads_intermediate = [ad for ad in processed_ads_list if isinstance(ad, dict)]
                    if len(processed_ads_list) - len(valid_ads_intermediate) > 0:
                        self._logger.warning(f"AdProcessor returned invalid items for {name}.")
                    processed_ads_list = valid_ads_intermediate
                    if processed_ads_list:
                        for ad_dict in processed_ads_list:
                            for key_to_remove in self.KEYS_TO_REMOVE_BEFORE_SAVE:
                                if key_to_remove in ad_dict: ad_dict.pop(key_to_remove, None)
        except (RuntimeError, RequestException, json.JSONDecodeError) as fetch_proc_e:
            error_msg = str(fetch_proc_e) or "Error during fetch/process"
            self._logger.error(f'Error for firm {name}: {error_msg}', exc_info=self.config.get('verbose', False))
            success_flag = False
            last_api_error = getattr(self.api_client, 'last_error_type', 'None')
            if isinstance(fetch_proc_e, RequestException) or last_api_error in ["SessionIssue", "PayloadSessionIssue",
                                                                                "PayloadBlock", "Block",
                                                                                "NetworkError"]:
                if await self.session_manager.create_new_session():
                    self._logger.info("Session recovery successful.")  # Changed to await
                else:
                    self._logger.error("Session recovery FAILED.")
        except Exception as e:
            error_msg = str(e) or "Unhandled exception in fetch/process"
            self._logger.error(f'Unhandled error for {name}: {error_msg}', exc_info=True)
            success_flag = False

        items_to_batch_write = []
        total_items_processed_locally = 0
        total_items_updated = 0
        total_items_added = 0
        total_fields_changed = 0
        processed_ads_map = {}
        if success_flag and processed_ads_list:
            try:
                keys_to_fetch = []
                for ad_dict in processed_ads_list:  # ad_dict here is already snake_case from AdProcessor
                    ad_pascal = self.fb_ad_db.snake_or_camel_to_pascal_case(ad_dict)  # Convert to PascalCase for DB
                    ad_id = ad_pascal.get('AdArchiveID')
                    start_date = ad_pascal.get('StartDate')
                    if ad_id and start_date:
                        pk_tuple = (str(ad_id), str(start_date))
                        keys_to_fetch.append({'AdArchiveID': str(ad_id), 'StartDate': str(start_date)})
                        processed_ads_map[pk_tuple] = ad_pascal
                if keys_to_fetch:
                    existing_items_list = list(self.fb_ad_db.batch_get_items(keys_to_fetch))
                    existing_items_map = {(item['AdArchiveID'], item['StartDate']): item for item in
                                          existing_items_list}
                    processing_date = self.end_date_iso
                    for pk_tuple, new_ad_pascal in processed_ads_map.items():
                        total_items_processed_locally += 1
                        fields_changed_count = 0
                        existing_ad = existing_items_map.get(pk_tuple)
                        final_item_to_write = self.fb_ad_db.sanitize_record(new_ad_pascal)
                        final_item_to_write['LastUpdated'] = processing_date
                        is_active_new = final_item_to_write.get('IsActive', False)
                        if existing_ad:
                            existing_end_date = existing_ad.get('EndDate')
                            if existing_end_date and processing_date and existing_end_date >= processing_date:
                                final_item_to_write['EndDate'] = existing_end_date
                            elif is_active_new is True and (
                                    not existing_end_date or existing_end_date < processing_date):
                                final_item_to_write['EndDate'] = processing_date
                            for key, new_value in final_item_to_write.items():
                                if key in ['AdArchiveID', 'StartDate', 'LastUpdated']: continue
                                if new_value != existing_ad.get(key): fields_changed_count += 1
                            if fields_changed_count > 0:
                                total_items_updated += 1;
                                total_fields_changed += fields_changed_count
                            elif final_item_to_write.get('LastUpdated') != existing_ad.get('LastUpdated'):
                                total_items_updated += 1
                            else:
                                continue  # Skip if no changes
                        else:
                            total_items_added += 1
                        items_to_batch_write.append(final_item_to_write)
            except Exception as get_compare_err:
                error_msg = f"Error during get/compare for {name}: {get_compare_err}"
                self._logger.error(error_msg, exc_info=True)
                success_flag = False

        db_save_success_count = 0
        db_save_failure_count = 0
        if success_flag and items_to_batch_write:
            try:
                db_save_success_count, db_save_failure_count = self.fb_ad_db.batch_insert_items(items_to_batch_write)
                if db_save_failure_count > 0: error_msg = f"FBAdArchive DB Save Failed for {db_save_failure_count} items"; success_flag = False
            except Exception as ad_db_e:
                error_msg = f"FBAdArchive DB Batch Upsert Exception: {str(ad_db_e)[:200]}"
                self._logger.error(f"Exception for {name}: {ad_db_e}", exc_info=self.config.get('verbose', False))
                success_flag = False
                db_save_failure_count = len(items_to_batch_write)

        if update_db:
            num_ads_to_record = len(processed_ads_map)
            if success_flag:
                try:
                    update_data = {'AdArchiveLastUpdated': self.end_date_iso, 'NumAds': num_ads_to_record}
                    key_dict = {'ID': firm_id, 'Name': name}
                    if hasattr(self, 'law_firm_db') and not self.law_firm_db.update_item(key_dict, update_data):
                        if error_msg == "Unknown processing error": error_msg = "LawFirm DB Update Failed"
                except Exception as firm_db_e:
                    if error_msg == "Unknown processing error": error_msg = f"LawFirm DB Update Exception: {str(firm_db_e)[:100]}"
            else:
                self._logger.warning(f"Skipping LawFirm DB update for {name} due to earlier failure.")

        if not success_flag:
            self._logger.warning(f"Processing FAILED for {name}. Error: {error_msg}")
            if hasattr(self, 'error_firms'): self.error_firms.append(
                {'ID': firm_id or 'N/A', 'Name': name or 'N/A', 'Error': error_msg[:250]})
        else:
            self._logger.info(
                f"Processing SUCCEEDED for {name}. Ads Fetched: {ads_fetched_count}, Processed Locally: {total_items_processed_locally}, DB Writes: {len(items_to_batch_write)}")

    async def _fetch_all_ad_payloads(self, company_id: str) -> Optional[List[List[Dict[str, Any]]]]:
        """Fetches all ad result pages for a company."""
        all_ads_raw_groups = []
        forward_cursor = None
        page_num = 1
        max_pages = self.config.get('max_ad_pages', 50)
        total_ads_reported = 0
        while page_num <= max_pages:
            await self.session_manager.random_sleep(short=True)  # Changed to await
            if self.session_manager.use_proxy and self.config.get('rotate_proxy_per_page', True):
                self.session_manager.rotate_proxy()
            payload_response = await self.api_client.fetch_ads_page(company_id, self.start_date_iso, self.end_date_iso,
                                                                    forward_cursor)  # Changed to await
            if payload_response is None:
                return None if page_num == 1 else all_ads_raw_groups
            payload_data = payload_response.get('payload')
            if not isinstance(payload_data, dict): break
            results_data = payload_data.get('results')
            if not isinstance(results_data, list): break
            if page_num == 1: total_ads_reported = int(payload_data.get('totalCount', 0))
            if results_data:
                valid_groups = [group for group in results_data if isinstance(group, list) and group]
                all_ads_raw_groups.extend(valid_groups)
            forward_cursor = payload_data.get('forwardCursor')
            if not forward_cursor or not payload_data.get('hasNextPage', bool(forward_cursor)): break
            page_num += 1
        self._logger.info(f"Retrieved {len(all_ads_raw_groups)} groups for {company_id}.")
        return all_ads_raw_groups

    def _summarize_errors(self):
        """Summarizes errors encountered during the run."""
        if self.error_firms:
            self.processing_tracker.update_processing_results(self.end_date_iso, self.error_firms)
        else:
            self._logger.info(f"No errors recorded for {self.end_date_iso}.")
        if self.error_firms:
            sorted_errors = sorted(self.error_firms, key=lambda x: x.get('Name', ''))
            table = Table(title=f"Error Summary ({self.end_date_iso})", show_lines=True, min_width=80)
            table.add_column("Firm ID", style="dim cyan", width=18)
            table.add_column("Firm Name", style="white", width=30, overflow="ellipsis")
            table.add_column("Error", style="red", overflow="fold", min_width=40)
            unique_errors = {}
            for firm in sorted_errors:
                error_msg = firm.get('Error', 'Unknown error')
                unique_errors[error_msg] = unique_errors.get(error_msg, 0) + 1
                table.add_row(firm.get('ID', 'N/A'), firm.get('Name', 'N/A'), error_msg)
            self.console.print("\n")
            self.console.print(table)
            error_summary_table = Table(title="Error Counts by Type", min_width=80)
            error_summary_table.add_column("Error Message", style="yellow", overflow="fold", min_width=50)
            error_summary_table.add_column("Count", style="dim white", justify="right", width=10)
            for msg, count in sorted(unique_errors.items(), key=lambda item: item[1], reverse=True):
                error_summary_table.add_row(msg, str(count))
            self.console.print(error_summary_table)
            self.console.print(
                Panel(f"Total firms with errors: [bold red]{len(self.error_firms)}[/]", title="Run Status",
                      border_style="red"))
        else:
            self.console.print(
                Panel(f"Run completed successfully for {self.end_date_iso}. No errors.", title="Run Status",
                      style="bold green", border_style="green"))

    async def add_law_firm_interactive(self):  # Changed to async
        """Interactively search and add a law firm by name."""
        if not self.session_manager.get_session():
            if not await self.session_manager.create_new_session(): self.console.print(
                "[red]Failed session.[/]"); return  # Changed to await
        should_quit_entirely = False
        while not should_quit_entirely:
            try:
                law_firm_name = self.console.input("Firm [bold]name[/] ('q' to quit): ").strip()
                if law_firm_name.lower() == 'q': should_quit_entirely = True; break
                if not law_firm_name: continue
                results = await self.api_client.search_company_by_name(law_firm_name)  # Changed to await
                if results is None: self.console.print(
                    f"[red]API search fail: {self.api_client.last_error_type}.[/]"); continue
                valid_options = [r for r in results if isinstance(r, dict) and not r.get('pageIsDeleted', False)]
                if not valid_options: self.console.print(f"[yellow]No active pages.[/]"); continue
                db_firm_names = {option.get('id'): (
                    db_data['Name'] if (db_data := self._get_single_firm_data(option.get('id'))) else None) for option
                    in valid_options if option.get('id')}
                table = Table(title="[bold green]Select page:[/]", header_style="bold blue")
                table.add_column("#", width=3)
                table.add_column("FB Page Name", min_width=25)
                table.add_column("Page ID", width=18)
                table.add_column("Category", min_width=15)
                table.add_column("Existing DB Name", min_width=25)
                for i, company in enumerate(valid_options, 1):
                    fb_page_id = company.get('id')
                    existing_db_name_display = db_firm_names.get(fb_page_id, "[dim]-[/]")
                    table.add_row(str(i), company.get('name'), fb_page_id, company.get('category', 'N/A'),
                                  existing_db_name_display)
                self.console.print(table)
                while True:
                    sel = self.console.input("# to add ('s' search again, 'q' quit): ").strip().lower()
                    if sel == 'q': should_quit_entirely = True; break
                    if sel == 's': break
                    try:
                        sel_int = int(sel)
                        if 1 <= sel_int <= len(valid_options):
                            selected = valid_options[sel_int - 1]
                            selected_page_id = selected.get('id')
                            existing_db_name_for_logic = db_firm_names.get(selected_page_id)
                            confirm = self.console.input(
                                f"Add '{selected.get('name')}' (ID: {selected_page_id})? (y/n): ").lower()
                            if confirm == 'y': await self._add_firm_to_db_and_save_pic(selected,
                                                                                       existing_db_name=existing_db_name_for_logic); break  # Changed to await
                        else:
                            self.console.print("[red]Invalid selection.[/]")
                    except ValueError:
                        self.console.print("[red]Invalid input.[/]")
                if should_quit_entirely: break
            except Exception as e:
                self.console.print(f"[bold red]Error: {e}")
                self._logger.error(f"Error interactive: {e}", exc_info=True)
                should_quit_entirely = True
        self.console.print("[yellow]Exiting interactive add.[/]")

    async def _add_firm_to_db_and_save_pic(self, firm_data: Dict[str, Any],
                                           existing_db_name: Optional[str] = None):  # Changed to async
        """Helper to add/update firm in DB, save pic, and optionally scrape."""
        firm_id = firm_data.get('id');
        firm_name_from_fb = firm_data.get('name')
        if not firm_id or not firm_name_from_fb: self.console.print("[red]Error: Firm data missing ID/Name.[/]"); return
        default_name_prompt = f"'{existing_db_name}' (DB)" if existing_db_name else f"'{firm_name_from_fb}' (FB)"
        new_name_input = self.console.input(f"Enter name (Enter for {default_name_prompt}): ").strip()
        final_firm_name_to_save = new_name_input or existing_db_name or firm_name_from_fb
        try:
            db_record = {'ID': str(firm_id), 'Name': final_firm_name_to_save, 'pageAlias': firm_data.get('pageAlias'),
                         'category': firm_data.get('category'), 'imageURI': firm_data.get('imageURI'),
                         'AdArchiveLastUpdated': None, 'NumAds': 0}
            if self.law_firm_db.add_or_update_record(db_record):
                self.console.print(f"[green]Added/Updated '{final_firm_name_to_save}' to DB.[/]")
            else:
                self.console.print(f"[yellow]DB add/update for '{final_firm_name_to_save}' may have failed.[/]")
            if firm_data.get('imageURI'): self.image_handler.save_profile_picture(str(firm_id), firm_data['imageURI'],
                                                                                  self.law_firm_dir)
            scrape_now = self.console.input(f"Scrape ads for '{final_firm_name_to_save}'? (y/n): ").strip().lower()
            if scrape_now == 'y':
                if not self.session_manager.get_session() or not self.session_manager.get_session_data().get('fb_dtsg'):
                    if not await self.session_manager.create_new_session(): self.console.print(
                        "[red]Failed session refresh.[/]"); return  # Changed to await
                await self.run_single_firm_scrape(str(firm_id))  # This is already async
        except Exception as e:
            self.console.print(f"[red]Error saving firm '{final_firm_name_to_save}': {e}");
            self._logger.error(
                f"Error adding firm {firm_id}: {e}", exc_info=True)

    async def add_attorney_by_page_id(self):  # Changed to async
        """Interactively add a law firm using its Facebook Page ID."""
        if not self.session_manager.get_session():
            if not await self.session_manager.create_new_session(): self.console.print(
                "[red]Failed session.[/]"); return  # Changed to await
        should_quit = False
        while not should_quit:
            try:
                page_id = self.console.input("FB [bold]Page ID[/] ('q' to quit): ").strip()
                if page_id.lower() == 'q': should_quit = True; break
                if not page_id.isdigit(): self.console.print("[red]Invalid Page ID.[/]"); continue
                results = await self.api_client.search_company_by_name(page_id)  # Changed to await
                if results is None: self.console.print(
                    f"[red]API search fail: {self.api_client.last_error_type}.[/]"); continue
                selected = next((r for r in results if
                                 isinstance(r, dict) and r.get('id') == page_id and not r.get('pageIsDeleted')), None)
                if not selected: self.console.print(f"[yellow]No active page for ID '{page_id}'.[/]"); continue
                existing_db_name = None;
                db_firm_data = self._get_single_firm_data(page_id)
                if db_firm_data and 'Name' in db_firm_data: existing_db_name = db_firm_data['Name']
                table = Table(title="[bold green]Found page:[/]", show_header=False, box=None)
                table.add_row("FB Page Name:", f"[cyan]{selected.get('name', 'N/A')}[/]")
                if existing_db_name and existing_db_name != selected.get('name'): table.add_row("Existing DB Name:",
                                                                                                f"[green]{existing_db_name}[/]")
                table.add_row("ID:", f"[magenta]{selected.get('id', 'N/A')}[/]");
                table.add_row("Category:", f"[yellow]{selected.get('category', 'N/A')}[/]")
                self.console.print(table)
                confirm = self.console.input("Add/Update this firm? (y/n): ").lower()
                if confirm == 'y':
                    await self._add_firm_to_db_and_save_pic(selected,
                                                            existing_db_name=existing_db_name)  # Changed to await
                else:
                    self.console.print("[yellow]Firm skipped.[/]")
            except Exception as e:
                self.console.print(f"[red]Error: {e}");
                self._logger.error(f"Error add by ID: {e}",
                                   exc_info=True);
                should_quit = True
        self.console.print("[yellow]Exiting add by ID.[/]")

    def search_law_firm_in_db(self, firm_id: str):
        """Searches the LawFirms database for an ID."""
        if not firm_id or not isinstance(firm_id, str): self.console.print("[red]Invalid Firm ID.[/]"); return
        firm_data = self._get_single_firm_data(firm_id)
        if firm_data:
            table = Table(title="[bold green]Law Firm Found:[/]", show_header=False, box=None)
            for k, v in firm_data.items(): table.add_row(f"{k}:", str(v))
            self.console.print(table)
        else:
            self.console.print(f"[red]No firm found with ID: {firm_id}[/]")

    def show_failed_firms(self, date: Optional[str] = None, add_to_skip: bool = False):
        """Shows failed firms, optionally by date and adds to skip list."""
        try:
            if date: datetime.strptime(date, '%Y%m%d')
            failed_firms = self.processing_tracker.get_failed_firms(date, add_to_skip)
            title = f"Failures on {date}" if date else "All Failures"
            if failed_firms:
                table = Table(title=title, header_style="bold magenta")
                table.add_column("Date(s)" if not date else "Date");
                table.add_column("ID");
                table.add_column("Name");
                table.add_column("Last Error")
                for firm in failed_firms:
                    dates = firm.get('Date', []);
                    date_str = ", ".join(sorted(dates, reverse=True)) if isinstance(dates, list) else str(dates)
                    table.add_row(date_str if not date else date, firm.get('ID', 'N/A'), firm.get('Name', 'N/A'),
                                  firm.get('Error', 'N/A'))
                self.console.print(table)
                if add_to_skip: self.console.print(f"[green]Added {len(failed_firms)} firms to skip list.[/]")
            else:
                self.console.print(f"[green]No failures recorded{' for this date' if date else ''}.[/]")
        except ValueError:
            self.console.print(f"[red]Invalid date format: '{date}'. Use YYYYMMDD.[/]")
        except Exception as e:
            self.console.print(f"[red]Error showing failed firms: {e}");
            self._logger.error(
                "Error in show_failed_firms", exc_info=True)

    def show_failure_summary(self, start_date: Optional[str] = None, end_date: Optional[str] = None):
        """Shows summary of processing failures."""
        try:
            if start_date: datetime.strptime(start_date, '%Y%m%d')
            if end_date: datetime.strptime(end_date, '%Y%m%d')
            if start_date and end_date and start_date > end_date: self.console.print(
                "[red]Start date after end date.[/]"); return
            summary = self.processing_tracker.get_summary(start_date, end_date)
            title = "Processing Failure Summary" + (
                f" (Range: {start_date or 'Start'} to {end_date or 'End'})" if start_date or end_date else "")
            self.console.print_json(data={"title": title, "summary": summary})
        except ValueError:
            self.console.print(f"[red]Invalid date format. Use YYYYMMDD.[/]")
        except Exception as e:
            self.console.print(f"[red]Error showing summary: {e}");
            self._logger.error("Error in show_failure_summary",
                               exc_info=True)

    def add_firms_to_skip_list(self, firm_ids: List[str]):
        """Adds firms to the skip list."""
        valid_ids = [fid for fid in firm_ids if isinstance(fid, str) and fid.strip()]
        if not valid_ids: self.console.print("[red]No valid firm IDs to add.[/]"); return
        try:
            self.processing_tracker.add_to_skip_list(valid_ids)
            self.console.print(f"[green]Added/Updated {len(valid_ids)} firms in skip list.[/]")
        except Exception as e:
            self.console.print(f"[red]Error adding to skip list: {e}");
            self._logger.error("Error adding to skip",
                               exc_info=True)

    def remove_firms_from_skip_list(self, firm_ids: List[str]):
        """Removes firms from the skip list."""
        valid_ids = [fid for fid in firm_ids if isinstance(fid, str) and fid.strip()]
        if not valid_ids: self.console.print("[red]No valid firm IDs to remove.[/]"); return
        try:
            self.processing_tracker.remove_from_skip_list(valid_ids)
            self.console.print(f"[green]Removed {len(valid_ids)} firms from skip list.[/]")
        except Exception as e:
            self.console.print(f"[red]Error removing from skip list: {e}");
            self._logger.error(
                "Error removing from skip", exc_info=True)

    def show_skip_list(self):
        """Displays the current skip list."""
        try:
            skip_list = sorted(list(self.processing_tracker.get_skip_list()))
            if skip_list:
                self.console.print(
                    Panel("\n".join(skip_list), title=f"Skip List ({len(skip_list)})", border_style="yellow"))
            else:
                self.console.print("[green]Skip list is empty.[/]")
        except Exception as e:
            self.console.print(f"[red]Error showing skip list: {e}");
            self._logger.error("Error showing skip list",
                               exc_info=True)

    async def lookup_ad_by_id(self, ad_id):  # Changed to async
        """Look up a Facebook ad by its archive ID."""
        try:
            self.console.print(f"Looking up ad ID: {ad_id}")
            ad_items = self.fb_ad_db.get_ads_by_archive_id(ad_id)
            if not ad_items:
                self.console.print(f"[yellow]No ads found with ID: {ad_id}[/]")
                if input("Search firm by name? (y/n): ").lower() == 'y':
                    if not self.session_manager.get_session():
                        if not await self.session_manager.create_new_session(): return  # Changed to await
                    await self._search_law_firm_interactive()  # Changed to await
                return
            table = Table(title=f"Ad Details - ID: {ad_id}", expand=True)
            table.add_column("Field");
            table.add_column("Value", overflow="fold")
            for i, ad in enumerate(ad_items):
                if i > 0: table.add_row("---", "---")
                processed_ad = self.fb_ad_db.process_record(ad)
                for field, value in processed_ad.items():
                    table.add_row(field,
                                  str(value) if not isinstance(value, (dict, list)) else json.dumps(value, indent=2))
            self.console.print(table)
            while True:
                choice = input("Another ad ID, 's' search firm, 'q' quit: ").strip().lower()
                if choice == 'q':
                    break
                elif choice == 's':
                    if not self.session_manager.get_session():
                        if not await self.session_manager.create_new_session(): break  # Changed to await
                    await self._search_law_firm_interactive();
                    break  # Changed to await
                else:
                    await self.lookup_ad_by_id(choice);
                    break  # Changed to await
        except Exception as e:
            self.console.print(f"[red]Error: {e}");
            self._logger.error(f"Error lookup_ad {ad_id}", exc_info=True)

    async def _search_law_firm_interactive(self):  # Changed to async
        """Interactive search for law firms by name."""
        try:
            if not self.session_manager.get_session():
                if not await self.session_manager.create_new_session(): self.console.print(
                    "[red]Failed session.[/]"); return  # Changed to await
            search_term = input("Firm name to search ('q' quit): ").strip()
            if search_term.lower() == 'q': return
            if not search_term: return
            results = await self.api_client.search_company_by_name(search_term)  # Changed to await
            if results is None: self.console.print(
                f"[red]API search fail: {self.api_client.last_error_type}.[/]"); return
            valid_options = [r for r in results if isinstance(r, dict) and not r.get('pageIsDeleted', False)]
            if not valid_options: self.console.print(f"[yellow]No active pages for '{search_term}'.[/]"); return
            db_firm_names = {option.get('id'): (
                db_data['Name'] if (db_data := self._get_single_firm_data(option.get('id'))) else None) for option in
                valid_options if option.get('id')}
            table = Table(title="[bold green]Found firms:[/]", header_style="bold blue")
            table.add_column("#");
            table.add_column("FB Page Name");
            table.add_column("Page ID");
            table.add_column("Category");
            table.add_column("Existing DB Name")
            for i, company in enumerate(valid_options, 1):
                fb_page_id = company.get('id');
                existing_db_name_display = db_firm_names.get(fb_page_id, "[dim]-[/]")
                table.add_row(str(i), company.get('name'), fb_page_id, company.get('category', 'N/A'),
                              existing_db_name_display)
            self.console.print(table)
            while True:
                choice = input("# to view details, 'q' quit: ").strip().lower()
                if choice == 'q': break
                try:
                    sel_int = int(choice)
                    if 1 <= sel_int <= len(valid_options):
                        selected = valid_options[sel_int - 1];
                        selected_page_id = selected.get('id');
                        selected_name = selected.get('name')
                        existing_db_name = db_firm_names.get(selected_page_id)
                        self.console.print(f"Selected: [cyan]{selected_name}[/] (ID: {selected_page_id})")
                        if existing_db_name:
                            self.console.print(f"[green]Firm in DB as: {existing_db_name}[/]")
                        else:
                            self.console.print(f"[yellow]Firm not in DB.[/]")
                            if input("Add to DB? (y/n): ").lower() == 'y': await self._add_firm_to_db_and_save_pic(
                                selected)  # Changed to await
                        if input("Scrape ads? (y/n): ").lower() == 'y': await self.run_single_firm_scrape(
                            selected_page_id)  # Already async
                except ValueError:
                    self.console.print("[red]Invalid input.[/]")
        except Exception as e:
            self.console.print(f"[red]Error: {e}");
            self._logger.error("Error search firms", exc_info=True)


# Ensure DateUtils is available
# from .utils import DateUtils # Adjust import based on your structure


# --- Main Execution ---

async def main_async():
    """Async entry point: parses args, sets up, executes action, cleans up."""
    parser = argparse.ArgumentParser(
        description='Facebook Ads Library Tool',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    # --- Main Actions ---
    action_group = parser.add_mutually_exclusive_group()
    action_group.add_argument('--run', action='store_true',
                              help='Run the full ad scraping process (default if no other action).')
    action_group.add_argument('-a', '--add', action='store_true',
                              help='Interactively search and add a law firm by name.')
    action_group.add_argument('--add-id', action='store_true', help='Interactively add a law firm by Facebook Page ID.')
    action_group.add_argument('-s', '--single', metavar='PAGE_ID',
                              help='Run scraper for a single firm by Facebook Page ID.')
    action_group.add_argument('--process-ignore', action='store_true',
                              help='Process only firms in the loaded ignore list.')
    action_group.add_argument('--search', metavar='PAGE_ID', help='Search for a law firm by ID in the local database.')
    action_group.add_argument('--lookup-ad', metavar='AD_ID', help='Look up a Facebook ad by its archive ID.')

    # --- Processing Tracker Actions ---
    tracker_group = parser.add_argument_group('Processing Tracker Management')
    tracker_group.add_argument('--get-failed', metavar='YYYYMMDD', help='Show failed firms for a specific date.')
    tracker_group.add_argument('--get-all-failed', action='store_true', help='Show all recorded failed firms.')
    tracker_group.add_argument('--summary', action='store_true', help='Show summary of all processing failures.')
    tracker_group.add_argument('--summary-range', nargs=2, metavar=('START', 'END'),
                               help='Show summary for date range (YYYYMMDD).')
    tracker_group.add_argument('--add-to-skip', nargs='+', metavar='FIRM_ID', help='Add firm IDs to skip list.')
    tracker_group.add_argument('--remove-from-skip', nargs='+', metavar='FIRM_ID',
                               help='Remove firm IDs from skip list.')
    tracker_group.add_argument('--show-skip-list', action='store_true', help='Show current skip list.')
    tracker_group.add_argument('--failed-to-skip', metavar='YYYYMMDD',
                               help='Show failed firms for date AND add them to skip list.')

    # --- Date Selection ---
    parser.add_argument('-d', '--date', help='Processing date (MM/DD/YY or YYYYMMDD). Defaults to today.')

    # --- Settings & Configuration ---
    settings_group = parser.add_argument_group('Runtime Settings')
    # Add --use-local explicitly to override default logic
    settings_group.add_argument('--use-local', action='store_true',
                                help='Explicitly force FBAdArchiveManager to use local DynamoDB.')
    settings_group.add_argument('--no-proxy', action='store_true',
                                help='Explicitly disable all proxy usage.')  # <-- ADDED
    settings_group.add_argument('--mobile-proxy', action='store_true', help='Use mobile proxy.')
    settings_group.add_argument('--use-proxy', action='store_true', help='Force proxy use (implies mobile proxy).')
    settings_group.add_argument('--render-html', action='store_true', help='Use Oxylabs HTML rendering.')
    settings_group.add_argument('--debug', action='store_true', help='Enable debug logging.')
    settings_group.add_argument('--verbose', action='store_true', help='Alias for --debug.')
    default_config_path_debug = Path(__file__).parent.parent.parent / 'config' / 'config.yaml'
    settings_group.add_argument('--config-file', default=str(default_config_path_debug),
                                help='Path to config file.')
    settings_group.add_argument('--disable-llava', action='store_true', help='Disable LLaVA.')
    settings_group.add_argument('--disable-gpt', action='store_true', help='Disable GPT.')
    settings_group.add_argument('--disable-deepseek', action='store_true', help='Disable DeepSeek.')
    settings_group.add_argument('--session-refresh-interval', type=int,
                                help='Override session refresh interval (firms).')
    settings_group.add_argument('--proxy-ban-duration', type=int, help='Override proxy ban duration (seconds).')
    settings_group.add_argument('--max-proxy-failures', type=int, help='Override max failures before proxy ban.')
    settings_group.add_argument('--airplane-mode', action='store_true', help='Enable SSLAdapter/TLS rotation.')
    settings_group.add_argument('--defer-image-processing', action='store_true',
                                help='Defer image text extraction for faster scraping (process later with process_image_queue.py)')

    args = parser.parse_args()

    # --- Logging Setup ---
    # Always enable verbose logging for proxy-related modules
    log_level = logging.DEBUG if args.debug or args.verbose else logging.INFO
    logging.getLogger().setLevel(log_level)

    # Set proxy-related modules to DEBUG level regardless of command line flags
    proxy_modules = ["src.lib.fb_ads.fb_session_manager", "src.lib.fb_ads.fb_api_client"]
    for module in proxy_modules:
        logging.getLogger(module).setLevel(logging.DEBUG)

    # Set other library loggers
    lib_level = logging.WARNING if log_level == logging.INFO else logging.DEBUG
    libs_to_adjust = ["urllib3", "botocore", "boto3", "PIL", "requests", "aiohttp", "PIL.PngImagePlugin", "httpx"]
    for lib_name in libs_to_adjust: logging.getLogger(lib_name).setLevel(lib_level)

    # Set this module's logger
    logging.getLogger(__name__).setLevel(log_level)  # Set logger for this module
    logging.info(f"Log level set to: {logging.getLevelName(log_level)}")
    logging.info("Proxy-related modules set to DEBUG level for better error visibility")
    if log_level == logging.DEBUG: logging.debug("Full debug logging enabled for all modules.")

    # --- Date Processing ---
    process_date_iso = None
    process_date_mmddyy = None
    try:
        if args.date:
            try:
                process_date_iso = datetime.strptime(args.date, '%Y%m%d').strftime('%Y%m%d')
            except ValueError:
                process_date_iso = DateUtils.date_to_iso(args.date)  # Assumes DateUtils handles MM/DD/YY etc.
            process_date_mmddyy = datetime.strptime(process_date_iso, '%Y%m%d').strftime('%m/%d/%y')
        else:
            now = datetime.now();
            process_date_iso = now.strftime('%Y%m%d');
            process_date_mmddyy = now.strftime('%m/%d/%y')
        console.print(f"Processing date set to: [cyan]{process_date_iso}[/]")
    except ValueError as ve:
        console.print(f"[bold red]Error parsing date '{args.date}': {ve}. Exiting.[/]");
        sys.exit(1)
    except Exception as e:
        console.print(f"[bold red]Unexpected error processing date '{args.date}': {e}. Exiting.[/]");
        sys.exit(1)

    # --- Load Configuration ---
    config = None
    try:
        logging.info(f"Calling load_config with date: {process_date_mmddyy}")
        config = load_config(process_date_mmddyy)  # Assumes load_config uses the date if needed
        logging.info("Configuration loaded.")

        # --- Update config with args ---
        config['iso_date'] = process_date_iso  # Ensure ISO date is in config
        config['verbose'] = args.debug or args.verbose

        # Always enable proxy by default, unless explicitly disabled
        # Default to residential proxy (not mobile)
        if args.no_proxy:
            config['use_proxy'] = False
            logging.info("Proxy explicitly disabled via --no-proxy flag")
        else:
            config['use_proxy'] = True  # Always enable proxy by default
            config['mobile_proxy'] = args.mobile_proxy  # Use mobile if specified, otherwise residential
            if args.render_html: config['render_html'] = True

            # Log proxy configuration
            proxy_type = "MOBILE" if config['mobile_proxy'] else "RESIDENTIAL"
            logging.info(f"Using {proxy_type} proxy by default")

            if not config.get('oxy_labs_username') or not config.get('oxy_labs_password'):
                logging.warning("Proxy use enabled, but Oxylabs credentials missing in config.")

        # --- *** CORRECTED use_local LOGIC *** ---
        # Priority:
        # --- *** CORRECTED use_local LOGIC (Proxies always enabled) *** ---
        # Priority:
        # 1. Explicit --use-local flag always forces local mode.
        # 2. Otherwise (since proxies are always enabled), default to AWS (use_local=False).
        if args.use_local:
            config['use_local'] = True
            logging.info("FBAdArchiveManager forced to LOCAL mode via --use-local flag.")
        else:
            # Proxies are enabled and --use-local not specified, default to AWS
            config['use_local'] = False
            logging.info("Proxy enabled and --use-local not set, FBAdArchiveManager defaulting to AWS mode.")
        # --- *** END CORRECTION *** ---

        # AI disable flags
        if args.disable_llava: config['disable_llava'] = True
        if args.disable_gpt: config['disable_gpt'] = True
        if args.disable_deepseek: config['disable_deepseek'] = True
        
        # Deferred image processing flag
        if args.defer_image_processing:
            config['defer_image_processing'] = True
            logging.info("Deferred image processing enabled via --defer-image-processing flag")

        # Other config overrides from args (session interval, etc.)
        if args.session_refresh_interval is not None: config['session_refresh_interval'] = args.session_refresh_interval
        if args.proxy_ban_duration is not None: config['proxy_ban_duration'] = args.proxy_ban_duration
        if args.max_proxy_failures is not None: config['max_proxy_failures'] = args.max_proxy_failures
        if args.airplane_mode: config['airplane_mode'] = True

        # --- Set Defaults (ensure use_local is NOT set here again) ---
        config.setdefault('bucket_name', '!!!MISSING!!!')  # Example, ensure your critical defaults are set
        # Always set oxylabs_num_proxies to 500, regardless of what might be in the config
        config['oxylabs_num_proxies'] = 500
        logging.info("Setting number of proxies to 500")
        config.setdefault('proxy_ban_duration', 600)
        config.setdefault('max_proxy_failures', 3)
        config.setdefault('session_refresh_interval', 25)
        config.setdefault('airplane_mode', False)
        # ... (Add ALL other necessary setdefault calls for your config values) ...
        config.setdefault('processing_results_dir', './processing_results')  # Example
        config.setdefault('law_firm_data_dir', './data/law_firms')  # Example

        # Sanity Checks...
        if config['bucket_name'] == '!!!MISSING!!!': raise ValueError("Missing 'bucket_name' in config.")
        # ... (add other essential sanity checks) ...

        logging.debug(
            f"Final configuration snippet: use_proxy={config.get('use_proxy')}, use_local={config.get('use_local')}")
        if config['verbose']: logging.debug(f"Full config (verbose):\n{json.dumps(config, indent=2, default=str)}")

    except FileNotFoundError as fnf_e:
        logging.critical(f"Config load failed: {fnf_e}. Exiting.", exc_info=True);
        sys.exit(1)
    except ValueError as val_e:
        logging.critical(f"Config error: {val_e}. Exiting.", exc_info=True);
        sys.exit(1)
    except Exception as e:
        logging.critical(f"Failed loading config: {e}. Exiting.", exc_info=True);
        sys.exit(1)

    # --- Initialize Orchestrator ---
    orchestrator = None
    aiohttp_session = None
    try:
        # Create aiohttp session for the orchestrator
        aiohttp_session = aiohttp.ClientSession()
        # Orchestrator init uses the finalized config with correct use_local
        orchestrator = FacebookAdsOrchestrator(config, aiohttp_session)
    except Exception as e:
        logging.critical(f"Failed to initialize Orchestrator: {e}. Exiting.", exc_info=True)
        if aiohttp_session:
            await aiohttp_session.close()
        if orchestrator and hasattr(orchestrator, 'cleanup'): await orchestrator.cleanup()
        sys.exit(1)

    # --- Execute Action ---
    action_taken = False
    exit_code = 0
    try:
        if args.add:
            action_taken = True;
            await orchestrator.add_law_firm_interactive()
        elif args.add_id:
            action_taken = True;
            await orchestrator.add_attorney_by_page_id()
        elif args.single:
            action_taken = True;
            await orchestrator.run_single_firm_scrape(args.single)
        elif args.search:
            action_taken = True;
            orchestrator.search_law_firm_in_db(args.search)  # Assuming sync method
        elif args.lookup_ad:
            action_taken = True;
            await orchestrator.lookup_ad_by_id(args.lookup_ad)
        elif args.process_ignore:
            action_taken = True;
            await orchestrator.run_ignore_list_processing()
        # Tracker Actions (assuming these are synchronous)
        elif args.get_failed:
            action_taken = True;
            orchestrator.show_failed_firms(args.get_failed)
        elif args.get_all_failed:
            action_taken = True;
            orchestrator.show_failed_firms()
        elif args.summary:
            action_taken = True;
            orchestrator.show_failure_summary()
        elif args.summary_range:
            action_taken = True
            if len(args.summary_range) == 2:
                orchestrator.show_failure_summary(args.summary_range[0], args.summary_range[1])
            else:
                console.print("[red]--summary-range requires two dates.[/]");
                exit_code = 1
        elif args.add_to_skip:
            action_taken = True;
            orchestrator.add_firms_to_skip_list(args.add_to_skip)
        elif args.remove_from_skip:
            action_taken = True;
            orchestrator.remove_firms_from_skip_list(args.remove_from_skip)
        elif args.show_skip_list:
            action_taken = True;
            orchestrator.show_skip_list()
        elif args.failed_to_skip:
            action_taken = True;
            orchestrator.show_failed_firms(args.failed_to_skip, add_to_skip=True)

        # Default or explicit run
        elif not action_taken or args.run:
            if not action_taken:
                console.print("[blue]Defaulting to full scrape...[/]")
            else:
                console.print("[blue]Executing full scrape via --run...[/]")
            action_taken = True
            await orchestrator.run_full_scrape()  # <--- This calls _process_single_firm
        else:
            logging.warning("No specific action identified or executed.")
            console.print("[yellow]No action performed. Use --run or another action flag.[/]")

    except KeyboardInterrupt:
        console.print("\n[yellow]Operation cancelled by user.[/]");
        exit_code = 1
    except Exception as e:
        logging.exception("Uncaught error during execution.")  # Logs traceback
        console.print(f"\n[bold red]:cross_mark: Unexpected error: {e}[/]")
        if config and config.get('verbose') and hasattr(console, 'print_exception'):
            console.print_exception(show_locals=True)
        exit_code = 1
    finally:
        if orchestrator: await orchestrator.cleanup()
        if aiohttp_session:
            await aiohttp_session.close()
        logging.info(f"Script exiting with code {exit_code}.")
        sys.exit(exit_code)


if __name__ == '__main__':
    # Setup basic logging immediately in case of early errors
    logging.basicConfig(level=logging.INFO, format="%(asctime)s [%(levelname)s] %(message)s")

    # Ensure an event loop exists for asyncio - Use asyncio.run for simplicity
    try:
        # Import necessary libraries for top-level execution if needed
        import asyncio
        from pathlib import Path  # For default config path

        # Call the main async function
        asyncio.run(main_async())

    except KeyboardInterrupt:
        # This is caught inside main_async now, but keep a top-level catch just in case.
        print("\n[yellow]Execution interrupted by user (Top Level).[/]", file=sys.stderr)
        sys.exit(1)
    except Exception as main_e:
        # Catch any errors that might occur *before* logging is fully configured in main_async
        print(f"\n[CRITICAL ERROR] Script failed during initial setup: {main_e}", file=sys.stderr)
        # Log if possible
        logging.critical("Critical error at top level", exc_info=True)
        sys.exit(1)
    # No explicit finally needed here as sys.exit is called within main_async's finally

# TODO: Make sure takes greater of current date or end date for scraping
