import asyncio
import json
import logging
import os
import re
from datetime import date as DateType, datetime
from pathlib import Path
from typing import Dict, Any, Optional, List

import fasteners
from botocore.exceptions import ClientError
from playwright.async_api import TimeoutError as PlaywrightTimeoutError, <PERSON>rror as PlaywrightError

from .case_relevance_engine import CaseRelevance<PERSON>ng<PERSON>
from .case_transfer_handler import CaseTransferHandler
from .file_manager import PacerFileManager
from .navigator import PacerNavigator
from .pacer_document_downloader import PacerDocumentDownloader
from .pacer_utils import parse_date_str
from ..config import PROJECT_ROOT
from ..district_courts_manager import DistrictCourtsManager
from ..gpt4_interface import GPT4
# Fix incorrect import path for HTMLCaseParser
try:
    from ..core.html.html_case_parser import HTMLCaseParser
except ImportError:
    # Alternate import path
    from src.lib.core.html.html_case_parser import HTMLCaseParser
from ..pacer_manager import Pacer<PERSON>anager
from ..s3_manager import S3Manager

DEFAULT_CONFIG_DIR = Path(PROJECT_ROOT) / 'src' / 'config' / 'pacer'


class DocketProcessor:
    """Processes a single docket report page, including parsing, relevance checks, and download orchestration."""

    def __init__(self,
                 court_id: str,
                 iso_date: str,
                 start_date_obj: DateType,
                 end_date_obj: DateType,
                 navigator: Optional[PacerNavigator],
                 file_manager: PacerFileManager,
                 pacer_db: Optional[PacerManager],
                 dc_db: Optional[DistrictCourtsManager],
                 s3_manager: Optional[S3Manager],
                 gpt_interface: Optional[GPT4],
                 config: Dict[str, Any],
                 config_dir: Path = DEFAULT_CONFIG_DIR,
                 logger: Optional[logging.Logger] = None):

        self.court_id = court_id
        self.iso_date = iso_date
        self.start_date_obj = start_date_obj
        self.end_date_obj = end_date_obj
        self.navigator = navigator
        self.file_manager = file_manager
        self.pacer_db = pacer_db
        self.dc_db = dc_db
        self.s3_manager = s3_manager
        self.gpt_interface = gpt_interface
        self.config = config
        self.config_dir = config_dir
        if logger:
            self.logger = logger
        else:
            # Fallback to a default logger for this component
            self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}.{court_id}")
        self.logger.info(f"{self.__class__.__name__}.{court_id} initialized.")
        # self.logger = logging.getLogger(f"{__name__}.DocketProcessor.{court_id}.{id(self) % 1000}")

        context_dl_path_str = self.config.get('context_download_path')
        if context_dl_path_str:
            self.context_download_path = Path(context_dl_path_str)
            self.logger.debug(f"DocketProcessor instance will monitor download path: {self.context_download_path}")
            try:
                self.context_download_path.mkdir(parents=True, exist_ok=True)
            except Exception as e_mkdir:
                self.logger.error(
                    f"Failed to ensure context_download_path exists ({self.context_download_path}): {e_mkdir}")
        else:
            self.context_download_path = None
            self.logger.debug("context_download_path not provided. Download monitoring might be disabled.")

        self._load_configurations()

        try:
            self.usa_defendant_regex_compiled = re.compile(
                self.relevance_config.get('usa_defendant_regex', '(?!)'), re.IGNORECASE)
        except (re.error, TypeError) as e:
            self.logger.error(f"Failed to compile USA defendant regex: {e}. Relevance checks affected.")
            self.usa_defendant_regex_compiled = re.compile('(?!)')

        self.is_removal = False
        self.is_downloaded = False
        self.html_only = self.config.get('html_only', False)
        self.current_case_docket_num_for_logging = None

        project_root_path = Path(PROJECT_ROOT) if isinstance(PROJECT_ROOT, str) else PROJECT_ROOT

        self.logger.info(f"PROJECT_ROOT (as Path) is set to: {project_root_path}")
        self.logger.info(f"config_dir is set to: {self.config_dir}")

        direct_path = project_root_path / 'src' / 'config' / 'pacer' / 'defendants' / 'relevant_defendants.json'
        self.logger.info(f"Trying direct path for relevant_defendants: {direct_path}")
        if direct_path.exists():
            self.logger.info(f"Direct path exists! Using {direct_path} for relevant_defendants")
            self.relevant_defendants_path = direct_path
        else:
            self.logger.error(f"Direct path for relevant_defendants does not exist: {direct_path}")
            relative_defendants_path = self.paths_config.get('relevant_defendants_relative_path')
            self.logger.info(f"Relative defendants path from config: {relative_defendants_path}")
            if relative_defendants_path:

                config_dir_path = self.config_dir if isinstance(self.config_dir, Path) else Path(self.config_dir)

                path_options = [
                    (config_dir_path / Path(relative_defendants_path).name).resolve(),
                    (project_root_path / relative_defendants_path).resolve(),
                    (config_dir_path / relative_defendants_path).resolve()
                ]
                try:
                    rel_path_parts = Path(relative_defendants_path).parts
                    path4 = config_dir_path
                    for part in rel_path_parts: path4 = path4 / part
                    path_options.append(path4.resolve())
                except Exception as e_path_build:
                    self.logger.error(f"Error constructing path part-by-part for relevant_defendants: {e_path_build}")

                found_path = False
                for i, p_opt in enumerate(path_options):
                    self.logger.debug(f"Trying path option #{i + 1} for relevant_defendants: {p_opt}")
                    if p_opt.exists():
                        self.logger.info(f"Using path option #{i + 1} for relevant_defendants: {p_opt}")
                        self.relevant_defendants_path = p_opt
                        found_path = True
                        break
                if not found_path:
                    self.logger.error(f"All path attempts for relevant_defendants.json failed. File not found.")
                    self.relevant_defendants_path = None
            else:
                self.logger.error("Relevant defendants relative path not found in paths_config.json")
                self.relevant_defendants_path = None

        self._relevant_defendants_lower = self._load_relevant_defendants()

        # self._court_lookup will be initialized in __aenter__ via _async_setup
        self._court_lookup: Dict[str, str] = {}

        # Initialize helper classes - these are lightweight and don't do I/O on init
        self.relevance_engine = CaseRelevanceEngine(
            self.relevance_config, self._relevant_defendants_lower,  # _relevant_defendants_lower is sync loaded
            self.usa_defendant_regex_compiled, self.court_id, self.logger
        )
        # Defer initialization of handlers that depend on _court_lookup or DB connections
        # until __aenter__ or first use, if they require async setup.
        # For now, their instantiation here is fine as long as their __init__ is not async.
        self.transfer_handler: Optional[CaseTransferHandler] = None  # Initialize in _async_setup
        self.document_downloader: Optional[PacerDocumentDownloader] = None  # Initialize in _async_setup

    async def _async_setup(self):
        """Performs asynchronous setup tasks, called from __aenter__."""
        self.logger.debug("DocketProcessor._async_setup started.")
        # Initialize court lookup asynchronously
        self._court_lookup = await self._create_district_court_lookup_async()

        # Now initialize handlers that might depend on the fully set up processor or _court_lookup
        if self.pacer_db and self.s3_manager and self.gpt_interface:
            self.transfer_handler = CaseTransferHandler(
                self.pacer_db, self.s3_manager, self.gpt_interface,
                self._court_lookup, self.court_id, self.config, self.logger
                # CHANGED self.current_court_id to self.court_id
            )
        else:
            self.logger.warning(
                "TransferHandler not initialized during async_setup due to missing pacer_db, s3_manager, or gpt_interface.")

        if self.navigator and self.file_manager and self.s3_manager:
            self.document_downloader = PacerDocumentDownloader(
                self.navigator, self.file_manager, self.s3_manager,
                self.stability_config, self.config, self.iso_date, self.court_id, self.logger
                # CHANGED self.current_court_id to self.court_id
            )
        else:
            self.logger.warning(
                "PacerDocumentDownloader not initialized during async_setup due to missing navigator, file_manager, or s3_manager.")

        if self.navigator and not self.navigator.is_ready:
            try:
                # Assuming PacerNavigator might have an async setup or check method
                # If _ensure_ready is from the old version, replicate or adapt
                # For now, let's assume is_ready check is sufficient or _ensure_ready is called elsewhere
                # if hasattr(self.navigator, '_ensure_ready') and callable(getattr(self.navigator, '_ensure_ready')):
                #    await self.navigator._ensure_ready()
                self.logger.debug("Navigator readiness checked/ensured in _async_setup.")
            except Exception as nav_err:
                self.logger.error(f"Error ensuring navigator readiness in _async_setup: {nav_err}", exc_info=True)
                # Depending on severity, might want to raise this
        self.logger.debug("DocketProcessor._async_setup completed.")

    async def __aenter__(self):
        """Async context manager entry."""
        self.logger.debug(f"DocketProcessor context entering for court {self.court_id}...")
        # Reset state variables that might persist across multiple uses if instance is reused
        # (though typically a new instance is created per `async with`)
        self.is_removal = False
        self.is_downloaded = False
        self.current_case_docket_num_for_logging = None  # Reset this too

        await self._async_setup()  # Perform async initializations
        self.logger.info(f"DocketProcessor context entered and async setup complete for court {self.court_id}.")
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if exc_type:
            self.logger.error(f"DocketProcessor context exited with error for court {self.court_id}: {exc_val}",
                              exc_info=(exc_type, exc_val, exc_tb))
        else:
            self.logger.info(f"DocketProcessor context exited successfully for court {self.court_id}.")
        # No specific resource cleanup defined here, but could be added if needed (e.g., closing connections)

    def _load_json_config(self, filename: str) -> Dict[str, Any]:
        """Loads configuration from a JSON file."""
        file_path = self.config_dir / filename
        try:
            if not file_path.exists():
                self.logger.error(f"Configuration file not found: {file_path}")
                return {}
            with open(file_path, 'r') as f:
                data = json.load(f)
                self.logger.info(f"Successfully loaded configuration from {filename}")
                return data
        except json.JSONDecodeError as e:
            self.logger.error(f"Error decoding JSON from {file_path}: {e}", exc_info=True)
            return {}
        except Exception as e:
            self.logger.error(f"Unexpected error loading config file {file_path}: {e}", exc_info=True)
            return {}

    def _load_configurations(self):
        """Loads all required JSON configurations."""
        self.relevance_config = self._load_json_config('relevance_config.json')
        self.stability_config = self._load_json_config('stability_config.json')
        self.paths_config = self._load_json_config('paths_config.json')
        
        # Load ignore_download configuration
        self.ignore_download_config = self._load_json_config('ignore_download/ignore_download.json')

        # Basic validation or default setting (optional but recommended)
        if not self.relevance_config: self.logger.warning("Relevance config is empty.")
        if not self.stability_config: self.logger.warning("Stability config is empty.")
        if not self.paths_config: self.logger.warning("Paths config is empty.")
        if not self.ignore_download_config: self.logger.warning("Ignore download config is empty.")

        # Set defaults for stability config if missing
        self.stability_config.setdefault('timeout_s', 45)
        self.stability_config.setdefault('check_interval_s', 0.5)
        self.stability_config.setdefault('required_duration_s', 2.0)

    def _title_case_versus(self, versus: str) -> str:
        """
        Converts a versus string to proper title case, handling special cases.
        Examples:
            "JOHN DOE v. JANE ROE" -> "John Doe v. Jane Roe"
            "ABC INC. v. XYZ CORP." -> "ABC Inc. v. XYZ Corp."
        """
        # Words that should remain lowercase unless they start a name
        lowercase_words = {'v.', 'vs.', 'vs', 'et', 'and', 'or', 'of', 'the', 'in', 'on', 'at', 'to', 'for', 'as', 'by'}
        
        # Special cases that need specific formatting
        special_cases = {'et al': 'et al.'}  # 'et al' should always be 'et al.'
        
        # Common abbreviations that should remain uppercase
        uppercase_words = {'LLC', 'LP', 'LLP', 'PC', 'PA', 'PLLC', 'INC', 'CO', 'CORP', 'LTD', 'USA', 'US', 'NA', 
                          'MD', 'PhD', 'JD', 'DDS', 'DMD', 'DVM', 'CEO', 'CFO', 'COO', 'CTO', 'VP', 'SVP', 'EVP'}
        
        # Split by spaces and process each word
        words = versus.split()
        result = []
        
        for i, word in enumerate(words):
            # Preserve punctuation
            if word.endswith(('.', ',', ';', ':', '!', '?')):
                punctuation = word[-1]
                word_without_punct = word[:-1]
            else:
                punctuation = ''
                word_without_punct = word
            
            # Check for special cases first (like 'et al' -> 'et al.')
            if word_without_punct.lower() in special_cases:
                result.append(special_cases[word_without_punct.lower()] + punctuation)
            # Check if it's a lowercase word (but not at the beginning)
            # Also check the word with punctuation for exact matches
            elif i > 0 and (word_without_punct.lower() in lowercase_words or word.lower() in lowercase_words):
                result.append(word_without_punct.lower() + punctuation)
            # Check if it's an uppercase abbreviation
            elif word_without_punct.upper() in uppercase_words:
                result.append(word_without_punct.upper() + punctuation)
            # Check if it's already in mixed case (like McDonald)
            elif not word_without_punct.isupper() and not word_without_punct.islower():
                result.append(word + punctuation)
            # Otherwise, title case it
            else:
                # Handle special cases like O'Brien, McDonald's
                if "'" in word_without_punct:
                    parts = word_without_punct.split("'")
                    titled = "'".join(part.capitalize() for part in parts)
                    result.append(titled + punctuation)
                else:
                    result.append(word_without_punct.capitalize() + punctuation)
        
        return ' '.join(result)

    def _check_ignore_download(self, case_details: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Checks if the case matches any entry in ignore_download.json.
        Returns the matching config entry if found, None otherwise."""
        if not self.ignore_download_config:
            return None
            
        court_id = case_details.get('court_id', self.court_id)
        # Check both law_firm and law_firm2 fields
        law_firm = str(case_details.get('law_firm', case_details.get('law_firm2', ''))).strip()
        attorney_list = case_details.get('attorney', [])
        flags = case_details.get('flags', [])
        
        # Extract attorney names and law firms from the list
        attorney_names = []
        attorney_law_firms = []
        if isinstance(attorney_list, list):
            for attorney in attorney_list:
                if isinstance(attorney, dict):
                    name = attorney.get('attorney_name', '').strip()
                    if name:
                        attorney_names.append(name.lower())
                    # Also collect law firms from attorney list
                    firm = attorney.get('law_firm', '').strip()
                    if firm:
                        attorney_law_firms.append(firm.lower())
                elif isinstance(attorney, str):
                    attorney_names.append(attorney.strip().lower())
        
        self.logger.debug(f"Checking ignore_download for court={court_id}, law_firm={law_firm}, attorney_law_firms={attorney_law_firms}, attorneys={attorney_names}, flags={flags}")
        
        for entry in self.ignore_download_config:
            # Check court_id match (case-sensitive)
            if entry.get('court_id') != court_id:
                continue
                
            # Check law_firm match (case-insensitive)
            # Check against both the main law_firm field and attorney law firms
            if 'law_firm' in entry:
                entry_law_firm = str(entry['law_firm']).strip().lower()
                if entry_law_firm:
                    # Check main law_firm field
                    firm_match = law_firm.lower() == entry_law_firm
                    # Also check if it matches any attorney law firm
                    if not firm_match and attorney_law_firms:
                        firm_match = entry_law_firm in attorney_law_firms
                    if not firm_match:
                        continue
                    
            # Check attorney_name match (case-insensitive)
            if 'attorney_name' in entry:
                entry_attorney = str(entry['attorney_name']).strip().lower()
                if entry_attorney and entry_attorney not in attorney_names:
                    continue
                    
            # Check flags match
            if 'flags' in entry and entry['flags']:
                entry_flags = entry['flags']
                if not isinstance(entry_flags, list):
                    entry_flags = [entry_flags]
                    
                # Check if any of the entry's flags are in the case's flags
                flag_match = False
                for entry_flag in entry_flags:
                    entry_flag_str = str(entry_flag).strip()
                    for case_flag in flags:
                        if entry_flag_str in str(case_flag):
                            flag_match = True
                            break
                    if flag_match:
                        break
                        
                if not flag_match:
                    continue
                    
            # All conditions matched
            self.logger.info(f"Found ignore_download match for case: {entry}")
            return entry
            
        return None

    def _load_relevant_defendants(self) -> List[str]:
        """Loads the list of relevant defendants from the JSON file specified by instance path."""
        if not self.relevant_defendants_path:
            self.logger.error("Path to relevant defendants file is not configured or invalid.")
            return []
        try:
            if not self.relevant_defendants_path.exists():
                self.logger.error(f"Relevant defendants file not found at: {self.relevant_defendants_path}")
                return []
            with open(self.relevant_defendants_path, 'r') as f:
                data = json.load(f)
                defendants = data.get("defendants", [])
                if not isinstance(defendants, list):
                    self.logger.error(f"Invalid format in relevant defendants file: 'defendants' is not a list.")
                    return []
                self.logger.info(
                    f"Loaded {len(defendants)} relevant defendants keywords from {self.relevant_defendants_path.name}.")
                # Return lowercased list for case-insensitive matching
                return [str(d).lower() for d in defendants if d]  # Ensure strings and lowercase
        except json.JSONDecodeError as e:
            self.logger.error(f"Error decoding JSON from relevant defendants file {self.relevant_defendants_path}: {e}",
                              exc_info=True)
            return []
        except FileNotFoundError:  # Should be caught by exists() check, but redundant safety
            self.logger.error(f"Relevant defendants file not found at: {self.relevant_defendants_path}")
            return []
        except Exception as e:
            self.logger.error(f"Unexpected error loading relevant defendants: {e}", exc_info=True)
            return []

    async def _create_district_court_lookup_async(self) -> Dict[str, str]:
        """Asynchronously creates an internal lookup from court name fragments to CourtId."""
        self.logger.info("Attempting to create district court lookup asynchronously...")

        if self.dc_db is None:
            self.logger.warning("Attribute 'self.dc_db' (DistrictCourtsManager) is None. Cannot create lookup.")
            return {}

        if not hasattr(self.dc_db, 'table') or self.dc_db.table is None:
            table_name = getattr(self.dc_db, 'table_name', 'DistrictCourts')
            self.logger.error(
                f"DistrictCourtsManager instance for table '{table_name}' has 'table' attribute as None. "
                f"Likely failed initialization. Cannot scan."
            )
            return {}

        try:
            table_name_to_scan = self.dc_db.table.name
            self.logger.info(f"Scanning DynamoDB table '{table_name_to_scan}' for district court items...")

            # Assuming self.dc_db.scan_table() returns a synchronous generator
            # or items directly from a synchronous SDK call.
            # We need to run the iteration and list conversion in a thread.
            def _sync_scan_and_collect():
                return list(self.dc_db.scan_table())

            court_items = await asyncio.to_thread(_sync_scan_and_collect)
            self.logger.info(f"Scan complete. Found {len(court_items)} raw items from '{table_name_to_scan}'.")

            lookup = {}
            processed_count = 0
            for court in court_items:
                if isinstance(court, dict) and 'CourtName' in court and 'CourtId' in court:
                    name = str(court.get('CourtName', '')).replace(' District Court', '').replace('U. S.', '').replace(
                        'US ', '').strip()
                    court_id_val = court.get('CourtId')  # Renamed to avoid conflict with self.court_id

                    if name and court_id_val:
                        lookup[name] = court_id_val
                        processed_count += 1
                    else:
                        self.logger.debug(f"Skipped court item due to empty name/ID after cleaning: {court}")
                else:
                    self.logger.warning(f"Skipped court item due to invalid format or missing keys: {court}")

            self.logger.info(
                f"Processed {processed_count} valid items into internal court lookup with {len(lookup)} unique entries.")
            if not lookup:
                self.logger.warning(
                    "District court lookup is empty after scanning. Transfer logic might fail to find court IDs.")
            return lookup

        except AttributeError as ae:
            self.logger.error(
                f"AttributeError during court lookup scan/processing. Error: {ae}",
                exc_info=True
            )
            return {}
        except ClientError as ce:
            self.logger.error(
                f"DynamoDB ClientError during district court scan: {ce.response['Error']['Code']} - {ce.response['Error']['Message']}")
            return {}
        except Exception as e:
            self.logger.error(
                f"Failed to create internal district court lookup due to unexpected error during scan/processing: {e}",
                exc_info=True)
            return {}

    @staticmethod
    def _clean_dict_values(original_dict: Dict[str, Any]) -> Dict[str, Any]:
        """Removes empty strings/None and strips whitespace from string values."""
        if not isinstance(original_dict, dict):
            return original_dict
        cleaned = {}
        for k, v in original_dict.items():
            if isinstance(v, str):
                v_stripped = v.strip()
                if v_stripped:  # Only add non-empty strings
                    cleaned[k] = v_stripped
            elif v is not None:  # Add non-None values
                cleaned[k] = v
        return cleaned

    def create_base_filename(self, case_details: Dict[str, Any]) -> str:
        """Creates a sanitized base filename for docket files."""
        court_id = case_details.get('court_id', self.court_id)
        docket_num = case_details.get('docket_num', 'unknown_docket')
        versus = case_details.get('versus', 'unknown_versus')

        # Clean docket number: Take part after ':', replace non-alphanum with '_', keep year and sequence number
        docket_parts = docket_num.split(':')[-1].split('-')
        if len(docket_parts) >= 2:
            # Assume format like YY-TYPE-##### or YY-#####
            year_part = docket_parts[0]
            # Find the numeric sequence number, usually the last part
            num_part_match = re.search(r'\d+$', docket_parts[-1])
            num_part = num_part_match.group(0) if num_part_match else docket_parts[-1]
            docket_num_cleaned = f"{year_part}_{num_part}"
        else:
            # Fallback for unexpected formats
            docket_num_cleaned = re.sub(r'[^\w-]', '_', docket_num)

        # Clean versus: remove non-alphanum, replace space with underscore, limit length
        versus_cleaned = re.sub(r'[^\w\s-]', '', versus)
        versus_cleaned = re.sub(r'\s+', '_', versus_cleaned).strip('_-')
        max_versus_len = 60  # Allow slightly longer versus part
        if len(versus_cleaned) > max_versus_len:
            versus_cleaned = versus_cleaned[:max_versus_len]

        # Combine and final cleanup
        base = f"{court_id}_{docket_num_cleaned}_{versus_cleaned}"
        # Final sanitation: replace any remaining invalid chars, ensure single underscores/hyphens
        base = re.sub(r'[^\w_-]+', '', base)  # Remove any lingering invalid chars
        base = re.sub(r'_+', '_', base)
        base = re.sub(r'-+', '-', base)
        return base.strip('_').strip('-')

    async def upload_html_to_s3(self, base_filename: str, html_content: str):
        """Uploads HTML content to S3."""
        if not self.s3_manager:
            self.logger.warning(f"S3Manager not available. Cannot upload HTML for {base_filename}.")
            return

        paths = self.file_manager._get_paths(self.iso_date, base_filename)
        s3_key = f"{self.iso_date}/html/{paths['html'].name}"

        try:
            success = await self.s3_manager.upload_html_string_async(html_content, s3_key)
            if success:
                self.logger.info(f"HTML uploaded to S3: s3://{self.s3_manager.bucket_name}/{s3_key}")
            else:
                self.logger.error(f"S3 upload failed for HTML (returned False): {s3_key}")
        except Exception as e:
            self.logger.error(f"Error uploading HTML to S3 key {s3_key}: {e}", exc_info=True)

    def update_case_details(self, page_html: str, existing_details: Dict[str, Any]) -> Dict[str, Any]:
        """Parses full HTML using HTMLCaseParser and merges results into existing details."""
        log_prefix = f"[{self.court_id}][{existing_details.get('docket_num', 'N/A')}]"
        self.logger.debug(f"{log_prefix} Parsing full HTML to update case details...")
        details = existing_details.copy()

        try:
            parser = HTMLCaseParser(page_html)
            parsed_results = parser.parse()

            case_info = parsed_results.get('case_info', {})
            if case_info:
                for key, value in case_info.items():
                    normalized_key = key.lower().replace(' ', '_').replace(':', '')
                    target_key = normalized_key
                    if target_key == 'date_filed' and value:
                        try:
                            dt_obj = parse_date_str(value)
                            details['filing_date'] = dt_obj.strftime('%Y-%m-%d') if dt_obj else details.get(
                                'filing_date')
                        except ValueError:
                            self.logger.warning(f"{log_prefix} Could not parse date_filed '{value}' from HTML.")
                    elif target_key == 'docket_num' and value:
                        if len(value) >= len(details.get('docket_num', '')):
                            details['docket_num'] = value
                    elif target_key == 'case_in_other_court' and value:
                        details['transferred_from'] = value
                    elif target_key in ['nos', 'cause', 'jury_demand', 'assigned_to', 'referred_to', 'office',
                                        'jurisdiction', 'flags', 'versus']:
                        details[target_key] = value

            plaintiffs_data = parsed_results.get('plaintiffs', [])
            if plaintiffs_data:
                details['plaintiff'] = [p.get('name') for p in plaintiffs_data if p.get('name')]
                all_attorneys, law_firms = [], set()
                for p in plaintiffs_data:
                    for attorney in p.get('attorneys', []):
                        attorney_name = str(attorney.get('attorney_name', '')).strip()
                        law_firm = str(attorney.get('law_firm', '')).strip()
                        if law_firm and law_firm.lower() != '(see above for address)':
                            law_firms.add(law_firm)
                        if attorney_name:
                            all_attorneys.append(
                                {'attorney_name': attorney_name, 'law_firm': law_firm if law_firm else None})
                details['attorney'] = list({att['attorney_name']: att for att in all_attorneys}.values())
                details['law_firm2'] = '; '.join(sorted(list(law_firms)))

            defendants_data = parsed_results.get('defendants', [])
            if defendants_data:
                details['defendant'] = defendants_data

            details = self._clean_dict_values(details)
            self.logger.debug(f"{log_prefix} Case details updated after full HTML parse.")
            return details
        except Exception as e:
            self.logger.error(f"{log_prefix} HTMLCaseParser failed or error during merge: {e}", exc_info=True)
            details['_processing_error'] = f"HTML Parse/Merge Error: {str(e)[:100]}"
            return details

    async def verify_case(self, case_details: Dict[str, Any]) -> bool:
        """
        Verifies if the case should be processed or skipped.
        Checks DB and local files. Returns True if needs processing, False otherwise.
        If _is_explicitly_requested, skips if it exists in DB (GSI) or is fully downloaded locally across the run's date range.
        """
        log_prefix = f"[{self.court_id}][{case_details.get('docket_num', 'UNKNOWN_DOCKET')}] VerifyCase:"
        court_id = case_details.get('court_id', self.court_id)
        docket_num = case_details.get('docket_num')
        filing_date_str = case_details.get('filing_date')  # May be None for explicit
        base_filename = case_details.get('base_filename')
        is_explicitly_requested = case_details.get('_is_explicitly_requested', False)

        if not docket_num:
            self.logger.warning(f"{log_prefix} Docket number missing. Cannot verify.")
            return False

        # --- Logic for Explicitly Requested Dockets ---
        if is_explicitly_requested:
            self.logger.debug(
                f"{log_prefix} EXPLICITLY REQUESTED. Verifying general DB existence and local full download across run range.")

            # Check 1: PacerDB for general existence using GSI (court_id, docket_num)
            if self.pacer_db and hasattr(self.pacer_db, 'check_docket_exists_by_id_num'):
                try:
                    exists_gsi = await asyncio.to_thread(
                        self.pacer_db.check_docket_exists_by_id_num, court_id, docket_num
                    )
                    if exists_gsi:
                        self.logger.info(f"{log_prefix} DB (GSI): Found (explicitly requested). SKIPPING.")
                        case_details['_processing_notes'] = (
                                case_details.get('_processing_notes', "") + "Skipped (DB GSI Exists);").strip()
                        return False  # Docket record exists in DB.
                except Exception as db_gsi_err:
                    self.logger.error(f"{log_prefix} Error checking PacerDB (GSI) for explicit case: {db_gsi_err}",
                                      exc_info=True)
            elif not self.pacer_db:
                self.logger.warning(f"{log_prefix} PacerDB unavailable for GSI check (explicitly requested).")

            # Check 2: Local files across the *entire orchestrator run's date range*
            # (self.start_date_obj to self.end_date_obj)
            if base_filename and self.file_manager and hasattr(self, 'start_date_obj') and hasattr(self,
                                                                                                   'end_date_obj'):
                try:
                    if await self.file_manager.check_if_downloaded_across_dates(
                            self.start_date_obj, self.end_date_obj, base_filename
                    ):
                        self.logger.info(
                            f"{log_prefix} Local: Found fully downloaded within run range ({self.start_date_obj.strftime('%Y-%m-%d')} to {self.end_date_obj.strftime('%Y-%m-%d')}) (explicitly requested). SKIPPING.")
                        case_details['_processing_notes'] = (
                                case_details.get('_processing_notes', "") + f"Skipped (Local - Run Range);").strip()
                        return False  # Already fully downloaded locally within the orchestrator's current date range.
                except Exception as local_err:
                    self.logger.error(
                        f"{log_prefix} Error checking local files (run range) for explicit case '{base_filename}': {local_err}",
                        exc_info=True)
            elif not self.file_manager:
                self.logger.warning(f"{log_prefix} FileManager unavailable for local check (explicitly requested).")
            elif not base_filename:
                self.logger.warning(f"{log_prefix} Base filename unavailable for local check (explicitly requested).")

            self.logger.info(
                f"{log_prefix} Explicitly requested case needs processing (not found in DB by GSI, nor fully downloaded locally within run range).")
            return True  # Needs processing

        # --- Original Logic for Report-Scraped Dockets (not explicitly requested) ---
        else:
            self.logger.debug(f"{log_prefix} REPORT-SCRAPED. Applying standard verification checks.")
            # PK Check (uses filing_date which might not be available for explicit requests initially)
            if filing_date_str and self.pacer_db:
                try:
                    exists_pk = await asyncio.to_thread(
                        self.pacer_db.docket_exists, filing_date_str, docket_num, court_id
                    )
                    if exists_pk:
                        self.logger.debug(f"{log_prefix} DB (PK): Found. Skipping.")
                        case_details['_processing_notes'] = (
                                case_details.get('_processing_notes', "") + "Skipped (DB PK);").strip()
                        return False
                except Exception as db_err:
                    self.logger.error(f"{log_prefix} Error in PacerManager.docket_exists (PK): {db_err}", exc_info=True)
            elif not self.pacer_db and filing_date_str:  # Only warn if PK check would have run
                self.logger.warning(f"{log_prefix} PacerDB unavailable for PK check.")

            # GSI Check
            if self.pacer_db and hasattr(self.pacer_db, 'check_docket_exists_by_id_num'):
                try:
                    exists_gsi = await asyncio.to_thread(
                        self.pacer_db.check_docket_exists_by_id_num, court_id, docket_num
                    )
                    if exists_gsi:
                        self.logger.debug(f"{log_prefix} DB (GSI): Found. Skipping.")
                        case_details['_processing_notes'] = (
                                case_details.get('_processing_notes', "") + "Skipped (DB GSI);").strip()
                        return False
                except Exception as db_gsi_err:
                    self.logger.error(
                        f"{log_prefix} Error in PacerManager.check_docket_exists_by_id_num (GSI): {db_gsi_err}",
                        exc_info=True)
            elif not self.pacer_db:
                if not (filing_date_str and self.pacer_db is None):
                    self.logger.warning(f"{log_prefix} PacerDB unavailable for GSI check.")

            # Local Files Check (across run range)
            if base_filename and self.file_manager and hasattr(self, 'start_date_obj') and hasattr(self,
                                                                                                   'end_date_obj'):
                try:
                    if await self.file_manager.check_if_downloaded_across_dates(
                            self.start_date_obj, self.end_date_obj, base_filename
                    ):
                        self.logger.debug(
                            f"{log_prefix} Local Check: Found '{base_filename}' (report range). Skipping.")
                        case_details['_processing_notes'] = (
                                case_details.get('_processing_notes', "") + "Skipped (Local - Report Range);").strip()
                        return False
                except Exception as local_err:
                    self.logger.error(f"{log_prefix} Error local file check for '{base_filename}': {local_err}",
                                      exc_info=True)
            elif not self.file_manager:
                self.logger.warning(f"{log_prefix} FileManager unavailable for local check.")
            elif not base_filename:
                self.logger.warning(f"{log_prefix} Base filename unavailable for local check.")

            self.logger.debug(f"{log_prefix} Report-scraped case needs processing.")
            return True

    @staticmethod
    def _check_no_proceedings(page_html: str) -> bool:
        """Checks for indicators that docket entries/proceedings are unavailable."""
        page_lower = page_html.lower()
        return ('proceedings for case' in page_lower and 'are not available' in page_lower) or \
            ('no docket entries found' in page_lower) or \
            ('no case entries found' in page_lower)

    def _check_notice_of_removal(self, page_html: str) -> Optional[Dict[str, Any]]:
        """Uses HTMLCaseParser to check for notice of removal details."""
        try:
            parser = HTMLCaseParser(page_html)
            removal_info = parser.is_removal()
            if removal_info and removal_info.get('is_removal'):
                raw_date = removal_info.get('removal_date')
                if raw_date:
                    try:
                        dt_obj = parse_date_str(raw_date)
                        removal_info['removal_date'] = dt_obj.strftime('%Y-%m-%d') if dt_obj else None
                    except ValueError:
                        self.logger.warning(f"Could not parse removal date '{raw_date}'.")
                        removal_info['removal_date'] = None
                return removal_info
            return None
        except Exception as e:
            self.logger.error(f"Error checking removal notice via HTMLCaseParser: {e}", exc_info=False)
            return None

    async def save_review_case_details(self, case_details: Dict[str, Any]):
        """Saves court_id and docket_num of review cases to a shared JSON log file with locking."""
        log_dir = self.file_manager.base_data_dir / self.iso_date / 'logs'
        log_dir.mkdir(parents=True, exist_ok=True)

        filename = "review_cases_all.json"
        file_path = log_dir / filename
        lock_path = log_dir / f".{filename}.lock"

        self.logger.info(f"Logging review case details (court_id, docket_num) to: {file_path}")

    async def save_irrelevant_case_details(self, case_details: Dict[str, Any]):
        """DEPRECATED: Use save_review_case_details() instead. Kept for backward compatibility."""
        await self.save_review_case_details(case_details)

        # Construct the item to append with only court_id and docket_num
        item_to_append = {
            "versus": case_details.get('versus'),
            'court_id': self.court_id,  # Use the processor's current court_id
            'docket_num': case_details.get('docket_num')  # Get docket_num from the passed case_details
        }

        # For more detailed internal logging, if needed (not written to file)
        debug_details_info = {
            **item_to_append,
            '_log_timestamp': datetime.now().isoformat(),
            '_reason_review': case_details.get('_reason_review', case_details.get('_reason_irrelevant', 'N/A'))
        }
        self.logger.debug(f"Details for review log entry: {debug_details_info}")

        process_lock = fasteners.InterProcessLock(str(lock_path))
        acquired_process_lock = False

        try:
            async with self.file_manager.async_lock:  # In-process async lock
                acquired_process_lock = await asyncio.to_thread(process_lock.acquire, blocking=True, timeout=15)
                if not acquired_process_lock:
                    raise TimeoutError(f"Timeout acquiring process lock for {file_path}")

                def read_append_write_json_sync():
                    existing_data = []
                    if file_path.exists() and file_path.stat().st_size > 0:
                        try:
                            with open(file_path, 'r') as f:
                                content = f.read()
                                if content: existing_data = json.loads(content)
                            if not isinstance(existing_data, list):
                                self.logger.warning(f"File {file_path} not list. Overwriting.")
                                existing_data = []
                        except json.JSONDecodeError:
                            self.logger.warning(f"Corrupted JSON {file_path}. Overwriting.")
                            existing_data = []
                        except Exception as read_err:
                            self.logger.error(f"Error reading {file_path}: {read_err}. Overwriting.")
                            existing_data = []

                    existing_data.append(item_to_append)  # Append the simplified item
                    temp_file_path = file_path.with_suffix('.json.tmp')
                    try:
                        with open(temp_file_path, 'w') as f:
                            json.dump(existing_data, f, indent=4, default=str)
                        os.replace(temp_file_path, file_path)
                    except Exception as write_err:
                        if temp_file_path.exists():
                            try:
                                temp_file_path.unlink()
                            except OSError:
                                pass
                        raise write_err

                await asyncio.to_thread(read_append_write_json_sync)
                self.logger.debug(f"Successfully appended irrelevant case (court_id, docket_num) to {file_path}")

        except TimeoutError as lock_e:
            self.logger.error(f"Timeout acquiring lock for irrelevant cases log {file_path}: {lock_e}")
        except Exception as e:
            self.logger.error(f"Error saving irrelevant case details (court_id, docket_num) to {file_path}: {e}",
                              exc_info=True)
        finally:
            if acquired_process_lock:
                try:
                    await asyncio.to_thread(process_lock.release)
                except Exception as release_err:
                    self.logger.error(f"Error releasing process lock for {file_path}: {release_err}")

    async def save_removal_case_details(self, case_details: Dict[str, Any]):
        """Saves details of removal cases to a shared JSON log file with locking."""
        log_dir = self.file_manager.base_data_dir / self.iso_date / 'logs'
        log_dir.mkdir(parents=True, exist_ok=True)

        filename = "removal_cases_all.json"  # Changed filename
        file_path = log_dir / filename
        lock_path = log_dir / f".{filename}.lock"

        self.logger.info(f"Logging removal case details to: {file_path}")

        details_to_save = self._clean_dict_values(case_details)
        details_to_save.update({
            '_log_timestamp': datetime.now().isoformat(),
            '_court_id': self.court_id,
            '_processing_status': 'Logged as Removal (HTML only or download failed)'  # More specific status
        })
        details_to_save.pop('_reason_review', None)  # Avoid confusion
        details_to_save.pop('_reason_irrelevant', None)  # Backward compatibility cleanup

        process_lock = fasteners.InterProcessLock(str(lock_path))
        acquired_process_lock = False

        try:
            async with self.file_manager.async_lock:
                acquired_process_lock = await asyncio.to_thread(process_lock.acquire, blocking=True, timeout=15)
                if not acquired_process_lock:
                    raise TimeoutError(f"Timeout acquiring process lock for {file_path}")

                def read_append_write_json_sync():  # Same logic as save_irrelevant
                    existing_data = []
                    if file_path.exists() and file_path.stat().st_size > 0:
                        try:
                            with open(file_path, 'r') as f:
                                content = f.read()
                                if content: existing_data = json.loads(content)
                            if not isinstance(existing_data, list):
                                self.logger.warning(f"File {file_path} not list. Overwriting.")
                                existing_data = []
                        except json.JSONDecodeError:
                            self.logger.warning(f"Corrupted JSON {file_path}. Overwriting.")
                            existing_data = []
                        except Exception as read_err:
                            self.logger.error(f"Error reading {file_path}: {read_err}. Overwriting.")
                            existing_data = []

                    existing_data.append(details_to_save)
                    temp_file_path = file_path.with_suffix('.json.tmp')
                    try:
                        with open(temp_file_path, 'w') as f:
                            json.dump(existing_data, f, indent=4, default=str)
                        os.replace(temp_file_path, file_path)
                    except Exception as write_err:
                        if temp_file_path.exists():
                            try:
                                temp_file_path.unlink()
                            except OSError:
                                pass
                        raise write_err

                await asyncio.to_thread(read_append_write_json_sync)
                self.logger.debug(f"Successfully appended removal case to {file_path}")

        except TimeoutError as lock_e:
            self.logger.error(f"Timeout acquiring lock for removal cases log {file_path}: {lock_e}")
        except Exception as e:
            self.logger.error(f"Error saving removal case details to {file_path}: {e}", exc_info=True)
        finally:
            if acquired_process_lock:
                try:
                    await asyncio.to_thread(process_lock.release)
                except Exception as release_err:
                    self.logger.error(f"Error releasing process lock for {file_path}: {release_err}")

    async def process_docket_page(self, initial_details: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        self.is_downloaded = False
        docket_num = initial_details.get('docket_num', 'UNKNOWN_PROCESS')
        self.current_case_docket_num_for_logging = docket_num
        log_prefix = f"[{self.court_id}][{docket_num}]"
        self.logger.info(f"{log_prefix} Processing docket page for case {docket_num} in court {self.court_id}")

        is_explicitly_requested = initial_details.get('_is_explicitly_requested', False)
        if is_explicitly_requested:
            self.logger.info(f"{log_prefix} Docket is EXPLICITLY REQUESTED. Relevance/date checks will be adjusted.")

        if not self.navigator or not self.navigator.is_ready:
            self.logger.error(f"{log_prefix} Navigator not available or not ready.")
            return None
        if not self.transfer_handler: self.logger.warning(f"{log_prefix} TransferHandler not initialized.")
        if not self.document_downloader and not self.html_only: self.logger.warning(
            f"{log_prefix} DocumentDownloader not initialized.")

        page = self.navigator.page
        case_details = initial_details.copy()

        # --- Defer base_filename generation ---
        # base_filename = self.create_base_filename(case_details) # OLD: Called too early
        # case_details['base_filename'] = base_filename
        # case_details['original_filename'] = Path(base_filename).stem
        base_filename: Optional[str] = None  # Initialize as None

        try:
            try:
                await page.locator("body").first.wait_for(state="visible", timeout=15000)
            except PlaywrightTimeoutError:
                self.logger.error(f"{log_prefix} Page body not visible.")
                # Generate a temporary base_filename for error logging if versus isn't known yet
                temp_error_base_filename = self.create_base_filename(case_details)  # uses initial_details
                await self.navigator.save_screenshot(f"body_timeout_{temp_error_base_filename}")
                case_details['_processing_error'] = "Page body did not become visible"
                if self.file_manager: await self.file_manager.save_json(self.iso_date, temp_error_base_filename,
                                                                        case_details)
                return None

            page_html_content = await page.content()

            # --- Full HTML Parse to get all details, including 'versus' ---
            case_details = self.update_case_details(page_html_content, case_details)
            case_details['court_id'] = self.court_id  # Ensure court_id is set from processor's context

            # --- Now that 'versus' is populated from HTML, generate the definitive base_filename ---
            base_filename = self.create_base_filename(case_details)
            case_details['base_filename'] = base_filename
            case_details['original_filename'] = Path(base_filename).stem
            self.logger.info(
                f"{log_prefix} Generated base_filename: {base_filename} using versus: '{case_details.get('versus')}'")

            if self._check_no_proceedings(page_html_content):
                self.logger.warning(f"{log_prefix} No proceedings in HTML. Saving basic info.")
                await self.upload_html_to_s3(base_filename, page_html_content)
                case_details[
                    's3_html'] = f"{self.config.get('cdn_base_url', '').rstrip('/')}/{self.iso_date}/html/{base_filename}.html"
                case_details.update({'_processing_notes': "No proceedings found."})
                # Ensure filing_date and versus are present even in this edge case (they should be from update_case_details or initial_details)
                case_details.setdefault('filing_date',
                                        initial_details.get('filing_date', initial_details.get('added_date')))
                case_details.setdefault('versus', initial_details.get('versus',
                                                                      'Unknown Versus From Initial'))  # Fallback if HTML parse failed
                case_details['is_removal'] = any(
                    term in str(initial_details.get('cause_from_report', '')).lower() for term in
                    ["petition for removal", "notice of removal", "28:1441",
                     "28:1446"]) if not is_explicitly_requested else False
                if self.file_manager: await self.file_manager.save_json(self.iso_date, base_filename,
                                                                        self._clean_dict_values(case_details))
                return self._clean_dict_values(case_details)

            main_content_selector = "table[width='99%'][border='1'], center > h3"
            try:
                await page.locator(main_content_selector).first.wait_for(state="visible",
                                                                         timeout=self.navigator.timeout)
            except PlaywrightTimeoutError:
                self.logger.error(f"{log_prefix} Timeout for main content ('{main_content_selector}').")
                await self.upload_html_to_s3(base_filename, page_html_content)
                case_details[
                    's3_html'] = f"{self.config.get('cdn_base_url', '').rstrip('/')}/{self.iso_date}/html/{base_filename}.html"
                case_details.update({'_processing_error': "Timeout finding main content."})
                case_details['is_removal'] = any(
                    term in str(initial_details.get('cause_from_report', '')).lower() for term in
                    ["petition for removal", "notice of removal", "28:1441",
                     "28:1446"]) if not is_explicitly_requested else False
                await self.navigator.save_screenshot(f"main_content_timeout_{base_filename}")
                if self.file_manager: await self.file_manager.save_json(self.iso_date, base_filename, case_details)
                return None

            await self.upload_html_to_s3(base_filename, page_html_content)
            case_details[
                's3_html'] = f"{self.config.get('cdn_base_url', '').rstrip('/')}/{self.iso_date}/html/{base_filename}.html"
            # case_details = self.update_case_details(page_html_content, case_details) # Already called
            # case_details['court_id'] = self.court_id # Already set

            # --- New MDL Flag Processing --- 
            flags = case_details.get('flags', [])
            if flags:
                self.logger.debug(f"{log_prefix} Checking flags for MDL number: {flags}")
                for flag in flags:
                    # Regex to match MDLNNNN, MDL NNNN, MDL-NNNN (assumes NNNN is 4+ digits)
                    mdl_match_from_flag = re.search(r'MDL[\s-]?(\d{4,})', str(flag), re.IGNORECASE)
                    if mdl_match_from_flag:
                        extracted_mdl_num = mdl_match_from_flag.group(1)
                        case_details['mdl_num'] = extracted_mdl_num
                        self.logger.info(f"{log_prefix} Set mdl_num='{extracted_mdl_num}' from flag: '{flag}'")
                        # Once found, no need to check other flags or derive later for this specific pattern
                        break 
            # --- End New MDL Flag Processing ---

            initial_is_removal_from_report = any(
                term in str(initial_details.get('cause_from_report', '')).lower() for term in
                ["petition for removal", "notice of removal", "28:1441", "28:1446"])
            html_indicates_removal = False
            removal_info_html = self._check_notice_of_removal(page_html_content)
            if removal_info_html and removal_info_html.get('is_removal'):
                html_indicates_removal = True
                if removal_info_html.get('removal_date'):
                    case_details['removal_date'] = removal_info_html['removal_date']
                    case_details.setdefault('initial_filing_date', removal_info_html['removal_date'])

            final_is_removal_status = initial_is_removal_from_report or html_indicates_removal
            case_details['is_removal'] = final_is_removal_status
            self.is_removal = final_is_removal_status
            self.logger.info(
                f"{log_prefix} 'is_removal' status for processing: {self.is_removal} (Explicitly requested: {is_explicitly_requested})")

            if (self.court_id == 'ilnd' and any(
                    "mdl 3060" in str(flag).lower() for flag in case_details.get('flags', []))):
                self.logger.info(f"{log_prefix} ILND MDL 3060 case. Overriding. Will download locally.")
                case_details['is_mdl_related_ilnd_3060'] = True
                case_details['transferred_in'] = False
                case_details['is_removal'] = False
                self.is_removal = False
                mdl_flag_str = next((f for f in case_details.get('flags', []) if "mdl 3060" in str(f).lower()),
                                    "MDL 3060")
                mdl_match = re.search(r'MDL\s*(\d+)', mdl_flag_str, re.IGNORECASE)
                if mdl_match: case_details['mdl_num'] = mdl_match.group(1)

            elif (self.court_id == 'cand' and any(
                    "mdl 3047" in str(flag).lower() for flag in case_details.get('flags', []))):
                self.logger.info(f"{log_prefix} CAND MDL 3047 case. Overriding. Will download locally.")
                case_details['is_mdl_related_ilnd_3047'] = True
                case_details['transferred_in'] = False
                case_details['is_removal'] = False
                self.is_removal = False
                mdl_flag_str = next((f for f in case_details.get('flags', []) if "mdl 3047" in str(f).lower()),
                                    "MDL 3047")
                mdl_match = re.search(r'MDL\s*(\d+)', mdl_flag_str, re.IGNORECASE)
                if mdl_match: case_details['mdl_num'] = mdl_match.group(1)

            elif case_details.get('transferred_from') and self.transfer_handler:
                self.logger.info(
                    f"{log_prefix} 'transferred_from': '{case_details['transferred_from']}' detected. Attempting federal transfer. Current 'is_removal' for handler: {case_details['is_removal']}.")
                case_details_after_transfer_attempt = await self.transfer_handler.handle_transfer(case_details.copy())

                if case_details_after_transfer_attempt.get(
                        'transferred_in') and case_details_after_transfer_attempt.get('s3_link'):
                    self.logger.info(
                        f"{log_prefix} SUCCESS: Case processed as FEDERAL inter-district transfer with S3 link. Download skipped.")
                    self.is_downloaded = True
                    case_details.update(case_details_after_transfer_attempt)
                    case_details['is_removal'] = False
                    self.is_removal = False
                    # Ensure base_filename reflects the S3 copied case details if it changed
                    # This typically happens if 'versus' or 'docket_num' for the original case gets updated
                    # For S3 copies, the filename should ideally reflect the original case's details.
                    # If handle_transfer updates 'versus' or 'docket_num' to match the original, create_base_filename should be called again.
                    # However, if an S3 link is found, we're usually done with *this specific court's entry*.
                    # The existing base_filename (derived from this court's HTML) is what we save the JSON under for *this court's record*.
                    if self.file_manager: await self.file_manager.save_json(self.iso_date, base_filename,
                                                                            case_details)  # Use current base_filename
                    return case_details
                else:
                    self.logger.info(
                        f"{log_prefix} Federal transfer S3 copy attempt FAILED/not applicable. Proceeding based on determined 'is_removal' status: {self.is_removal}.")
                    case_details.update(case_details_after_transfer_attempt)

            if not is_explicitly_requested:
                # --- New MDL Relevance Check ---
                mdl_num_from_details = case_details.get('mdl_num')
                if mdl_num_from_details and str(mdl_num_from_details).upper() != 'NA':
                    self.logger.info(f"{log_prefix} Case has MDL Number '{mdl_num_from_details}' from flags/details. Marking as relevant.")
                    case_details['_reason_relevant'] = f"Relevant due to MDL Number: {mdl_num_from_details}"
                    # Skip further relevance engine checks if MDL num makes it relevant
                elif self.is_removal:
                    self.logger.info(
                        f"{log_prefix} Path: REMOVAL case (report-scraped). Applying removal-specific relevance.")
                    case_details['transferred_in'] = False
                    if self.relevance_engine.should_exclude_removal_defendant(case_details):
                        self.logger.info(
                            f"{log_prefix} Report-scraped removal excluded by defendant: {case_details.get('_reason_review', case_details.get('_reason_irrelevant'))}")
                        await self.save_review_case_details(case_details)
                        if self.file_manager: await self.file_manager.save_json(self.iso_date, base_filename,
                                                                                case_details)
                        return case_details
                else:
                    self.logger.info(
                        f"{log_prefix} Path: NON-REMOVAL case (report-scraped). Applying general relevance.")
                    # --- New MDL Relevance Check (ensure it's not already handled by the above) ---
                    mdl_num_from_details_non_removal = case_details.get('mdl_num')
                    if mdl_num_from_details_non_removal and str(mdl_num_from_details_non_removal).upper() != 'NA':
                        # This condition is already checked above, but added here for logical completeness
                        # if the structure were different. In the current flow, this 'else' block might not
                        # be reached if the first MDL check passes.
                        self.logger.info(f"{log_prefix} Case has MDL Number '{mdl_num_from_details_non_removal}' from flags/details. Marking as relevant (non-removal path).")
                        case_details['_reason_relevant'] = f"Relevant due to MDL Number: {mdl_num_from_details_non_removal}"
                    elif self.relevance_engine.check_overall_relevance(case_details):
                        self.logger.info(
                            f"{log_prefix} Report-scraped case needs review by general check: {case_details.get('_reason_review', case_details.get('_reason_irrelevant'))}")
                        await self.save_review_case_details(case_details)
                        if self.file_manager: await self.file_manager.save_json(self.iso_date, base_filename,
                                                                                case_details)
                        return case_details

                if self.is_removal and \
                        case_details.get('transferred_from') and \
                        not case_details.get('is_mdl_related_ilnd_3060', False) and \
                        self.transfer_handler:

                    transferor_court_id_final_check = case_details.get('transferor_court_id')
                    if not transferor_court_id_final_check:
                        temp_transfer_info = await self.transfer_handler._gather_transfer_info_async(
                            case_details['transferred_from'])
                        if temp_transfer_info: transferor_court_id_final_check = temp_transfer_info.get(
                            'transferor_court_id')

                    is_source_known_federal_and_different = transferor_court_id_final_check and \
                                                            not transferor_court_id_final_check.startswith('UNK') and \
                                                            transferor_court_id_final_check != self.court_id

                    if not is_source_known_federal_and_different:
                        self.logger.info(
                            f"{log_prefix} Rule Apply (Report-Scraped): REMOVAL from non-federal/unidentified source. Logging for review.")
                        case_details[
                            '_reason_review'] = "Removal from non-federal/unidentified source (to be handled later)"
                        await self.save_review_case_details(case_details)
                        if self.file_manager: await self.file_manager.save_json(self.iso_date, base_filename,
                                                                                case_details)
                        return case_details

            if not await self.verify_case(case_details):
                self.logger.info(f"{log_prefix} Case verification indicates SKIP (already processed/exists).")
                if self.file_manager and not await self.file_manager.check_if_json_exists(self.iso_date, base_filename):
                    await self.file_manager.save_json(self.iso_date, base_filename, case_details)
                return case_details

            # base_filename should be final here
            if self.file_manager: await self.file_manager.save_json(self.iso_date, base_filename, case_details)

            # Check if this case matches ignore_download configuration
            ignore_match = self._check_ignore_download(case_details)
            if ignore_match:
                self.logger.info(f"{log_prefix} Case matches ignore_download configuration. Skipping PDF download.")
                case_details['html_only'] = True
                case_details['_processing_notes'] = (case_details.get('_processing_notes', '') + " Matched ignore_download config.").strip()
                
                # Title-case the versus field
                if 'versus' in case_details and case_details['versus']:
                    original_versus = case_details['versus']
                    case_details['versus'] = self._title_case_versus(original_versus)
                    self.logger.info(f"{log_prefix} Title-cased versus: '{original_versus}' -> '{case_details['versus']}'")
                
                # Title-case the plaintiff list
                if 'plaintiff' in case_details and isinstance(case_details['plaintiff'], list):
                    original_plaintiffs = case_details['plaintiff'].copy()
                    case_details['plaintiff'] = [self._title_case_versus(p) for p in case_details['plaintiff'] if p]
                    self.logger.info(f"{log_prefix} Title-cased plaintiffs: {original_plaintiffs} -> {case_details['plaintiff']}")
                
                # Title-case the defendant list
                if 'defendant' in case_details and isinstance(case_details['defendant'], list):
                    original_defendants = case_details['defendant'].copy()
                    case_details['defendant'] = [self._title_case_versus(d) for d in case_details['defendant'] if d]
                    self.logger.info(f"{log_prefix} Title-cased defendants: {original_defendants} -> {case_details['defendant']}")
                
                # Apply the override values from the configuration
                if 'report_law_firm' in ignore_match:
                    report_law_firm = ignore_match['report_law_firm']
                    # Split by " ; " to get individual law firms
                    law_firms_list = [firm.strip() for firm in report_law_firm.split(' ; ') if firm.strip()]
                    case_details['law_firms'] = law_firms_list
                    # Set law_firm to the full string (not split)
                    case_details['law_firm'] = report_law_firm
                    self.logger.info(f"{log_prefix} Applied report_law_firm: '{report_law_firm}' -> law_firms: {law_firms_list}")
                # Set s3_link to the s3_html value with full CDN URL
                if 's3_html' in case_details:
                    s3_html = case_details['s3_html']
                    # If s3_html is a relative path, prepend the CDN base URL
                    if s3_html.startswith('/'):
                        cdn_base = self.config.get('cdn_base_url', 'https://cdn.lexgenius.ai').rstrip('/')
                        case_details['s3_link'] = f"{cdn_base}{s3_html}"
                    else:
                        # If it's already a full URL, use as is
                        case_details['s3_link'] = s3_html
                    self.logger.info(f"{log_prefix} Set s3_link to: {case_details['s3_link']}")
                if 'mdl_num' in ignore_match:
                    case_details['mdl_num'] = ignore_match['mdl_num']
                    
                # Save the updated case details
                if self.file_manager: 
                    await self.file_manager.save_json(self.iso_date, base_filename, case_details)
                return case_details

            if self.html_only or not self.document_downloader:
                mode = "HTML Only" if self.html_only else "Downloader unavailable"
                self.logger.info(f"{log_prefix} {mode}. Skipping complaint click and download.")
                if self.is_removal and not self.is_downloaded and not is_explicitly_requested:
                    await self.save_removal_case_details(case_details)
                return case_details

            self.logger.info(
                f"{log_prefix} Proceeding to download. Final 'is_removal' state for downloader: {self.is_removal}")

            complaint_clicked = await self.document_downloader.click_complaint_link(case_details)
            if not complaint_clicked:
                notes = case_details.get('_processing_notes',
                                         "") + " Primary complaint/main document link not found/failed."
                if self.is_removal: notes += " Critical for removal."
                case_details['_processing_notes'] = notes.strip()
                if self.is_removal and not is_explicitly_requested:
                    await self.save_removal_case_details(case_details)
                self.logger.warning(
                    f"{log_prefix} Complaint/main document link click failed. Not proceeding to download trigger.")
                if self.file_manager: await self.file_manager.save_json(self.iso_date, base_filename, case_details)
                return case_details

            self.logger.info(f"{log_prefix} Complaint/main document link clicked. Proceeding to download trigger.")
            download_successful = await self.document_downloader.trigger_and_finalize_download(case_details)
            self.is_downloaded = download_successful
            if self.is_downloaded: case_details['is_downloaded'] = True

            if not download_successful:
                self.logger.warning(f"{log_prefix} Download attempt was not successful.")
                case_details['_processing_notes'] = (
                        case_details.get('_processing_notes', "") + " Download failed.").strip()
                if self.is_removal and not is_explicitly_requested:
                    await self.save_removal_case_details(case_details)

            if self.file_manager:
                await self.file_manager.save_json(self.iso_date, base_filename, case_details)
            return case_details

        except PlaywrightError as pe:
            err_msg = f"PlaywrightError: {str(pe)[:150]}"
            if "Target closed" in str(pe).lower():
                self.logger.error(f"{log_prefix} CRITICAL: Page closed: {pe}", exc_info=True)
            else:
                self.logger.error(f"{log_prefix} {err_msg}", exc_info=True)
            # Use a temp filename for screenshot if base_filename isn't set yet
            screenshot_filename_base = base_filename if base_filename else f"error_{docket_num.replace(':', '_')}"
            if self.navigator and self.navigator.is_ready: await self.navigator.save_screenshot(
                f"docket_proc_pw_error_{screenshot_filename_base}")
            case_details['_processing_error'] = err_msg
        except Exception as e:
            err_msg = f"Exception: {str(e)[:150]}"
            self.logger.error(f"{log_prefix} Unexpected error: {e}", exc_info=True)
            screenshot_filename_base = base_filename if base_filename else f"error_{docket_num.replace(':', '_')}"
            if self.navigator and self.navigator.is_ready: await self.navigator.save_screenshot(
                f"docket_proc_unexp_err_{screenshot_filename_base}")
            case_details['_processing_error'] = err_msg

        case_details['_processing_notes'] = (
                case_details.get('_processing_notes', "") + " Processing error occurred.").strip()
        case_details['is_removal'] = self.is_removal
        try:
            # Ensure base_filename is set for error saving, even if it's a fallback
            if not base_filename:
                base_filename = self.create_base_filename(case_details)  # Create from potentially incomplete details
                case_details['base_filename'] = base_filename
                case_details['original_filename'] = Path(base_filename).stem

            if self.file_manager: await self.file_manager.save_json(self.iso_date, base_filename, case_details)
        except Exception as save_err:
            self.logger.error(f"{log_prefix} Failed to save error JSON: {save_err}")
        finally:
            self.current_case_docket_num_for_logging = None
        return None
