import os
from typing import Optional, Dict, Any

from pydantic import BaseModel, SecretStr, Field, field_validator, ConfigDict, ValidationInfo

# Path constants
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
DISTRICT_COURTS_PATH = os.path.join(PROJECT_ROOT, "src", "config", "courts", "district_courts.json")


class PacerCredentials(BaseModel):
    """PACER login credentials."""
    username: str
    password: SecretStr


class BrowserSettings(BaseModel):
    """Browser configuration settings."""
    headless: bool = True
    timeout_ms: int = 30000
    download_path: Optional[str] = None
    user_agent: Optional[str] = None


class PacerConfig(BaseModel):
    """Configuration for PACER scraping operations."""
    # Authentication
    pacer_credentials: Optional[PacerCredentials] = None
    openai_api_key: Optional[str] = None

    # Browser settings
    browser_settings: BrowserSettings = Field(default_factory=BrowserSettings)

    # AWS and storage settings
    aws_region: str = "us-west-2"
    bucket_name: str = ""
    s3_bucket_name: Optional[str] = None  # Alias for bucket_name

    # Database settings
    use_local_db: bool = False
    local_db_port: int = 8000

    # File system settings
    project_root: Optional[str] = None
    data_path: Optional[str] = None
    directories: Optional[Dict[str, str]] = None

    # Timeout settings
    timeout_ms: int = 30000

    # Other settings
    headless: bool = True

    # Pydantic v2 config
    model_config = ConfigDict(
        extra='allow',  # Allow extra fields for backward compatibility
        populate_by_name=True
    )

    @classmethod
    @field_validator('s3_bucket_name', mode='before')
    def set_s3_bucket_name(cls, v: Optional[str], info: ValidationInfo) -> str:
        if v is not None:
            return v
        if info.data and 'bucket_name' in info.data:
            return info.data['bucket_name']
        return ""

    def model_dump(self, **kwargs) -> Dict[str, Any]:
        """Convert model to dictionary, including browser settings at the top level."""
        result = super().model_dump(**kwargs)
        if 'browser_settings' in result and isinstance(result['browser_settings'], dict):
            result.update(result.pop('browser_settings'))
        return result

    # For backward compatibility
    def dict(self, **kwargs) -> Dict[str, Any]:
        """Deprecated: Use model_dump() instead."""
        return self.model_dump(**kwargs)


def create_pacer_config(config: Dict[str, Any]) -> PacerConfig:
    """Create a PacerConfig from a dictionary or return existing PacerConfig."""
    if isinstance(config, PacerConfig):
        return config
    return PacerConfig(**config)
