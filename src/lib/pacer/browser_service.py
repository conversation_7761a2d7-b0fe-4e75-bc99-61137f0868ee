import logging
import tempfile  # Added
import json  # Added
import os  # Added
from pathlib import Path
from typing import Optional, List  # Added List

from playwright.async_api import (
    async_playwright,
    Playwright,
    # Browser, # Browser is no longer directly managed this way
    <PERSON><PERSON><PERSON><PERSON><PERSON>x<PERSON>,
    Error as PlaywrightError,
    StorageState
)


# pacer_utils.retry_async is not used directly on these methods anymore,
# PacerOrchestrator handles retries at a higher level.
# from .pacer_utils import retry_async


class BrowserService:
    """Manages Playwright browser lifecycle, creating persistent contexts with PDF download preferences."""

    def __init__(self, headless: bool = True, timeout_ms: int = 30000):
        self.headless = headless
        self.timeout_ms = timeout_ms
        self.playwright: Optional[Playwright] = None
        # self.browser: Optional[Browser] = None # Replaced by managing contexts directly
        self.logger = logging.getLogger(f"{__name__}.BrowserService")
        # Store TemporaryDirectory objects to ensure they are cleaned up
        self._managed_temp_dirs: List[tempfile.TemporaryDirectory] = []

    async def connect(self):
        """Initializes the Playwright connection if not already active."""
        if self.playwright: # Corrected: Removed .is_connected
            self.logger.info("Playwright connection already active.")
            return
        self.logger.info("Starting Playwright...")
        try:
            self.playwright = await async_playwright().start()
            self.logger.info("Playwright started successfully.")
        except Exception as e:
            self.logger.error(f"Failed to start Playwright: {e}", exc_info=True)
            await self.close() # Ensure cleanup if connect fails
            raise

    async def new_context(self,
                          download_path: Optional[str] = None,
                          storage_state: Optional[StorageState] = None  # Parameter can remain for API consistency
                          ) -> BrowserContext:
        """
        Creates a new persistent browser context with specific preferences for PDF handling.
        Each call to this method launches a new isolated browser instance via a persistent context.
        """
        self.logger.info("BrowserService: Attempting to create new persistent context...")
        if not self.playwright:
            self.logger.info("BrowserService: Playwright not initialized. Attempting to connect...")
            try:
                await self.connect()
                if not self.playwright:
                    self.logger.error(
                        "BrowserService: Playwright is None even after connect() call. Aborting new_context.")
                    raise ConnectionError("Playwright could not be initialized.")
            except Exception as connect_err:
                self.logger.error(f"BrowserService: Error during connect() call: {connect_err}. Aborting new_context.")
                raise ConnectionError(f"Playwright connection failed: {connect_err}") from connect_err

        self.logger.debug("BrowserService: Playwright instance confirmed.")

        temp_user_data_dir_obj = None
        try:
            temp_user_data_dir_obj = tempfile.TemporaryDirectory(prefix="pw_user_data_")
            self._managed_temp_dirs.append(temp_user_data_dir_obj)
            user_data_dir_path = temp_user_data_dir_obj.name
            self.logger.info(f"BrowserService: Created temporary user data directory: {user_data_dir_path}")

            default_prefs_dir = Path(user_data_dir_path) / "Default"
            os.makedirs(default_prefs_dir, exist_ok=True)
            prefs_file_path = default_prefs_dir / "Preferences"
            default_preferences = {
                'plugins': {'always_open_pdf_externally': True},
                'download': {
                    'prompt_for_download': False,
                    'directory_upgrade': True,
                }
            }
            if download_path:
                default_preferences['download']['default_directory'] = download_path

            with open(prefs_file_path, 'w') as f:
                json.dump(default_preferences, f) # type: ignore
            self.logger.debug(
                f"BrowserService: Created Preferences file at {prefs_file_path} with PDF and download settings.")

        except Exception as e_prefs:
            self.logger.error(
                f"BrowserService: Failed during user_data_dir/Preferences setup for {user_data_dir_path if 'user_data_dir_path' in locals() else 'N/A'}: {e_prefs}",
                exc_info=True)
            if temp_user_data_dir_obj:
                try:
                    temp_user_data_dir_obj.cleanup()
                    if temp_user_data_dir_obj in self._managed_temp_dirs:
                        self._managed_temp_dirs.remove(temp_user_data_dir_obj)
                except Exception as e_cleanup_prefs:
                    self.logger.error(
                        f"BrowserService: Error cleaning up temp_user_data_dir after prefs failure: {e_cleanup_prefs}")
            raise IOError(f"Failed to set up user data directory preferences: {e_prefs}") from e_prefs

        chromium_launch_args = [
            "--disable-gpu", "--window-size=1400,2500", "--no-sandbox",
            "--disable-dev-shm-usage", '--disable-pdf-extension', '--disable-extensions',
            '--disable-component-extensions-with-background-pages',
            '--disable-default-apps', '--disable-features=PDFViewer',
        ]

        context_options = {
            "user_data_dir": user_data_dir_path,
            "headless": self.headless,
            "args": chromium_launch_args,
            "accept_downloads": True,
            # "storage_state": storage_state, # <--- REMOVED THIS LINE
        }

        # If you need to apply storage_state, you'd typically do it AFTER getting the context,
        # usually by creating a new context based on the browser from the persistent one,
        # or if the API evolves to support it directly on persistent context pages.
        # For now, persistent context relies on the user_data_dir for state.
        if storage_state:
            self.logger.warning(
                "BrowserService: 'storage_state' was provided but is NOT directly used by 'launch_persistent_context'. "
                "The persistent context will load state from its user_data_dir. "
                "If specific initial state is needed, alternative context creation might be required.")

        if download_path:
            context_options["downloads_path"] = download_path
            self.logger.info(
                f"BrowserService: Persistent context launch_persistent_context will use explicit downloads_path: {download_path}")
            try:
                Path(download_path).mkdir(parents=True, exist_ok=True)
            except Exception as e_mkdir_dl:
                self.logger.warning(
                    f"BrowserService: Could not ensure explicit downloads_path '{download_path}' exists: {e_mkdir_dl}")
        else:
            self.logger.info(
                f"BrowserService: No explicit download_path given to new_context. Persistent context will use its default (likely within user_data_dir).")

        self.logger.debug(f"BrowserService: Attempting to launch persistent context with options: {context_options}")
        try:
            context = await self.playwright.chromium.launch_persistent_context(**context_options)
            context.set_default_timeout(self.timeout_ms)
            self.logger.info(f"BrowserService: Persistent context launched successfully from {user_data_dir_path}.")
            return context
        except PlaywrightError as e_launch:
            self.logger.error(
                f"BrowserService: Failed to launch persistent context from {user_data_dir_path}: {e_launch}",
                exc_info=True)
            if temp_user_data_dir_obj:
                try:
                    temp_user_data_dir_obj.cleanup()
                    if temp_user_data_dir_obj in self._managed_temp_dirs:
                        self._managed_temp_dirs.remove(temp_user_data_dir_obj)
                except Exception as e_cleanup_launch:
                    self.logger.error(
                        f"BrowserService: Error cleaning up temp_user_data_dir after launch failure: {e_cleanup_launch}")
            raise ConnectionError(f"Failed to create persistent browser context: {e_launch}") from e_launch
        except Exception as e_generic_launch:
            self.logger.error(
                f"BrowserService: Generic error launching persistent context from {user_data_dir_path}: {e_generic_launch}",
                exc_info=True)
            if temp_user_data_dir_obj:
                try:
                    temp_user_data_dir_obj.cleanup()
                    if temp_user_data_dir_obj in self._managed_temp_dirs:
                        self._managed_temp_dirs.remove(temp_user_data_dir_obj)
                except Exception as e_cleanup_generic:
                    self.logger.error(
                        f"BrowserService: Error cleaning up temp_user_data_dir after generic launch failure: {e_cleanup_generic}")
            raise ConnectionError(
                f"Generic error creating persistent browser context: {e_generic_launch}") from e_generic_launch

    async def close(self):
        self.logger.info(
            "Closing BrowserService: cleaning up temporary user data directories and stopping Playwright...")

        # Clean up all managed temporary directories
        # Contexts themselves should be closed by their users (e.g., PacerOrchestrator)
        for temp_dir_obj in self._managed_temp_dirs:
            try:
                temp_dir_obj.cleanup()
                self.logger.debug(f"Cleaned up temporary user data directory: {temp_dir_obj.name}")
            except Exception as e:
                self.logger.error(f"Error cleaning up temp user data dir {temp_dir_obj.name}: {e}", exc_info=False)
        self._managed_temp_dirs.clear()

        if self.playwright:  # Corrected: Removed .is_connected
            try:
                await self.playwright.stop()
                self.logger.info("Playwright stopped.")
            except Exception as e:  # Catch potential errors during stop
                self.logger.error(f"Error stopping Playwright: {e}", exc_info=False)
        else:
            self.logger.info("Playwright was not running or already stopped.")

        self.playwright = None  # Reset playwright state

    async def __aenter__(self):
        await self.connect()  # Ensures Playwright is started
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()