from typing import Dict, Any, List, Optional
import logging
import re

class CaseRelevanceEngine:
    def __init__(self, relevance_config: Dict[str, Any],
                 relevant_defendants_lower: List[str],
                 usa_defendant_regex_compiled: re.Pattern,
                 current_court_id: str,
                 logger: Optional[logging.Logger] = None):
        self.relevance_config = relevance_config
        self._relevant_defendants_lower = relevant_defendants_lower
        self.usa_defendant_regex_compiled = usa_defendant_regex_compiled
        self.court_id = current_court_id
        if logger:
            self.logger = logger
        else:
            # Fallback to a default logger for this component
            self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}.{self.court_id}")
        self.logger.info(f"{self.__class__.__name__}.{self.court_id} initialized.")

    def _is_relevant_nos(self, nos: Optional[str]) -> bool:
        if not nos: return False
        nos_lower = nos.lower()
        relevant_keywords = self.relevance_config.get('relevant_nos_keywords', [])
        return any(keyword.lower() in nos_lower for keyword in relevant_keywords)

    def _is_relevant_cause(self, cause: Optional[str]) -> bool:
        if not cause: return False
        cause_lower = cause.lower()
        excluded_statutes = self.relevance_config.get('excluded_cause_statutes', [])
        irrelevant_keywords = self.relevance_config.get('irrelevant_cause_keywords', [])

        cause_pattern = r'\d{1,2}:\d{4}'
        found_statutes = re.findall(cause_pattern, cause_lower)

        if any(statute in excluded_statutes for statute in found_statutes):
            return False
        if any(keyword.lower() in cause_lower for keyword in irrelevant_keywords):
            return False
        return True

    def _is_relevant_defendant(self, case_details: Dict[str, Any]) -> bool:
        # --- START DEBUG LOGGING ---
        docket_num_log = case_details.get('docket_num', 'N/A_DEF_CHECK')
        log_prefix = f"[{self.court_id}][{docket_num_log}] IsRelevantDefendant:"
        self.logger.debug(f"{log_prefix} Checking defendant relevance.")
        self.logger.debug(f"{log_prefix} Loaded relevant defendant keywords (lowercase): {self._relevant_defendants_lower}")
        # --- END DEBUG LOGGING ---

        if not self._relevant_defendants_lower:
            self.logger.warning(f"{log_prefix} Relevant defendant list is empty or not loaded, cannot perform check.")
            return False

        defendants_field = case_details.get('defendant', [])
        versus_text = str(case_details.get('versus', '')).lower()
        defendant_names_lower = []

        # --- START DEBUG LOGGING ---
        self.logger.debug(f"{log_prefix} Extracted 'defendant' field: {defendants_field}")
        self.logger.debug(f"{log_prefix} Extracted 'versus' field (lower): '{versus_text}'")
        # --- END DEBUG LOGGING ---

        if isinstance(defendants_field, list):
            for item in defendants_field:
                if isinstance(item, dict):
                    defendant_names_lower.append(str(item.get('name', '')).lower())
                elif isinstance(item, str):
                    defendant_names_lower.append(item.lower())
        elif isinstance(defendants_field, str):
            defendant_names_lower.append(defendants_field.lower())

        all_names_to_check = defendant_names_lower + [versus_text]
        # --- START DEBUG LOGGING ---
        self.logger.debug(f"{log_prefix} Combined text to check (lower): {all_names_to_check}")
        # --- END DEBUG LOGGING ---

        for name_or_versus in all_names_to_check:
            for d_keyword in self._relevant_defendants_lower:
                if d_keyword in name_or_versus:
                    self.logger.info(f"{log_prefix} MATCH FOUND: Keyword '{d_keyword}' in text '{name_or_versus[:100]}...' -> Relevant")
                    return True # Return True as soon as a match is found

        self.logger.info(f"{log_prefix} NO MATCH FOUND after checking all keywords/text -> Irrelevant")
        return False

    def _is_usa_defendant(self, case_details: Dict[str, Any]) -> bool:
        if self.court_id == "nced": # TODO: Consider moving "nced" exception to config?
            return False

        defendants_field = case_details.get('defendant', [])
        versus_lower = str(case_details.get('versus', '')).lower()
        defendant_names_lower = []

        if isinstance(defendants_field, list):
            for item in defendants_field:
                if isinstance(item, dict):
                    defendant_names_lower.append(str(item.get('name', '')).lower())
                elif isinstance(item, str):
                    defendant_names_lower.append(item.lower())
        elif isinstance(defendants_field, str):
            defendant_names_lower.append(defendants_field.lower())

        combined_text_to_check = [versus_lower] + defendant_names_lower
        for text in combined_text_to_check:
            if self.usa_defendant_regex_compiled.search(text):
                self.logger.debug(f"USA defendant pattern found in text: '{text[:100]}...'")
                return True
        return False

    def case_needs_review(self, case_details: Dict[str, Any]) -> bool:
        """
        Determines if a case needs review. Updates case_details with '_reason_review'.
        Returns True if needs review, False otherwise.

        This is the main method for checking case relevance.
        """
        cause = case_details.get('cause', '')
        nos = case_details.get('nos', '')
        versus_lower = str(case_details.get('versus', '')).lower()
        cause_lower = cause.lower()

        explicitly_relevant_causes = self.relevance_config.get('explicitly_relevant_causes', [])
        explicitly_relevant_nos = self.relevance_config.get('explicitly_relevant_nos', [])
        irrelevant_defendant_keywords = self.relevance_config.get('irrelevant_defendant_keywords', [])
        irrelevant_cause_keywords = self.relevance_config.get('irrelevant_cause_keywords', [])
        potentially_relevant_nos = self.relevance_config.get('potentially_relevant_nos', [])

        if any(cause == rel_cause for rel_cause in explicitly_relevant_causes):
            return False  # RELEVANT
        if any(rel_nos in str(nos) for rel_nos in explicitly_relevant_nos):
            return False  # RELEVANT

        if self._is_usa_defendant(case_details):
            case_details['_reason_review'] = "USA Defendant"
            return True  # NEEDS REVIEW

        if any(keyword in versus_lower for keyword in irrelevant_defendant_keywords):
            case_details['_reason_review'] = f"Review Defendant Keyword in Versus"
            return True  # NEEDS REVIEW

        if any(keyword in cause_lower for keyword in irrelevant_cause_keywords):
            case_details['_reason_review'] = f"Review Cause Keyword in Cause Text"
            return True  # NEEDS REVIEW

        is_rel_cause = self._is_relevant_cause(cause)
        if not is_rel_cause:
            case_details['_reason_review'] = f"Cause Needs Review (Excluded Statute or Keyword) ('{cause[:50]}...')"
            return True  # NEEDS REVIEW

        if any(pot_nos in str(nos) for pot_nos in potentially_relevant_nos):
            if not self._is_relevant_defendant(case_details):
                case_details['_reason_review'] = f"Potentially Relevant NOS ('{nos}') but Review Defendant"
                return True  # NEEDS REVIEW
            else:
                return False  # RELEVANT

        is_rel_nos = self._is_relevant_nos(nos)
        if not is_rel_nos:
            case_details['_reason_review'] = f"NOS Needs Review ('{nos}')"
            return True  # NEEDS REVIEW

        return False  # RELEVANT

    def is_case_irrelevant(self, case_details: Dict[str, Any]) -> bool:
        """
        DEPRECATED: Use case_needs_review() instead.
        Determines if a case needs review. Updates case_details with '_reason_review'.
        Returns True if needs review, False otherwise.

        This method is kept for backward compatibility.
        """
        return self.case_needs_review(case_details)

    def check_overall_relevance(self, case_details: Dict[str, Any]) -> bool:
        """
        Determines if a case needs review. Updates case_details with '_reason_review'.
        Returns True if needs review, False otherwise.

        This is an alias for case_needs_review to maintain backward compatibility.
        """
        return self.case_needs_review(case_details)

    def should_exclude_removal_defendant(self, case_details: Dict[str, Any]) -> bool:
        versus = str(case_details.get('versus', '')).lower()
        defendant_list = case_details.get('defendant', [])
        excluded_keywords = self.relevance_config.get('excluded_removal_defendant_keywords', [])

        if not excluded_keywords:
            return False

        defendant_names_lower = []
        if isinstance(defendant_list, list):
            for item in defendant_list:
                name = (str(item.get('name', '')).lower() if isinstance(item, dict)
                        else item.lower() if isinstance(item, str) else "")
                if name: defendant_names_lower.append(name)
        elif isinstance(defendant_list, str) and defendant_list:
            defendant_names_lower.append(defendant_list.lower())

        combined_text_to_check = [versus] + defendant_names_lower
        for text_part in combined_text_to_check:
            if any(keyword.lower() in text_part for keyword in excluded_keywords):
                case_details['_reason_review'] = f"Excluded Removal Defendant Keyword in '{text_part[:50]}...'"
                return True
        return False