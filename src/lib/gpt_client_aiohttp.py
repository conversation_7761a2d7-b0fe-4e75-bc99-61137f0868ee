# --- Imports Update ---
import asyncio
import json
import logging
from typing import Optional, Dict, Any
import aiohttp
from rich.console import Console

# --- Standard Logger Setup ---
logger = logging.getLogger(__name__)  # Use __name__ for better hierarchy

class ServiceUnavailableError(Exception):
    pass

class RequestException(Exception):
    pass

class RetryableError(Exception):
    pass

class GPTClient:
    def __init__(self, 
                 config: Dict[str, Any], 
                 session: Optional[aiohttp.ClientSession] = None,
                 **kwargs):  # Use **kwargs for backward compatibility
        """
        Initialize GPTClient with backward compatibility.
        
        Args:
            config: Configuration dictionary
            session: Optional aiohttp ClientSession
            **kwargs: Additional arguments (including logger) for compatibility
        """
        self.api_key = config.get('openai_api_key')
        if not self.api_key:
            raise ValueError("OpenAI API key not found in config")
            
        self.session = session
        self.console = Console()
        self._initialized = False
        
        # Handle logger with backward compatibility
        self.logger = kwargs.get('logger', logger)
        
    def set_session(self, session: aiohttp.ClientSession):
        """Set the aiohttp session after initialization."""
        self.session = session
        self._initialized = True
        
    async def initialize(self):
        """Initialize the client session if not provided."""
        if not self.session:
            self.session = aiohttp.ClientSession()
        self._initialized = True
        self.logger.debug("GPTClient initialized")

    async def chat_completion(
        self,
        prompt: str,
        system_prompt: str = None,
        json_mode: bool = False,
        temperature: float = 0.7,
        max_tokens: int = 2000
    ) -> Dict[str, Any]:
        """
        Send a chat completion request to GPT API.
        """
        if not self._initialized:
            await self.initialize()

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})

        data = {
            "model": "gpt-4o-mini",
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens,
            "response_format": {"type": "json_object"} if json_mode else None
        }

        try:
            async with self.session.post(
                "https://api.openai.com/v1/chat/completions",
                headers=headers,
                json=data
            ) as response:
                if response.status == 503:
                    self.logger.error("OpenAI API is temporarily unavailable")
                    raise ServiceUnavailableError("OpenAI API is temporarily unavailable")
                
                response_data = await response.json()
                
                if response.status != 200:
                    error_msg = f"API request failed: {response_data.get('error', {}).get('message', 'Unknown error')}"
                    self.logger.error(error_msg)
                    raise RequestException(error_msg)

                return json.loads(response_data['choices'][0]['message']['content']) if json_mode else response_data['choices'][0]['message']['content']

        except aiohttp.ClientError as e:
            self.logger.error(f"Network error during API call: {e}")
            raise RetryableError(f"Network error during API call: {e}")
