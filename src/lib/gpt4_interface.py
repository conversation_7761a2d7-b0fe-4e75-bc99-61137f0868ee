import asyncio
import json
import logging
import re
from typing import Dict, Any, Op<PERSON>, Tu<PERSON>, List, Union

import backoff
import requests
import tiktoken
from requests.exceptions import RequestException, ConnectionError, Timeout

from src.lib.utils.law_firm_normalizer import normalize_law_firm_name


class GPT4:
    def __init__(self, config: Dict[str, Any], default_model: str = 'gpt-4o'):
        self.api_key = config['openai_api_key']
        self.default_model = default_model
        self.endpoint = "https://api.openai.com/v1/chat/completions"
        self.encoding = tiktoken.get_encoding("cl100k_base")
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }
        self.logger = logging.getLogger(__name__)

        self.prompt_settings = {
            'full': ('prompt_full', 'gpt-4o', 4096),
            'federal': ('prompt_allegations_and_claims', 'gpt-4o', 4096),
            'short_summary': ('prompt_short_summary', 'gpt-4o-mini', 4096),
            'mdl': ('direct_mdl', 'gpt-4o-mini', 1024),
            'fb_ad_summary': ('prompt_fb_ad_summary', 'gpt-4o-mini', 1024),
            'get_attorney_info': ('prompt_extract_attorney_info', 'gpt-4o', 2048),
            'find_removal_link': ('prompt_find_state_filings', 'gpt-4o-mini', 100),
            'get_court_id': ('prompt_get_court_id', 'gpt-4o-mini', 100),
            'parse_sig_page': ('prompt_signature_page', 'gpt-4o-mini', 2048),
            'format_fb_body': ('format_fb_body', 'gpt-4o', 1024),
            'detailed_description': ('prompt_detailed_description', 'gpt-4o', 2048),
            'new_summary': ('prompt_new_summary', 'gpt-4o', 2048),
            'create_title': ('prompt_create_title', 'gpt-4o', 500),
            'classify_ad': ('prompt_classify_ad', 'gpt-4o-mini', 200),
        }

        self.prompt_get_court_id = """
                Given a string describing a court, return a JSON object with the court name and court ID based on the state and district information. The JSON object should be formatted with no additional text before or after the JSON object.

                    Format:
                    - If the district is specified (e.g., Eastern, Northern), include it in the court name and court ID.
                    - If only the state is listed without a specific district, use the state abbreviation as the court ID.

                    Examples:
                    - Input: 'USDC-Eastern District of Louisiana (New Orleans), 2:24-cv-01496-JCZ-MBN-'
                      Output: {"court_name": "Louisiana Eastern", "court_id": "laed"}

                    - Input: 'USDC-New Jersey District, 1:23-cv-01234-ABC-DEF-'
                      Output: {"court_name": "New Jersey", "court_id": "njd"}

                    - Input: 'USDC-District of Columbia, 3:22-cv-05678-GHI-JKL-'
                      Output: {"court_name": "District of Columbia", "court_id": "dcd"}

                    - Input: 'USDC-Western District of Texas (Austin), 5:20-cv-04567-STU-VWX-'
                      Output: {"court_name": "Texas Western", "court_id": "txwd"}

                    - Input: 'USDC-Northern District of California (San Francisco), 6:19-cv-07890-YZA-BBC-'
                      Output: {"court_name": "California Northern", "court_id": "cand"}

                    The prompt to be processed is:

                    <insert your string here>
                    """

        self.prompt_extract_attorney_info = """
                        Prompt: Extract detailed attorney and law firm information from legal document signature pages. Each entry should include the attorney's name, bar number, firm name, address, phone number, fax (if available), and email address. Additionally, extract the filing date and docket number.

                        Input Format:
                        - Text containing one or more signature blocks from legal documents.

                        Expected Output:
                        - A JSON object with the following keys: 'filing_date', 'docket_number', and 'attorneys'. The 'attorneys' key should be an array of objects, each containing the attorney's name, bar number, firm name, and address.

                        For each attorney:
                        1. Extract the attorney's name and any listed bar number.
                        2. Identify the law firm's name.
                        3. Capture the full address, including street, suite (if available), city, state, and zip code.
                        4. Record phone numbers and, if provided, fax numbers.
                        5. List email addresses associated with the attorney.
                        6. Extract the filing date in the format MM/DD/YY.
                        7. Extract the docket number from lines formatted as "Case: X:YY-cc-ZZZZZ Document #: N Filed: MM/DD/YY Page A of B PageID #: C". The docket number format is one or two digits, followed by a colon, followed by two digits, followed by a dash, followed by two alphabetical characters, followed by a dash, followed by five digits. This information will always be in the line where it says "Page A of B, PageID #: C". The filing date will also be in the same line.
                        8. Ensure that each attorney is correctly associated with the right law firm.

                        If you cannot find an item, return NA for that value.

                        The JSON structure should appear as follows and must use double quotes for all keys and values:

                        {
                          "filing_date": "Filing Date",
                          "docket_number": "Docket Number",
                          "attorneys": [
                            {
                              "attorney_name": "Name",
                              "bar_number": "Bar Number",
                              "law_firm": "Law Firm Name",
                              "address": {
                                "street": "Street Address",
                                "suite": "Suite Number",
                                "city": "City",
                                "state": "State",
                                "zip_code": "Zip Code"
                              },
                              "phone_number": "Phone Number",
                              "fax_number": "Fax Number",
                              "email": "Email Address"
                            }
                          ]
                        }

                        Please ensure the response is valid JSON and uses double quotes for all keys and values. Do not include any backticks, markdown, or other non-JSON formatting in the response. The response should be a plain JSON object.
                        """

        self.prompt_fb_ad_summary = """
                        Prompt for Generating Litigation Summaries from Facebook Ads

                        Objective: Create a concise summary of potential litigation or investigation topics from Facebook ad content, focusing specifically on the entity involved and the nature of the legal matter, without mentioning the law firm name. Summaries should be no longer than 10 words, ideally between 6-8 words.

                        Input Parameters:

                        Title: A brief headline of the ad, which usually indicates the subject matter.
                        Body: The main text content of the ad, detailing the issue at hand and the parties affected.
                        Link Description: Supplementary information provided in the link description, which may offer additional context. If this description is "NA" or "None", it should be disregarded.
                        Law Firm Name: Name of the law firm associated with the ad (This is for context only and must not be included in the summary output).
                        Expected Output:

                        A JSON object containing a "summary" key. The value should be a succinct description of the legal concern, prominently featuring the company's name and the specific issue. If the ad content is too generic or lacks a distinct legal challenge or investigation, the summary should return "NA".
                        Summary Determination Guidelines:

                        Default to using "Investigation" in the summary unless the ad content explicitly refers to an ongoing lawsuit.
                        Use "Lawsuit" in the summary specifically when "Camp Lejeune" is mentioned.
                        If the general content mentions CooperSurgical and in-vitro fertilization, the summary should be 'CooperSurgical IVF Culture Media Product Liability'.
                        If the general content mentions Teva Pharmaceuticals and Paragard, the summary should be 'Paragard IUD Product Liability Litigation'.
                        If the general content mentions video games and addiction, the summary should be 'Video Game Addiction Investigation'.
                        If the general content mentions PFAS contamination, the summary should be 'PFAS Contamination Investigation'.
                        For ads that mention a specific geographical location, include that location in the summary. For example, an ad mentioning ethylene oxide exposure in the Smyrna area should be summarized as "Smyrna Ethylene Oxide Cancer Investigation".

                        Return "NA" for ads that are generic solicitations for legal services without specifying a particular investigation or lawsuit (e.g., general advertisements for personal injury attorneys).
                        Include the name of the company or entity under scrutiny.
                        Clearly specify the nature of the issue being investigated or litigated.
                        Ensure the law firm's name is not mentioned in the summary.
                        Examples:

                        Title: Failed Embryo Transfer Investigation
                        Body: Were your embryos damaged due to an incubator malfunction? Contact us to discuss.
                        Link Description: Learn more about how we can help.
                        Law Firm Name: XYZ Legal Group
                        Output: {"summary": "Ovation Fertility Embryo Damage Investigation"}

                        Title: Camp Lejeune Water Contamination
                        Body: Exposed to toxic water at Camp Lejeune? Veterans and their families may be eligible for compensation.
                        Link Description: Contact us for immediate legal support.
                        Law Firm Name: Legal Solutions Firm
                        Output: {"summary": "Camp Lejeune Lawsuit"}

                        Title: Get the Compensation You Deserve
                        Body: Injured? Our experienced personal injury attorneys can help you win your case.
                        Link Description: Call us today!
                        Law Firm Name: Injury Lawyers Now
                        Output: {"summary": "NA"}

                        The word 'Compensation' should never be used in the summary'.
                        Use this prompt to generate precise and relevant summaries for legal professionals regarding ongoing investigations or lawsuits highlighted in Facebook advertisements, while filtering out generic legal service offers and excluding the law firm's name from the summaries.
                        """

        self.prompt_signature_page = (
            "Given the text extracted from a legal document's signature page, extract the law firm's name representing the plaintiff. "
            "If the attorney's name follows 'LAW OFFICE OF' without a specific firm name, format it as 'LAW OFFICE OF [ATTORNEY NAME]'. "
            "For example, if the signature reads 'Robert King, Esq.\nLAW OFFICE OF', the output should be 'LAW OFFICE OF ROBERT KING'. "
            "If no law firm is mentioned but an attorney's name is present, return the attorney's name followed by ', Attorney'. "
            "Capitalize the first letter of each word in the law firm name, except for abbreviations like LLP, PC, etc., which should remain capitalized without periods. "
            "Include a comma and space before these abbreviations if they are at the end of the law firm name (e.g., 'McDonald Worley, PC'). "
            "Structure the JSON object as follows and **return only the JSON object with no additional text**:\n"
            "{\n"
            '  "law_firm": "[Law Firm]"\n'
            "}\n"
            "List multiple law firms separated by semicolons. If no law firm is mentioned, use 'NA'."
        )

        self.prompt_signature_page_old = (
            "Given the text extracted from a legal document, generate a JSON object summarizing the key elements. "
            "Focus on identifying the law firm representing the plaintiff, usually found in the signature block of the document. "
            "If the attorney's name follows 'LAW OFFICE OF' without a specific firm name, format it as 'LAW OFFICE OF [ATTORNEY NAME]'. "
            "For example, if the signature reads 'Robert King, Esq.\nLAW OFFICE OF', the output should be 'LAW OFFICE OF ROBERT KING'. "
            "If no law firm is mentioned but an attorney's name is present, return the attorney's name followed by ', Attorney'. "
            "Capitalize the first letter of each word in the law firm name, except for abbreviations like LLP, PC, etc., which should remain capitalized. "
            "Remove periods from these abbreviations (e.g., 'L.L.C' should be formatted as 'LLC', 'P.C.' as 'PC'), and include a comma and space before these abbreviations if they are at the end of the law firm name. "
            "For example, 'MCDONALD WORLEY, PC'. All names should be returned with the first letter of each word capitalized as appropriate, "
            "so 'MCDONALD WORLEY, PC' should be 'McDonald Worley, PC'. If the name is 'NS PR Law Services LLC', 'NS PR' should remain capitalized. "
            "If the signature block contains 'Attorney for defendant' or 'Attorneys for defendant', return 'Defense Filing'"
            "Structure the JSON object as follows:\n"
            "{\n"
            "  'law_firm': '[Law Firm]'\n"
            "}\n"
            "List multiple law firms separated by semi-colons. If no law firm is mentioned, use 'NA'."
            "Please complete the JSON structure based on the document content."
        )

        self.prompt_get_attorney_info = """
                                Prompt: Extract detailed attorney and law firm information from legal document signature pages. Each entry should include the attorney's name, bar number, firm name, address, phone number, fax (if available), and email address.

                                Input Format:
                                - Text containing one or more signature blocks from legal documents.

                                Expected Output:
                                - Return only the JSON block containing a list of JSON objects, each representing an attorney listed on the signature page. Each JSON object should contain the following keys: 'attorney_name', 'bar_number', 'law_firm', 'address', 'phone_number', 'fax_number' (if available), 'email_address'. Do not include any additional commentary or statements.

                                For each attorney:
                                1. Extract the attorney's name and any listed bar number including state(s) as listed.
                                2. Identify the law firm's name.
                                3. Capture the full address, including street, suite (if available), city, state, and zip code.
                                4. Record phone numbers and, if provided, fax numbers.
                                5. List email addresses associated with the attorney.

                                If you cannot find an item, return NA for that value.

                                The JSON structure should appear as follows for each attorney:

                                [
                                  {
                                    "attorney_name": "Name",
                                    "bar_number": "Bar Number",
                                    "law_firm": "Law Firm Name",
                                    "address": {
                                      "street": "Street Address",
                                      "suite": "Suite Number",
                                      "city": "City",
                                      "state": "State",
                                      "zip_code": "Zip Code"
                                    },
                                    "phone_number": "Phone Number",
                                    "fax_number": "Fax Number",
                                    "email_address": "Email Address"
                                  },
                                  ...
                                ]
                                """

        self.prompt_detailed_description = """
            "You are an attorney drafting a description of the allegations, claims, and title for a mass tort case. Write in a clear, concise, and professional tone. The description should focus on the following key points:
            Plaintiff’s Injuries & Device/Product:
            The plaintiff is suing the defendants for injuries caused by [insert product/device], which allegedly led to [specific injury or condition]. The plaintiff claims that the defendants [failed to adequately warn/misrepresented the product, etc.] about the risks and side effects associated with the device.
            Defendant’s Alleged Misconduct:
            The defendants are accused of [specific misconduct], and the plaintiff alleges that the defendants were negligent in [designing, manufacturing, marketing, etc.] the product, leading to [specific injury/condition].
            Legal Claims:
            The plaintiff is pursuing claims of [negligence, product liability, breach of warranty, etc.] and seeks damages for [injuries, emotional distress, medical expenses, etc.].
            Legal Category:
            [e.g., Product Liability]
            This description should reflect the level of detail and professionalism expected in legal filings or communications among attorneys."
        """

        self.prompt_full_backup = (
            "Given the text extracted from a legal document, generate a JSON object that summarizes the key elements. "
            "The summary should include the following fields: \n"
            "1. 'title': Search for the phrase 'IN RE: [Case Description]' within the document to identify the title of the case. "
            "Capitalize the first letter of each major word in the title, while keeping abbreviations and company acronyms (like 'INC.', 'LLC') in uppercase. 'IN RE:' should remain capitalized. "
            "For example, from 'IN RE: DAVOL, INC./C.R. BARD, INC., POLYPROPYLENE HERNIA MESH PRODUCTS LIABILITY LITIGATION', "
            "it should be formatted as 'In Re: Davol, Inc./C.R. Bard, Inc., Polypropylene Hernia Mesh Products Liability Litigation'.\n"
            "2. 'lawFirm': Determine the law firm representing the plaintiff, which is usually listed in the signature block. "
            "If the signature block follows 'LAW OFFICE OF' without a specific firm name, format it as 'LAW OFFICE OF [ATTORNEY NAME]'. "
            "If no law firm is mentioned but an attorney’s name is present, return the attorney’s name followed by ', Attorney'. "
            "If no law firm or attorney's name is mentioned, enter 'NA'.\n"
            "If the signature block contains 'Attorney for defendant' or 'Attorneys for defendant', return 'Defense Filing'"
            "3. 'allegations': Summarize the allegations in a narrative format, focusing on factual details and specifically mentioning any involved devices or products.\n"
            "4. 'claims': List the types of legal claims made, and include a general category of the lawsuit at the end.\n\n"
            "Additionally, format the output as follows:\n"
            "5. 'mdl_num': Identify if the litigation is part of a Multidistrict Litigation (MDL). Look for an MDL number, usually following 'MDL No.' or within a case number (e.g., '1:24-md-03092-JPC'). "
            "MDL numbers are typically preceded by 'md-' or 'mdl-' and consist of a 4-digit integer. Extract and return only the 4-digit integer, without any prefix or additional text.\n"
            "{\n"
            "  'title': '[Title]',\n"
            "  'law_firm': '[Law Firm]',\n"
            "  'allegations': 'The plaintiff is suing the defendant(s) for [describe the allegations concisely, mentioning any specific devices or products involved].',\n"
            "  'claims': 'The lawsuit claims [brief description of the claims such as defects, negligence, lack of warnings], and as a result, the plaintiff seeks [mention the type of damages or relief sought]. [Category of the complaint]',\n"
            "  'mdl_num': '[MDL Number]'  // Note: Return only the 4-digit integer of the MDL number.\n"
            "}\n"
            "Please construct this JSON structure based on the content of the document."
        )

        self.prompt_create_title = """
            INSTRUCTIONS: Create a concise litigation title based on the following text. The title should follow this format:
            [Primary Defendant] [Primary Issue/Product] Litigation
    
            Generate the litigation title using these steps:
    
            1. Identify the primary plaintiff(s):
               - If there is only one plaintiff, use their name.
               - If there are multiple plaintiffs, use the first plaintiff's name followed by "et al."
    
            2. Identify the primary or most well-known defendant(s):
               - If there is only one defendant, use their name.
               - If there are multiple defendants, use the most well-known defendant's name followed by "et al."
    
            3. Determine the primary product, service, or general category of the litigation.
               - Specify any products, services, or procedures involved in the case.
               - If class action is explicitly mentioned in the case, include "Class Action" at the end of the title.
    
            4. Construct the title in this format:
               [Primary Defendant] [Primary Issue/Product] Litigation [Class Action, if applicable]
    
            Examples:
            "The Healing Lodge of the Seven Nations Treatment Facility for Minors Sex Abuse Litigation"
            "XYZ Corporation Product Liability Litigation Class Action"
    
            Do not include "Class Action" unless it is specifically mentioned in the document.
            Do not add a period at the end.
            Do not include any explanations or additional text. Only return the title.
    
            TEXT TO SUMMARIZE:
        """

        self.prompt_full = """
                Given text extracted from a legal document, generate a JSON object summarizing the key elements with the following fields:

                1. 'title': First, search for the phrase 'IN RE: [Case Description]' within the document to identify and format the title, capitalizing the first letter of each major word and keeping abbreviations and acronyms (like 'INC.', 'LLC') in uppercase. If 'IN RE:' is not found:
                   - Identify the primary or most well-known defendant in the document.
                   - Determine the primary product or general category of litigation involved. For specific categories:
                     - If the litigation involves asbestos without specifying a product, use 'Asbestos Litigation'.
                     - For PFAS or PFOA cases involving a municipality without specifying a specific product, format as '[Municipality Name] PFAS Litigation'.
                     - If the case mentions CooperSurgical and in vitro fertilization, the title should be 'IN RE: THE COOPER COMPANIES, INC., IN VITRO FERTILIZATION GLOBAL CULTURE MEDIA PRODUCTS LIABILITY LITIGATION'.
                     - If the case mentions Teva Pharmaceuticals and Paragard, the title should be 'IN RE: PARAGARD PRODUCTS LIABILITY LITIGATION'
                     - If the case mentions AngioDynamics and Port Catheter, the title should be '− IN RE: ANGIODYNAMICS, INC., AND NAVILYST MEDICAL, INC.,PORT CATHETER PRODUCTS LIABILITY LITIGATION'.
                     - If the case mentions video game addiction, the title should be 'Video Game Addiction Product Liability'.
                     - If the case mentions Abbott Laboratories and necrotizing entercolitis, the title should be 'IN RE: Abbott Laboratories et al. Preterm Infant Nutrition Products Liability Litigation'.
                     - If the case mentions Monsanto and Roundup, the title should be 'IN RE: Roundup Products Liability Litigation'.
                     - If the case mentions Zimmer Biomet Hip Replacement, the title should be 'IN RE: ZIMMER M/L TAPER HIP PROSTHESIS OR M/L TAPER HIP PROSTHESIS WITH KINECTIV TECHNOLOGY AND VERSYS FEMORAL HEAD PRODUCTS LIABILITY LITIGATION'.
                     - If the case mentions pressure cooker or instant pot, the title should be 'Pressure Cooker/Instant Pot Product Liability'
                     - If the case mentions Exactech and hip, knee, or ankle replacement, the title should be 'IN RE: EXACTECH POLYETHYLENE ORTHOPEDIC PRODUCTS LIABILITY LITIGATION'.
                   - Construct the title by combining the defendant's name with the product or category, ensuring that it does not include plaintiff names unless specified by "IN RE:". Example: 'Allergan USA and Lifecell Corporation Strattice Hernia Mesh Device'.
                   - If the the content is related to sex abuse or abuse, it should be named '{name of institution} Sex Abuse Litigation'. For example, if the named institution is the Catholic Churich the title would be 'Catholic Church Sex Abuse Litigation'.
                   - If it meets not of the criteria above, the title should be '{company} {product name} Product Liability Litigation'.
                   - Under no circumstances are you to use the defendant in the title name.
                   - Under no circumstances are you to use the defendant in the title name. 

                2. 'law_firm': Determine the representing law firm, typically listed in the signature block. If the text follows 'LAW OFFICE OF' without specifying a firm name, format it as 'Law Office of [Attorney Name]'. If no firm is mentioned but an attorney’s name is present, append ', Attorney' to the name. If neither is mentioned, return 'NA'. Keep corporate acronyms like 'LLC', 'PLLC', 'INC' without periods. If the signature block contains 'Attorney for defendant' or 'Attorneys for defendant', return 'Defense Filing'.
                    If there is more than one law firm mentioned, list each law firm with a semi-colon between it.

                3. 'allegations': Summarize the allegations in a narrative format, focusing on factual details and specifically mentioning any involved devices or products.

                4. 'claims': List the types of legal claims made, including a general category of the lawsuit at the end.

                5. 'plaintiff': If the document includes "SHORT FORM COMPLAINT", use the name following "Applies To" as the plaintiff. Otherwise, identify the plaintiff's name mentioned before 'v.', which indicates the opposing party in the case. The plaintiff's name should follow directly after "IN RE:" or "Plaintiff," if specified.

                6. 'defendant': Identify the name(s) of the defendant(s), typically found immediately after 'v.' on the first page. If multiple defendants are listed, return only the first one.

                7. 'mdl_num': Identify if the litigation is part of a Multidistrict Litigation (MDL). Look for an MDL number, usually following 'MDL No.' or within a case number (e.g., '1:24-md-03092-JPC'). Extract and return only the 4-digit integer.
                    - If the document mentions Monsanto Company Roundup Herbicide, return an mdl_num of 2741.
                    - If the document mentions Teva Pharmaceuticals and Paragard, return an mdl_num of 2974.
                    - If the document mentions Bard or Davol and Bard Implanted Port Catheter, return an mdl_num of 3081.
                    - If the document mentions AngioDynamics and Port Catheter, return an mdl_num of 3125.
                    - If the document mentions Aqueous Film-Forming Foams Products Liability Litigation, return an mdl_num of 2873.
                    - If the document mentions Lifecell Strattice hernia mesh, return an mdl_num of 17705.
                    - If the document mentions Future Motion, return an mdl_num of 3087.
                    - If the document mentions Necrotizing Enterocolitis, return an mdl_num of 3026. 
                    - If the document mentions Video Game Addiction, return an mdl_num of 3091.
                    - If the document mentions Monsanto and Roundup, return an mdl_num 2741.
                    - If the document mentions Zimmer Biomet Hip Replacement, return an mdl_num 2859.
                    - if the document mentions pressure cooker or instant pot, return an mdl_num 9000.
                    - If the document mentions CooperSurgical and in vitro fertilization, return an mdl_num of 3122.
                    - If the case mentions Exactech and hip, knee, or ankle replacement, return an mdl_num of 3044.
                    - If the document mentions Depo-Provera, return an mdl_num of 3140.
                    - If the document mentions Church of Latter-day Saints and sex abuse return an mdl_num of 3150.


                JSON Structure:
                {
                  "title": "[Title]",
                  "law_firm": "[Law Firm]",
                  "allegations": "The plaintiff is suing the defendant(s) for [describe the allegations concisely, mentioning any specific devices or products involved].",
                  "claims": "The lawsuit claims [brief description of the claims such as defects, negligence, lack of warnings], and as a result, the plaintiff seeks [mention the type of damages or relief sought]. [Category of the complaint]",
                  "plaintiff": "[Name of the Plaintiff]",
                  "defendant": "[Name(s) of the Defendant(s)]",
                  "mdl_num": "[MDL Number]"  // Note: Return only the 4-digit integer of the MDL number.
                }

                Please construct this JSON structure based on the content of the document. There should be no characters before or after the first parenthesis and last parenthesis of the JSON Object.
                """

        self.prompt_allegations_and_claims = """
                Summarize federal-level allegations and claims in JSON format:
                {
                  "allegations": "Concise summary of allegations, mentioning devices/products",
                  "claims": "List main legal claims and lawsuit category"
                }
                Avoid specific names, dates, locations. Use 'state consumer protection laws' generally.
                """

        self.prompt_short_summary = """
            Provide a concise summary of the lawsuit in under 500 characters, including:
            1. Main parties involved
            2. Core issue with the product/service
            3. Resulting injuries/damages
            4. Key legal claims
            5. Critical products/services involved
            """

        self.prompt_new_summary = """Summarize the following legal case details:
            Identify the plaintiff, defendants, and the main cause of the lawsuit.
            Specify any products, services, or procedures involved and how they allegedly caused harm.
            Detail the plaintiff’s allegations regarding the nature of injuries or harm suffered, including specific injuries or damages claimed.
            Include a brief overview of the damages the plaintiff is seeking.
            Respond in the following format: Plaintiff is suing Defendants because reason for lawsuit. Plaintiff claims that the use of product/service led to specific injury/harm. They allege that details of how the injury occurred. Plaintiff is seeking damages for procedures/costs incurred and any additional damages. 
            End with two words for the legal category (e.g., Product Liability) without adding any additional text or prefix.
        """

        self.direct_mdl = """
                        Given the text from the first page of a federal legal document, perform the following tasks:
                1. Identify the name of the plaintiff mentioned before the 'v.' which indicates the opposing party in the case. The plaintiff's name should follow directly after "IN RE:" or "Plaintiff," if specified.
                2. Identify the name(s) of the defendant(s), which are typically found immediately after the 'v.' on the first page. If there is more than one defendant listed, just return the first one.
                3. Extract the name of the law firm representing the plaintiff. If the law firm's name includes any of the following corporate structure acronyms with periods: 'LLC', 'PLLC', 'INC', 'PC', 'PA', 'LLP', 'LTD', 'PLC', these should be formatted without periods.

                Return the results in a JSON object structured as follows:

                {
                  "plaintiff": "<Name of the plaintiff>",
                  "defendant": "<Name(s) of the defendant(s)>",
                  "law_firm": "<Formatted name of the law firm>"
                }

                This task requires precise extraction of the specified details from the document, ensuring the name of the law firm is formatted correctly by removing periods from specific acronyms as listed.
                """

        self.prompt_find_state_filings = """
                Given the text of a docket entry for a case removed from state to federal court, identify the link to the civil complaint or the state court filings. 
                - You should ignore civil cover sheets and unrelated exhibits.
                - You should prioritize state court filings or plaintiffs original petition, and if those are not available, consider declarations with a person's name next to it.
                - The links are identified by descriptions after the word 'Filed by', and are listed as attachments. They typically follow a number that refers to the document.
                - The document is usually referred to is State Complaint, State Filing or Declaration (by plaintiff name).
                - If no relevant state court filing is found, return 'NA'.

                The docket entry is as follows:
                <insert docket entry text here>

                Return a JSON object with the link number as a numerical digit that refers to the state court filings or declarations, and if nothing is found, return 'NA'.
                The JSON object should have the format:
                {
                     "number": "{an integer number}"
                }
                It should return no other text except the example above. 
                """

        self.prompt_format_fb_body = """
            Format the following text for a Facebook ad. Ensure that the ad has proper line breaks.. If there are any emojis,
            do not remove them but make sure they are logically placed. Do not add any emojis or change the original text, 
            just adjust the formatting to handle line breaks properly.
            Here is the text:
            {insert_text_here}
        """

        self.prompt_classify_ad = """
                        {
                          "task": "Identify the corresponding legal campaign for a given litigation summary.",
                          "instructions": [
                            {
                              "step": "Compare the provided summary to the following list of legal campaigns and their definitions. Return the exact campaign name if there is a clear match. Otherwise, follow the special instructions for certain cases or return the original summary text if no match is found."
                            },
                            {
                              "step": "If the case involves a religious institution and mentions sexual abuse. If it mentions a state and sex abuse, just disregard the name and continue to classify:",
                              "example_format": "[Institution Name] Sex Abuse Investigation",
                              "details": "Use common names (e.g., 'Latter-day Saints' instead of 'Mormon Church'). Examples include Catholic Church, Baptist Church, Latter-day Saints, Jehovah's Witnesses, etc."
                            },
                            {
                              "step": "If the summary mentions doctors with allegations of abuse, assault, or inappropriate behavior:",
                              "example_format": "Dr. [Last Name] Sex Abuse Lawsuit",
                              "details": "Format as 'Dr. [Last Name] Sex Abuse Lawsuit'. Example: 'Dr. Mark Jones Assault Lawsuit' -> 'Dr. Jones Sex Abuse Lawsuit'."
                            },
                            {
                              "step": "For privacy violation or data sharing cases:",
                              "example_format": "[Institution Name] [Privacy Type] Privacy Violation",
                              "details": "When identifying a privacy violation case, return the institution or company name and type of privacy violation if specified. Examples include VPPA, CIPA, GIPA, BIPA, and DPPA. For instance, if Electronic Arts is involved in a VPPA case, return 'Electronic Arts VPPA Privacy Violation'."
                            },
                            {
                              "step": "If no specific privacy type is mentioned:",
                              "example_format": "[Institution Name] Privacy Violation",
                              "details": "If the case mentions a privacy violation but no specific type (e.g., VPPA, BIPA, GIP), return '[Institution Name] Privacy Violation'. Example: 'Pop Market Privacy Violation'."
                            },
                            {
                              "step": "If the ad mentions a data breach:",
                              "example_format": "[Institution Name] Data Breach Investigation",
                              "details": "Format as '[Institution Name] Data Breach' if the ad mentions a data breach."
                            },
                            {
                              "step": "If the provided summary does not match any of the specific legal campaigns listed, just return the initial Summary:",                                
                          ],
                          "legal_campaigns": [
                            {
                                "campaign": "RoundUp Products Liability",
                                "description": "Cases involving Roundup herbicide and cancer risks."
                            },
                            {
                                "campaign": "Paraquat Products Liability",
                                "description": "Cases involving Paraquat herbicide and Parkinson's disease."
                            },
                            {
                                "campaign": "Depo-Provera Products Liability",
                                "description": "Cases involving Depo Provera contraceptive or injectiable birth control and health risks."
                            },
                            {
                                "campaign": "Oxbryta Products Liability",
                                "description": "Cases involving Oxbryta medication and sickle cell disease complications."
                            },
                            {
                                "campaign": "Abbott Laboratories Preterm Infant Nutrition Products Liability",
                                "description": "Cases involving Similac formula and necrotizing enterocolitis (NEC)."
                            },
                            {
                                "campaign": "Johnson & Johnson Talcum Powder Litigation",
                                "description": "Cases involving talcum powder products and cancer."
                            },
                            {
                                "campaign": "Bard/AngioDynamics PowerPort Products Liability",
                                "description": "Cases involving PowerPort implants, AngioDynamics port implants, port-a-cath, ports or catheters implants and complications."
                            },
                            {
                                "campaign": "Ethylene Oxide Toxic Tort",
                                "description": "Cases involving medical sterilizers or Sterigenics, or EtO exposure and cancer. If it mentions a geographical location, disregard location and classify"
                            },
                            {
                                "campaign": "Suboxone Products Liability",
                                "description": "Cases involving Suboxone Film and dental damage."
                            },
                            {
                                "campaign": "Uber Technologies Sexual Assault",
                                "description": "Cases involving sexual assault in rideshare services."
                            },
                            {
                                "campaign": "Hair Relaxer Products Liability",
                                "description": "Cases involving chemical hair relaxers or chemical straighteners or paraben and cancer."
                            },
                            {
                                "campaign": "GLP-1 Products Liability",
                                "description": "Cases involving semaglutide, GLP-1,  Ozempic, Wegovy, Rybelsus, Trulicity and side-effects
                                 or health complications."
                            },
                            {
                                "campaign": "AFFF Products Liability",
                                "description": "Cases involving firefighting foam and PFAS or cancer."
                            },
                            {
                                "campaign": "Juvenile Detention Abuse",
                                "description": "Cases involving abuse or inappropriate touching or sexual assault/abuse 
                                in juvenile detention or youth facilities. If it mentions a county or municipality and sex abuse,
                                classify it as this campaign"
                            },
                            {
                                "campaign": "Bair Hugger Products Liability",
                                "description": "Cases involving Bair Hugger warming devices and infections."
                            },
                            {
                                "campaign": "Gardasil Products Liability",
                                "description": "Cases involving HPV vaccine and health issues."
                            },
                            {
                                "campaign": "Baby Food Products Liability",
                                "description": "Cases involving contaminated baby food, heavy metals, and health issues."
                            },
                            {
                                "campaign": "Exactech Products Liability",
                                "description": "Cases involving Exactech joint replacements."
                            },
                            {
                                "campaign": "Video Game Addiction Products Liability",
                                "description": "Cases involving video game addiction and related harm."
                            },
                            {
                                "campaign": "Social Media Addiction Products Liability",
                                "description": "Cases involving social media addiction, mental health, or child harm."
                            },
                            {
                                "campaign": "Zimmer Biomet Products Liability",
                                "description": "Cases involving Zimmer Biomet hip implants."
                            },
                            {
                                "campaign": "Veozah Products Liability",
                                "description": "Cases involving Veozah, liver damage, and hot flash treatments."
                            },
                            {
                                "campaign": "Dacthal Pesticide Products Liability",
                                "description": "Cases involving Dacthal Pesticide, AMVACO, and child injury/disability."
                            },
                            {
                                "campaign": "Asbestos Products Liability",
                                "description": "Cases involving asbestos, lung cancer, and mesothelioma."
                            },
                            {
                                "campaign": "PFAS Drinking Water Contamination",
                                "description": "Cases involving contaminated water, water contamination, drinking water contamination and/or PFAS."
                            },
                            {
                                "campaign": "Pressure Cooker Products Liability",
                                "description": "Cases involving pressure cookers and injuries."
                            },
                            {
                                "campaign": "P. Diddy Sex Abuse Litigation",
                                "description": "Cases involving P. Diddy, Sean Combs, or other names he is known by."
                            },
                            {
                                "campaign": "Bard/Covidien Hernia Mesh Products Liability",
                                "description": "Cases involving hernia mesh and complications."
                            },
                            {
                                "campaign": "Tepezza Products Liability",
                                "description": "Cases involving Tepezza and hearing loss."
                            },
                            {
                                "campaign": "Filshie Clips Product Liability",
                                "description": "Cases involving Filshie Clips."
                            },
                            {
                                "campaign": "Cooper Surgical IVF Culture Media",
                                "description": "Cases involving CooperSurgical IVF Culture Media or defective IVF culture medium."
                            },
                            {
                                "campaign": "Abiomed Impella Heart Pump Products Liability",
                                "description": "Cases involving Abiomed Impella Heart Pump."
                            },
                            {
                                "campaign": "Juvenile Detention Center Sex Abuse",
                                "description": "Cases involving juvenile detention facilities and abuse."
                            },
                            {
                                "campaign": "Unpaid Wage Claims",
                                "description": "Cases involving compensation, paid wages, overtime, or worker misclassification."
                            },
                            {
                                "campaign": "Dacthal Products Liability",
                                "description": "Cases involving Dacthal. Cases involving Dacthal or pesticide and child defects or injuries"
                            },
                            {
                                "campaign": "TCPA Violations",
                                "description": "Cases involving TCPA or spam texts or call Violations"
                            },
                            {
                                "campaign": "Zantac Products Liability Litigation",
                                "description": "Cases involving Zantac and cancer"
                          ]
                        }
                    """

    def _load_prompt(self, prompt_name: str) -> str:
        return getattr(self, prompt_name, "")

    def _get_prompt_settings(self, prompt_type: str) -> Tuple[str, str, int]:
        # Check for the full prompt type first
        settings = self.prompt_settings.get(prompt_type)

        # If not found, check for a partial match
        if settings is None:
            for key in self.prompt_settings:
                if key.startswith(prompt_type):
                    settings = self.prompt_settings[key]
                    break

        if settings is None:
            self.logger.warning(f"Unknown prompt type: {prompt_type}. Using default settings.")
            return self._load_prompt('prompt_full'), self.default_model, 16384
        return self._load_prompt(settings[0]), settings[1], settings[2]

    @backoff.on_exception(
        backoff.expo,
        (ConnectionError, Timeout, RequestException),
        max_tries=5,
        jitter=None,
        max_time=300  # 5 minutes total retry time
    )
    def make_api_request(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        try:
            response = requests.post(
                self.endpoint,
                headers=self.headers,
                json=data,
                timeout=60  # Increased from 30 to 60 seconds
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Request failed: {e}")
            if 'response' in locals():
                self.logger.error(f"Response status code: {response.status_code}")
            raise  # Re-raise to trigger retry logic

    def calculate_tokens(self, input_data: Any) -> int:
        try:
            text = json.dumps(input_data) if isinstance(input_data, dict) else str(input_data)
            return len(self.encoding.encode(text))
        except Exception as e:
            self.logger.error(f"Failed to calculate tokens: {e}")
            raise

    def parse_law_firm_sig_page(self, signature_page: str) -> Any:
        if not signature_page.strip():
            self.logger.error("Signature page content is empty.")
            return {"law_firm": "NA"}
        self.logger.debug(f"Signature page content: {signature_page}")
        data = self._prepare_api_data('parse_sig_page', signature_page)
        try:
            response_data = self.make_api_request(data)
            parsed_response = self.parse_gpt_response(response_data, expect_json=True)
            if isinstance(parsed_response, dict) and 'law_firm' in parsed_response:
                return parsed_response
            else:
                self.logger.error("Failed to extract law firm information.")
                return {'law_firm': 'NA'}
        except Exception as e:
            self.logger.error(f"An error occurred: {e}")
            return {'law_firm': 'NA'}

    def _truncate_text(self, text: str, max_tokens: int) -> str:
        tokens = self.encoding.encode(text)
        if len(tokens) <= max_tokens:
            return text
        return self.encoding.decode(tokens[:max_tokens])

    def extract_summary(self, text: str, prompt_type: str = 'full', expect_json: Optional[bool] = True) -> Dict[
        str, Any]:
        data = self._prepare_api_data(prompt_type, text)
        try:
            response_data = self.make_api_request(data)
            if response_data is None:
                return {"status": "error", "message": "API response failed"}
            return self.parse_gpt_response(response_data, expect_json=expect_json)
        except Exception as e:
            self.logger.error(f"Summary extraction error: {e}")
            return {"status": "error", "message": str(e)}

    @backoff.on_exception(backoff.expo, RequestException, max_tries=5, jitter=None)
    def _retry_process_summary(self, chunk: str, prompt_type: str) -> Dict[str, Any]:
        """
        This method processes a single chunk and retries on failure.
        """
        try:
            return self._process_summary(chunk, prompt_type)
        except RequestException as e:
            self.logger.error(f"Request failed for chunk: {e}")
            raise  # Re-raise to trigger retry logic

    @staticmethod
    def _combine_summary_results(results: List[Dict[str, Any]], prompt_type: str) -> Dict[str, Any]:
        if not results:
            return {"status": "error", "message": "No valid results to combine"}

        if prompt_type == 'full':
            combined = {
                "title": results[0].get("title", ""),
                # Import at the top of the file: from src.lib.utils.law_firm_normalizer import normalize_law_firm_name
                # Normalize law firm names and filter out empty strings
                "law_firm": "; ".join(set(normalize_law_firm_name(r.get("law_firm", "")) for r in results if r.get("law_firm") and normalize_law_firm_name(r.get("law_firm", "")))),
                "allegations": " ".join(r.get("allegations", "") for r in results if r.get("allegations")),
                "claims": " ".join(r.get("claims", "") for r in results if r.get("claims")),
                "plaintiff": results[0].get("plaintiff", ""),
                "defendant": results[0].get("defendant", ""),
                "mdl_num": results[0].get("mdl_num", "")
            }
        elif prompt_type == 'federal':
            combined = {
                "allegations": " ".join(r.get("allegations", "") for r in results if r.get("allegations")),
                "claims": " ".join(r.get("claims", "") for r in results if r.get("claims"))
            }
        else:
            # For other prompt types, you might need to implement specific combining logic
            combined = results[0]  # Default to just using the first result

        return combined

    def _chunk_text(self, text: str, max_tokens: int) -> List[str]:
        tokens = self.encoding.encode(text)
        chunks = []
        current_chunk = []
        current_length = 0

        for token in tokens:
            if current_length + 1 > max_tokens:
                chunks.append(self.encoding.decode(current_chunk))
                current_chunk = []
                current_length = 0
            current_chunk.append(token)
            current_length += 1

        if current_chunk:
            chunks.append(self.encoding.decode(current_chunk))

        return chunks

    def _prepare_api_data(self, prompt_type: str, content: str) -> Dict[str, Any]:
        prompt, model, max_tokens = self._get_prompt_settings(prompt_type)
        
        # For other prompt types, use a generic system message
        system_message = {"role": "system", 
                          "content": "You are a helpful assistant that analyzes legal documents."}
        
        # Create the user prompt with the provided content
        user_prompt = {"role": "user", "content": f"{prompt}\n\n{content}"}
        
        messages = [system_message, user_prompt]

        data = {
            "model": model,
            "messages": messages,
            "max_tokens": max_tokens,  # Use the max_tokens from prompt settings
            "temperature": 0.0,
        }
        
        return data

    def get_attorney_info(self, text: str) -> Dict[str, Any]:
        data = self._prepare_api_data('get_attorney_info', text)
        try:
            # Send request to GPT
            response_data = self.make_api_request(data)
            if response_data is None:
                return 'NA'
            parsed_response = self.parse_gpt_response(response_data, expect_json=True)
            return parsed_response if isinstance(parsed_response, dict) else 'NA'
        except Exception as e:
            self.logger.error(f"An error occurred: {e}")
            return {"attorneys": []}

    def _chunk_and_process_attorney_info(self, text: str, max_tokens: int) -> Union[Dict[str, Any], str]:
        chunks = self._chunk_text(text, max_tokens // 2)  # Use half of max_tokens to ensure we're well under the limit
        all_responses = [self._process_attorney_info(chunk) for chunk in chunks]

        # Filter out any string responses (which are likely error messages)
        valid_responses = [r for r in all_responses if isinstance(r, dict)]

        if not valid_responses:
            return 'NA'  # Return 'NA' if no valid responses were obtained

        return self._combine_attorney_info_results(valid_responses)

    def _process_attorney_info(self, text: str) -> Union[Dict[str, Any], str]:
        data = self._prepare_api_data('get_attorney_info', text)
        try:
            response_data = self.make_api_request(data)
            if response_data is None:
                return 'NA'
            parsed_response = self.parse_gpt_response(response_data, expect_json=True)
            if isinstance(parsed_response, dict):
                return parsed_response
            else:
                self.logger.warning(f"Unexpected response format: {parsed_response}")
                return 'NA'
        except Exception as e:
            self.logger.error(f"An error occurred: {e}")
            return 'NA'

    @staticmethod
    def _combine_attorney_info_results(results: List[Dict[str, Any]]) -> Dict[str, Any]:
        combined_response = {
            "filing_date": None,
            "docket_number": None,
            "attorneys": []
        }
        for response in results:
            if response and isinstance(response, dict):
                if not combined_response["filing_date"]:
                    combined_response["filing_date"] = response.get("filing_date")
                if not combined_response["docket_number"]:
                    combined_response["docket_number"] = response.get("docket_number")
                combined_response["attorneys"].extend(response.get("attorneys", []))

        return combined_response if combined_response["attorneys"] else 'NA'

    def parse_gpt_text_response(self, response_data: Optional[Dict[str, Any]]) -> str:
        """
        Parses the text content from a GPT response.

        Args:
        response_data (Optional[Dict[str, Any]]): The raw response data from the GPT API.

        Returns:
        str: The extracted text content, or an error message if parsing fails.
        """
        try:
            if isinstance(response_data, dict) and 'choices' in response_data and response_data['choices']:
                response_content = response_data['choices'][0]['message']['content']

                # Remove any leading/trailing whitespace
                cleaned_content = response_content.strip()

                # Remove code block markers if present
                if cleaned_content.startswith('```') and cleaned_content.endswith('```'):
                    cleaned_content = re.sub(r'^```(\w*\n)?|```$', '', cleaned_content, flags=re.MULTILINE).strip()

                return cleaned_content
            else:
                self.logger.error(f"Unexpected response format or missing 'choices': {response_data}")
                return "Error: Unable to parse GPT response due to unexpected format."
        except Exception as e:
            self.logger.error(f"An unexpected error occurred while parsing response: {e}")
            return f"Error: {str(e)}"

    def parse_gpt_response(self, response_data: Optional[Dict[str, Any]], expect_json: bool = False,
                           expect_number: bool = False) -> Any:
        try:
            # Validate response_data structure
            if not isinstance(response_data, dict) or not response_data.get('choices'):
                return 'NA'

            content = response_data['choices'][0]['message']['content'].strip()

            # Handle JSON parsing if expect_json is True
            if expect_json:
                json_match = re.search(r'({.*})', content, re.DOTALL)
                if not json_match:
                    return 'NA'

                parsed_json = json.loads(json_match.group(0))

                # Handle number parsing if expect_number is True
                if expect_number:
                    number_str = parsed_json.get('number')
                    if number_str:
                        try:
                            parsed_json['number'] = int(number_str)
                        except ValueError:
                            return 'NA'

                return parsed_json

            # Return raw content if JSON is not expected
            return content

        except Exception as e:
            self.logger.error(f"Response parsing error: {e}")
            return 'NA'

    def determine_court_id(self, court_description: str) -> Dict[str, Any]:
        """Synchronous method to determine court ID"""
        data = self._prepare_api_data('get_court_id', court_description)
        try:
            response_data = self.make_api_request(data)
            return self.parse_gpt_response(response_data, expect_json=True)
        except Exception as e:
            self.logger.error(f"An error occurred: {e}")
            return {"status": "error", "message": "Failed to process the request."}

    async def determine_court_id_async(self, court_description: str) -> Dict[str, Any]:
        """Asynchronous method to determine court ID"""
        data = self._prepare_api_data('get_court_id', court_description)
        try:
            loop = asyncio.get_running_loop()
            response_data = await loop.run_in_executor(None, self.make_api_request, data)
            return self.parse_gpt_response(response_data, expect_json=True)
        except Exception as e:
            self.logger.error(f"An error occurred: {e}")
            return {"status": "error", "message": "Failed to process the request."}

    def find_state_filing_link(self, docket_text: str) -> Dict[str, str]:
        data = self._prepare_api_data('find_removal_link', docket_text)
        try:
            response_data = self.make_api_request(data)
            parsed_response = self.parse_gpt_response(response_data, expect_json=True, expect_number=True)
            if isinstance(parsed_response, dict) and 'number' in parsed_response:
                return parsed_response
            else:
                return {"number": "NA"}
        except Exception as e:
            self.logger.error(f"An error occurred in find_state_filing_link: {e}")
            return {"number": "NA"}

    def get_detailed_summary(self, docket_text: str) -> Dict[str, Any]:
        data = self._prepare_api_data('detailed_description', docket_text)
        try:
            response_data = self.make_api_request(data)
            parsed_response = self.parse_gpt_response(response_data, expect_json=True, expect_number=True)
            if isinstance(parsed_response, dict) and 'number' in parsed_response:
                return parsed_response
            else:
                return {"number": "NA"}
        except Exception as e:
            self.logger.error(f"An error occurred in find_state_filing_link: {e}")
            return {"number": "NA"}

    def format_fb_body(self, content: str) -> Dict[str, str]:
        prompt_type = 'format_fb_body'
        _, model, max_tokens = self._get_prompt_settings(prompt_type)

        # Check if content is the method itself and extract the actual text
        if str(content).startswith('<bound method'):
            self.logger.warning("Method passed as content. Attempting to extract actual content.")
            try:
                content = str(content).split('\n', 1)[1].strip()
            except IndexError:
                self.logger.error("Failed to extract content from method string.")
                return {"formatted_text": "Error: Invalid content provided."}

        # Prepare the prompt with the actual content
        formatted_prompt = self.prompt_format_fb_body.format(insert_text_here=content)

        # Prepare the messages
        system_message = {"role": "system",
                          "content": "You are a helpful assistant that formats text for Facebook ads."}
        user_prompt = {"role": "user", "content": formatted_prompt}

        messages = [system_message, user_prompt]

        # Calculate tokens and truncate if necessary
        total_tokens = self.num_tokens_from_messages(messages, model)
        if total_tokens > max_tokens:
            available_tokens = max_tokens - self.num_tokens_from_messages([system_message], model) - 1000
            truncated_content = self._truncate_text(content, available_tokens)
            formatted_prompt = self.prompt_format_fb_body.format(insert_text_here=truncated_content)
            user_prompt["content"] = formatted_prompt
            messages = [system_message, user_prompt]

        data = {
            "model": model,
            "messages": messages,
            "max_tokens": 1024,
            "temperature": 0.0,
        }

        try:
            response_data = self.make_api_request(data)
            formatted_text = self.parse_gpt_text_response(response_data)
            return {"formatted_text": formatted_text}
        except Exception as e:
            self.logger.error(f"An error occurred in format_fb_body: {e}")
            return {"formatted_text": f"Error: {str(e)}"}

    def _get_encoding(self, model: str) -> tiktoken.Encoding:
        try:
            if model.startswith('gpt-4o'):
                # Map 'gpt-4o' to a known model for encoding purposes
                return tiktoken.encoding_for_model("gpt-4")
            return tiktoken.encoding_for_model(model)
        except (KeyError, ValueError):
            self.logger.warning(f"Model {model} not found. Using cl100k_base encoding.")
            return tiktoken.get_encoding("cl100k_base")

    def num_tokens_from_messages(self, messages: List[Dict[str, str]], model: str) -> int:
        """Return the number of tokens used by a list of messages."""
        tokens_per_message = 4  # Use a conservative estimate
        tokens_per_name = 1

        num_tokens = 0
        for message in messages:
            num_tokens += tokens_per_message
            for key, value in message.items():
                num_tokens += len(self.encoding.encode(value))
                if key == "name":
                    num_tokens += tokens_per_name
        num_tokens += 3  # every reply is primed with <|start|>assistant<|message|>
        return num_tokens

    def fb_ad_summary(self, title: str, body: str, link_description: str, law_firm_name: str, image_text: str = None) -> Dict[str, str]:
        prompt_type = 'fb_ad_summary'
        prompt, model, max_tokens = self._get_prompt_settings(prompt_type)

        # Prepare the input content
        content = f"Title: {title or ''}\nBody: {body or ''}\n"
        if image_text:
            content += f"ImageText: {image_text}\n"
        content += f"Link Description: {link_description or ''}\nLaw Firm Name: {law_firm_name or ''}"

        # Prepare the messages for the GPT-4 API call
        system_message = {"role": "system",
                          "content": "You are a helpful assistant that summarizes Facebook ads for legal services."}
        user_prompt = {"role": "user", "content": f"{prompt}\n\n{content}"}

        messages = [system_message, user_prompt]

        data = {
            "model": model,
            "messages": messages,
            "max_tokens": 100,  # Set a fixed limit for the response
            "temperature": 0.0,
            "response_format": {"type": "json_object"} # Explicitly request JSON output
        }

        try:
            # Make the API request
            response_data = self.make_api_request(data)

            # Log raw response for debugging
            self.logger.debug(f"Raw GPT-4 response for ad summary: {response_data}")

            # *** Use the corrected parsing method ***
            parsed_response = self.parse_gpt_response(response_data, expect_json=True)

            # Check if parsing was successful and the expected key exists
            if isinstance(parsed_response, dict) and 'summary' in parsed_response:
                 summary_value = parsed_response['summary']
                 # Handle potential 'NA' or empty strings returned by the model
                 if isinstance(summary_value, str) and summary_value.strip().upper() == 'NA':
                     return {"summary": "NA"}
                 elif not summary_value: # Handle empty string or None
                     return {"summary": "NA"}
                 else:
                    return {"summary": str(summary_value)} # Ensure string return
            else:
                 self.logger.error(f"Failed to parse summary or 'summary' key missing. Parsed: {parsed_response}")
                 return {"summary": "NA"}

        except Exception as e:
            self.logger.error(f"An error occurred in fb_ad_summary: {e}", exc_info=True) # Add exc_info for traceback
            return {"summary": f"Error: {str(e)}"} # Return error in summary for visibility


if __name__ == "__main__":
    from config import load_config

    config = load_config('01/01/1970')
    # pdf_extractor = PDFExtractor(config, '/Users/<USER>/PycharmProjects/mt_competitive_analysis/src/data/20240912/dockets/cacd_24_01948_Lorenz_et_al_v_URL_Pharma_Inc_et_al.pdf' )
    gpt = GPT4(config)
    # all_text = pdf_extractor.extract_text_with_tesseract_local()
    # summary = gpt.extract_summary(all_text)
    # attorneys_gpt = pdf_extractor.
    # pprint(summary)
