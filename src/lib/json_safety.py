import os
import json
import shutil
import tempfile


def safe_json_write(file_path, data, indent=4):
    """
    Safely write JSON data to a file using a temporary file approach to prevent data loss.
    
    Args:
        file_path (str): The path to the file to write.
        data (dict or list): The JSON data to write.
        indent (int, optional): The indentation level for the JSON file. Defaults to 4.
        
    Returns:
        bool: True if successful, False otherwise.
    """
    try:
        # Create a temporary file in the same directory as the target file
        directory = os.path.dirname(file_path)
        # Ensure the directory exists
        os.makedirs(directory, exist_ok=True)
        
        # Create a temporary file with a random name
        fd, temp_path = tempfile.mkstemp(dir=directory, suffix='.tmp')
        try:
            with os.fdopen(fd, 'w') as temp_file:
                json.dump(data, temp_file, indent=indent)
                
            # If we got here, the write was successful, so replace the original file
            if os.path.exists(file_path):
                # Keep a backup of the original file
                backup_path = file_path + '.bak'
                shutil.copy2(file_path, backup_path)
                
            # Replace the original file with the temporary file
            shutil.move(temp_path, file_path)
            return True
        except Exception as e:
            # If there was an error, clean up the temporary file
            if os.path.exists(temp_path):
                os.remove(temp_path)
            raise e
    except Exception as e:
        print(f"Error writing JSON to {file_path}: {e}")
        return False


def safe_json_read(file_path):
    """
    Safely read JSON data from a file, with fallback to backup if the main file is corrupted.
    
    Args:
        file_path (str): The path to the file to read.
        
    Returns:
        dict or list: The JSON data if successful, None otherwise.
    """
    try:
        with open(file_path, 'r') as file:
            return json.load(file)
    except (json.JSONDecodeError, FileNotFoundError) as e:
        # Try to read from backup
        backup_path = file_path + '.bak'
        if os.path.exists(backup_path):
            try:
                print(f"Main file {file_path} is corrupted or missing, trying backup...")
                with open(backup_path, 'r') as backup_file:
                    data = json.load(backup_file)
                # If backup read is successful, restore from backup
                if os.path.exists(file_path):
                    os.remove(file_path)
                shutil.copy2(backup_path, file_path)
                print(f"Successfully restored from backup.")
                return data
            except Exception as backup_error:
                print(f"Error reading backup file: {backup_error}")
        
        print(f"Error reading JSON from {file_path}: {e}")
        return None