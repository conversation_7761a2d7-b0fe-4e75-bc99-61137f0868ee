import os
import tempfile
import json
import time
from typing import Optional, List, Union
from mistralai import Mistral
from .s3_manager import S3Manager  # Import S3Manager
import logging
from rich.console import Console
from rich.logging import <PERSON><PERSON>andler
from rich.progress import Progress
from rich.traceback import install as install_rich_traceback

# Initialize Rich console and traceback
console = Console()
install_rich_traceback()

def setup_logger(name: str, log_file: str = None) -> logging.Logger:
    """Set up logger with both Rich console and file output"""
    logger = logging.getLogger(name)
    logger.setLevel(logging.DEBUG)

    # Remove any existing handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # Rich console handler
    console_handler = RichHandler(
        rich_tracebacks=True,
        console=console,
        show_time=True,
        show_path=True
    )
    console_handler.setLevel(logging.INFO)
    logger.addHandler(console_handler)

    # File handler if log_file is specified
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(module)s.%(funcName)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        ))
        file_handler.setLevel(logging.DEBUG)
        logger.addHandler(file_handler)

    return logger

class MistralOCR:
    def __init__(self, config: dict, logger: logging.Logger = None):
        self.config = config
        log_file = config.get('log_file', 'mistral_ocr.log')
        self.logger = logger or setup_logger('mistral_ocr', log_file)
        
        # Initialize Mistral client
        self.mistral_client = Mistral(api_key=self.config.get("mistral_api_key"))
        
        # Initialize S3Manager instead of boto3 client
        self.s3_manager = S3Manager(self.config, self.config.get('bucket_name'))

    def parse_pdf(self, pdf_input: Union[str, List[str]]) -> str:
        """Process one or more PDFs through Mistral OCR.
        Accepts a string (file path or URL) or a list of such strings."""

        MIN_DELAY_BETWEEN_FILES = 0.2

        if isinstance(pdf_input, str):
            pdfs = [pdf_input]
        else:
            pdfs = pdf_input

        def api_call_with_retry(api_func, max_retries=5, initial_delay=1, max_delay=60):
            """
            Execute an API call with exponential backoff retry for various error types.
            
            Args:
                api_func: Lambda function that makes the API call
                max_retries: Maximum number of retry attempts
                initial_delay: Initial delay in seconds
                max_delay: Maximum delay in seconds
                
            Returns:
                API call result
                
            Raises:
                Exception: If all retries fail
            """
            import random
            import logging
            
            logger = logging.getLogger(__name__)
            delay = initial_delay
            
            for attempt in range(max_retries):
                try:
                    return api_func()
                    
                except Exception as e:
                    error_str = str(e)
                    
                    # Handle different error types with appropriate strategies
                    if "Status 429" in error_str or "rate limit exceeded" in error_str.lower():
                        # Rate limiting - use exponential backoff with jitter
                        jitter = random.uniform(0, 0.1 * delay)  # 10% jitter
                        wait_time = delay + jitter
                        
                        logger.warning(
                            f"Rate limit exceeded (429) on attempt {attempt+1}/{max_retries}. "
                            f"Retrying in {wait_time:.2f} seconds..."
                        )
                        
                        time.sleep(wait_time)
                        # Exponential backoff: double the delay for next attempt
                        delay = min(delay * 2, max_delay)
                        
                    elif "Status 520" in error_str:
                        # Cloudflare error - use fixed delay with slight jitter
                        wait_time = initial_delay + random.uniform(0, 2)
                        logger.warning(
                            f"Cloudflare error (520) on attempt {attempt+1}/{max_retries}. "
                            f"Retrying in {wait_time:.2f} seconds..."
                        )
                        time.sleep(wait_time)
                        
                    elif "Status 503" in error_str or "Service Unavailable" in error_str:
                        # Service unavailable - use exponential backoff
                        wait_time = delay
                        logger.warning(
                            f"Service unavailable (503) on attempt {attempt+1}/{max_retries}. "
                            f"Retrying in {wait_time:.2f} seconds..."
                        )
                        time.sleep(wait_time)
                        delay = min(delay * 2, max_delay)
                        
                    else:
                        # For other errors, just raise immediately
                        logger.error(f"API call failed with error: {error_str}")
                        raise
                    
                    # If this was the last attempt, raise the exception
                    if attempt == max_retries - 1:
                        logger.error(f"All {max_retries} retry attempts failed. Last error: {error_str}")
                        raise
            
            # This should never be reached, but just in case
            raise RuntimeError("Unexpected error in retry mechanism")

        all_markdowns = []
        for item in pdfs:
            try:
                # Upload the file with retry
                if item.startswith("http"):
                    uploaded_file = api_call_with_retry(lambda: self.mistral_client.files.upload(
                        file={"file_name": os.path.basename(item), "content": item},
                        purpose="ocr"
                    ))
                else:
                    with open(item, "rb") as f:
                        content = f.read()
                    uploaded_file = api_call_with_retry(lambda: self.mistral_client.files.upload(
                        file={"file_name": os.path.basename(item), "content": content},
                        purpose="ocr"
                    ))

                # Get signed URL with retry
                signed_url = api_call_with_retry(
                    lambda: self.mistral_client.files.get_signed_url(file_id=uploaded_file.id)
                )

                # Process OCR with retry
                ocr_response = api_call_with_retry(
                    lambda: self.mistral_client.ocr.process(
                        model="mistral-ocr-latest",
                        document={"type": "document_url", "document_url": signed_url.url}
                    )
                )

                markdown = "\n\n".join(page.markdown for page in ocr_response.pages)
                all_markdowns.append(markdown)
            except Exception as e:
                self.logger.error(f"Failed to process item '{item}': {e}", exc_info=True)
                # Optionally append an error marker or skip
                # all_markdowns.append(f"ERROR processing {os.path.basename(str(item))}: {e}") # Example

            finally:
                # --- IMPROVEMENT: Add delay AFTER each file's processing ---
                if len(pdfs) > 1:  # Only sleep if processing multiple files
                    time.sleep(MIN_DELAY_BETWEEN_FILES)
            
        return "\n\n".join(all_markdowns)

    def parse_s3_link(self, s3_source: str) -> Optional[str]:
        """
        Process a PDF from an S3 source (URI or CDN URL) through Mistral OCR.
        Extracts the object key, downloads the file using S3Manager,
        and processes the temporary file.
        """
        logger = self.logger # Use instance logger
        object_key = None
        temp_path = None # Initialize temp_path

        if not s3_source:
            logger.error("Empty S3 source provided.")
            return None

        # --- 1. Extract Object Key ---
        if s3_source.startswith("s3://"):
            # Expected format: s3://bucket-name/path/to/object.pdf
            try:
                parts = s3_source[5:].split('/', 1)
                if len(parts) == 2:
                    # Optional: Validate bucket name if needed
                    # expected_bucket = self.config.get('bucket_name')
                    # if parts[0] != expected_bucket:
                    #     logger.warning(f"S3 URI bucket '{parts[0]}' doesn't match expected '{expected_bucket}'")
                    object_key = parts[1]
                    logger.debug(f"Extracted key '{object_key}' from S3 URI: {s3_source}")
                else:
                    logger.error(f"Invalid S3 URI format (missing key?): {s3_source}")
                    return None
            except Exception as e:
                logger.error(f"Error parsing S3 URI {s3_source}: {e}")
                return None
        elif s3_source.startswith("https://cdn.lexgenius.ai/"):
            # Expected format: https://cdn.lexgenius.ai/path/to/object.pdf
            try:
                base_url = "https://cdn.lexgenius.ai/"
                if len(s3_source) > len(base_url):
                    object_key = s3_source[len(base_url):]
                    # Normalize path separators just in case
                    object_key = object_key.replace('\\', '/')
                    logger.debug(f"Extracted key '{object_key}' from CDN URL: {s3_source}")
                else:
                    logger.error(f"Invalid CDN URL format (too short?): {s3_source}")
                    return None
            except Exception as e:
                logger.error(f"Error parsing CDN URL {s3_source}: {e}")
                return None
        # Add other potential S3 HTTP URL formats if necessary
        # elif s3_source.startswith(f"https://{self.config.get('bucket_name')}.s3"):
        #     try:
        #         # Example parsing for direct S3 HTTP URL
        #         from urllib.parse import urlparse
        #         parsed_url = urlparse(s3_source)
        #         object_key = parsed_url.path.lstrip('/')
        #         logger.debug(f"Extracted key '{object_key}' from direct S3 URL: {s3_source}")
        #     except Exception as e:
        #          logger.error(f"Error parsing direct S3 URL {s3_source}: {e}")
        #          return None
        else:
            # If it's not a recognized S3 source, treat it as potentially a generic HTTP URL
            # or raise an error if only S3 sources are expected.
            # For now, let's log an error and return None, as the method name implies S3.
            logger.error(f"Unrecognized S3 source format: {s3_source}. Expected s3://... or https://cdn.lexgenius.ai/...")
            # Alternative: Try downloading as a generic URL? Could be risky.
            return None

        if not object_key:
            logger.error(f"Could not determine object key from source: {s3_source}")
            return None

        # --- 2. Validate Key ---
        if not object_key.lower().endswith(".pdf"):
            logger.error(f"Object key '{object_key}' derived from '{s3_source}' does not end with .pdf")
            # Consider if non-PDFs should raise ValueError or just return None
            return None # Or raise ValueError("S3 source must point to a PDF file")

        # --- 3. Download Content using S3Manager and Object Key ---
        try:
            # Define retry logic for S3 download
            def get_s3_file_with_retry(key, max_retries=3, delay=2):
                for attempt in range(max_retries):
                    try:
                        # *** Use the NEW S3Manager method ***
                        logger.debug(
                            f"Attempting S3 download for key: {key} (Attempt {attempt + 1}/{max_retries}) using get_content_by_key")
                        # Pass the object key directly to the new method
                        file_content_bytes = self.s3_manager.get_content_by_key(key)  # <--- CHANGE HERE

                        if file_content_bytes is None:
                            logger.warning(f"S3 download returned None for key: {key} on attempt {attempt + 1}")
                            if attempt == max_retries - 1:
                                raise RuntimeError(
                                    f"Failed to download file from S3 after {max_retries} attempts (returned None): {key}")
                            # Fall through to sleep and retry
                        else:
                            logger.info(f"Successfully downloaded S3 content for key: {key}")
                            return file_content_bytes  # Return the bytes

                    except Exception as e:
                        if attempt < max_retries - 1:
                            wait_time = delay * (2 ** attempt)  # Exponential backoff
                            logger.warning(
                                f"S3 download attempt {attempt + 1}/{max_retries} failed for key '{key}': {str(e)}. "
                                f"Retrying in {wait_time} seconds..."
                            )
                            time.sleep(wait_time)
                        else:
                            logger.error(f"Failed to download S3 file key '{key}' after {max_retries} attempts.")
                            raise RuntimeError(
                                f"Failed to download S3 file after {max_retries} attempts: {str(e)}") from e
                # Should not be reached if download fails after retries
                return None

            # Call the download function
            file_content = get_s3_file_with_retry(object_key)

            if file_content is None:
                 # Error already logged in retry function if all attempts failed
                 return None

            # --- 4. Save to Temporary File ---
            # Use 'with' statement for safer file handling
            with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as temp_file:
                temp_file.write(file_content)
                temp_path = temp_file.name
                logger.info(f"Downloaded S3 content (key: {object_key}) to temporary location: {temp_path}")

            # --- 5. Process Temporary PDF using Mistral OCR ---
            # The parse_pdf method handles its own API retries
            return self.parse_pdf(temp_path) # Pass the path to the temp file

        except FileNotFoundError as fnf_err:
             # Catch file not found errors specifically if raised by retry logic
             logger.error(f"File not found during S3 processing for source {s3_source}: {fnf_err}")
             return None
        except Exception as e:
            logger.error(f"Error processing S3 source {s3_source} (key: {object_key}): {e}", exc_info=True)
            return None # Return None on failure

        finally:
            # --- 6. Clean Up Temporary File ---
            if temp_path and os.path.exists(temp_path):
                try:
                    os.remove(temp_path)
                    logger.debug(f"Removed temporary file: {temp_path}")
                except Exception as e:
                    logger.warning(f"Failed to remove temporary file {temp_path}: {str(e)}")

    @staticmethod
    def save_markdown_to_file(markdown_content: str, output_path: str) -> None:
        """Save OCR-generated markdown content to a local file path"""
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(markdown_content)

    def without_batch_inference(self, pdf_paths: List[str]) -> List[str]:
        """Process multiple PDFs sequentially without batch processing"""
        results = []
        for path in pdf_paths:
            try:
                result = self.parse_pdf(path)
                results.append(result)
            except Exception as e:
                results.append(f"Error processing {path}: {str(e)}")
        return results

    def with_batch_inference(self, pdf_paths: List[str]) -> List[str]:
        """Process multiple PDFs using Mistral's batch inference API with robust retry and error handling."""

        logger = self.logger  # Use instance logger

        # Move our retry function definition here since we need it multiple times
        def api_call_with_retry(api_func, max_retries=5, initial_delay=1, max_delay=60):
            """
            Execute an API call with exponential backoff retry for various error types.

            Args:
                api_func: Lambda function that makes the API call
                max_retries: Maximum number of retry attempts
                initial_delay: Initial delay in seconds
                max_delay: Maximum delay in seconds

            Returns:
                API call result

            Raises:
                Exception: If all retries fail
            """
            import random  # Moved import inside function to avoid module-level if not used elsewhere
            delay = initial_delay

            for attempt in range(max_retries):
                try:
                    return api_func()

                except Exception as e:
                    error_str = str(e)

                    # Handle different error types with appropriate strategies
                    if "Status 429" in error_str or "rate limit exceeded" in error_str.lower():
                        # Rate limiting - use exponential backoff with jitter
                        jitter = random.uniform(0, 0.1 * delay)  # 10% jitter
                        wait_time = delay + jitter

                        logger.warning(
                            f"Rate limit exceeded (429) on attempt {attempt + 1}/{max_retries}. "
                            f"Retrying in {wait_time:.2f} seconds..."
                        )

                        time.sleep(wait_time)
                        # Exponential backoff: double the delay for next attempt
                        delay = min(delay * 2, max_delay)

                    elif "Status 520" in error_str:
                        # Cloudflare error - use fixed delay with slight jitter
                        wait_time = initial_delay + random.uniform(0, 2)
                        logger.warning(
                            f"Cloudflare error (520) on attempt {attempt + 1}/{max_retries}. "
                            f"Retrying in {wait_time:.2f} seconds..."
                        )
                        time.sleep(wait_time)

                    elif "Status 503" in error_str or "Service Unavailable" in error_str:
                        # Service unavailable - use exponential backoff
                        wait_time = delay
                        logger.warning(
                            f"Service unavailable (503) on attempt {attempt + 1}/{max_retries}. "
                            f"Retrying in {wait_time:.2f} seconds..."
                        )
                        time.sleep(wait_time)
                        delay = min(delay * 2, max_delay)

                    else:
                        # For other errors, just raise immediately
                        logger.error(f"API call failed with error: {error_str}")
                        raise

                    # If this was the last attempt, raise the exception
                    if attempt == max_retries - 1:
                        logger.error(f"All {max_retries} retry attempts failed. Last error: {error_str}")
                        raise

            # This should never be reached, but just in case
            raise RuntimeError("Unexpected error in retry mechanism")

        # Upload files and collect signed URLs with retry
        signed_urls = []
        logger.info(f"Uploading {len(pdf_paths)} PDFs for batch processing")

        for path in pdf_paths:
            try:
                # Upload file with retry
                with open(path, "rb") as f:
                    uploaded_file = api_call_with_retry(
                        lambda: self.mistral_client.files.upload(
                            file={"file_name": os.path.basename(path), "content": f},
                            purpose="ocr"
                        )
                    )

                # Get signed URL with retry
                signed_url = api_call_with_retry(
                    lambda: self.mistral_client.files.get_signed_url(file_id=uploaded_file.id)
                )
                signed_urls.append(signed_url.url)
                logger.debug(f"Successfully uploaded and got signed URL for {os.path.basename(path)}")

            except Exception as e:
                logger.error(f"Failed to upload {path}: {str(e)}")
                # We'll continue with other files even if one fails
                continue

        if not signed_urls:
            logger.error("No files were successfully uploaded for batch processing")
            return []

        # Create batch JSONL file
        batch_entries = []
        for idx, url in enumerate(signed_urls):
            batch_entries.append({
                "custom_id": str(idx),
                "body": {
                    "model": "mistral-ocr-latest",
                    "document": {
                        "type": "document_url",
                        "document_url": url
                    }
                }
            })

        # Write to temporary file
        temp_path = None
        try:
            with tempfile.NamedTemporaryFile(mode='w+', delete=False, suffix='.jsonl') as f:
                for entry in batch_entries:
                    f.write(json.dumps(entry) + '\n')
                temp_path = f.name

            logger.info(f"Created batch JSONL file with {len(batch_entries)} entries")

            # Upload batch file with retry
            with open(temp_path, 'rb') as batch_file:
                uploaded_batch = api_call_with_retry(
                    lambda: self.mistral_client.files.upload(
                        file={"file_name": "batch_ocr.jsonl", "content": batch_file},
                        purpose="batch"
                    )
                )

            logger.info(f"Successfully uploaded batch file: {uploaded_batch.id}")

            # Create batch job with retry
            job = api_call_with_retry(
                lambda: self.mistral_client.batch.jobs.create(
                    input_files=[uploaded_batch.id],
                    model="mistral-ocr-latest",
                    endpoint="/v1/ocr",
                    metadata={"batch_type": "pdf_ocr"}
                )
            )

            logger.info(f"Successfully created batch job: {job.id}")

            # Monitor job status with retries for API calls
            last_status = None
            max_poll_attempts = 60  # 10 minutes of polling at 10-second intervals
            poll_count = 0

            while poll_count < max_poll_attempts:
                try:
                    status = api_call_with_retry(
                        lambda: self.mistral_client.batch.jobs.get(job.id),
                        max_retries=3,  # Fewer retries for status checks
                        initial_delay=1
                    )

                    # Log status changes
                    if last_status != status.status:
                        logger.info(f"Batch job {job.id} status: {status.status}")
                        last_status = status.status

                    if status.status in ['SUCCESS', 'FAILED']:
                        break

                    time.sleep(10)
                    poll_count += 1

                except Exception as e:
                    logger.warning(f"Error checking job status: {str(e)}. Will retry.")
                    time.sleep(10)
                    poll_count += 1

            # Check if we exceeded maximum polling time
            if poll_count >= max_poll_attempts:
                logger.error(f"Exceeded maximum polling time for job {job.id}")
                raise RuntimeError(f"Batch job polling timed out after {max_poll_attempts * 10} seconds")

            if status.status != 'SUCCESS':
                error_msg = f"Batch job failed: {status.errors}" if hasattr(status,
                                                                            'errors') else f"Batch job failed with status: {status.status}"
                logger.error(error_msg)
                raise RuntimeError(error_msg)

            # Retrieve results with retry
            results_file = api_call_with_retry(
                lambda: self.mistral_client.files.download(status.output_file)
            )

            logger.info(f"Successfully downloaded batch results")

            # Process the results
            try:
                results = []
                for line in results_file.content.decode().splitlines():
                    response_data = json.loads(line)
                    # Handle possible errors in individual results
                    if 'error' in response_data:
                        logger.warning(f"Error in batch result: {response_data['error']}")
                        results.append(f"Error: {response_data['error']}")
                    else:
                        # Extract markdown from successful results
                        markdown = "\n\n".join(page['markdown'] for page in response_data['response']['pages'])
                        results.append(markdown)

                return results

            except Exception as e:
                logger.error(f"Error processing batch results: {str(e)}")
                raise RuntimeError(f"Failed to process batch results: {str(e)}")

        except Exception as e:
            logger.error(f"Batch processing failed: {str(e)}")
            raise

        finally:
            # Clean up temporary file
            if temp_path and os.path.exists(temp_path):
                try:
                    os.remove(temp_path)
                    logger.debug(f"Removed temporary batch file: {temp_path}")
                except Exception as e:
                    logger.warning(f"Failed to remove temporary batch file {temp_path}: {str(e)}")
