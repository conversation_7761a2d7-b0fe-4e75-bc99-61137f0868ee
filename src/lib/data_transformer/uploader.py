# --- START OF FILE uploader.py ---

# !/usr/bin/env python3
import asyncio
import logging
import os
from typing import Dict, List, Optional, Set  # Added Any

from src.lib.pacer_manager import PacerManager
from src.lib.s3_manager import S3Manager
# Assuming these imports are correct relative to the project structure
from .file_handler import FileHandler


class Uploader:
    def __init__(self, config: Dict, s3_manager: S3Manager, pacer_db: PacerManager,
                 file_handler: FileHandler):
        self.config = config
        self.logger = logging.getLogger(__name__)  # Use __name__ for logger
        self.s3_manager = s3_manager
        self.pacer_db = pacer_db
        self.file_handler = file_handler
        self.bucket_name = config.get('bucket_name', config.get('aws_s3', {}).get('bucket_name',
                                                                                  'lexgenius-dockets'))  # Ensure bucket name is available
        if not self.bucket_name:
            self.logger.critical("S3 Bucket Name not configured. S3 uploads will fail.")
            # Depending on requirements, you might raise an error here
            # raise ValueError("S3 Bucket Name is required but not configured.")

    async def upload_batch_to_aws_async(
            self,
            json_paths: List[str],
            upload_types: Set[str],
            force_s3_upload: bool = False  # Renamed force_upload for clarity
    ) -> Dict[str, List[str]]:
        """
        Uploads a batch of JSON files (and associated PDF/MD) asynchronously
        to specified AWS services (S3, DynamoDB).

        Args:
            json_paths: List of absolute paths to the JSON files to upload.
            upload_types: A set indicating where to upload (e.g., {'s3', 'dynamodb'}).
            force_s3_upload: If True, skips the S3 existence check and uploads S3 files regardless.
                             Only applies if 's3' is in upload_types.

        Returns:
            A dictionary summarizing the upload results: {'uploaded': [], 'skipped': [], 'failed': []}.
        """
        self.logger.info(
            f"Starting batch upload for {len(json_paths)} files. Targets: {upload_types}, Force S3: {force_s3_upload}")
        upload_summary = {'uploaded': [], 'skipped': [], 'failed': []}
        if not json_paths:
            self.logger.warning("No JSON paths provided for upload.")
            return upload_summary
        if not self.bucket_name and 's3' in upload_types:
            self.logger.error("Cannot perform S3 upload because S3 bucket name is not configured.")
            # Add all paths to failed if S3 upload is requested but not possible
            for json_path in json_paths:
                upload_summary['failed'].append(os.path.basename(json_path))
            return upload_summary

        # Determine max concurrency based on config or default
        max_concurrent_uploads = self.config.get('uploader_workers', 10)
        semaphore = asyncio.Semaphore(max_concurrent_uploads)

        tasks = []
        for json_path in json_paths:
            # Pass all necessary args to the single file upload helper
            tasks.append(
                self._upload_single_file_with_semaphore(
                    semaphore,
                    json_path,
                    upload_types,
                    force_s3_upload,  # Pass the renamed flag
                    upload_summary  # Pass summary dict to be updated directly
                )
            )

        await asyncio.gather(*tasks)  # Wait for all upload tasks to complete

        self.logger.info(f"--- Upload Batch Summary ---")
        self.logger.info(f"Successfully uploaded (or verified exists): {len(upload_summary['uploaded'])} files")
        self.logger.info(f"Skipped (e.g., S3 already exists and force=False): {len(upload_summary['skipped'])} files")
        self.logger.info(f"Failed uploads: {len(upload_summary['failed'])} files")
        if upload_summary['failed']:
            self.logger.error(f"Failed file list (first 5): {upload_summary['failed'][:5]}")
        self.logger.info(f"--- End Upload Batch Summary ---")

        return upload_summary

    async def _upload_single_file_with_semaphore(
            self,
            semaphore: asyncio.Semaphore,
            json_path: str,
            upload_types: Set[str],
            force_s3_upload: bool,
            upload_summary: Dict[str, List[str]]
    ):
        """Helper to manage semaphore acquisition for single file upload."""
        async with semaphore:
            await self._upload_single_file_to_aws(
                json_path,
                upload_types,
                force_s3_upload,
                upload_summary
            )

    async def _upload_single_file_to_aws(
            self,
            json_path: str,
            upload_types: Set[str],
            force_s3_upload: bool,
            upload_summary: Dict[str, List[str]]
    ) -> None:
        """
        Uploads a single JSON file and its corresponding PDF and MD files
        to the specified AWS services based on upload_types. Updates the summary dict.
        """
        base_filename = os.path.splitext(os.path.basename(json_path))[0]
        current_dir = os.path.dirname(json_path)
        pdf_path = os.path.join(current_dir, f"{base_filename}.pdf")
        md_path = os.path.join(current_dir, f"{base_filename}.md")

        s3_upload_attempted = False
        s3_upload_succeeded = False
        dynamodb_upload_attempted = False
        dynamodb_upload_succeeded = False
        s3_skipped = False  # Track if S3 upload was skipped due to existence check

        file_basename_for_log = os.path.basename(json_path)
        item_data: Optional[Dict] = None  # Variable to hold loaded JSON data

        try:
            # --- Load JSON Data First (needed for S3 key and potentially DynamoDB) ---
            try:
                # Use FileHandler's async load method
                item_data = await self.file_handler.load_json_async(json_path)
                if item_data is None:
                    raise ValueError(f"Failed to load JSON data from {json_path}")  # Raise to enter main catch block
            except Exception as load_err:
                self.logger.error(
                    f"Upload Task: Critical - Failed to load JSON data for {file_basename_for_log}, cannot proceed with S3 or DynamoDB. Error: {load_err}")
                upload_summary['failed'].append(file_basename_for_log)
                return  # Cannot proceed without data

            # --- S3 Upload Logic ---
            if 's3' in upload_types:
                s3_upload_attempted = True
                # Check again if bucket name is configured, in case it wasn't checked at batch level
                if not self.bucket_name:
                    self.logger.error(f"S3 Upload: Skipping {file_basename_for_log} - Bucket name not configured.")
                    s3_upload_succeeded = False  # Mark as failed
                else:
                    self.logger.debug(f"S3 Upload: Starting for {file_basename_for_log} (Force={force_s3_upload})")

                    # +++ MODIFIED: Generate S3 key base using the loaded data +++
                    s3_key_base = self.file_handler.get_s3_key_base(item_data)
                    if not s3_key_base:
                        # Log the specific error and raise to stop this file's upload
                        self.logger.error(
                            f"S3 Upload: Failed to generate S3 key base for {file_basename_for_log}. Check FileHandler logs.")
                        raise ValueError(f"Could not determine S3 key base for {json_path}")
                    # +++ END MODIFICATION +++

                    s3_json_key = f"{s3_key_base}.json"

                    # S3 Existence Check (only if force_s3_upload is False)
                    s3_exists = False
                    if not force_s3_upload:
                        s3_exists = await self.s3_manager.check_s3_existence_async(self.bucket_name, s3_json_key)
                        self.logger.debug(f"S3 Upload: Existence check for {s3_json_key}: {s3_exists}")

                    if s3_exists and not force_s3_upload:
                        self.logger.info(
                            f"S3 Upload: Skipping {file_basename_for_log} - already exists on S3 and force_s3_upload=False.")
                        s3_skipped = True
                        # Even if skipped, consider it a 'success' for the summary unless other uploads fail
                        s3_upload_succeeded = True
                    else:
                        # Proceed with S3 upload (JSON, PDF, MD)
                        if force_s3_upload and s3_exists:
                            self.logger.info(f"S3 Upload: Forcing upload/overwrite for {file_basename_for_log}")

                        # Upload JSON
                        self.logger.debug(f"S3 Upload: Uploading JSON {file_basename_for_log} to {s3_json_key}")
                        json_uploaded = await self.s3_manager.upload_file_async(json_path, self.bucket_name,
                                                                                s3_json_key)
                        if not json_uploaded:
                            raise IOError(f"Failed to upload JSON to S3: {s3_json_key}")

                        # Upload PDF if exists
                        if await asyncio.to_thread(os.path.exists, pdf_path):
                            s3_pdf_key = f"{s3_key_base}.pdf"
                            self.logger.debug(f"S3 Upload: Uploading PDF {os.path.basename(pdf_path)} to {s3_pdf_key}")
                            pdf_uploaded = await self.s3_manager.upload_file_async(pdf_path, self.bucket_name,
                                                                                   s3_pdf_key)
                            if not pdf_uploaded:
                                self.logger.error(
                                    f"S3 Upload: Failed to upload PDF {s3_pdf_key} for {file_basename_for_log}")

                        # Upload MD if exists
                        if await asyncio.to_thread(os.path.exists, md_path):
                            s3_md_key = f"{s3_key_base}.md"
                            self.logger.debug(f"S3 Upload: Uploading MD {os.path.basename(md_path)} to {s3_md_key}")
                            md_uploaded = await self.s3_manager.upload_file_async(md_path, self.bucket_name, s3_md_key)
                            if not md_uploaded:
                                self.logger.error(
                                    f"S3 Upload: Failed to upload MD {s3_md_key} for {file_basename_for_log}")

                        # If JSON upload was successful, mark S3 as succeeded
                        s3_upload_succeeded = json_uploaded
                        if s3_upload_succeeded:
                            self.logger.info(f"S3 Upload: Successfully uploaded artifacts for {file_basename_for_log}")

            # --- DynamoDB Upload Logic ---
            if 'dynamodb' in upload_types:
                dynamodb_upload_attempted = True
                self.logger.debug(f"DynamoDB Upload: Starting for {file_basename_for_log}")
                try:
                    # --- Use the already loaded item_data ---
                    if not item_data:  # Should not happen due to check above, but safety check
                        raise ValueError("item_data is None, cannot proceed with DynamoDB upload")

                    # Log the data being uploaded for debugging
                    self.logger.debug(f"DynamoDB Upload: Data for {file_basename_for_log}: {item_data}")

                    # Call the synchronous PacerManager method using asyncio.to_thread
                    await asyncio.to_thread(self.pacer_db.add_or_update_record, item_data)
                    dynamodb_upload_succeeded = True  # Assume success if no exception from to_thread

                    if dynamodb_upload_succeeded:
                        self.logger.info(
                            f"DynamoDB Upload: Successfully triggered add/update for {file_basename_for_log}")
                    # Note: add_or_update_record logs its own errors internally

                # Removed JSONDecodeError and FileNotFoundError catches as load happens earlier
                except Exception as db_err:
                    # Catch errors from asyncio.to_thread or within add_or_update_record if they bubble up
                    self.logger.error(
                        f"DynamoDB Upload: Error during DB operation for {file_basename_for_log}: {db_err}",
                        exc_info=True)
                    dynamodb_upload_succeeded = False  # Mark as failed

            # --- Determine Final Status and Update Summary ---
            # (This logic remains the same)
            overall_success = True
            if s3_upload_attempted and not s3_upload_succeeded:
                overall_success = False
            if dynamodb_upload_attempted and not dynamodb_upload_succeeded:
                overall_success = False

            targeted_s3 = 's3' in upload_types
            targeted_dynamodb = 'dynamodb' in upload_types

            final_status = 'unknown'  # Default

            if targeted_s3 and targeted_dynamodb:
                if s3_upload_succeeded and dynamodb_upload_succeeded:
                    final_status = 'uploaded'
                elif s3_skipped and dynamodb_upload_succeeded:  # S3 skipped, DB OK -> Overall OK
                    final_status = 'uploaded'
                else:  # Any failure in targeted services means overall failure
                    final_status = 'failed'
            elif targeted_s3:  # Only S3 targeted
                if s3_upload_succeeded:
                    final_status = 'uploaded'
                elif s3_skipped:
                    final_status = 'skipped'  # Skipped is a distinct success state when only S3 targeted
                else:
                    final_status = 'failed'
            elif targeted_dynamodb:  # Only DynamoDB targeted
                if dynamodb_upload_succeeded:
                    final_status = 'uploaded'
                else:
                    final_status = 'failed'
            else:  # No targets specified? Should not happen if upload_types is validated
                self.logger.warning(
                    f"Upload called for {file_basename_for_log} but no valid targets in {upload_types}.")
                final_status = 'skipped'  # Or 'failed'? Treat as skipped for now.

            # Update summary based on determined status
            if final_status == 'uploaded':
                upload_summary['uploaded'].append(file_basename_for_log)
            elif final_status == 'skipped':
                upload_summary['skipped'].append(file_basename_for_log)
            elif final_status == 'failed':
                upload_summary['failed'].append(file_basename_for_log)
                # Log which part failed if helpful
                if s3_upload_attempted and not s3_upload_succeeded: self.logger.debug(
                    f"Upload Result: S3 portion failed for {file_basename_for_log}")
                if dynamodb_upload_attempted and not dynamodb_upload_succeeded: self.logger.debug(
                    f"Upload Result: DynamoDB portion failed for {file_basename_for_log}")
            else:  # Should not happen
                self.logger.error(f"Upload Task: Unknown final status '{final_status}' for {file_basename_for_log}")
                upload_summary['failed'].append(file_basename_for_log)


        except Exception as e:
            self.logger.error(f"Upload Task Failed: Unhandled error processing file {file_basename_for_log}: {e}",
                              exc_info=True)
            # Ensure it's marked as failed in the summary if an unexpected error occurs
            if file_basename_for_log not in upload_summary['failed']:
                upload_summary['failed'].append(file_basename_for_log)
