import asyncio
import glob
import json
import logging
import os
import re
import shutil
import tempfile
from datetime import datetime
from typing import Dict, Any, Optional, Set, Union, List
from urllib.parse import urlparse

import aiofiles
from tqdm import tqdm

from src.lib import try_remove

module_file_handler_logger = logging.getLogger(__name__)
# Ensure it uses root handlers (like RichHandler set up in transformer.py)
# by clearing any specific handlers it might have and enabling propagation.
module_file_handler_logger.handlers = []
module_file_handler_logger.propagate = True


class FileHandler:
    """Manages file operations including filename generation."""

    NULL_CONDITIONS = ['', 'NA', None, 'Pro Se', 'PRO SE', "None", []]

    def __init__(self, config: Dict, s3_manager: Optional[Any] = None):
        self.config = config
        self.logger = logging.getLogger(__name__)  # Use __name__ for logger
        self.s3_manager = s3_manager
        self.skip_files = set()
        self.download_dir = self._determine_download_dir()
        if not self.download_dir:
            self.logger.critical("CRITICAL: Could not determine download directory from config in FileHandler.")
            raise ValueError("Could not determine download directory from config.")
        self.logger.info(f"FileHandler initialized. Download directory: '{self.download_dir}'")
        try:
            os.makedirs(self.download_dir, exist_ok=True)
        except OSError as e:
            self.logger.error(f"Error creating download directory {self.download_dir}: {e}")

    def _determine_download_dir(self) -> Optional[str]:
        self.logger.debug("FileHandler: Determining date-specific download directory...")
        project_root = self.config.get('directories', {}).get('base_dir') or self.config.get('project_root')
        if not project_root:
            self.logger.warning("No project_root found in config, defaulting to config['directories']['base_dir']")
        data_subdir = 'data'
        iso_date = self.config.get('iso_date')
        if not iso_date:
            self.logger.critical("CRITICAL: 'iso_date' not found in config for FileHandler.")
            return None
        elif not (isinstance(iso_date, str) and len(iso_date) == 8 and iso_date.isdigit()):
            self.logger.critical(f"CRITICAL: Invalid 'iso_date' format ('{iso_date}') found in config.")
            return None
        else:
            download_dir_path = os.path.join(project_root, data_subdir, iso_date)
            self.logger.debug(f"FileHandler: Determined download directory path: '{download_dir_path}'")
            return download_dir_path

    def get_skip_files(self) -> Set[str]:
        """Returns the current set of files to skip."""
        # Ensure self.skip_files exists, returning empty set otherwise
        return getattr(self, 'skip_files', set())

    def set_skip_files(self, skip_files_list: Optional[List[str]]):
        """
        Sets the internal set of files to be skipped during processing.
        Normalizes paths to realpaths for consistent matching.
        """
        if skip_files_list:
            # Convert list to set of real paths
            self.skip_files = {os.path.realpath(p) for p in skip_files_list if isinstance(p, str)}
            self.logger.info(f"Skip list updated with {len(self.skip_files)} files.")
        else:
            # If None or empty list is passed, clear the skip list
            self.skip_files = set()
            self.logger.info("Skip list cleared.")

    def load_json(self, json_path: str) -> Optional[Dict]:
        if not isinstance(json_path, str) or not json_path.endswith('.json'):
            self.logger.error(f"Invalid json_path provided: {json_path}")
            return None
        json_path = os.path.normpath(json_path)
        if not os.path.isfile(json_path):
            self.logger.error(f"File does not exist: {json_path}")
            return None
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON Decode Error loading {os.path.basename(json_path)}: {str(e)}")
            try:
                with open(json_path, 'r', encoding='utf-8') as f_err:
                    content_preview = f_err.read(500)
                self.logger.debug(f"Content preview (first 500 chars):\n{content_preview}")
            except Exception as read_err:
                self.logger.error(f"Could not read file content after decode error: {read_err}")
            return None
        except Exception as e:
            self.logger.error(f"Error loading JSON {os.path.basename(json_path)}: {str(e)}", exc_info=True)
            return None

    async def load_json_async(self, json_path: str) -> Optional[Dict]:
        if not isinstance(json_path, str) or not json_path.endswith('.json'):
            self.logger.error(f"Invalid json_path provided for async load: {json_path}")
            return None
        json_path = os.path.normpath(json_path)
        try:
            exists = await asyncio.to_thread(os.path.isfile, json_path)
            if not exists:
                self.logger.error(f"File does not exist (async check): {json_path}")
                return None
        except Exception as e:
            self.logger.error(f"Error checking file existence for {json_path}: {e}")
            return None
        try:
            async with aiofiles.open(json_path, mode='r', encoding='utf-8') as f:
                content = await f.read()
                return json.loads(content)
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON Decode Error loading async {os.path.basename(json_path)}: {str(e)}")
            try:
                async with aiofiles.open(json_path, mode='r', encoding='utf-8') as f_err:
                    content_preview = await f_err.read(500)
                self.logger.debug(f"Async content preview (first 500 chars):\n{content_preview}")
            except Exception as read_err:
                self.logger.error(f"Could not read file content after async decode error: {read_err}")
            return None
        except FileNotFoundError:
            self.logger.error(f"File not found during async open (race condition?): {json_path}")
            return None
        except Exception as e:
            self.logger.error(f"Error loading JSON asynchronously {os.path.basename(json_path)}: {str(e)}",
                              exc_info=True)
            return None

    def save_json(self, json_path: str, data: Dict) -> bool:
        # ... (save_json logic remains the same) ...
        if not data or not isinstance(data, dict) or len(data) == 0:
            self.logger.error(
                f"CRITICAL SAFETY CHECK FAILED: Attempted to save empty/invalid data to {os.path.basename(json_path)}")
            return False
        if not isinstance(json_path, str) or not json_path.endswith('.json'):
            self.logger.error(f"Invalid json_path provided for save: {json_path}")
            return False

        json_path = os.path.normpath(json_path)
        target_dir = os.path.dirname(json_path)
        try:
            os.makedirs(target_dir, exist_ok=True)
        except OSError as e:
            self.logger.error(f"Failed to create directory {target_dir} for saving {os.path.basename(json_path)}: {e}")
            return False

        backup_path = f"{json_path}.bak"
        temp_path = f"{json_path}.tmp"
        write_successful = False

        try:
            # 1. Create Backup (if original exists)
            if os.path.exists(json_path):
                try:
                    shutil.copy2(json_path, backup_path)
                    self.logger.debug(f"Created backup: {os.path.basename(backup_path)}")
                except Exception as backup_error:
                    self.logger.warning(f"Could not create backup of {os.path.basename(json_path)}: {backup_error}")

            # 2. Write to Temporary File
            try:
                # Check if attorney data exists and log it
                if 'attorney' in data and isinstance(data['attorney'], list):
                    self.logger.info(f"Data contains {len(data['attorney'])} attorneys before writing to temp file")
                else:
                    self.logger.debug(f"No attorney array found in data before writing to temp file")

                # Check if attorneys_gpt data exists and log it
                if 'attorneys_gpt' in data and isinstance(data['attorneys_gpt'], list):
                    self.logger.info(
                        f"Data contains {len(data['attorneys_gpt'])} attorneys_gpt before writing to temp file")
                else:
                    self.logger.debug(f"No attorneys_gpt array found in data before writing to temp file")

                with open(temp_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=4)
                    f.flush()
                    os.fsync(f.fileno())  # Ensure data is written to disk
                write_successful = True
                self.logger.debug(f"Successfully wrote to temp file: {os.path.basename(temp_path)}")
            except Exception as write_error:
                self.logger.error(f"Error writing to temporary file {os.path.basename(temp_path)}: {write_error}",
                                  exc_info=True)
                if os.path.exists(temp_path): try_remove(temp_path, self.logger, "failed temp file")
                return False

            # 3. Validate Temporary File
            if not os.path.exists(temp_path):
                self.logger.error(
                    f"CRITICAL SAVE ERROR: Temp file {os.path.basename(temp_path)} does not exist after write attempt. Aborting save.")
                if not os.path.exists(json_path) and os.path.exists(backup_path):
                    try:
                        shutil.move(backup_path, json_path)
                        self.logger.info(
                            f"Restored {os.path.basename(json_path)} from backup after temp file missing.")
                    except Exception as restore_err:
                        self.logger.error(f"Could not restore backup after temp file missing: {restore_err}")
                return False
            try:
                temp_size = os.path.getsize(temp_path)
                if temp_size < 2:  # Check if empty or just {}
                    self.logger.error(
                        f"CRITICAL SAVE ERROR: Temp file {os.path.basename(temp_path)} suspiciously small ({temp_size} bytes). Aborting save.")
                    os.remove(temp_path)
                    if not os.path.exists(json_path) and os.path.exists(backup_path):
                        try:
                            shutil.move(backup_path, json_path)
                            self.logger.info(
                                f"Restored {os.path.basename(json_path)} from backup after small temp file.")
                        except Exception as restore_err:
                            self.logger.error(f"Could not restore backup after small temp file: {restore_err}")
                    return False
            except Exception as size_check_err:
                self.logger.error(
                    f"CRITICAL SAVE ERROR: Error checking size of temp file {os.path.basename(temp_path)}: {size_check_err}. Aborting save.")
                if os.path.exists(temp_path): try_remove(temp_path, self.logger, "temp file after size check error")
                if not os.path.exists(json_path) and os.path.exists(backup_path):
                    try:
                        shutil.move(backup_path, json_path)
                        self.logger.info(
                            f"Restored {os.path.basename(json_path)} from backup after temp size check error.")
                    except Exception as restore_err:
                        self.logger.error(f"Could not restore backup after temp size check error: {restore_err}")
                return False

            # 4. Atomic Move (Rename) Temporary File to Final Destination
            try:
                os.replace(temp_path, json_path)
                self.logger.debug(f"Successfully moved temp file to final path: {os.path.basename(json_path)}")
            except OSError as move_err:  # Catch os-level errors specifically
                self.logger.error(
                    f"Error replacing/moving temp file {os.path.basename(temp_path)} to {os.path.basename(json_path)}: {move_err}",
                    exc_info=True)
                if os.path.exists(temp_path): try_remove(temp_path, self.logger, "failed move temp file")
                if not os.path.exists(json_path) and os.path.exists(backup_path):
                    try:
                        shutil.move(backup_path, json_path)
                        self.logger.info(f"Restored {os.path.basename(json_path)} from backup after failed move.")
                    except Exception as restore_error:
                        self.logger.error(
                            f"Could not restore {os.path.basename(json_path)} from backup: {restore_error}")
                return False

            # 5. Final Verification and Cleanup
            if os.path.exists(json_path):
                try:
                    final_size = os.path.getsize(json_path)
                    if final_size < 2:
                        self.logger.error(
                            f"CRITICAL SAVE ERROR: Final file {os.path.basename(json_path)} is suspiciously small ({final_size} bytes) after move.")
                        if os.path.exists(backup_path):
                            try:
                                os.replace(backup_path, json_path)
                                self.logger.info(
                                    f"Restored {os.path.basename(json_path)} from backup due to small final size.")
                            except Exception as restore_error:
                                self.logger.error(
                                    f"Could not restore {os.path.basename(json_path)} from backup: {restore_error}")
                        return False
                except Exception as final_check_err:
                    self.logger.error(
                        f"CRITICAL SAVE ERROR: Error checking final file {os.path.basename(json_path)}: {final_check_err}. Aborting.")
                    if os.path.exists(backup_path):
                        try:
                            os.replace(backup_path, json_path)
                            self.logger.info(
                                f"Attempted restore {os.path.basename(json_path)} from backup after final check error.")
                        except Exception as restore_err:
                            self.logger.error(f"Could not restore backup after final check error: {restore_err}")
                    return False

                # Success path
                if os.path.exists(backup_path): try_remove(backup_path, self.logger, "backup file")
                return True
            else:
                self.logger.error(
                    f"CRITICAL SAVE ERROR: Final file {os.path.basename(json_path)} does not exist after supposed move completion.")
                if os.path.exists(backup_path):
                    try:
                        shutil.move(backup_path, json_path)
                        self.logger.info(
                            f"Restored {os.path.basename(json_path)} from backup after file missing post-move.")
                    except Exception as restore_error:
                        self.logger.error(
                            f"Could not restore {os.path.basename(json_path)} from backup: {restore_error}")
                return False

        except Exception as e:
            self.logger.error(f"Unexpected error during save operation for {os.path.basename(json_path)}: {str(e)}",
                              exc_info=True)
            if os.path.exists(temp_path): try_remove(temp_path, self.logger, "temp file after general save error")
            if not os.path.exists(json_path) and os.path.exists(backup_path):
                try:
                    shutil.move(backup_path, json_path)
                    self.logger.info(f"Restored {os.path.basename(json_path)} from backup after general save error.")
                except Exception as restore_error:
                    self.logger.error(f"Could not restore {os.path.basename(json_path)} from backup: {restore_error}")
            return False

    async def save_json_async(self, json_path: str, data: Dict) -> bool:
        # ... (save_json_async logic remains the same) ...
        if not isinstance(json_path, str) or not json_path.endswith('.json'):
            self.logger.error(f"Invalid json_path provided for async save: {json_path}")
            return False
        if not data or not isinstance(data, dict):
            self.logger.error(f"Attempted to save empty/invalid data async to {os.path.basename(json_path)}")
            return False
        try:
            loop = asyncio.get_running_loop()
            result = await loop.run_in_executor(None, self.save_json, json_path, data)
            return result
        except Exception as e:
            self.logger.error(f"Error in save_json_async wrapper for {os.path.basename(json_path)}: {str(e)}",
                              exc_info=True)
            return False

    def _get_target_docket_dir(self) -> Optional[str]:
        """Internal method to determine the target directory."""
        # Ensure download_dir is set
        if not self.download_dir:
            self.logger.error("Download directory not set in FileHandler.")
            return None
        # Simple case: target is the download directory itself
        # Adjust if you have subdirectories like 'processed'
        target_dir = os.path.join(self.download_dir, 'dockets')
        # Ensure the target directory exists
        try:
            os.makedirs(target_dir, exist_ok=True)
            return target_dir
        except OSError as e:
            self.logger.error(f"Error creating target directory {target_dir}: {e}")
            return None

    def get_target_docket_directory(self) -> Optional[str]:
        """Returns the path to the target directory for processed dockets."""
        return self._get_target_docket_dir()

    def get_filtered_json_files(self, files_to_process: Optional[Union[List[str], bool]] = None) -> List[str]:
        # ... (get_filtered_json_files logic remains the same) ...
        target_docket_dir = self._get_target_docket_dir()
        if not target_docket_dir:
            return []
        initial_json_paths = []
        if isinstance(files_to_process, (list, set)):
            self.logger.info(f"Processing only the specified {len(files_to_process)} files.")
            initial_json_paths = []
            for f in files_to_process:
                path_in_target = os.path.join(target_docket_dir, os.path.basename(f))
                if os.path.isfile(path_in_target) and path_in_target.endswith('.json'):
                    initial_json_paths.append(os.path.normpath(path_in_target))
                elif os.path.isabs(f) and os.path.isfile(f) and f.endswith('.json'):
                    initial_json_paths.append(os.path.normpath(f))
                else:
                    self.logger.warning(f"Specified file not found or not a JSON file: {f}")
        elif files_to_process is True:
            self.logger.info(f"Processing ALL JSON files in date directory: {target_docket_dir}")
            try:
                initial_json_paths = [os.path.join(target_docket_dir, f) for f in os.listdir(target_docket_dir) if
                                      f.endswith('.json')]
            except OSError as e:
                self.logger.error(f"Error listing files in {target_docket_dir}: {e}")
                return []
        else:
            self.logger.info(f"Processing relevant JSON files in date directory: {target_docket_dir}")
            exclude_pattern = r'.*[a-zA-Z]{3,5}_\d{2}_\d{5}_M\d+\.json$'
            archive_pattern = r'.*_COLLISION_\d{8}_\d{6}\.json$'
            backup_pattern = r'\.json\.bak$'
            temp_pattern = r'\.json\.tmp$'
            try:
                initial_json_paths = [os.path.join(target_docket_dir, f) for f in os.listdir(target_docket_dir) if
                                      f.endswith('.json') and not re.match(exclude_pattern, f) and not re.match(
                                          archive_pattern, f) and not f.endswith('.bak') and not f.endswith('.tmp')]
            except OSError as e:
                self.logger.error(f"Error listing files in {target_docket_dir}: {e}")
                return []
        if not initial_json_paths: self.logger.info("No initial JSON files found to filter."); return []
        self.logger.info(f"Applying skip list and 'added_on' filter to {len(initial_json_paths)} files...")
        normalized_skip_files = {os.path.realpath(path) for path in self.skip_files} if self.skip_files else set()
        filtered_dockets_json = []
        for json_path in tqdm(initial_json_paths, desc="Filtering files", leave=False):
            normalized_json_path = os.path.realpath(json_path)
            if normalized_json_path in normalized_skip_files: self.logger.debug(
                f"Skipping file due to skip list: {os.path.basename(json_path)}"); continue
            try:
                if not os.path.isfile(json_path): self.logger.warning(
                    f"File listed but not found during filtering: {json_path}. Skipping."); continue
                data = self.load_json(json_path)
                if data is None: self.logger.warning(
                    f"Skipping file due to load error: {os.path.basename(json_path)}"); continue
                if data.get('added_on') == '00000000': self.logger.debug(
                    f"Skipping file due to added_on=00000000: {os.path.basename(json_path)}"); continue
            except Exception as e:
                self.logger.error(
                    f"Error reading/checking {os.path.basename(json_path)} during filtering: {e}. Skipping.")
                continue
            filtered_dockets_json.append(json_path)
        self.logger.info(f"Found {len(filtered_dockets_json)} JSON files after filtering.")
        return filtered_dockets_json

    def _format_date(self, date: Any) -> str:
        # ... (_format_date logic remains the same) ...
        if not date or date in self.NULL_CONDITIONS: return '00000000'
        date_str = str(date)
        try:
            for fmt in ('%m/%d/%Y', '%Y-%m-%d', '%Y%m%d'):
                try:
                    return datetime.strptime(date_str, fmt).strftime('%Y%m%d')
                except ValueError:
                    continue
            for fmt in ('%m/%d/%y', '%m-%d-%y'):
                try:
                    return datetime.strptime(date_str, fmt).strftime('%Y%m%d')
                except ValueError:
                    continue
            for fmt in ('%m-%d-%Y', '%b %d, %Y'):
                try:
                    return datetime.strptime(date_str, fmt).strftime('%Y%m%d')
                except ValueError:
                    continue
            self.logger.warning(f"Unable to parse date format: {date_str}. Returning default.")
            return '00000000'
        except Exception as e:
            self.logger.error(f"Error formatting date '{date_str}': {str(e)}")
            return '00000000'

    def process_all_zip_files(self) -> List[str]:
        # ... (process_all_zip_files logic remains the same) ...
        processed_json_paths = []
        docket_dir = self._get_target_docket_dir()
        if not docket_dir: self.logger.error("Cannot process zip files: dockets directory not found."); return []
        self.logger.info(f"Scanning for zip files to potentially process/delete in: {docket_dir}")
        try:
            zip_filenames = glob.glob(os.path.join(docket_dir, '*.zip'))
        except Exception as e:
            self.logger.error(f"Error finding zip files in {docket_dir}: {e}")
            return []
        self.logger.info(f"Found {len(zip_filenames)} zip files.")
        if not zip_filenames: return []
        for zip_path in tqdm(zip_filenames, desc="Checking Zips"):
            base_name_no_ext = os.path.splitext(os.path.basename(zip_path))[0]
            json_path = os.path.join(docket_dir, f"{base_name_no_ext}.json")
            pdf_path = os.path.join(docket_dir, f"{base_name_no_ext}.pdf")
            if os.path.isfile(json_path):
                if os.path.isfile(pdf_path): self.logger.debug(
                    f"Zip exists ({os.path.basename(zip_path)}) but target PDF ({os.path.basename(pdf_path)}) already exists. Zip will be deleted by main process if PDF extraction isn't needed from it.")
                processed_json_paths.append(json_path)
            else:
                self.logger.warning(
                    f"Zip file '{os.path.basename(zip_path)}' found, but corresponding JSON '{os.path.basename(json_path)}' is missing. Skipping.")
        return processed_json_paths

    def cleanup(self):
        # ... (cleanup logic remains the same) ...
        self.logger.info("Running temporary file cleanup...")
        temp_dir = tempfile.gettempdir()
        cleaned_count = 0
        patterns_to_remove = [
            re.compile(r'^(marker|pacer|docket|tmp).*\.(pdf|png|json|tmp|bak|jsonl|md)$', re.IGNORECASE),
            re.compile(r'.*_tempmd_.*\.md$', re.IGNORECASE),
            re.compile(r'.*_s3_temp\.pdf$', re.IGNORECASE)
        ]
        try:
            for filename in os.listdir(temp_dir):
                file_path = os.path.join(temp_dir, filename)
                try:
                    if os.path.isfile(file_path) or os.path.islink(file_path):
                        should_remove = any(pattern.match(filename) for pattern in patterns_to_remove)
                        if should_remove:
                            os.remove(file_path)
                            self.logger.debug(f"Removed temp file: {filename}")
                            cleaned_count += 1
                    elif os.path.isdir(file_path):
                        pass
                except OSError as e:
                    self.logger.debug(f"Failed to remove temp file {filename}: {e}")
            self.logger.info(f"Temporary file cleanup complete. Removed {cleaned_count} files from {temp_dir}.")
        except Exception as e:
            self.logger.error(f"Error during general temp file cleanup: {e}", exc_info=True)

    def _cleanup_extra_pdfs(self, target_dir: str):
        # ... (_cleanup_extra_pdfs logic remains the same) ...
        self.logger.warning(f"Running cleanup for extra/temp files in {target_dir}. Review patterns carefully.")
        cleaned_count = 0
        patterns_to_remove = [
            re.compile(r'.*_tempmd_.*\.md$', re.IGNORECASE),
            re.compile(r'.*_s3_temp\.pdf$', re.IGNORECASE),
            re.compile(r'.*\.tmp$', re.IGNORECASE),
            re.compile(r'.*\.bak$', re.IGNORECASE)
        ]
        try:
            for filename in os.listdir(target_dir):
                file_path = os.path.join(target_dir, filename)
                try:
                    if os.path.isfile(file_path):
                        should_remove = any(pattern.match(filename) for pattern in patterns_to_remove)
                        if should_remove:
                            os.remove(file_path)
                            self.logger.debug(f"Removed potentially orphaned/temp file: {filename}")
                            cleaned_count += 1
                except OSError as e:
                    self.logger.error(f"Error removing file {filename} during target dir cleanup: {e}")
            if cleaned_count > 0: self.logger.info(
                f"Target directory cleanup removed {cleaned_count} extra/temp files from {target_dir}.")
        except Exception as e:
            self.logger.error(f"Error during extra file cleanup in {target_dir}: {e}", exc_info=True)

    def get_json_files(self) -> List[str]:
        # ... (get_json_files logic remains the same) ...
        docket_dir = self._get_target_docket_dir()
        if not docket_dir: return []
        try:
            archive_pattern = r'.*_COLLISION_\d{8}_\d{6}\.json$'
            return [os.path.join(docket_dir, f) for f in os.listdir(docket_dir) if
                    f.endswith('.json') and not f.endswith('.bak') and not f.endswith('.tmp') and not re.match(
                        archive_pattern, f)]
        except OSError as e:
            self.logger.error(f"Error listing JSON files in {docket_dir}: {e}")
            return []

    @staticmethod
    def get_pdf_path(json_path: str) -> Optional[str]:
        if not json_path or not isinstance(json_path, str) or not json_path.endswith('.json'):
            module_file_handler_logger.warning(f"Invalid input for get_pdf_path: {json_path}")
            return None
        return json_path.replace('.json', '.pdf')

    @staticmethod
    def _normalize_filename(name: str, preserve_case: bool = False) -> str:
        """
        Normalize a filename component by removing special chars, standardizing spaces
        to underscores, and optionally preserving original case. Truncates long names.
        v4.3 - Explicitly preserves case if requested, fixes potential lowercasing.
        """
        if not isinstance(name, str): return ""
        # --- Character Cleaning (applies regardless of case) ---
        # Allow letters, numbers, underscore, hyphen, period, space (will be replaced)
        cleaned_name = re.sub(r'[^\w\s.-]', '', name)
        # Replace one or more whitespace/hyphen/underscore chars with a single underscore
        cleaned_name = re.sub(r'[\s_-]+', '_', cleaned_name)
        # Remove leading/trailing underscores that might result
        cleaned_name = cleaned_name.strip('_')

        # --- Truncation (applies regardless of case) ---
        max_len = 100  # Maximum length for this component
        if len(cleaned_name) > max_len:
            name_base = cleaned_name[:max_len]
            last_underscore = name_base.rfind('_')
            if last_underscore > max_len / 2:
                cleaned_name = name_base[:last_underscore]
            else:
                cleaned_name = name_base
            try:
                # Use the pre-configured module-level logger
                module_file_handler_logger.debug(f"Truncated normalized component to: {cleaned_name}")
            except NameError:  # Should not happen with module_logger defined
                print(f"DEBUG: Truncated normalized component to: {cleaned_name}")

        # --- Case Handling ---
        if preserve_case:
            # Return the cleaned and truncated string AS IS, preserving original/calculated case
            return cleaned_name
        else:
            # Lowercase only if preserve_case is False
            return cleaned_name.lower()

    @staticmethod
    def create_filename(data: Dict[str, Any]) -> str:
        """
        Create a filename from case data following the format:
        {court_id}_{YY}_{NNNNN}_{cleaned versus}
        - YY: two digits after the colon in docket_num (e.g., "3:25-cv-02446" → 25)
        - NNNNN: the only 5 consecutive digits in docket_num (e.g., "3:25-cv-02446" → 02446)
        - Cleaned versus: remove periods and commas, replace spaces with underscores, preserve case.
        """
        # Use the pre-configured module-level logger
        logger_to_use = module_file_handler_logger  # Use local variable for clarity in this method
        if not isinstance(data, dict):
            try:
                logger_to_use.error("Input to create_filename must be a dictionary")
            except NameError:
                print("ERROR: Input to create_filename must be a dictionary")
            raise TypeError("Input must be a dictionary")

        # Get required fields
        court_id = str(data.get('court_id', '')).strip()
        docket_num = str(data.get('docket_num', '')).strip()
        versus = data.get('versus')
        if not versus or versus in FileHandler.NULL_CONDITIONS:
            versus = data.get('title', '')
        versus = str(versus).strip()

        # Validate
        if not court_id or court_id in FileHandler.NULL_CONDITIONS:
            raise ValueError("Missing/Invalid court_id")
        if not docket_num or docket_num in FileHandler.NULL_CONDITIONS:
            raise ValueError("Missing/Invalid docket_num")
        if not versus or versus in FileHandler.NULL_CONDITIONS:
            try:
                logger_to_use.warning("Missing/Invalid 'versus' or 'title'. Using fallback.")
            except NameError:
                print("WARNING: Missing/Invalid 'versus' or 'title'. Using fallback.")
            versus = "UnknownPlaintiff v. UnknownDefendant"

        # Extract YY: two digits after the colon
        year_str = "YY"
        colon_match = re.search(r':(\d{2})', docket_num)
        if colon_match:
            year_str = colon_match.group(1)
        else:
            try:
                logger_to_use.warning(f"Could not extract YY (2 digits after colon) from docket_num '{docket_num}'")
            except NameError:
                print(f"WARNING: Could not extract YY (2 digits after colon) from docket_num '{docket_num}'")

        # Extract NNNNN: first 5 consecutive digits
        num_str = "NNNNN"
        nnnnn_match = re.search(r'(\d{5})', docket_num)
        if nnnnn_match:
            num_str = nnnnn_match.group(1)
        else:
            try:
                logger_to_use.warning(f"Could not extract NNNNN (5 consecutive digits) from docket_num '{docket_num}'")
            except NameError:
                print(f"WARNING: Could not extract NNNNN (5 consecutive digits) from docket_num '{docket_num}'")

        # Clean versus: remove periods and commas, replace spaces and forward slashes with underscores
        versus_clean = versus.replace('.', '').replace(',', '').replace(' ', '_').replace('/', '_')

        # --- Normalization step for versus_clean using _normalize_filename ---
        # Preserve case, apply character cleaning and truncation from the helper
        versus_normalized = FileHandler._normalize_filename(versus_clean, preserve_case=True)

        # Construct final filename using the normalized versus part
        filename = f"{court_id}_{year_str}_{num_str}_{versus_normalized}"

        try:
            logger_to_use.info(f"Generated filename: {filename}")
        except NameError:
            print(f"INFO: Generated filename: {filename}")
        return filename

    # +++ START NEW METHOD +++
    def get_s3_key_base(self, data: Dict[str, Any]) -> Optional[str]:
        """
        Generates the base S3 key (directory path + base filename without extension)
        using the iso_date from config and the filename generated from the provided data.

        Args:
            data: The dictionary containing the docket information.

        Returns:
            The base S3 key (e.g., "20230101/dockets/court_23_12345_versus") or None on error.
        """
        log_prefix = "[get_s3_key_base]"  # For clarity in logs
        # 1. Get iso_date
        iso_date = self.config.get('iso_date')
        if not iso_date:
            self.logger.error(f"{log_prefix} Cannot generate S3 key base: 'iso_date' missing from config.")
            return None
        if not (isinstance(iso_date, str) and len(iso_date) == 8 and iso_date.isdigit()):
            self.logger.error(
                f"{log_prefix} Cannot generate S3 key base: Invalid 'iso_date' format ('{iso_date}') in config.")
            return None

        # 2. Generate base filename from provided data
        try:
            # Ensure data is a dict
            if not isinstance(data, dict):
                raise TypeError("Input 'data' must be a dictionary.")
            # Use the static create_filename method
            base_filename = FileHandler.create_filename(data)
            if not base_filename:
                raise ValueError("create_filename returned an empty string")
        except (ValueError, KeyError, TypeError) as e:
            self.logger.error(
                f"{log_prefix} Cannot generate S3 key base: Failed to create base filename from provided data. Error: {e}")
            return None
        except Exception as e:
            self.logger.error(
                f"{log_prefix} Unexpected error generating base filename for S3 key from provided data: {e}",
                exc_info=True)
            return None

        # 3. Construct the S3 key base (e.g., YYYYMMDD/dockets/filename_base)
        # Ensure posix-style paths for S3 keys
        s3_key_base = f"{iso_date}/dockets/{base_filename}"
        self.logger.debug(f"{log_prefix} Generated S3 key base: {s3_key_base}")
        return s3_key_base

    # +++ END NEW METHOD +++

    async def download_s3_file_async(self, s3_url: str, local_download_path: str) -> bool:
        """
        Asynchronously downloads a file from an S3 URL by calling a synchronous
        helper method (_perform_s3_download_sync) within an executor.
        """
        if not self.s3_manager:
            self.logger.error("S3 Manager not configured in FileHandler. Cannot download from S3.")
            return False
        if not s3_url or not isinstance(s3_url, str):
            self.logger.error(f"Invalid S3 URL provided for download: {s3_url}")
            return False
        if not local_download_path:
            self.logger.error("Invalid local download path provided.")
            return False

        self.logger.debug(f"Attempting S3 download via sync helper: {s3_url} -> {local_download_path}")

        # --- START DIAGNOSTIC LOGGING (Still useful) ---
        self.logger.debug(f"Type of self.s3_manager before executor: {type(self.s3_manager)}")
        if self.s3_manager:
            try:
                import inspect
                s3_manager_file = inspect.getfile(self.s3_manager.__class__)
                self.logger.debug(f"File path for self.s3_manager's class: {s3_manager_file}")
            except Exception as inspect_err:
                self.logger.debug(f"Could not inspect file path for s3_manager class: {inspect_err}")
            try:
                available_methods = [m for m in dir(self.s3_manager) if
                                     not m.startswith('_') and callable(getattr(self.s3_manager, m))]
                self.logger.debug(f"Available public methods on self.s3_manager (sample): {available_methods[:20]}")
            except Exception as dir_err:
                self.logger.debug(f"Could not list methods for s3_manager: {dir_err}")
        else:
            self.logger.warning("self.s3_manager is None during diagnostic check.")
        # --- END DIAGNOSTIC LOGGING ---

        try:
            # --- Call the synchronous helper using run_in_executor ---
            loop = asyncio.get_running_loop()
            self.logger.debug(f"Executing _perform_s3_download_sync for URL '{s3_url}' in executor.")

            # Pass the helper method *bound to the current instance* (self)
            success = await loop.run_in_executor(
                None,  # Use default executor
                self._perform_s3_download_sync,  # The synchronous method of this FileHandler instance
                s3_url,  # Argument 1 for the helper
                local_download_path  # Argument 2 for the helper
            )

            # --- Process the result ---
            if success:
                self.logger.info(
                    f"Asynchronous S3 download successful (via sync helper) for: {os.path.basename(local_download_path)}")
                return True
            else:
                self.logger.error(f"Asynchronous S3 download failed (sync helper returned False) for: {s3_url}")
                return False

        except Exception as e:
            # Catch errors happening during the executor call itself or the helper execution
            self.logger.error(f"Unexpected error during async S3 download process ({s3_url}): {e}", exc_info=True)
            # Attempt cleanup
            if os.path.exists(local_download_path):
                await asyncio.to_thread(try_remove, local_download_path, self.logger, "partial download on async error")
            return False

    def _perform_s3_download_sync(self, s3_url: str, local_download_path: str) -> bool:
        """
        Synchronous helper to download from S3 URL using the attached s3_manager.
        This method is intended to be run within an executor.
        """
        object_key = None
        try:
            # --- Parse S3 URL to get object key ---
            # (Reusing parsing logic from async version)
            parsed_url = urlparse(s3_url)
            if parsed_url.scheme == 's3':
                object_key = parsed_url.path.lstrip('/')
            elif 'cdn.lexgenius.ai' in parsed_url.netloc:
                object_key = parsed_url.path.lstrip('/')
            elif 's3.' in parsed_url.netloc and 'amazonaws.com' in parsed_url.netloc:
                path_parts = parsed_url.path.lstrip('/').split('/', 1)
                # Handle both virtual-hosted-style (bucket.s3...) and path-style (s3.region.../bucket)
                # Check if bucket name is part of the host
                if parsed_url.netloc.split('.')[0] != 's3':  # Likely virtual-hosted, path starts with key
                    object_key = parsed_url.path.lstrip('/')
                else:  # Likely path-style, path starts with bucket/key
                    # The original logic seems slightly off, let's refine
                    # Assuming path style: s3.amazonaws.com/bucket-name/key
                    # OR s3.region.amazonaws.com/bucket-name/key
                    bucket_name_in_path = path_parts[0]  # First part should be bucket
                    # We need the configured bucket name to be sure, but S3Manager handles this.
                    # Let's assume the key is everything after the bucket name in the path.
                    object_key = path_parts[1] if len(path_parts) > 1 else ''
                    # A better approach in S3Manager might be preferred, but let's try this for now.
                    # Reverting to simpler logic that worked before, S3 manager must handle bucket name removal if needed
                    object_key = parsed_url.path.lstrip('/')


            else:
                self.logger.error(f"[SyncDownload] Cannot determine S3 object key from URL format: {s3_url}")
                return False

            if not object_key:
                self.logger.error(f"[SyncDownload] Failed to extract a valid S3 object key from URL: {s3_url}")
                return False

            self.logger.debug(f"[SyncDownload] Parsed S3 object key: {object_key}")

            # --- Ensure target directory exists ---
            target_dir = os.path.dirname(local_download_path)
            # Use synchronous os.makedirs within this sync function
            os.makedirs(target_dir, exist_ok=True)

            # --- Directly call the download_file method on the s3_manager instance ---
            # Check attribute existence again right before calling
            if not hasattr(self.s3_manager, 'download_file'):
                self.logger.critical(
                    f"[SyncDownload] FATAL: S3Manager instance STILL missing 'download_file' method for key {object_key}")
                return False

            self.logger.debug(f"[SyncDownload] Calling s3_manager.download_file for key: {object_key}")
            # Call the method added previously to S3Manager
            self.s3_manager.download_file(object_key, local_download_path)  # Assumes S3Manager takes full key

            # Basic verification after download attempt (method handles detailed errors)
            if os.path.exists(local_download_path) and os.path.getsize(local_download_path) > 0:
                self.logger.info(f"[SyncDownload] S3 download successful for {object_key} to {local_download_path}")
                return True
            else:
                self.logger.error(
                    f"[SyncDownload] Download call completed but file verification failed for {local_download_path}. Check S3Manager logs.")
                # Attempt cleanup if file exists but is empty/corrupt
                if os.path.exists(local_download_path):
                    try_remove(local_download_path, self.logger, "failed/empty sync S3 download")
                return False

        except AttributeError as ae:
            # Catch if download_file is STILL missing
            self.logger.critical(
                f"[SyncDownload] AttributeError calling s3_manager.download_file for key {object_key}: {ae}",
                exc_info=True)
            return False
        except Exception as e:
            self.logger.error(
                f"[SyncDownload] Unexpected error during S3 download for {s3_url} (key: {object_key}): {e}",
                exc_info=True)
            # Attempt cleanup
            if os.path.exists(local_download_path):
                try_remove(local_download_path, self.logger, "partial sync S3 download on error")
            return False
