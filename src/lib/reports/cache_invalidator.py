# --- START OF UPDATED cache_invalidator.py ---
import asyncio # Add this import
import logging
import os
# Ensure CloudFrontInvalidator is imported correctly
# Adjust path if needed based on your structure
try:
    from src.lib.cloudfront_invalidator import CloudFrontInvalidator
except ImportError:
    # Handle case where it might not be found (e.g., dummy class)
    CloudFrontInvalidator = None
    logging.getLogger(__name__).warning("CloudFrontInvalidator not found, cache invalidation disabled.")

from src.lib.reports.config import ReportConfig


class CacheInvalidator:
    """Handles CloudFront cache invalidation."""
    def __init__(self, report_config: ReportConfig):
        self.config = report_config
        self.logger = logging.getLogger(f"{__name__}.CacheInvalidator")
        self.invalidator = None
        # Check if the class exists before trying to use it
        if CloudFrontInvalidator and self.config.cloudfront_distribution_id:
            try:
                # Create config with required AWS credentials from the main config object
                # ReportConfig should ideally hold these if needed, or load from env
                cf_config = {
                    'cloudfront_distribution_id': self.config.cloudfront_distribution_id,
                    'aws_access_key': self.config.config_dict.get('aws_access_key', os.environ.get('AWS_ACCESS_KEY_ID')), # Check common env var too
                    'aws_secret_key': self.config.config_dict.get('aws_secret_key', os.environ.get('AWS_SECRET_ACCESS_KEY')) # Check common env var too
                }

                # Check if we have the required credentials
                if not cf_config['aws_access_key'] or not cf_config['aws_secret_key']:
                    self.logger.error("AWS credentials missing (checked config and environment). Cannot initialize CloudFrontInvalidator.")
                    return # Keep self.invalidator as None

                self.invalidator = CloudFrontInvalidator(cf_config, self.config.cloudfront_distribution_id)
                self.logger.info(f"CloudFront invalidator initialized for distribution ID: {self.config.cloudfront_distribution_id}")
            except Exception as e:
                self.logger.error(f"Failed to initialize CloudFrontInvalidator: {e}", exc_info=True)
                self.invalidator = None # Ensure it's None on error
        elif not CloudFrontInvalidator:
             self.logger.warning("CloudFrontInvalidator class not imported. Cache invalidation will be skipped.")
        else: # CloudFrontInvalidator exists but ID is missing
            self.logger.warning("CloudFront Distribution ID not found in config. Cache invalidation will be skipped.")

    # Make invalidate_cache async
    async def invalidate_cache(self):
        """Asynchronously invalidates relevant CloudFront cache paths."""
        if not self.invalidator: # Check if invalidator was successfully initialized
            self.logger.info("Skipping CloudFront invalidation (invalidator not available).")
            return
        if not self.config.iso_date:
             self.logger.info("Skipping CloudFront invalidation (iso_date missing).")
             return

        paths_to_invalidate = [
            f'/{self.config.iso_date}/index.html',
            f'/{self.config.iso_date}/ads/*', # Invalidate all generated ad pages for the day
            f'/{self.config.iso_date}/dockets/*', # Invalidate all docket PDFs for the day
            '/index.html' # Invalidate the root daily report
        ]
        # Always invalidate weekly report if this is a weekly report generation
        # Note: weekly reports can be generated on any day, not just Sundays
        if hasattr(self.config, 'is_weekly_report') and self.config.is_weekly_report:
            paths_to_invalidate.append(f'/{self.config.iso_date}/index-weekly.html')
        elif self.config.is_sunday:  # Keep the original Sunday logic as fallback
            paths_to_invalidate.append(f'/{self.config.iso_date}/index-weekly.html')

        self.logger.info(f"Requesting CloudFront invalidation for {len(paths_to_invalidate)} paths...")
        self.logger.debug(f"Paths: {paths_to_invalidate}")

        try:
            # Run the synchronous boto3 call in a thread
            response = await asyncio.to_thread(
                self.invalidator.create_invalidation, # Pass the method
                paths_to_invalidate                   # Pass the argument
            )
            # Process response (no await needed here)
            if response and 'Invalidation' in response:
                inval_info = response['Invalidation']
                self.logger.info(f'CloudFront Invalidation submitted. ID: {inval_info.get("Id")}, Status: {inval_info.get("Status")}')
            else:
                self.logger.warning(f"CloudFront invalidation response missing expected 'Invalidation' data: {response}")
        except Exception as e:
            self.logger.error(f"CloudFront invalidation failed: {e}", exc_info=True)

# --- END OF UPDATED cache_invalidator.py ---