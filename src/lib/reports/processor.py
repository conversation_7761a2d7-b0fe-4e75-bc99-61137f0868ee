import json
import logging
import os
import re
from datetime import datetime
from typing import Any, Dict, List, Tuple, Optional

import pandas as pd

from src.lib.pacer_manager import PacerManager
from src.lib.reports.config import ReportConfig


class ReportDataProcessor:
    """Processes loaded dataframes for reporting."""

    def __init__(self, report_config: ReportConfig, pacer_manager: PacerManager):
        self.config = report_config
        self.pacer_db = pacer_manager  # Needed for MDL summary
        self.logger = logging.getLogger(f"{__name__}.ReportDataProcessor")
        # Use the correct path to MDL lookup file
        self.mdl_lookup_path = os.path.abspath(os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'config', 'mdl', 'mdl_lookup.json'
        ))
        if not os.path.exists(self.mdl_lookup_path):
            self.logger.warning(f"MDL lookup file not found at {self.mdl_lookup_path}. Summaries may be limited.")
            self.mdl_lookup_df = pd.DataFrame()
        else:
            try:
                self.mdl_lookup_df = pd.read_json(self.mdl_lookup_path)
                if 'mdl_num' in self.mdl_lookup_df.columns:
                    self.mdl_lookup_df['mdl_num'] = self.mdl_lookup_df['mdl_num'].astype(
                        str).str.strip().str.removesuffix('.0')
                else:
                    self.logger.warning("MDL lookup file missing 'mdl_num' column.")
            except Exception as e:
                self.logger.error(f"Failed to load MDL lookup file: {e}", exc_info=True)
                self.mdl_lookup_df = pd.DataFrame()

        # Path to AFFF stats file
        self.afff_stats_path = os.path.abspath(os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'config', 'reports', 'afff_stats.json'
        ))

    @staticmethod
    def _format_text(value: Any) -> str:
        """Helper to clean text fields."""
        # Duplicated from DynamicDataLoader - consider moving to a shared utils module
        if not isinstance(value, str): return ''
        value = value.strip()
        value = re.sub(r'\r\n', '\n', value)
        value = re.sub(r'\n{3,}', '\n\n', value)
        return value

    def group_filings_by_litigation(self, docket_df: pd.DataFrame) -> Tuple[Dict[str, Dict[str, Any]], pd.DataFrame]:
        """Groups docket data by normalized title for the litigation report."""
        if docket_df is None or docket_df.empty:
            self.logger.warning("Cannot group titles: Input Docket DF is empty or None.")
            return {}, pd.DataFrame(columns=['title', 'Total Filings', 'mdl_num'])

        required_cols = ['title', 'law_firm', 'num_plaintiffs', 'pending_cto', 'transferred_in', 's3_link', 'versus',
                         'mdl_num']
        if not all(col in docket_df.columns for col in required_cols):
            missing = [col for col in required_cols if col not in docket_df.columns]
            self.logger.error(f"Input DataFrame to group_filings_by_litigation is missing columns: {missing}.")
            return {}, pd.DataFrame(columns=['title', 'Total Filings', 'mdl_num'])

        self.logger.info(f'Grouping {len(docket_df)} docket entries by title and law firm.')
        df_copy = docket_df.copy()

        # --- Ensure types ---
        df_copy['mdl_num'] = df_copy['mdl_num'].fillna('').astype(str).str.strip().replace('nan', '',
                                                                                           regex=False).replace('None',
                                                                                                                '',
                                                                                                                regex=False).replace(
            'NA', '', regex=False)
        df_copy['title'] = df_copy['title'].astype(str)
        # law_firm should already be processed by LawFirmProcessor via DynamicDataLoader
        df_copy['law_firm'] = df_copy['law_firm'].fillna('Unknown Firm').astype(str)

        # Ensure num_plaintiffs is numeric and default to 0 for non-numeric/missing
        # This is crucial for the filing_weight calculation.
        df_copy['num_plaintiffs'] = pd.to_numeric(df_copy['num_plaintiffs'], errors='coerce').fillna(0).astype(int)

        df_copy['pending_cto'] = df_copy['pending_cto'].astype(bool)
        df_copy['transferred_in'] = df_copy['transferred_in'].astype(bool)
        df_copy['s3_link'] = df_copy['s3_link'].fillna('').astype(str)
        if 'versus_sort' not in df_copy.columns:
            df_copy['versus_sort'] = df_copy['versus'].fillna('Unknown Defendant').astype(str).str.lower()

        # --- Calculate filing weight ---
        # If num_plaintiffs > 0, use that value. Otherwise (if num_plaintiffs is 0), count as 1.
        df_copy['filing_weight'] = df_copy['num_plaintiffs']
        df_copy.loc[df_copy['filing_weight'] == 0, 'filing_weight'] = 1

        # --- ADDED LOGGING for num_plaintiffs and filing_weight ---
        if not df_copy.empty:
            # Log a few relevant rows, especially if you know which ones are problematic
            # For example, filter for "Suboxone" or "Benton Law Firm" if possible
            # This generic log shows the first 5 and last 5 after processing.
            self.logger.debug(f"DataFrame sample before grouping (showing num_plaintiffs and filing_weight):")
            pd_options = ['display.max_rows', None, 'display.max_columns', None, 'display.width', 1000]
            with pd.option_context(*pd_options):
                if len(df_copy) > 10:
                    self.logger.debug(
                        f"Head:\n{df_copy[['docket_num', 'title', 'law_firm', 'num_plaintiffs', 'filing_weight']].head().to_string()}")
                    self.logger.debug(
                        f"Tail:\n{df_copy[['docket_num', 'title', 'law_firm', 'num_plaintiffs', 'filing_weight']].tail().to_string()}")
                else:
                    self.logger.debug(
                        f"\n{df_copy[['docket_num', 'title', 'law_firm', 'num_plaintiffs', 'filing_weight']].to_string()}")

            # Example: Log specific case if "Benton Law Firm" is present and related to "Suboxone"
            # Note: Law firm name might be normalized by LawFirmProcessor
            # benton_suboxone_sample = df_copy[
            #     df_copy['law_firm'].str.contains("Benton", case=False, na=False) &
            #     df_copy['title'].str.contains("Suboxone", case=False, na=False)
            # ]
            # if not benton_suboxone_sample.empty:
            #     self.logger.info(f"Specific Sample (e.g., Benton/Suboxone) for num_plaintiffs/filing_weight check:\n"
            #                      f"{benton_suboxone_sample[['docket_num', 'title', 'law_firm', 'num_plaintiffs', 'filing_weight']].to_string()}")
            # else:
            #     self.logger.info("Specific sample (Benton/Suboxone) not found for detailed logging.")

        # --- End Logging ---

        # --- Aggregation Logic (original) ---
        def get_first_valid_mdl(series):
            first_valid = series.astype(str).str.strip().replace('', pd.NA).dropna().iloc[0] if not series.astype(
                str).str.strip().replace('', pd.NA).dropna().empty else ''
            return first_valid.removesuffix('.0')

        # def aggregate_links(series): # Not used currently
        #     return list(series.astype(str).replace('', pd.NA).dropna())

        try:
            grouped_by_firm = df_copy.groupby(['title', 'law_firm'], as_index=False).agg(
                mdl_num=('mdl_num', get_first_valid_mdl),
                Filings=('filing_weight', 'sum')  # This sums the calculated filing_weight
            )
        except Exception as e:
            self.logger.error(f"Error during initial groupby aggregation: {e}", exc_info=True)
            return {}, pd.DataFrame(columns=['title', 'Total Filings', 'mdl_num'])

        title_totals_agg = grouped_by_firm.groupby('title', as_index=False)['Filings'].sum().rename(
            columns={'Filings': 'Total Filings'})
        
        # Use consistent MDL aggregation instead of drop_duplicates to avoid mixing up MDL numbers
        title_mdl_map = grouped_by_firm.groupby('title', as_index=False).agg(
            mdl_num=('mdl_num', get_first_valid_mdl)
        ).set_index('title')
        
        title_totals = title_totals_agg.set_index('title').join(title_mdl_map).reset_index()
        title_totals['mdl_num'] = title_totals['mdl_num'].fillna('').astype(str)
        title_totals = title_totals.sort_values(by=['Total Filings', 'title'], ascending=[False, True])
        # --- End Aggregation ---

        # --- Pre-process for Jinja (original logic) ---
        grouped_litigation_processed = {}
        for title_text in title_totals['title']:  # Renamed 'title' to 'title_text' to avoid conflict
            firm_data_df = grouped_by_firm[grouped_by_firm['title'] == title_text].sort_values(by='Filings',
                                                                                               ascending=False).reset_index(
                drop=True)
            title_mdl_num = firm_data_df['mdl_num'].iloc[0] if not firm_data_df.empty else ''
            firm_records = firm_data_df[['law_firm', 'Filings']].to_dict('records')

            current_total_filings_for_title = int(firm_data_df['Filings'].sum())
            # Log the total filings per title after aggregation
            self.logger.debug(
                f"Title: '{title_text}', MDL: {title_mdl_num}, Calculated Total Filings: {current_total_filings_for_title}")

            grouped_litigation_processed[title_text] = {
                'firm_records': firm_records,
                'mdl_num': title_mdl_num,
                'total_filings': current_total_filings_for_title
            }
        # --- End Pre-processing ---

        self.logger.info(f"Finished grouping litigation data. Found {len(title_totals)} unique titles.")
        # Log a sample of the final title_totals to verify aggregated 'Total Filings'
        if not title_totals.empty:
            self.logger.debug(f"Final title_totals sample:\n{title_totals.head().to_string()}")

        return grouped_litigation_processed, title_totals

    def group_filings_by_firm(self, docket_df: pd.DataFrame) -> Dict[str, Dict[str, List[Dict[str, Any]]]]:
        """Pre-processes detailed filings data grouped by firm and title."""
        detailed_filings_grouped = {}
        if docket_df is None or docket_df.empty:
            self.logger.warning("Cannot group detailed filings: Input Docket DF is empty.")
            return detailed_filings_grouped
        if 'law_firm' not in docket_df.columns or 'title' not in docket_df.columns:
            self.logger.warning("Docket DF missing 'law_firm' or 'title', cannot pre-process detailed filings.")
            return detailed_filings_grouped

        self.logger.info("Pre-processing detailed filings data...")
        try:
            # Ensure required columns for output exist, add if missing
            required_output_cols = ['versus', 's3_link', 'mdl_num', 'num_plaintiffs', 'transferred_in', 'pending_cto']
            for col in required_output_cols:
                if col not in docket_df.columns:
                    docket_df[col] = '' if col in ['versus', 's3_link', 'mdl_num'] else (
                        0 if col == 'num_plaintiffs' else False)

            # Sort and group (original logic)
            sort_cols = ['law_firm', 'title', 'versus_sort']
            if 'versus_sort' not in docket_df.columns:
                docket_df['versus_sort'] = docket_df['versus'].fillna('').astype(str).lower()

            sorted_df = docket_df.sort_values(by=sort_cols)
            grouped_by_firm = sorted_df.groupby('law_firm')

            for law_firm, firm_group in grouped_by_firm:
                firm_data = {}
                grouped_by_title = firm_group.groupby('title')
                for title, title_group in grouped_by_title:
                    # Select only needed columns and convert to dict
                    case_records = title_group[required_output_cols].to_dict('records')
                    firm_data[title] = case_records
                detailed_filings_grouped[law_firm] = firm_data
            self.logger.info(f"Successfully pre-processed detailed filings for {len(detailed_filings_grouped)} firms.")
        except Exception as group_err:
            self.logger.error(f"Error pre-processing detailed filings data: {group_err}", exc_info=True)
            detailed_filings_grouped = {}  # Return empty on error

        return detailed_filings_grouped

    def filter_ad_summaries(self, ad_df: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """ Filters ad summaries within each law firm group to reduce redundancy."""
        grouped_ads_filtered_dict = {}
        if ad_df is None or ad_df.empty or 'law_firm' not in ad_df.columns:
            self.logger.warning("Cannot filter ad summaries: Ad DF is empty or missing 'law_firm'.")
            return grouped_ads_filtered_dict

        grouped_ad_data = ad_df.groupby('law_firm')
        self.logger.info(f"Filtering ad summaries for {len(grouped_ad_data.groups)} law firm groups.")

        for law_firm, group_df in grouped_ad_data:
            self.logger.debug(f"Filtering ads for: {law_firm} ({len(group_df)} ads initially)")
            if group_df.empty or 'summary' not in group_df.columns:
                self.logger.debug(f"Skipping empty or invalid group for {law_firm}")
                continue

            # --- Filtering Logic ---
            group_df_copy = group_df.copy()
            group_df_copy['summary'] = group_df_copy['summary'].fillna('').astype(str)
            group_df_copy = group_df_copy[group_df_copy['summary'].str.strip() != '']
            if group_df_copy.empty:
                continue

            ride_share_keywords = {'uber', 'lyft'}
            try:
                group_df_copy['first_word_lower'] = group_df_copy['summary'].str.split(n=1).str[0].str.lower()
                is_ride_share = group_df_copy['first_word_lower'].isin(ride_share_keywords)
            except Exception as e:
                self.logger.warning(f"Error extracting first word for filtering in group {law_firm}: {e}.")
                is_ride_share = pd.Series([False] * len(group_df_copy), index=group_df_copy.index)

            ride_share_rows = group_df_copy[is_ride_share]
            non_ride_share_rows = group_df_copy[~is_ride_share]

            unique_ride_share = ride_share_rows.drop_duplicates(subset=['summary'], keep='first')

            if not non_ride_share_rows.empty:
                unique_non_ride_share = non_ride_share_rows.drop_duplicates(subset=['first_word_lower'], keep='first')
            else:
                # Ensure unique_non_ride_share has the same columns as group_df_copy if it's empty
                unique_non_ride_share = pd.DataFrame(columns=group_df_copy.columns)

            dfs_to_concat = []
            if not unique_ride_share.empty:
                dfs_to_concat.append(unique_ride_share)
            if not unique_non_ride_share.empty:
                dfs_to_concat.append(unique_non_ride_share)

            if dfs_to_concat:
                filtered_group = pd.concat(dfs_to_concat, ignore_index=True)
            else:
                # If both unique_ride_share and unique_non_ride_share are empty (0 rows),
                # create an empty DataFrame with the columns from group_df_copy (which includes 'first_word_lower').
                filtered_group = pd.DataFrame(columns=group_df_copy.columns)

            if 'first_word_lower' in filtered_group.columns:
                filtered_group = filtered_group.drop(columns=['first_word_lower'])
            # --- End Filtering Logic ---

            if not filtered_group.empty:
                grouped_ads_filtered_dict[law_firm] = filtered_group
                self.logger.debug(f"Kept {len(filtered_group)} ads for {law_firm} after filtering.")

        num_filtered_ads = sum(len(df) for df in grouped_ads_filtered_dict.values())
        self.logger.info(
            f"Finished filtering ads. {num_filtered_ads} ads remaining across {len(grouped_ads_filtered_dict)} firms.")
        return grouped_ads_filtered_dict

    def calculate_afff_stats(self, docket_df: pd.DataFrame) -> Dict[str, Any]:
        """ Calculates AFFF specific statistics."""
        self.logger.info("Calculating AFFF stats...")
        # Load stats from JSON file
        if os.path.exists(self.afff_stats_path):
            try:
                with open(self.afff_stats_path) as f:
                    return json.load(f)
            except Exception as e:
                self.logger.error(f"Failed to load AFFF stats file: {e}", exc_info=True)

        # Fallback to default values if file doesn't exist or fails to load
        default_stats = {'pacer_cases_period': 0, 'pacer_plaintiffs_period': 0, 'transferred_in_period': 0,
                         'transferred_in_plaintiffs_period': 0, 'direct_filings_placeholder': 28,
                         'direct_filings_30_days_placeholder': 610, 'total_plaintiffs_30_days_placeholder': 840,
                         'filings_ytd_placeholder': 2559}

        if docket_df is None or docket_df.empty:
            self.logger.warning("Cannot calculate AFFF stats: Docket DF is empty.")
            return default_stats

        required_cols = ['mdl_num', 'pending_cto', 'transferred_in', 'num_plaintiffs']
        if not all(col in docket_df.columns for col in required_cols):
            missing = [col for col in required_cols if col not in docket_df.columns]
            self.logger.warning(f"Cannot calculate AFFF stats: Docket DF missing columns {missing}.")
            return default_stats

        try:
            afff_df = docket_df[docket_df['mdl_num'] == '2873'].copy()
            self.logger.info(f"Found {len(afff_df)} AFFF (MDL 2873) records.")

            # Ensure correct types (original logic)
            afff_df['pending_cto'] = afff_df['pending_cto'].astype(bool)
            afff_df['transferred_in'] = afff_df['transferred_in'].astype(bool)
            afff_df['num_plaintiffs'] = pd.to_numeric(afff_df['num_plaintiffs'], errors='coerce').fillna(0).astype(int)

            pending_mask = afff_df['pending_cto'] & ~afff_df['transferred_in']
            transferred_mask = afff_df['transferred_in']

            calculated_pending_cases = afff_df[pending_mask].shape[0]
            calculated_pending_plaintiffs = int(afff_df.loc[pending_mask, 'num_plaintiffs'].sum())
            calculated_transferred_cases = afff_df[transferred_mask].shape[0]
            calculated_transferred_plaintiffs = int(afff_df.loc[transferred_mask, 'num_plaintiffs'].sum())

            stats = {
                'pacer_cases_period': calculated_pending_cases,
                'pacer_plaintiffs_period': calculated_pending_plaintiffs,
                'transferred_in_period': calculated_transferred_cases,
                'transferred_in_plaintiffs_period': calculated_transferred_plaintiffs,
                # Pass through the placeholders calculated earlier or use defaults
                'direct_filings_placeholder': default_stats['direct_filings_placeholder'],
                'direct_filings_30_days_placeholder': default_stats['direct_filings_30_days_placeholder'],
                'total_plaintiffs_30_days_placeholder': default_stats['total_plaintiffs_30_days_placeholder'],
                'filings_ytd_placeholder': default_stats['filings_ytd_placeholder']
            }
            self.logger.info(f"Calculated AFFF Stats: {stats}")
            return stats
        except Exception as e:
            self.logger.error(f"Error during AFFF stats calculation: {e}", exc_info=True)
            return default_stats  # Return defaults on error

    def get_mdl_summary_data(self, afff_stats: Optional[Dict[str, Any]] = None) -> Tuple[List[str], List[int]]:
        """
        Retrieves Top N MDLs by filing count for the chart, using cache.
        Applies AFFF *combined* placeholder value (sum of specific stats) BEFORE sorting and slicing if stats are provided.
        Maps MDL numbers to short names using a comprehensive lookup.
        """
        self.logger.info("Retrieving MDL summary for chart...")
        if not self.config.iso_date or not self.config.download_dir:
            self.logger.error("Cannot get MDL summary: iso_date or download_dir not set.")
            return [], []

        cache_file_path = os.path.join(self.config.download_dir, 'reports', f"mdl_summary_{self.config.iso_date}.json")

        # --- Cache Check ---
        if os.path.exists(cache_file_path):
            try:
                with open(cache_file_path, 'r', encoding='utf-8') as file:
                    data = json.load(file)
                if isinstance(data, dict) and 'chart_labels' in data and 'chart_data' in data:
                    self.logger.info(f"Loaded MDL summary from cache: {cache_file_path}")
                    return data['chart_labels'], data['chart_data']
                else:
                    self.logger.warning(f"MDL summary cache file '{cache_file_path}' invalid. Recalculating.")
            except Exception as e:
                self.logger.warning(f"Error reading MDL summary cache '{cache_file_path}': {e}. Recalculating.")

        # --- Calculation (if cache miss/invalid) ---
        self.logger.info("Calculating MDL summary (cache miss or invalid).")
        try:
            # Assumes self.pacer_db is an initialized PacerManager instance
            mdl_summary_df = self.pacer_db.get_mdl_summary(self.config.iso_date)
            if mdl_summary_df is None or mdl_summary_df.empty:
                self.logger.warning(f"No MDL summary data returned from DB for {self.config.iso_date}.")
                return [], []

            # --- Processing Logic ---
            required_db_cols = ['MdlNum', 'Total']
            if not all(col in mdl_summary_df.columns for col in required_db_cols):
                missing_cols = [col for col in required_db_cols if col not in mdl_summary_df.columns]
                self.logger.error(f"MDL summary DataFrame from DB missing required columns: {missing_cols}.")
                return [], []

            df_proc = mdl_summary_df.copy().dropna(subset=required_db_cols)
            df_proc['MdlNum'] = df_proc['MdlNum'].astype(str).str.strip().replace('nan', '', regex=False).replace(
                'None', '', regex=False).replace('NA', '', regex=False)
            df_proc = df_proc[~df_proc['MdlNum'].str.upper().isin(['', 'NA'])]
            df_proc['Total'] = pd.to_numeric(df_proc['Total'], errors='coerce').fillna(0).astype(int)

            # Define COMPREHENSIVE MDL Lookup dictionary TODO: Load from src/config/mdl/short_name_lookup.json
            mdl_lookup = {
                "3060": "Hair Relaxer",
                "2873": "AFFF",
                "3094": "Ozempic/GLP-1",
                "2738": "Talcum Powder",
                "CLJ": "Camp Lejeune",  # Key for lookup
                "25": "Camp Lejeune",  # Map '25' to CLJ key
                "3092": "Suboxone Film",
                "2666": "Bair Hugger",
                "3004": "Paraquat",
                "2974": "Paragard IUD",
                "3081": "Bard Port Catheter",
                "3029": "Covidien Mesh",
                "3140": "Depo-Provera",
                "2741": "Roundup",
                "2921": "Allergan Biocell",
                "3125": "AngioDynamics Port",
                "2846": "Bard Hernia Mesh",
                "3084": "Rideshare Assault",
                "3047": "Social Media Addiction"
                # Add any other necessary mappings
            }
            self.logger.debug(f"Using MDL Lookup: {list(mdl_lookup.keys())}")  # Log keys being used

            # *** START: Apply AFFF Placeholder Logic (SUM of two values) BEFORE Sorting ***
            afff_combined_placeholder_value = None
            if afff_stats and isinstance(afff_stats, dict):
                placeholder_key_plaintiffs = 'total_plaintiffs_30_days_placeholder'
                placeholder_key_filings = 'direct_filings_30_days_placeholder'
                plaintiffs_val = afff_stats.get(placeholder_key_plaintiffs)
                filings_val = afff_stats.get(placeholder_key_filings)

                if plaintiffs_val is not None and filings_val is not None:
                    try:
                        # Calculate the SUM here
                        afff_combined_placeholder_value = int(plaintiffs_val) + int(filings_val)
                        self.logger.info(
                            f"Calculated combined AFFF placeholder value: {plaintiffs_val} + {filings_val} = {afff_combined_placeholder_value}")
                    except (ValueError, TypeError) as conv_err:
                        self.logger.error(
                            f"Could not convert one or both AFFF placeholder values ('{plaintiffs_val}', '{filings_val}') to int. Placeholder not applied. Error: {conv_err}")
                        afff_combined_placeholder_value = None  # Ensure it's None if conversion fails
                else:
                    missing_keys = []
                    if plaintiffs_val is None: missing_keys.append(placeholder_key_plaintiffs)
                    if filings_val is None: missing_keys.append(placeholder_key_filings)
                    self.logger.warning(
                        f"Key(s) {missing_keys} not found in provided afff_stats dict. Cannot calculate combined placeholder.")

            if afff_combined_placeholder_value is not None:
                afff_mdl_num = "2873"  # AFFF MDL number as string key
                afff_mask = df_proc['MdlNum'] == afff_mdl_num
                if afff_mask.any():
                    # Safely get original value(s) for logging before overwrite
                    original_values = df_proc.loc[afff_mask, 'Total'].tolist()
                    # Apply the CALCULATED SUM to the DataFrame Total column
                    df_proc.loc[afff_mask, 'Total'] = afff_combined_placeholder_value
                    self.logger.info(
                        f"Applied COMBINED AFFF placeholder: Updated MDL {afff_mdl_num} total from {original_values} to {afff_combined_placeholder_value} BEFORE sorting.")
                else:
                    self.logger.info(
                        f"MDL {afff_mdl_num} (AFFF) not found in summary data before sorting. Combined placeholder not applied.")
            else:
                self.logger.warning(
                    "Could not apply combined AFFF placeholder value: Values missing from afff_stats or calculation failed.")
            # *** END: Apply AFFF Placeholder Logic ***

            # --- Sort AFTER potentially modifying AFFF value ---
            # Sort by the (potentially adjusted) 'Total', then by 'MdlNum' for stable sort
            df_proc = df_proc.sort_values(by=['Total', 'MdlNum'], ascending=[False, True])

            # --- Select Top N ---
            # Assumes self.config.mdl_chart_top_n is set
            top_n = df_proc.head(self.config.mdl_chart_top_n)
            self.logger.debug(
                f"Top {self.config.mdl_chart_top_n} MDL Summary DF (after potential AFFF adjustment & sorting):\n{top_n.to_string()}")

            # --- Generate Labels/Data from FINAL Top N ---
            chart_labels = []
            chart_data = []
            for index, row in top_n.iterrows():  # Use index for logging if needed
                # Clean the MDL number from the potentially modified DataFrame row
                mdl_num_from_row = str(row['MdlNum']).strip().removesuffix('.0')
                # Determine the key for lookup (handle Camp Lejeune case)
                mdl_key = 'CLJ' if mdl_num_from_row == '25' else mdl_num_from_row
                # Perform the lookup using the derived key and the COMPREHENSIVE dictionary
                mdl_name = mdl_lookup.get(mdl_key, f"MDL {mdl_key}")  # Default if key not in lookup

                # Debugging Log: Check the mapping result for each row
                self.logger.debug(
                    f"Row Index: {index}, MdlNum: '{row['MdlNum']}', Cleaned Num: '{mdl_num_from_row}', Lookup Key: '{mdl_key}', Mapped Name: '{mdl_name}', Value: {row['Total']}")

                chart_labels.append(mdl_name)
                # Use the 'Total' value from the row, which will be the placeholder if applied
                chart_data.append(int(row['Total']))

            # --- Save to Cache ---
            if chart_labels:
                # Save the final calculated labels and data
                data_to_save = {
                    'chart_labels': chart_labels,
                    'chart_data': chart_data,
                    'timestamp': datetime.now().isoformat()
                }
                try:
                    os.makedirs(os.path.dirname(cache_file_path), exist_ok=True)
                    with open(cache_file_path, 'w', encoding='utf-8') as file:
                        json.dump(data_to_save, file, indent=2)
                    self.logger.info(f"Saved calculated MDL summary to cache: {cache_file_path}")
                except Exception as e:
                    self.logger.error(f"Failed to save MDL summary cache to {cache_file_path}: {e}", exc_info=True)

            return chart_labels, chart_data

        except Exception as e:
            self.logger.error(f"Error calculating MDL summary: {e}", exc_info=True)
            return [], []

    def get_allegations_and_causes(self, title: str, mdl_num: Any, docket_df: pd.DataFrame) -> str:
        """Retrieves a summary/description for a litigation, prioritizing MDL lookup, then docket."""
        if not isinstance(title, str) or not title: return ""
        default_summary = "No specific allegation summary available."

        mdl_num_str = str(mdl_num).strip().removesuffix('.0')

        # 1. Try MDL Lookup File
        if mdl_num_str and mdl_num_str not in ['NA', 'None', 'nan',
                                               ''] and not self.mdl_lookup_df.empty and 'mdl_num' in self.mdl_lookup_df.columns:
            try:
                match = self.mdl_lookup_df[self.mdl_lookup_df['mdl_num'].str.fullmatch(mdl_num_str, case=False)]
                if not match.empty:
                    summary_col = 'short_summary' if 'short_summary' in match.columns else (
                        'description' if 'description' in match.columns else None)
                    if summary_col and pd.notna(match[summary_col].iloc[0]):
                        summary = str(match[summary_col].iloc[0]).strip()
                        if summary:
                            self.logger.debug(f"Found summary in MDL lookup for MDL {mdl_num_str}.")
                            return self._format_text(summary)
                    self.logger.debug(f"MDL {mdl_num_str} found in lookup but lacks summary.")
            except Exception as e:
                self.logger.error(f"Error searching MDL lookup data for {mdl_num_str}: {e}", exc_info=True)

        # 2. Fallback to provided Docket DataFrame
        self.logger.debug(
            f"MDL lookup failed/no summary for '{title}' (MDL: {mdl_num_str}). Falling back to docket data.")
        if docket_df is None or docket_df.empty:
            self.logger.warning(f"Cannot fallback to docket data for '{title}': docket_df is None or empty.")
            return default_summary
        try:
            # Ensure required columns exist in the passed df
            if 'title' not in docket_df.columns or 'allegations' not in docket_df.columns or 'claims' not in docket_df.columns:
                self.logger.warning(f"Fallback docket_df missing required columns for '{title}'.")
                return default_summary

            match_df = docket_df[docket_df['title'] == title]
            if not match_df.empty:
                # Find the first row with non-empty combined allegations/claims
                for _, row in match_df.iterrows():
                    allegation = str(row.get('allegations', '')).strip()
                    claim = str(row.get('claims', '')).strip()
                    combined = f"{allegation} {claim}".strip()
                    if combined:
                        self.logger.debug(f"Found summary in docket data for '{title}'.")
                        return self._format_text(combined)
                self.logger.debug(f"No non-empty allegations/claims found in docket data for '{title}'.")
                return default_summary  # Return default if loop finishes without finding anything
            else:
                self.logger.warning(f"Could not find title '{title}' in provided docket_df for fallback summary.")
                return default_summary
        except Exception as e:
            self.logger.error(f"Error extracting fallback summary from docket_df for title '{title}': {e}",
                              exc_info=True)
            return default_summary

    @staticmethod
    def _clean_firm_name_for_display(firm_name: str) -> str:
        """
        Cleans a single law firm name or a semicolon-separated list
        specifically for display purposes by removing commas and periods.
        Relies on prior capitalization logic having already been applied.
        """
        if not isinstance(firm_name, str):
            return ""

        cleaned_parts = []
        # Split by semicolon, handle potential extra whitespace around parts
        parts = [part.strip() for part in firm_name.split(';') if part.strip()]

        for part in parts:
            # Remove commas and periods using regex for simplicity
            # This happens *after* the main capitalization logic (from DataLoader)
            # so it won't interfere with lookup keys that might contain periods/commas.
            cleaned = re.sub(r'[.,]', '', part).strip()
            # We assume the capitalization applied earlier is correct (e.g., 'PC', 'LLC')
            # This step *only* removes the specified punctuation.
            cleaned_parts.append(cleaned)

        # Join back with semicolon and space
        return ' ; '.join(cleaned_parts)

# TODO: Move mdl_lookup.json from src/lib/data/mdls -> src/config/mdls