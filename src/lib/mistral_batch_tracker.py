"""
SQLite database for tracking Mistral batch OCR jobs and processed files
"""

import sqlite3
from pathlib import Path
from datetime import datetime
from typing import Optional, List, Dict, Tuple
import json


class MistralBatchTracker:
    """Track Mistral batch jobs and processed files to prevent reprocessing."""
    
    def __init__(self, db_path: str = "mistral_ocr_tracker.db"):
        """Initialize the tracker with SQLite database."""
        self.db_path = db_path
        self.conn = sqlite3.connect(db_path)
        self.conn.row_factory = sqlite3.Row
        self._create_tables()
    
    def _create_tables(self):
        """Create tables if they don't exist."""
        cursor = self.conn.cursor()
        
        # Table for batch jobs
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS batch_jobs (
                job_id TEXT PRIMARY KEY,
                status TEXT NOT NULL,
                created_at TIMESTAMP NOT NULL,
                completed_at TIMESTAMP,
                total_files INTEGER,
                processed_files INTEGER DEFAULT 0,
                failed_files INTEGER DEFAULT 0,
                batch_file_path TEXT,
                metadata TEXT
            )
        """)
        
        # Table for processed files
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS processed_files (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                job_id TEXT NOT NULL,
                pdf_path TEXT NOT NULL,
                md_path TEXT NOT NULL,
                custom_id TEXT,
                processed_at TIMESTAMP NOT NULL,
                status TEXT NOT NULL,
                error_message TEXT,
                FOREIGN KEY (job_id) REFERENCES batch_jobs (job_id),
                UNIQUE(pdf_path)
            )
        """)
        
        # Table for batch result files
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS batch_results (
                result_file TEXT PRIMARY KEY,
                job_id TEXT NOT NULL,
                processed_at TIMESTAMP,
                total_results INTEGER,
                successfully_processed INTEGER DEFAULT 0,
                skipped INTEGER DEFAULT 0,
                failed INTEGER DEFAULT 0,
                FOREIGN KEY (job_id) REFERENCES batch_jobs (job_id)
            )
        """)
        
        # Create indexes
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_pdf_path ON processed_files(pdf_path)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_job_status ON batch_jobs(status)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_processed_at ON processed_files(processed_at)")
        
        self.conn.commit()
    
    def add_batch_job(self, job_id: str, total_files: int, batch_file_path: str = None, metadata: dict = None) -> None:
        """Add a new batch job to tracking."""
        cursor = self.conn.cursor()
        cursor.execute("""
            INSERT OR REPLACE INTO batch_jobs 
            (job_id, status, created_at, total_files, batch_file_path, metadata)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (
            job_id, 
            "SUBMITTED", 
            datetime.now().isoformat(),
            total_files,
            batch_file_path,
            json.dumps(metadata) if metadata else None
        ))
        self.conn.commit()
    
    def update_job_status(self, job_id: str, status: str, processed: int = None, failed: int = None) -> None:
        """Update job status and counts."""
        cursor = self.conn.cursor()
        
        # Update status
        cursor.execute("UPDATE batch_jobs SET status = ? WHERE job_id = ?", (status, job_id))
        
        # Update counts if provided
        if processed is not None:
            cursor.execute("UPDATE batch_jobs SET processed_files = ? WHERE job_id = ?", (processed, job_id))
        
        if failed is not None:
            cursor.execute("UPDATE batch_jobs SET failed_files = ? WHERE job_id = ?", (failed, job_id))
        
        # Set completion time if status is terminal
        if status in ["SUCCESS", "FAILED", "CANCELLED"]:
            cursor.execute("UPDATE batch_jobs SET completed_at = ? WHERE job_id = ?", 
                         (datetime.now().isoformat(), job_id))
        
        self.conn.commit()
    
    def is_pdf_processed(self, pdf_path: str) -> bool:
        """Check if a PDF has already been processed."""
        cursor = self.conn.cursor()
        result = cursor.execute("""
            SELECT 1 FROM processed_files 
            WHERE pdf_path = ? AND status = 'SUCCESS'
            LIMIT 1
        """, (str(pdf_path),)).fetchone()
        return result is not None
    
    def add_processed_file(self, job_id: str, pdf_path: str, md_path: str, 
                          custom_id: str = None, status: str = "SUCCESS", 
                          error_message: str = None) -> None:
        """Add a processed file record."""
        cursor = self.conn.cursor()
        cursor.execute("""
            INSERT OR REPLACE INTO processed_files 
            (job_id, pdf_path, md_path, custom_id, processed_at, status, error_message)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (
            job_id,
            str(pdf_path),
            str(md_path),
            custom_id,
            datetime.now().isoformat(),
            status,
            error_message
        ))
        self.conn.commit()
    
    def is_batch_result_processed(self, result_file: str) -> bool:
        """Check if a batch result file has been processed."""
        cursor = self.conn.cursor()
        result = cursor.execute("""
            SELECT processed_at FROM batch_results 
            WHERE result_file = ?
        """, (result_file,)).fetchone()
        return result is not None and result['processed_at'] is not None
    
    def add_batch_result(self, result_file: str, job_id: str, total_results: int) -> None:
        """Add a batch result file to tracking."""
        cursor = self.conn.cursor()
        cursor.execute("""
            INSERT OR REPLACE INTO batch_results 
            (result_file, job_id, total_results)
            VALUES (?, ?, ?)
        """, (result_file, job_id, total_results))
        self.conn.commit()
    
    def update_batch_result_stats(self, result_file: str, processed: int, skipped: int, failed: int) -> None:
        """Update batch result processing statistics."""
        cursor = self.conn.cursor()
        cursor.execute("""
            UPDATE batch_results 
            SET processed_at = ?, successfully_processed = ?, skipped = ?, failed = ?
            WHERE result_file = ?
        """, (datetime.now().isoformat(), processed, skipped, failed, result_file))
        self.conn.commit()
    
    def get_unprocessed_pdfs(self, pdf_paths: List[Tuple[Path, Path]]) -> List[Tuple[Path, Path]]:
        """Filter out already processed PDFs from a list."""
        unprocessed = []
        for pdf_path, md_path in pdf_paths:
            if not self.is_pdf_processed(str(pdf_path)):
                unprocessed.append((pdf_path, md_path))
        return unprocessed
    
    def get_job_summary(self, job_id: str) -> Optional[Dict]:
        """Get summary information for a job."""
        cursor = self.conn.cursor()
        result = cursor.execute("""
            SELECT * FROM batch_jobs WHERE job_id = ?
        """, (job_id,)).fetchone()
        
        if result:
            return dict(result)
        return None
    
    def get_pending_jobs(self) -> List[Dict]:
        """Get all jobs that are not in a terminal state."""
        cursor = self.conn.cursor()
        results = cursor.execute("""
            SELECT * FROM batch_jobs 
            WHERE status NOT IN ('SUCCESS', 'FAILED', 'CANCELLED')
            ORDER BY created_at DESC
        """).fetchall()
        
        return [dict(row) for row in results]
    
    def get_stats(self) -> Dict:
        """Get overall statistics."""
        cursor = self.conn.cursor()
        
        stats = {
            'total_jobs': cursor.execute("SELECT COUNT(*) FROM batch_jobs").fetchone()[0],
            'pending_jobs': cursor.execute(
                "SELECT COUNT(*) FROM batch_jobs WHERE status IN ('SUBMITTED', 'RUNNING', 'QUEUED')"
            ).fetchone()[0],
            'completed_jobs': cursor.execute(
                "SELECT COUNT(*) FROM batch_jobs WHERE status = 'SUCCESS'"
            ).fetchone()[0],
            'total_files_processed': cursor.execute(
                "SELECT COUNT(*) FROM processed_files WHERE status = 'SUCCESS'"
            ).fetchone()[0],
            'failed_files': cursor.execute(
                "SELECT COUNT(*) FROM processed_files WHERE status = 'FAILED'"
            ).fetchone()[0],
        }
        
        return stats
    
    def cleanup_old_records(self, days: int = 30) -> int:
        """Remove old records older than specified days."""
        cursor = self.conn.cursor()
        cutoff_date = datetime.now().timestamp() - (days * 24 * 60 * 60)
        cutoff_iso = datetime.fromtimestamp(cutoff_date).isoformat()
        
        # Delete old completed jobs and their files
        cursor.execute("""
            DELETE FROM processed_files 
            WHERE job_id IN (
                SELECT job_id FROM batch_jobs 
                WHERE completed_at < ? AND status IN ('SUCCESS', 'FAILED', 'CANCELLED')
            )
        """, (cutoff_iso,))
        
        deleted = cursor.rowcount
        
        cursor.execute("""
            DELETE FROM batch_jobs 
            WHERE completed_at < ? AND status IN ('SUCCESS', 'FAILED', 'CANCELLED')
        """, (cutoff_iso,))
        
        self.conn.commit()
        return deleted
    
    def close(self):
        """Close database connection."""
        self.conn.close()