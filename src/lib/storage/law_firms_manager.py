from decimal import Decimal
from typing import Optional, Dict, Any, List
import asyncio  # Added for asyncio operations

from boto3.dynamodb.conditions import Key  # Compatible
from botocore.exceptions import ClientError

# Assuming async base class and its helpers
from src.lib.dynamodb_base_manager import AsyncDynamoDbBaseManager, convert_decimals


class AsyncLawFirmsManager(AsyncDynamoDbBaseManager):
    def __init__(self, config: Dict[str, Any]):
        # Initialize with the key for the table config or direct name.
        # 'LawFirms' is the table name key.
        # use_local=False is hardcoded, meaning this manager always targets AWS unless config overrides.
        super().__init__(config, "LawFirms", use_local=False)
        # Async initialization is handled by the base class's _ensure_initialized pattern.
        self.logger.info(
            f"{self.__class__.__name__} synchronous part of init complete. Async init will occur on first I/O.")

    async def add_or_update_record(self, record_data: dict[str, any]) -> bool:  # Renamed record
        """Adds or updates a law firm record (async)."""
        await self._ensure_initialized()  # Ensure async resources are ready

        # Handle 'page_id' mapping (synchronous part)
        if 'page_id' in record_data and 'id' not in record_data:
            record_data['id'] = record_data.pop('page_id')  # Use pop to avoid duplicate data under different keys
            self.logger.debug(f"Mapped 'page_id' to 'id': {record_data['id']}")

        record_pascal = self.snake_or_camel_to_pascal_case(record_data)
        record_sanitized = self.sanitize_record(record_pascal)
        record_final = record_sanitized.copy()  # Use a copy for final modifications

        # Map 'PageId' to 'Id' if 'Id' is missing (PascalCase from input)
        if 'PageId' in record_final and 'Id' not in record_final:
            record_final['Id'] = record_final.pop('PageId')  # Use pop
            self.logger.debug(f"Mapped 'PageId' to 'Id': {record_final['Id']}")

        # Validate essential keys ('Id', 'Name' for the PK of LawFirms table)
        # These are PascalCase after transformations.
        id_val = record_final.get('Id')
        name_val = record_final.get('Name')

        if not id_val or not name_val:
            self.logger.error(
                f"Record missing 'Id' or 'Name' after transformations. Found Keys: {list(record_final.keys())}. "
                f"ID='{id_val}', Name='{name_val}'"
            )
            self.logger.debug(f"Problematic record content (post-transform): {record_final}")
            return False

        try:
            # DynamoDB key attributes are 'ID' and 'Name' (uppercase as per schema in base manager)
            # The values come from the processed record's 'Id' and 'Name' (PascalCase)
            key_for_dynamo = {'ID': str(id_val), 'Name': str(name_val)}  # Renamed key

            get_response = await self.table.get_item(Key=key_for_dynamo)  # await
            existing_item_raw = get_response.get('Item')

            if existing_item_raw:
                existing_item_processed = self.process_record(existing_item_raw)  # process_record is sync
                update_payload = {}  # Renamed update_data

                # Compare record_final (new data) with existing_item_processed
                for field_key, new_value in record_final.items():  # Renamed k, v_new
                    # Skip attributes that form the primary key for the update payload
                    # The comparison should be against PascalCase field_key from record_final
                    if field_key == 'Id' or field_key == 'Name':  # Using PascalCase 'Id', 'Name' as in record_final
                        continue

                    old_value = existing_item_processed.get(field_key)  # Renamed v_old

                    # Comparison logic (remains synchronous)
                    # Ensure types are compatible for comparison, especially Decimals from DB vs Python numbers
                    if isinstance(new_value, Decimal) and isinstance(old_value, (int, float)):
                        try:
                            old_value_decimal = Decimal(str(old_value))  # Renamed
                        except:
                            old_value_decimal = None  # Handle conversion error
                        if new_value != old_value_decimal: update_payload[field_key] = new_value
                    elif new_value != old_value:  # General comparison
                        update_payload[field_key] = new_value

                if update_payload:
                    self.logger.debug(
                        f"Async updating existing record {key_for_dynamo} with data: {list(update_payload.keys())}")
                    # self.update_item from base class is async
                    success_flag = await self.update_item(key_for_dynamo, update_payload,
                                                          consistent_read_verify=False)  # await, Renamed success
                    if not success_flag:
                        self.logger.warning(f"Async update failed for {key_for_dynamo} (update_item returned False).")
                        return False
                    self.logger.info(f"Record updated successfully (async) for {key_for_dynamo}")
                else:
                    self.logger.info(f"No update needed for existing record (async): {key_for_dynamo}")
            else:  # New item, insert it
                # Prepare item for DynamoDB: ensure primary key attributes match DB schema ('ID', 'Name')
                item_to_insert = record_final.copy()  # Renamed item_to_put
                # If 'Id' (PascalCase) is the key in item_to_insert, but DB expects 'ID' (UPPERCASE) for the HASH key.
                # The key_config for LawFirms specifies 'ID' and 'Name' as key attributes.
                if 'Id' in item_to_insert and 'ID' not in item_to_insert:  # Check if 'ID' already exists (e.g. from earlier step)
                    item_to_insert['ID'] = item_to_insert.pop('Id')
                    self.logger.debug(f"Ensured 'ID' key for DynamoDB put: {item_to_insert['ID']}")
                # 'Name' is already PascalCase, matching key_config.

                self.logger.debug(f"Async adding new record {key_for_dynamo} with data: {list(item_to_insert.keys())}")
                await self.table.put_item(Item=item_to_insert)  # await
                self.logger.info(f"New record added (async) for {key_for_dynamo}")
            return True

        except ClientError as e:
            log_key_val = key_for_dynamo if 'key_for_dynamo' in locals() else 'N/A'  # Renamed
            self.logger.error(f"ClientError for record {log_key_val}: {e.response['Error']['Message']}", exc_info=True)
            return False
        except KeyError as e_key:  # Renamed e
            record_keys_debug = list(record_final.keys()) if 'record_final' in locals() else 'N/A'  # Renamed
            self.logger.error(f"KeyError '{e_key}' during DB op. Record keys: {record_keys_debug}", exc_info=True)
            return False
        except ValueError as e_val:  # Renamed e
            self.logger.error(f"ValueError during DB op: {e_val}", exc_info=True)
            return False
        except Exception as e_exc:  # Renamed e
            self.logger.error(f"Unexpected error in async add_or_update_record: {e_exc}", exc_info=True)
            return False

    async def search_law_firms(self, search_term_str: str) -> list:  # Renamed search_term
        """Searches law firms by name or alias (async)."""
        await self._ensure_initialized()
        try:
            # Attribute names in DynamoDB: 'Name', 'PageAlias' (PascalCase)
            expr_attr_names_search = {"#nm": "Name", "#pa": "PageAlias"}  # Renamed
            filter_expr_search = "contains(#nm, :st) OR contains(#pa, :st)"  # Renamed
            expr_attr_vals_search = {":st": search_term_str}  # Renamed

            # self.table.scan is now async
            # For a full table scan, pagination needs to be handled if the table is large.
            # The base class's self.scan_table() handles pagination and is async.
            all_matching_items = []
            async for item_data in self.scan_table(  # Use async generator from base
                    FilterExpression=filter_expr_search,
                    ExpressionAttributeNames=expr_attr_names_search,
                    ExpressionAttributeValues=expr_attr_vals_search
            ):
                all_matching_items.append(item_data)  # item_data is already processed (decimals converted)

            self.logger.info(f"Found {len(all_matching_items)} law firms matching '{search_term_str}'.")
            return all_matching_items

        except ClientError as e:
            self.logger.error(
                f"ClientError searching law firms for '{search_term_str}': {e.response['Error']['Message']}",
                exc_info=True)
            return []
        except Exception as e:  # Catch other errors
            self.logger.error(f"Unexpected error searching law firms for '{search_term_str}': {e}", exc_info=True)
            return []

    async def update_all_law_firms_last_updated(self, new_last_updated_iso_date: str):  # Renamed
        """Updates AdArchiveLastUpdated for all law firm records (async)."""
        await self._ensure_initialized()
        self.logger.info(
            f"Starting async update of AdArchiveLastUpdated to '{new_last_updated_iso_date}' for all law firms.")

        updated_count = 0
        failed_count = 0

        # Use the async scan_table from the base class to get all items
        # It's an async generator, so iterate with `async for`
        items_processed_count = 0
        async for item_data in self.scan_table(ProjectionExpression="ID, Name"):  # Only need PKs for update
            items_processed_count += 1
            if items_processed_count % 100 == 0:  # Log progress
                self.logger.debug(f"Processed {items_processed_count} items for LastUpdated update...")

            # Ensure 'ID' and 'Name' are present (they should be if ProjectionExpression worked)
            if 'ID' not in item_data or 'Name' not in item_data:
                self.logger.warning(f"Skipping item due to missing ID/Name in scan result: {item_data}")
                failed_count += 1
                continue

            key_for_update = {'ID': item_data['ID'], 'Name': item_data['Name']}  # Renamed

            # Use the base class's async update_item method
            # The update_data for base update_item should be attribute names as they are in the record (PascalCase)
            update_payload_lu = {'AdArchiveLastUpdated': new_last_updated_iso_date}  # Renamed

            try:
                success_flag_lu = await self.update_item(key_for_update, update_payload_lu,
                                                         consistent_read_verify=False)  # await, Renamed
                if success_flag_lu:
                    updated_count += 1
                else:
                    failed_count += 1
                    self.logger.warning(
                        f"Failed to update AdArchiveLastUpdated for Law Firm with key {key_for_update} (update_item returned False).")
            except Exception as e_update_lu:  # Renamed
                failed_count += 1
                self.logger.error(f"Exception updating AdArchiveLastUpdated for {key_for_update}: {e_update_lu}",
                                  exc_info=False)  # Less verbose for many errors

        self.logger.info(
            f"Async AdArchiveLastUpdated update complete. Successfully updated: {updated_count}. Failed: {failed_count}. Total scanned: {items_processed_count}.")

    async def get_record(self, company_id_str: str) -> Optional[Dict[str, Any]]:  # Renamed company_id
        """Retrieves a law firm record by company ID (async). Scans for ID."""
        await self._ensure_initialized()
        try:
            # Scanning for a single ID is inefficient. If this is common, a GSI on 'ID' would be better.
            # Assuming 'ID' is the HASH key of the table (LawFirms: ['ID', 'Name']).
            # If 'ID' is the HASH key, and 'Name' is the RANGE key, you cannot query/get only by 'ID'.
            # You would need to SCAN with a FilterExpression.
            self.logger.info(f"Async scanning for law firm record with ID: {company_id_str}")

            # Use the async scan_table from base class
            matching_items_list = []  # Renamed items
            async for item_data in self.scan_table(  # await for
                    FilterExpression=Key('ID').eq(str(company_id_str))  # Key needs to be imported if not already
            ):
                matching_items_list.append(item_data)  # item_data is already processed

            if matching_items_list:
                if len(matching_items_list) > 1:
                    self.logger.warning(f"Multiple records found for company ID {company_id_str}. Returning first one.")
                # process_record is sync, and item_data from scan_table is already processed (decimals)
                # If further processing specific to LawFirmsManager was needed, it would happen here.
                return matching_items_list[0]
            else:
                self.logger.warning(f"No law firm record found for company ID {company_id_str} via scan.")
                return None

        except ClientError as e:
            self.logger.error(f"ClientError retrieving law firm ID {company_id_str}: {e.response['Error']['Message']}",
                              exc_info=True)
            return None
        except Exception as e:  # Catch other errors
            self.logger.error(f"Unexpected error retrieving law firm ID {company_id_str}: {e}", exc_info=True)
            return None

    async def get_all_records(self, projection_expression: Optional[str] = None,
                              expression_attribute_names: Optional[Dict[str, str]] = None) -> List[Dict[str, Any]]:
        """Scans and retrieves all records from the table (async), handling pagination."""
        await self._ensure_initialized()

        scan_params = {}  # Renamed scan_kwargs
        if projection_expression: scan_params['ProjectionExpression'] = projection_expression
        if expression_attribute_names: scan_params['ExpressionAttributeNames'] = expression_attribute_names

        self.logger.info(f"Async scanning all records from LawFirms table with params: {scan_params}")

        all_retrieved_items = []  # Renamed all_items
        try:
            # Use the async scan_table from base class, which handles pagination and decimal conversion.
            async for item_data in self.scan_table(**scan_params):  # await for
                all_retrieved_items.append(item_data)

            self.logger.info(f"Async scan complete. Retrieved {len(all_retrieved_items)} records.")
            return all_retrieved_items
        except ClientError as e:
            self.logger.error(f"ClientError scanning all LawFirms records: {e.response['Error']['Message']}",
                              exc_info=True)
            return []
        except Exception as e:  # Catch other errors
            self.logger.error(f"Unexpected error scanning all LawFirms records: {e}", exc_info=True)
            return []


async def main_async_law_firms():  # Main test function for async
    from src.lib.config import load_config  # Assuming this path is correct
    import logging

    # Setup basic logging for the test run
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    logger_main = logging.getLogger("AsyncLawFirmsTest")  # Renamed logger

    try:
        test_config_lf = load_config('01/01/70')  # Renamed config
        async_lf_manager = AsyncLawFirmsManager(test_config_lf)  # Renamed db
        await async_lf_manager._ensure_initialized()  # Explicitly initialize for test

        logger_main.info("AsyncLawFirmsManager initialized for testing.")

        # Example: Test add_or_update_record
        test_firm_record = {
            'id': 'async_firm_123',  # Will be mapped to 'Id', then 'ID' for DB key
            'name': 'Async Test Firm & Co.',  # Will be mapped to 'Name'
            'page_alias': 'asynctestfirm',
            'AdArchiveLastUpdated': '20240101'
        }
        logger_main.info(f"Attempting to add/update test firm: {test_firm_record['id']}")
        add_success = await async_lf_manager.add_or_update_record(test_firm_record.copy())  # Pass a copy
        logger_main.info(f"Add/update test firm result: {add_success}")

        if add_success:
            # Example: Test get_record
            logger_main.info(f"Attempting to get record for ID: {test_firm_record['id']}")
            fetched_record = await async_lf_manager.get_record(test_firm_record['id'])
            if fetched_record:
                logger_main.info(
                    f"Fetched record: {fetched_record.get('ID')}, Name: {fetched_record.get('Name')}, PageAlias: {fetched_record.get('PageAlias')}")
            else:
                logger_main.warning("Test record not found after add/update.")

        # Example: Test search_law_firms
        search_query = "Async Test"
        logger_main.info(f"Attempting to search for firms with term: '{search_query}'")
        search_results = await async_lf_manager.search_law_firms(search_query)
        logger_main.info(
            f"Search found {len(search_results)} firms. First result (if any): {search_results[0] if search_results else 'None'}")

        # Example: Test update_all_law_firms_last_updated
        # This can be a long operation on a large table.
        # logger_main.info("Attempting to update AdArchiveLastUpdated for all firms to 20250511...")
        # await async_lf_manager.update_all_law_firms_last_updated('20250511')
        # logger_main.info("Finished update_all_law_firms_last_updated call.")

        # Example: Test get_all_records
        # logger_main.info("Attempting to get all law firm records (projecting ID, Name, PageAlias)...")
        # all_firms = await async_lf_manager.get_all_records(projection_expression="ID, #nm, PageAlias", expression_attribute_names={"#nm": "Name"})
        # logger_main.info(f"Retrieved {len(all_firms)} firms. First firm (if any): {all_firms[0] if all_firms else 'None'}")


    except Exception as e_main_lf:  # Renamed e
        logger_main.error(f"Error during async LawFirmsManager main execution: {e_main_lf}", exc_info=True)
    finally:
        # If using local Docker-managed DynamoDB, and if the manager instance started it.
        if 'async_lf_manager' in locals() and async_lf_manager.is_local and async_lf_manager._local_dynamodb_manager:
            logger_main.info("Stopping local DynamoDB container from async LawFirmsManager test...")
            await asyncio.to_thread(async_lf_manager._local_dynamodb_manager.stop_dynamodb)
            logger_main.info("Local DynamoDB container stop command issued.")


if __name__ == '__main__':
    asyncio.run(main_async_law_firms())