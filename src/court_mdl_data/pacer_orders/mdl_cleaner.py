import os
import re
import shutil


def organize_files_by_prefix():
    # Initialize a set to store unique prefixes
    unique_prefixes = set()

    # Get the current working directory
    cwd = os.getcwd()

    # Define the regular expression pattern to match the 4-digit prefix
    pattern = re.compile(r'^(\d{4})-(.+)\.(json|pdf)$')

    # Loop through all files in the current working directory
    for filename in os.listdir(cwd):
        match = pattern.match(filename)
        if match:
            # Extract the 4-digit prefix and the rest of the filename
            prefix = match.group(1)
            new_filename = match.group(2) + '.' + match.group(3)
            unique_prefixes.add(prefix)

            # Create the subdirectory for the prefix if it doesn't exist
            directory_path = os.path.join(cwd, prefix)
            if not os.path.exists(directory_path):
                os.makedirs(directory_path)
                print(f"Created directory: {directory_path}")

            # Move and rename the file to the appropriate directory
            src_path = os.path.join(cwd, filename)
            dest_path = os.path.join(directory_path, new_filename)
            shutil.move(src_path, dest_path)
            print(f"Moved and renamed file: {filename} -> {dest_path}")

    # Log the number of unique prefixes found
    print(f"Number of unique 4-digit prefixes: {len(unique_prefixes)}")


# Run the function
organize_files_by_prefix()
