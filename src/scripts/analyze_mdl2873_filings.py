#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to analyze MDL 2873 filings, categorizing them by filing type and counting plaintiffs.
"""

import os
import json
import argparse
from datetime import datetime
from rich.console import Console
from rich.table import Table
from rich.panel import Panel

# Import the project root handling function
try:
    from utils import get_project_root
except ImportError:
    # Fall back to direct import if utils.py is not available
    try:
        from src.lib.config import PROJECT_ROOT
    except ImportError:
        # If can't import, try to load from environment or use a fallback
        try:
            from dotenv import load_dotenv
            load_dotenv()
            PROJECT_ROOT = os.getenv('PROJECT_ROOT', os.path.expanduser('~/PycharmProjects/lexgenius'))
        except ImportError:
            PROJECT_ROOT = os.path.expanduser('~/PycharmProjects/lexgenius')
else:
    PROJECT_ROOT = get_project_root()

console = Console()

def validate_date(date_str: str) -> bool:
    """Validate date string format."""
    try:
        datetime.strptime(date_str, '%Y%m%d')
        return True
    except ValueError:
        return False

def analyze_mdl_filings(date_str: str) -> None:
    """Analyze MDL 2873 filings and display results."""
    base_path = os.path.join(PROJECT_ROOT, "data", date_str, "dockets")
    
    if not os.path.exists(base_path):
        console.print(f"[red]Error: Directory not found: {base_path}[/red]")
        return

    # Initialize counters
    transferred = {"cases": 0, "plaintiffs": 0}
    pending_cto = {"cases": 0, "plaintiffs": 0}
    direct = {"cases": 0, "plaintiffs": 0}
    
    # Track filenames for each category
    transferred_files = []
    pending_files = []
    direct_files = []

    # Process JSON files
    for filename in os.listdir(base_path):
        if not filename.endswith('.json'):
            continue

        file_path = os.path.join(base_path, filename)
        
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
            
            if data.get('mdl_num') != '2873':
                continue

            num_plaintiffs = int(data.get('num_plaintiffs', 1))
            base_name = filename.replace('.json', '')

            if data.get('transferred_in'):
                transferred["cases"] += 1
                transferred["plaintiffs"] += num_plaintiffs
                transferred_files.append(base_name)
            elif data.get('pending_cto'):
                pending_cto["cases"] += 1
                pending_cto["plaintiffs"] += num_plaintiffs
                pending_files.append(base_name)
            else:
                direct["cases"] += 1
                direct["plaintiffs"] += num_plaintiffs
                direct_files.append(base_name)

        except Exception as e:
            console.print(f"[red]Error processing {filename}: {str(e)}[/red]")

    # Create results table
    table = Table(
        title=f"MDL 2873 Filing Analysis for {date_str}",
        show_header=True,
        header_style="bold magenta"
    )
    
    table.add_column("Category", style="cyan")
    table.add_column("Cases", justify="right", style="green")
    table.add_column("Total Plaintiffs", justify="right", style="yellow")
    
    table.add_row(
        "Transferred In",
        str(transferred["cases"]),
        str(transferred["plaintiffs"])
    )
    table.add_row(
        "Pending CTO",
        str(pending_cto["cases"]),
        str(pending_cto["plaintiffs"])
    )
    table.add_row(
        "Direct Filings",
        str(direct["cases"]),
        str(direct["plaintiffs"])
    )
    table.add_row(
        "Total",
        str(transferred["cases"] + pending_cto["cases"] + direct["cases"]),
        str(transferred["plaintiffs"] + pending_cto["plaintiffs"] + direct["plaintiffs"])
    )

    # Display results
    console.print("\n")
    console.print(Panel.fit("MDL 2873 Analysis Results", style="bold blue"))
    console.print(table)

    # Display file lists if any exist
    if transferred_files:
        console.print("\n[bold cyan]Transferred In Files:[/bold cyan]")
        console.print(", ".join(transferred_files))
    
    if pending_files:
        console.print("\n[bold cyan]Pending CTO Files:[/bold cyan]")
        console.print(", ".join(pending_files))
    
    if direct_files:
        console.print("\n[bold cyan]Direct Filing Files:[/bold cyan]")
        console.print(", ".join(direct_files))

def main():
    parser = argparse.ArgumentParser(description="Analyze MDL 2873 filings")
    parser.add_argument('--date', required=True, help='Date in YYYYMMDD format')
    args = parser.parse_args()

    if not validate_date(args.date):
        console.print("[red]Error: Invalid date format. Please use YYYYMMDD format.[/red]")
        return

    analyze_mdl_filings(args.date)

if __name__ == "__main__":
    main()