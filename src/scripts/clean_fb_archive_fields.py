#!/usr/bin/env python3
"""
Script to clean up specific fields from FBAdArchive table in both local and AWS DynamoDB.
"""
import logging
import pandas as pd
import sys
import os
from typing import List, Dict, Any
from rich.console import Console
from rich.progress import Progress, TextColumn, BarColumn, SpinnerColumn, TimeElapsedColumn, TimeRemainingColumn
from rich.panel import Panel
from rich.text import Text
from rich.logging import RichHandler
from rich.prompt import Confirm

# Add the project root to the Python path to fix imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))
from src.lib.config import load_config
from src.lib.fb_archive_manager import FBAdArchiveManager

# Configure logging with rich handler
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
    datefmt="[%X]",
    handlers=[RichHandler(rich_tracebacks=True)]
)
logger = logging.getLogger(__name__)

# Fields to remove
FIELDS_TO_REMOVE = [
    'VideoHdUrl',
    'VideoSdUrl',
    'DbRecordFound',
    'LlavaAttempted',
    'ResizedImageUrl',
    'OriginalImageUrl',
    'Spend',
    'PotentialReach',
    'AdCreationTime'
]

# Initialize rich console
console = Console()

def get_records_to_clean(fb_manager: FBAdArchiveManager, existing_fields: List[str], max_records: int = 500) -> pd.DataFrame:
    """
    Scan local DynamoDB and filter records containing specified fields.

    Args:
        fb_manager: The FBAdArchiveManager instance
        existing_fields: List of fields that exist in the database and should be removed
        max_records: Maximum number of records to process (for efficiency)

    Returns:
        DataFrame containing records that have any of the specified fields
    """
    if not existing_fields:
        console.print("[yellow]No fields to clean in the database.[/yellow]")
        return pd.DataFrame()

    console.print(Panel.fit(
        f"[bold yellow]Scanning local DynamoDB for up to {max_records} records with fields to remove...[/bold yellow]",
        border_style="yellow"
    ))

    # Create a filter expression to only fetch records that have the fields we want to remove
    # This is much more efficient than fetching all records and filtering afterward
    filter_expressions = []
    expression_attr_names = {}

    for i, field in enumerate(existing_fields):
        filter_expressions.append(f"attribute_exists(#field{i})")
        expression_attr_names[f"#field{i}"] = field

    filter_expression = " OR ".join(filter_expressions)

    # Only fetch the fields we need (primary keys + fields to remove)
    projection_fields = ', '.join(['AdArchiveID', 'StartDate'] + existing_fields)

    with Progress(
        SpinnerColumn(),
        TextColumn("[bold blue]{task.description}[/bold blue]"),
        BarColumn(),
        TextColumn("[bold]{task.completed}/{task.total} items[/bold]"),
        TimeElapsedColumn(),
        expand=True
    ) as progress:
        scan_task = progress.add_task("Scanning for records to clean", total=max_records)

        # Get records that have any of the fields to remove
        items = []
        record_count = 0
        batch_size = 50
        current_batch = []

        try:
            # Use the scan_table generator with our filter expression and a limit
            for item in fb_manager.scan_table(
                FilterExpression=filter_expression,
                ExpressionAttributeNames=expression_attr_names,
                ProjectionExpression=projection_fields,
                Limit=1000  # Limit each scan operation to 1000 items for faster response
            ):
                current_batch.append(item)
                record_count += 1

                # Process in batches to show progress
                if len(current_batch) >= batch_size:
                    items.extend(current_batch)
                    progress.update(scan_task, completed=min(record_count, max_records))
                    current_batch = []

                # Stop if we've reached the maximum number of records
                if record_count >= max_records:
                    console.print(f"[yellow]Reached maximum record limit ({max_records}). Stopping scan.[/yellow]")
                    break

            # Add any remaining items
            if current_batch:
                items.extend(current_batch)

            # Update progress with final count
            progress.update(scan_task, completed=min(record_count, max_records))

            # Convert to DataFrame
            df = pd.DataFrame(items) if items else pd.DataFrame()

            console.print(f"[green]Found {len(df)} records containing fields to remove.[/green]")

        except KeyboardInterrupt:
            console.print("\n[yellow]Scan interrupted by user. Processing collected records...[/yellow]")
            # Add any records we've collected so far
            if current_batch:
                items.extend(current_batch)
            df = pd.DataFrame(items) if items else pd.DataFrame()
            return df
        except Exception as e:
            error_msg = f"Error scanning for records: {str(e)}"
            console.print(f"[bold red]{error_msg}[/bold red]")
            logger.error(error_msg)
            return pd.DataFrame()

    return df

def update_dynamodb_batch(fb_manager: FBAdArchiveManager, records: pd.DataFrame, is_aws: bool = False) -> int:
    """
    Update DynamoDB records in batches to remove specified fields.
    """
    updated_count = 0
    total_records = len(records)
    db_type = 'AWS' if is_aws else 'local'

    if total_records == 0:
        logger.info(f"No records to update in {db_type} DynamoDB.")
        return 0

    # Create a new manager with the appropriate use_local setting
    if is_aws:
        # Get the config from the existing manager
        config = fb_manager.config
        logger.info(f"Creating new FBAdArchiveManager for AWS DynamoDB")
        # Create a new manager with use_local=False for AWS
        fb_manager = FBAdArchiveManager(config, use_local=False, no_filter=True)

    # Log what we're about to do
    logger.info(f"Preparing to update {total_records} records in {db_type} DynamoDB")
    logger.info(f"Fields to remove: {', '.join(FIELDS_TO_REMOVE)}")

    with Progress(
        SpinnerColumn(),
        TextColumn(f"[bold cyan]Updating {db_type} DynamoDB:[/bold cyan] "),
        BarColumn(bar_width=40),
        TextColumn("[progress.percentage]{task.percentage:>3.1f}%"),
        TextColumn("•"),
        TextColumn("[bold blue]{task.completed}/{task.total}[/bold blue]"),
        TextColumn("•"),
        TimeElapsedColumn(),
        TextColumn("•"),
        TimeRemainingColumn(),
        expand=True
    ) as progress:
        task = progress.add_task(f"Updating {db_type} records", total=total_records)

        for _, record in records.iterrows():
            try:
                # Construct the key for the record
                key = {
                    'AdArchiveID': record['AdArchiveID'],
                    'StartDate': record['StartDate']
                }

                # Construct the update expression for all fields we want to remove
                update_expr = "REMOVE " + ", ".join(f"#{i}" for i in range(len(FIELDS_TO_REMOVE)))
                expr_attr_names = {f"#{i}": field for i, field in enumerate(FIELDS_TO_REMOVE)}

                # Log the update operation
                logger.info(f"Updating record {key} in {db_type} DynamoDB")
                logger.info(f"Update expression: {update_expr}")
                logger.info(f"Expression attribute names: {expr_attr_names}")

                # Update the record
                fb_manager.table.update_item(
                    Key=key,
                    UpdateExpression=update_expr,
                    ExpressionAttributeNames=expr_attr_names
                )
                updated_count += 1
                logger.info(f"Successfully updated record {key} in {db_type} DynamoDB")

            except Exception as e:
                error_msg = f"Error updating record {key} in {db_type} DynamoDB: {str(e)}"
                logger.error(error_msg)
                progress.console.print(f"[bold red]{error_msg}[/bold red]")

            progress.update(task, advance=1)

    return updated_count

def load_all_records_and_check_schema(fb_manager: FBAdArchiveManager):
    """Load all records from the database and check the schema."""
    console.print("[bold yellow]Loading ALL records from the database to check schema...[/bold yellow]")

    try:
        # Load all records without any filtering or limits
        all_items = []
        record_count = 0
        batch_size = 100

        with Progress(
            SpinnerColumn(),
            TextColumn("[bold blue]Loading all records[/bold blue]"),
            BarColumn(),
            TextColumn("[bold]{task.completed} records[/bold]"),
            expand=True
        ) as progress:
            task = progress.add_task("Loading", total=None)

            # Use the scan_table generator to get ALL records
            for item in fb_manager.scan_table():
                all_items.append(item)
                record_count += 1

                if record_count % batch_size == 0:
                    progress.update(task, description=f"Loaded {record_count} records")

            # Final update
            progress.update(task, description=f"Loaded {record_count} records", completed=record_count, total=record_count)

        if not all_items:
            console.print("[yellow]No records found in the database.[/yellow]")
            return None, []

        # Convert to DataFrame
        all_df = pd.DataFrame(all_items)

        # Check which fields exist
        all_columns = list(all_df.columns)
        all_columns.sort()  # Sort alphabetically for easier reading

        console.print(Panel(
            f"[cyan]ALL columns in database ({len(all_columns)} total):[/cyan]\n{', '.join(all_columns)}",
            title=f"[bold]Complete Database Schema ({len(all_items)} records)[/bold]",
            border_style="cyan"
        ))

        # Check for target fields
        existing_fields = [field for field in FIELDS_TO_REMOVE if field in all_df.columns]
        missing_fields = [field for field in FIELDS_TO_REMOVE if field not in all_df.columns]

        # Print summary of fields found
        console.print(Panel(
            f"[cyan]Fields found in database:[/cyan]\n{', '.join(existing_fields) if existing_fields else 'None'}",
            title="[bold]Fields Analysis[/bold]",
            border_style="cyan"
        ))

        # Create a variable to hold records with fields to remove
        records_with_fields = pd.DataFrame()

        if existing_fields:
            # Count records with these fields
            records_with_fields = all_df[all_df[existing_fields].notna().any(axis=1)]
            console.print(Panel(
                f"[green]Found {len(records_with_fields)} records containing fields to remove.[/green]\n\n" +
                f"[cyan]Fields to remove:[/cyan] {', '.join(existing_fields)}",
                title="[bold]Records Found[/bold]",
                border_style="green"
            ))
        else:
            console.print(Panel(
                f"[yellow]None of the specified fields exist in ANY records:[/yellow]\n{', '.join(FIELDS_TO_REMOVE)}",
                title="[bold]Fields Not Found[/bold]",
                border_style="yellow"
            ))

        return all_df, existing_fields

    except Exception as e:
        error_msg = f"Error loading all records: {str(e)}"
        console.print(Panel(f"[bold red]{error_msg}[/bold red]", title="[bold red]ERROR[/bold red]", border_style="red"))
        logger.error(error_msg)
        return None, []

def main():
    """Main function to orchestrate the cleanup process."""
    try:
        # Initialize FBAdArchiveManager with no_filter=True to ensure we get all attributes
        config = load_config('01/01/70')
        fb_manager = FBAdArchiveManager(config, use_local=True, no_filter=True)

        # Load ALL records and check schema
        all_df, existing_fields = load_all_records_and_check_schema(fb_manager)

        if all_df is None or all_df.empty:
            console.print("[yellow]No records to process.[/yellow]")
            return

        if not existing_fields:
            console.print("[yellow]No fields to remove found in the database.[/yellow]")
            return

        # Get records to clean using the existing fields we found (limit to 500 records for efficiency)
        records_df = get_records_to_clean(fb_manager, existing_fields, max_records=500)
        total_records = len(records_df)

        if total_records == 0:
            console.print("[green]No records found containing the specified fields.[/green]")
            return

        # Calculate fields not in schema
        missing_fields = [field for field in FIELDS_TO_REMOVE if field not in existing_fields]

        console.print(Panel(
            f"[bold yellow]Found {total_records} records containing fields to remove.[/bold yellow]\n\n" +
            f"[cyan]Fields found in schema:[/cyan] {', '.join(existing_fields)}\n" +
            (f"[dim]Fields not in schema:[/dim] {', '.join(missing_fields)}" if missing_fields else ""),
            title="[bold]Cleanup Summary[/bold]",
            border_style="yellow"
        ))

        # Update local DynamoDB
        console.print("\n[bold yellow]Updating local DynamoDB records...[/bold yellow]")
        local_updated = update_dynamodb_batch(fb_manager, records_df)
        console.print(Text.from_markup(f"[bold green]✓ Successfully updated {local_updated} records in local DynamoDB.[/bold green]"))

        # Prompt for AWS update using rich's Confirm
        if Confirm.ask("\n[bold yellow]Update AWS DynamoDB as well?[/bold yellow]", console=console):
            aws_updated = update_dynamodb_batch(fb_manager, records_df, is_aws=True)
            console.print(Text.from_markup(f"[bold green]✓ Successfully updated {aws_updated} records in AWS DynamoDB.[/bold green]"))
        else:
            console.print("[bold yellow]Skipping AWS DynamoDB update.[/bold yellow]")

    except Exception as e:
        error_msg = f"An error occurred: {str(e)}"
        console.print(Panel(f"[bold red]{error_msg}[/bold red]", title="[bold red]ERROR[/bold red]", border_style="red"))
        logger.error(error_msg)

if __name__ == '__main__':
    main()