#!/usr/bin/env python3
"""
<PERSON>ript to inspect Facebook ads to understand their structure.
"""

import sys
import json
import boto3
from boto3.dynamodb.conditions import Key
from rich.console import Console
from rich.table import Table
from rich import print as rprint

console = Console()

def main():
    if len(sys.argv) != 2:
        console.print("Usage: python inspect_fb_ads.py <page_id>")
        sys.exit(1)
    
    page_id = sys.argv[1]
    
    # Connect to local DynamoDB
    dynamodb = boto3.resource(
        'dynamodb',
        endpoint_url='http://localhost:8000',
        region_name='us-east-1',
        aws_access_key_id='dummy',
        aws_secret_access_key='dummy'
    )
    
    table = dynamodb.Table('FBAdArchive')
    
    # Query for a few ads
    response = table.query(
        IndexName='PageID-StartDate-index',
        KeyConditionExpression=Key('PageID').eq(str(page_id)),
        Limit=5
    )
    
    ads = response.get('Items', [])
    
    if not ads:
        console.print(f"No ads found for PageID {page_id}")
        return
    
    console.print(f"\n[cyan]Found {len(ads)} ads. First ad structure:[/cyan]")
    
    # Show first ad in full
    first_ad = ads[0]
    console.print("\n[yellow]First ad fields:[/yellow]")
    for key, value in sorted(first_ad.items()):
        value_str = str(value)[:100] + "..." if len(str(value)) > 100 else str(value)
        console.print(f"  [green]{key}[/green]: {value_str}")
    
    # Check image-related fields across all ads
    console.print("\n[yellow]Image-related fields summary:[/yellow]")
    table = Table(title="Image Fields Analysis")
    table.add_column("AdArchiveID", style="cyan")
    table.add_column("ImageHash", style="yellow")
    table.add_column("ImageURL", style="green")
    table.add_column("ImageText", style="red")
    table.add_column("HasCreativeIDs", style="magenta")
    
    for ad in ads:
        table.add_row(
            str(ad.get('AdArchiveID', 'N/A'))[:15],
            str(ad.get('ImageHash', 'N/A'))[:20],
            "Yes" if ad.get('ImageURL') else "No",
            str(ad.get('ImageText', 'N/A'))[:20],
            "Yes" if ad.get('AdCreativeIDs') else "No"
        )
    
    console.print(table)
    
    # Check for AdCreativeIDs content
    console.print("\n[yellow]AdCreativeIDs content:[/yellow]")
    for i, ad in enumerate(ads[:3]):
        creative_ids = ad.get('AdCreativeIDs', [])
        console.print(f"Ad {i+1}: {creative_ids}")

if __name__ == "__main__":
    main()