#!/usr/bin/env python3
from typing import List, <PERSON><PERSON>
from pacer_manager import Pacer<PERSON>anager
from config import load_config
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'case_check_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


def check_missing_cases(cases: List[Tuple[str, str]], pacer_db: PacerManager) -> List[Tuple[str, str]]:
    """
    Check which cases are not present in the database using the docket_exists method.

    Args:
        cases: List of tuples containing (court_id, docket_num)
        pacer_db: Instance of PacerManager

    Returns:
        List of tuples (court_id, docket_num) that are not in the database
    """
    missing_cases = []
    total_cases = len(cases)

    logger.info(f"Starting to process {total_cases} cases")

    for index, (court_id, docket_num) in enumerate(cases, 1):
        try:
            logger.info(f"Processing case {index}/{total_cases}: {court_id} - {docket_num}")

            # Use the docket_exists method instead of direct table query
            # The method expects a filing_date parameter, but returns False if not found
            # We'll pass a recent date as we're just checking existence
            logger.info(f"Input parameters: court_id={court_id}, docket_num={docket_num}")

            response = pacer_db.docket_exists(
                filing_date="20250101",  # Using a recent date
                docket_num=docket_num,
                court_id=court_id
            )
            logger.info(f"Response for {court_id} - {docket_num}: {response}")

            # docket_exists returns False if not found, or a list/dict if found
            if not response:
                logger.warning(f"No items found for {court_id} - {docket_num}")
                missing_cases.append((court_id, docket_num))
            else:
                logger.info(f"Found match for {court_id} - {docket_num}")

        except Exception as e:
            logger.error(f"Error processing {court_id} - {docket_num}: {str(e)}")
            missing_cases.append((court_id, docket_num))

    logger.info(f"Completed processing all cases. Found {len(missing_cases)} missing cases")
    return missing_cases


def process_cases(cases_list: str) -> List[Tuple[str, str]]:
    """
    Process the cases and return missing ones in the same format as input.

    Args:
        cases_list: String containing the list of cases

    Returns:
        List of missing cases in the same format
    """
    logger.info("Initializing PacerManager")

    # Load configuration
    config = load_config('01/01/1970')
    pacer_db = PacerManager(config, use_local=False)

    # Convert string list to actual list of tuples if needed
    if isinstance(cases_list, str):
        cases = eval(cases_list)
        logger.info("Converted string input to dlist of cases")
    else:
        cases = cases_list
        logger.info("Using provided list of cases directly")

    # Get missing cases
    missing_cases = check_missing_cases(cases, pacer_db)

    return missing_cases


if __name__ == "__main__":
    # Example usage
    cases = [('scd', '2:25-cv-00152'), ('scd', '2:25-cv-00159'), ('scd', '2:25-cv-00169'), ('scd', '2:25-cv-00175'),
                 ('scd', '2:25-cv-00176'), ('scd', '2:25-cv-00219'), ('scd', '2:25-cv-00221'), ('scd', '2:25-cv-00223'),
                 ('scd', '2:25-cv-00253'), ('scd', '2:25-cv-00255'), ('scd', '2:25-cv-00256'), ('scd', '2:25-cv-00257'),
                 ('scd', '2:25-cv-00258'), ('scd', '2:25-cv-00260'), ('scd', '2:25-cv-00261'), ('scd', '2:25-cv-00262'),
                 ('scd', '2:25-cv-00263'), ('scd', '2:25-cv-00269'), ('scd', '2:25-cv-00283'), ('scd', '2:25-cv-00284'),
                 ('scd', '2:25-cv-00285'), ('scd', '2:25-cv-00287'), ('scd', '2:25-cv-00288'), ('scd', '2:25-cv-00289'),
                 ('scd', '2:25-cv-00290'), ('scd', '2:25-cv-00291'), ('scd', '2:25-cv-00292'), ('scd', '2:25-cv-00295'),
                 ('scd', '2:25-cv-00296'), ('scd', '2:25-cv-00297'), ('scd', '2:25-cv-00298'), ('scd', '2:25-cv-00299'),
                 ('scd', '2:25-cv-00303'), ('scd', '2:25-cv-00304'), ('scd', '2:25-cv-00305'), ('scd', '2:25-cv-00306'),
                 ('scd', '2:25-cv-00307'), ('scd', '2:25-cv-00308'), ('scd', '2:25-cv-00319'), ('scd', '2:25-cv-00320'),
                 ('scd', '2:25-cv-00321'), ('scd', '2:25-cv-00322'), ('scd', '2:25-cv-00323'), ('scd', '2:25-cv-00324'),
                 ('scd', '2:25-cv-00325'), ('scd', '2:25-cv-00326'), ('scd', '2:25-cv-00327'), ('scd', '2:25-cv-00328'),
                 ('scd', '2:25-cv-00329'), ('scd', '2:25-cv-00343'), ('scd', '2:25-cv-00346'), ('scd', '2:25-cv-00348'),
                 ('scd', '2:25-cv-00349'), ('scd', '2:25-cv-00351'), ('scd', '2:25-cv-00352'), ('scd', '2:25-cv-00353'),
                 ('scd', '2:25-cv-00354'), ('scd', '2:25-cv-00355'), ('scd', '2:25-cv-00356'), ('scd', '2:25-cv-00357'),
                 ('scd', '2:25-cv-00358'), ('scd', '2:25-cv-00359'), ('scd', '2:25-cv-00360'), ('scd', '2:25-cv-00361'),
                 ('scd', '2:25-cv-00362'), ('scd', '2:25-cv-00363'), ('scd', '2:25-cv-00364'), ('scd', '2:25-cv-00365'),
                 ('scd', '2:25-cv-00366'), ('scd', '2:25-cv-00367'), ('scd', '2:25-cv-00368'), ('scd', '2:25-cv-00369'),
                 ('scd', '2:25-cv-00370'), ('scd', '2:25-cv-00372'), ('scd', '2:25-cv-00373'), ('scd', '2:25-cv-00386'),
                 ('scd', '2:25-cv-00388'), ('scd', '2:25-cv-00391'), ('scd', '2:25-cv-00392'), ('scd', '2:25-cv-00395'),
                 ('scd', '2:25-cv-00397'), ('scd', '2:25-cv-00399'), ('scd', '2:25-cv-00400'), ('scd', '2:25-cv-00401'),
                 ('scd', '2:25-cv-00410'), ('scd', '2:25-cv-00411'), ('scd', '2:25-cv-00412'), ('scd', '2:25-cv-00413'),
                 ('scd', '2:25-cv-00414'), ('scd', '2:25-cv-00415'), ('scd', '2:25-cv-00417'), ('scd', '2:25-cv-00419'),
                 ('scd', '2:25-cv-00422'), ('scd', '2:25-cv-00423'), ('scd', '2:25-cv-00424'), ('scd', '2:25-cv-00425'),
                 ('scd', '2:25-cv-00426'), ('scd', '2:25-cv-00449'), ('scd', '2:25-cv-00450'), ('scd', '2:25-cv-00451'),
                 ('scd', '2:25-cv-00452'), ('scd', '2:25-cv-00456'), ('scd', '2:25-cv-00460'), ('scd', '2:25-cv-00461'),
                 ('scd', '2:25-cv-00462'), ('scd', '2:25-cv-00464'), ('scd', '2:25-cv-00465'), ('scd', '2:25-cv-00468'),
                 ('scd', '2:25-cv-00469'), ('scd', '2:25-cv-00470'), ('scd', '2:25-cv-00473'), ('scd', '2:25-cv-00478'),
                 ('scd', '2:25-cv-00482'), ('scd', '2:25-cv-00483'), ('scd', '2:25-cv-00484'), ('scd', '2:25-cv-00501'),
                 ('scd', '2:25-cv-00502'), ('scd', '2:25-cv-00504'), ('scd', '2:25-cv-00505'), ('scd', '2:25-cv-00506'),
                 ('scd', '2:25-cv-00507'), ('scd', '2:25-cv-00522'), ('scd', '2:25-cv-00523'), ('scd', '2:25-cv-00524'),
                 ('scd', '2:25-cv-00525'), ('scd', '2:25-cv-00526'), ('scd', '2:25-cv-00527'), ('scd', '2:25-cv-00528'),
                 ('scd', '2:25-cv-00530'), ('scd', '2:25-cv-00540'), ('scd', '2:25-cv-00541'), ('scd', '2:25-cv-00542'),
                 ('scd', '2:25-cv-00543'), ('scd', '2:25-cv-00544'), ('scd', '2:25-cv-00545'), ('scd', '2:25-cv-00546'),
                 ('scd', '2:25-cv-00547'), ('scd', '2:25-cv-00548'), ('scd', '2:25-cv-00549')]
    missing = process_cases(cases)
    print(f"cases = {missing}")