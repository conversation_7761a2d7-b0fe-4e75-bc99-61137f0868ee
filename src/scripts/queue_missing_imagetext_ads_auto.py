#!/usr/bin/env python3
"""
Automated script to find Facebook ads with missing ImageText and add them to the deferred image processing queue.
Defaults to local DynamoDB without prompting.

Usage:
    python queue_missing_imagetext_ads_auto.py <page_id> [scrape_date]
"""

import sys
import sqlite3
import logging
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any
import boto3
from boto3.dynamodb.conditions import Key
from rich.console import Console
from rich.panel import Panel
from rich.table import Table

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

console = Console()


class SimpleFBAdArchiveManager:
    """Minimal FB Ad Archive manager for querying DynamoDB."""
    
    def __init__(self, use_local=True, local_port=8000):
        if use_local:
            self.dynamodb = boto3.resource(
                'dynamodb',
                endpoint_url=f'http://localhost:{local_port}',
                region_name='us-east-1',
                aws_access_key_id='dummy',
                aws_secret_access_key='dummy'
            )
        else:
            self.dynamodb = boto3.resource('dynamodb')
        
        self.table = self.dynamodb.Table('FBAdArchive')
        
    def query_by_page_id(self, page_id: str) -> List[Dict[str, Any]]:
        """Query all ads for a given PageID."""
        items = []
        last_evaluated_key = None
        
        while True:
            if last_evaluated_key:
                response = self.table.query(
                    IndexName='PageID-StartDate-index',
                    KeyConditionExpression=Key('PageID').eq(str(page_id)),
                    ExclusiveStartKey=last_evaluated_key
                )
            else:
                response = self.table.query(
                    IndexName='PageID-StartDate-index',
                    KeyConditionExpression=Key('PageID').eq(str(page_id))
                )
            
            items.extend(response.get('Items', []))
            last_evaluated_key = response.get('LastEvaluatedKey')
            
            if not last_evaluated_key:
                break
                
        return items


class SimpleLocalImageQueue:
    """Minimal local image queue manager."""
    
    def __init__(self, db_path="./data/image_queue/image_queue.db"):
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self._init_db()
        
    def _init_db(self):
        """Initialize the database schema."""
        conn = sqlite3.connect(self.db_path)
        conn.execute("""
            CREATE TABLE IF NOT EXISTS image_queue (
                image_hash TEXT PRIMARY KEY,
                ad_archive_id TEXT NOT NULL,
                start_date TEXT NOT NULL,
                s3_path TEXT NOT NULL,
                creative_id TEXT,
                law_firm_name TEXT,
                scrape_date TEXT NOT NULL,
                status TEXT DEFAULT 'pending',
                queued_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                processed_at TIMESTAMP,
                image_text TEXT,
                processing_time_ms INTEGER,
                error TEXT,
                retry_count INTEGER DEFAULT 0,
                last_retry_at TIMESTAMP
            )
        """)
        conn.commit()
        conn.close()
        
    def add_to_queue(self, image_hash: str, ad_archive_id: str, start_date: str,
                     s3_path: str, scrape_date: str, creative_id=None, law_firm_name=None) -> bool:
        """Add an image to the processing queue."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("""
                INSERT OR IGNORE INTO image_queue 
                (image_hash, ad_archive_id, start_date, s3_path, 
                 creative_id, law_firm_name, scrape_date) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (image_hash, ad_archive_id, start_date, s3_path, 
                  creative_id, law_firm_name, scrape_date))
            
            added = cursor.rowcount > 0
            conn.commit()
            conn.close()
            return added
        except Exception as e:
            logger.error(f"Error adding to queue: {e}")
            return False
            
    def get_stats(self, scrape_date: str) -> Dict[str, int]:
        """Get queue statistics for a given date."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT 
                COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending,
                COUNT(CASE WHEN status = 'processed' THEN 1 END) as processed,
                COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed,
                COUNT(CASE WHEN status = 'skipped' THEN 1 END) as skipped
            FROM image_queue 
            WHERE scrape_date = ?
        """, (scrape_date,))
        
        row = cursor.fetchone()
        conn.close()
        
        return {
            'pending': row[0] or 0,
            'processed': row[1] or 0,
            'failed': row[2] or 0,
            'skipped': row[3] or 0
        }


def main():
    """Main function."""
    if len(sys.argv) < 2:
        console.print(Panel(
            "[red]Error:[/red] Invalid arguments\n\n"
            "Usage: python queue_missing_imagetext_ads_auto.py <page_id> [scrape_date]",
            title="Usage Error",
            border_style="red"
        ))
        sys.exit(1)
    
    page_id = sys.argv[1]
    scrape_date = sys.argv[2] if len(sys.argv) > 2 else datetime.now().strftime('%Y%m%d')
    
    console.print(Panel(
        f"[bold blue]Queue Missing ImageText Ads (Automated)[/bold blue]\n\n"
        f"PageID: [cyan]{page_id}[/cyan]\n"
        f"Scrape Date: [cyan]{scrape_date}[/cyan]\n"
        f"Using: [green]Local DynamoDB[/green]",
        title="FB Ad Image Queue Tool",
        border_style="blue"
    ))
    
    try:
        # Initialize managers (always use local)
        console.print("\n[cyan]Initializing managers...[/cyan]")
        fb_manager = SimpleFBAdArchiveManager(use_local=True)
        queue = SimpleLocalImageQueue()
        
        # Query ads
        console.print(f"\n[cyan]Querying ads for PageID {page_id}...[/cyan]")
        all_ads = fb_manager.query_by_page_id(page_id)
        console.print(f"[green]Found {len(all_ads)} total ads[/green]")
        
        # Filter for missing ImageText
        missing_ads = []
        for ad in all_ads:
            image_text = ad.get('ImageText')
            if not image_text or image_text == 'NA' or image_text == 'null':
                missing_ads.append(ad)
        
        if not missing_ads:
            console.print(Panel(
                f"[green]No ads found with missing ImageText for PageID {page_id}[/green]",
                title="Results",
                border_style="green"
            ))
            return
        
        console.print(f"\n[yellow]Found {len(missing_ads)} ads with missing ImageText[/yellow]")
        
        # Display table
        table = Table(title=f"Ads with Missing ImageText (showing first 10)")
        table.add_column("AdArchiveID", style="cyan")
        table.add_column("StartDate", style="green")
        table.add_column("LawFirm", style="yellow")
        table.add_column("ImageHash", style="magenta")
        
        for i, ad in enumerate(missing_ads[:10]):
            table.add_row(
                str(ad.get('AdArchiveID', 'N/A')),
                str(ad.get('StartDate', 'N/A')),
                str(ad.get('LawFirm', 'N/A')),
                str(ad.get('ImageHash', 'N/A'))
            )
        
        console.print(table)
        
        # Show current queue stats
        stats = queue.get_stats(scrape_date)
        console.print(f"\n[cyan]Current queue stats for {scrape_date}:[/cyan]")
        console.print(f"  Pending: {stats['pending']}")
        console.print(f"  Processed: {stats['processed']}")
        console.print(f"  Failed: {stats['failed']}")
        console.print(f"  Skipped: {stats['skipped']}")
        
        # Add to queue automatically
        console.print(f"\n[cyan]Adding {len(missing_ads)} ads to queue...[/cyan]")
        added = 0
        skipped = 0
        errors = 0
        
        for ad in missing_ads:
            try:
                image_hash = ad.get('ImageHash')
                ad_archive_id = str(ad.get('AdArchiveID'))
                start_date = str(ad.get('StartDate'))
                image_url = ad.get('ImageURL')
                law_firm = ad.get('LawFirm')
                creative_ids = ad.get('AdCreativeIDs', [])
                creative_id = creative_ids[0] if creative_ids else None
                
                if not image_hash or not image_url:
                    skipped += 1
                    continue
                
                # Simple S3 path
                s3_path = f"fb-ads/{scrape_date}/{image_hash}.jpg"
                
                if queue.add_to_queue(
                    image_hash=image_hash,
                    ad_archive_id=ad_archive_id,
                    start_date=start_date,
                    s3_path=s3_path,
                    scrape_date=scrape_date,
                    creative_id=creative_id,
                    law_firm_name=law_firm
                ):
                    added += 1
                else:
                    skipped += 1
                    
            except Exception as e:
                logger.error(f"Error processing ad: {e}")
                errors += 1
        
        # Show results
        console.print(Panel(
            f"[bold green]Queue Operation Complete[/bold green]\n\n"
            f"Total ads: {len(missing_ads)}\n"
            f"Added to queue: [green]{added}[/green]\n"
            f"Skipped: [yellow]{skipped}[/yellow]\n"
            f"Errors: [red]{errors}[/red]",
            title="Results",
            border_style="green"
        ))
        
        # Show updated stats
        updated_stats = queue.get_stats(scrape_date)
        console.print(f"\n[cyan]Updated queue stats for {scrape_date}:[/cyan]")
        console.print(f"  Pending: {updated_stats['pending']}")
        console.print(f"  Processed: {updated_stats['processed']}")
        console.print(f"  Failed: {updated_stats['failed']}")
        console.print(f"  Skipped: {updated_stats['skipped']}")
        
    except Exception as e:
        console.print(Panel(
            f"[red]Error:[/red] {str(e)}",
            title="Error",
            border_style="red"
        ))
        logger.exception("Unexpected error")
        sys.exit(1)


if __name__ == "__main__":
    main()