#!/usr/bin/env python3
"""
Script to find Facebook ads with missing ImageText and add them to the deferred image processing queue.

Usage:
    python queue_missing_imagetext_ads.py <page_id>
"""

import sys
import logging
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any, Optional
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.prompt import Confirm, Prompt
from rich import print as rprint

# Add parent directories to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))
sys.path.insert(0, str(Path(__file__).parent.parent))

# Direct imports to avoid going through __init__.py
try:
    from lib.fb_archive_manager import FBAdArchiveManager
    from lib.fb_ads.local_image_queue import LocalImageQueue
    from lib.config import load_config
    from lib.utils.date import DateUtils
except ImportError:
    # Fallback imports
    from src.lib.fb_archive_manager import FBAdArchiveManager
    from src.lib.fb_ads.local_image_queue import LocalImageQueue
    from src.lib.config import load_config
    from src.lib.utils.date import DateUtils

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

console = Console()


def find_ads_missing_imagetext(manager: FBAdArchiveManager, page_id: str) -> List[Dict[str, Any]]:
    """
    Find all ads for a given PageID where ImageText is missing, null, or 'NA'.
    
    Args:
        manager: FBAdArchiveManager instance
        page_id: PageID to search for
        
    Returns:
        List of ads with missing ImageText
    """
    console.print(f"[cyan]Querying ads for PageID: {page_id}...[/cyan]")
    
    # Get all items for this PageID using the manager's query method
    items = []
    last_evaluated_key = None
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        task = progress.add_task("Fetching ads from DynamoDB...", total=None)
        
        # Use the manager's query_by_page_id method directly
        items = manager.query_by_page_id(page_id)
        progress.update(task, completed=True)
    
    console.print(f"[green]Found {len(items)} total ads for PageID {page_id}[/green]")
    
    # Filter for items with missing ImageText
    missing_imagetext = []
    for item in items:
        image_text = item.get('ImageText')
        if not image_text or image_text == 'NA' or image_text == 'null':
            missing_imagetext.append(item)
    
    return missing_imagetext


def display_missing_ads(ads: List[Dict[str, Any]]) -> None:
    """Display ads with missing ImageText in a table."""
    table = Table(title=f"Ads with Missing ImageText ({len(ads)} total)")
    table.add_column("AdArchiveID", style="cyan")
    table.add_column("StartDate", style="green")
    table.add_column("LawFirm", style="yellow")
    table.add_column("ImageURL", style="blue", overflow="fold", max_width=50)
    table.add_column("ImageHash", style="magenta")
    
    # Show first 10 items
    display_items = ads[:10]
    for ad in display_items:
        table.add_row(
            str(ad.get('AdArchiveID', 'N/A')),
            str(ad.get('StartDate', 'N/A')),
            str(ad.get('LawFirm', 'N/A')),
            str(ad.get('ImageURL', 'N/A'))[:50] + "..." if ad.get('ImageURL') else 'N/A',
            str(ad.get('ImageHash', 'N/A'))
        )
    
    if len(ads) > 10:
        table.add_row("[dim]...[/dim]", "[dim]...[/dim]", "[dim]...[/dim]", "[dim]...[/dim]", "[dim]...[/dim]")
    
    console.print(table)


def add_to_processing_queue(
    ads: List[Dict[str, Any]], 
    queue: LocalImageQueue, 
    scrape_date: str
) -> Dict[str, int]:
    """
    Add ads to the local image processing queue.
    
    Args:
        ads: List of ads to process
        queue: LocalImageQueue instance
        scrape_date: Date to use for tracking
        
    Returns:
        Statistics dictionary
    """
    stats = {
        'total': len(ads),
        'added': 0,
        'skipped': 0,
        'errors': 0
    }
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        task = progress.add_task(f"Adding {len(ads)} ads to queue...", total=len(ads))
        
        for ad in ads:
            try:
                # Extract required fields
                image_hash = ad.get('ImageHash')
                ad_archive_id = str(ad.get('AdArchiveID'))
                start_date = str(ad.get('StartDate'))
                image_url = ad.get('ImageURL')
                law_firm = ad.get('LawFirm')
                creative_ids = ad.get('AdCreativeIDs', [])
                creative_id = creative_ids[0] if creative_ids else None
                
                # Skip if no image hash or URL
                if not image_hash or not image_url:
                    stats['skipped'] += 1
                    progress.advance(task)
                    continue
                
                # Convert image URL to S3 path if needed
                # Assuming images are stored in S3 with pattern: s3://bucket/fb-ads/YYYYMMDD/hash.jpg
                s3_path = f"fb-ads/{scrape_date}/{image_hash}.jpg"
                
                # Add to queue
                added = queue.add_to_queue(
                    image_hash=image_hash,
                    ad_archive_id=ad_archive_id,
                    start_date=start_date,
                    s3_path=s3_path,
                    scrape_date=scrape_date,
                    creative_id=creative_id,
                    law_firm_name=law_firm
                )
                
                if added:
                    stats['added'] += 1
                else:
                    stats['skipped'] += 1
                    
            except Exception as e:
                logger.error(f"Error processing ad {ad.get('AdArchiveID')}: {e}")
                stats['errors'] += 1
            
            progress.advance(task)
    
    return stats


def main():
    """Main function."""
    if len(sys.argv) != 2:
        console.print(Panel(
            "[red]Error:[/red] Invalid arguments\n\n"
            "Usage: python queue_missing_imagetext_ads.py <page_id>",
            title="Usage Error",
            border_style="red"
        ))
        sys.exit(1)
    
    page_id = sys.argv[1]
    
    console.print(Panel(
        f"[bold blue]Queue Missing ImageText Ads[/bold blue]\n\n"
        f"PageID: [cyan]{page_id}[/cyan]",
        title="FB Ad Image Queue Tool",
        border_style="blue"
    ))
    
    try:
        # Load configuration with today's date as end_date
        from datetime import datetime
        today = datetime.now().strftime('%m/%d/%y')
        config = load_config(today)
        
        # Use local DynamoDB by default
        use_local = Confirm.ask("Use local DynamoDB?", default=True)
        
        # Initialize managers
        console.print("\n[cyan]Initializing managers...[/cyan]")
        fb_manager = FBAdArchiveManager(config, use_local=use_local)
        
        # Get current date for queue tracking
        scrape_date = config.get('iso_date', DateUtils.current_date())
        
        # Allow override of scrape date
        custom_date = Prompt.ask(
            f"Enter scrape date for queue tracking (YYYYMMDD)",
            default=scrape_date
        )
        if custom_date and custom_date != scrape_date:
            scrape_date = custom_date
        
        # Initialize local queue
        queue = LocalImageQueue()
        
        # Find ads with missing ImageText
        console.print(f"\n[cyan]Searching for ads with missing ImageText...[/cyan]")
        missing_ads = find_ads_missing_imagetext(fb_manager, page_id)
        
        if not missing_ads:
            console.print(Panel(
                f"[green]No ads found with missing ImageText for PageID {page_id}[/green]",
                title="Results",
                border_style="green"
            ))
            return
        
        # Display findings
        console.print(f"\n[yellow]Found {len(missing_ads)} ads with missing ImageText[/yellow]")
        display_missing_ads(missing_ads)
        
        # Show current queue stats
        queue_stats = queue.get_summary(scrape_date)
        if queue_stats:
            stats = queue_stats[0]
            console.print(f"\n[cyan]Current queue stats for {scrape_date}:[/cyan]")
            console.print(f"  Queued: {stats.get('total_queued', 0)}")
            console.print(f"  Processed: {stats.get('total_processed', 0)}")
            console.print(f"  Failed: {stats.get('total_failed', 0)}")
            console.print(f"  Skipped: {stats.get('total_skipped', 0)}")
        
        # Confirm before adding to queue
        if not Confirm.ask(f"\nAdd {len(missing_ads)} ads to processing queue?", default=True):
            console.print("[yellow]Operation cancelled[/yellow]")
            return
        
        # Add to queue
        console.print(f"\n[cyan]Adding ads to processing queue...[/cyan]")
        results = add_to_processing_queue(missing_ads, queue, scrape_date)
        
        # Display results
        console.print(Panel(
            f"[bold green]Queue Operation Complete[/bold green]\n\n"
            f"Total ads: {results['total']}\n"
            f"Added to queue: [green]{results['added']}[/green]\n"
            f"Skipped (already in queue): [yellow]{results['skipped']}[/yellow]\n"
            f"Errors: [red]{results['errors']}[/red]\n\n"
            f"[dim]Run process_image_queue.py to process these images[/dim]",
            title="Results",
            border_style="green"
        ))
        
        # Show updated queue stats
        updated_stats = queue.get_summary(scrape_date)
        if updated_stats:
            stats = updated_stats[0]
            console.print(f"\n[cyan]Updated queue stats for {scrape_date}:[/cyan]")
            console.print(f"  Queued: {stats.get('total_queued', 0)}")
            console.print(f"  Processed: {stats.get('total_processed', 0)}")
            console.print(f"  Failed: {stats.get('total_failed', 0)}")
            console.print(f"  Skipped: {stats.get('total_skipped', 0)}")
        
    except Exception as e:
        console.print(Panel(
            f"[red]Error:[/red] {str(e)}",
            title="Error",
            border_style="red"
        ))
        logger.exception("Unexpected error")
        sys.exit(1)


if __name__ == "__main__":
    main()