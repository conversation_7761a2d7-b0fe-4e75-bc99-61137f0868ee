#!/usr/bin/env python3

import os
import json
from datetime import datetime
from typing import Dict, Any, <PERSON><PERSON>
from rich.console import Console
from rich.table import Table
from rich.panel import Panel


# Import the project root handling function
try:
    from utils import get_project_root
except ImportError:
    # Fall back to direct import if utils.py is not available
    try:
        from src.lib.config import PROJECT_ROOT
    except ImportError:
        # If can't import, try to load from environment or use a fallback
        try:
            from dotenv import load_dotenv
            load_dotenv()
            PROJECT_ROOT = os.getenv('PROJECT_ROOT', os.path.expanduser('~/PycharmProjects/lexgenius'))
        except ImportError:
            PROJECT_ROOT = os.path.expanduser('~/PycharmProjects/lexgenius')
else:
    PROJECT_ROOT = get_project_root()


console = Console()

def validate_date(date_str: str) -> bool:
    try:
        datetime.strptime(date_str, "%Y%m%d")
        return True
    except ValueError:
        return False

def load_json_file(file_path: str) -> Dict[str, Any]:
    try:
        with open(file_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        console.print(f"[red]Error loading {file_path}: {str(e)}[/red]")
        return {}

def compare_json_files(date_str: str) -> Tuple[list, list]:
    prod_dir = fos.path.join(PROJECT_ROOT, "data", {date_str}_prod/dockets)
    regular_dir = fos.path.join(PROJECT_ROOT, "data", {date_str}/dockets)
    
    if not os.path.exists(prod_dir) or not os.path.exists(regular_dir):
        console.print(f"[red]One or both directories do not exist:[/red]")
        console.print(f"Prod: {prod_dir}")
        console.print(f"Regular: {regular_dir}")
        return [], []

    differences = []
    unmatched_files = []
    
    # Get list of files in both directories
    prod_files = {f for f in os.listdir(prod_dir) if f.endswith('.json')}
    regular_files = {f for f in os.listdir(regular_dir) if f.endswith('.json')}
    
    # Find unmatched files
    prod_only = prod_files - regular_files
    regular_only = regular_files - prod_files
    
    for filename in prod_only:
        unmatched_files.append(('prod_only', filename))
    for filename in regular_only:
        unmatched_files.append(('regular_only', filename))

    # Compare matching files
    matching_files = prod_files & regular_files
    
    for filename in matching_files:
        prod_path = os.path.join(prod_dir, filename)
        regular_path = os.path.join(regular_dir, filename)
        
        prod_data = load_json_file(prod_path)
        regular_data = load_json_file(regular_path)
        
        if not prod_data or not regular_data:
            continue
        
        # Compare all fields
        all_keys = set(prod_data.keys()) | set(regular_data.keys())
        file_differences = []
        
        for key in all_keys:
            prod_value = prod_data.get(key)
            regular_value = regular_data.get(key)
            
            if prod_value != regular_value:
                file_differences.append({
                    'field': key,
                    'prod_value': prod_value,
                    'regular_value': regular_value
                })
        
        if file_differences:
            differences.append({
                'filename': filename,
                'differences': file_differences
            })
    
    return differences, unmatched_files

def display_results(differences: list, unmatched_files: list, date_str: str):
    # Display differences
    if differences:
        console.print(Panel(f"[yellow]Differences found in {len(differences)} files[/yellow]"))
        
        for diff in differences:
            table = Table(title=f"File: {diff['filename']}")
            table.add_column("Field", style="cyan")
            table.add_column("Prod Value", style="green")
            table.add_column("Regular Value", style="yellow")
            
            for d in diff['differences']:
                table.add_row(
                    str(d['field']),
                    str(d['prod_value']),
                    str(d['regular_value'])
                )
            
            console.print(table)
            console.print("")
    
    # Display unmatched files
    if unmatched_files:
        unmatched_table = Table(title="Unmatched Files")
        unmatched_table.add_column("Location", style="cyan")
        unmatched_table.add_column("Filename", style="yellow")
        
        for location, filename in unmatched_files:
            unmatched_table.add_row(location, filename)
        
        console.print(unmatched_table)

def main():
    while True:
        date_str = console.input("[yellow]Enter date in YYYYMMDD format: [/yellow]")
        if validate_date(date_str):
            break
        console.print("[red]Invalid date format. Please use YYYYMMDD format.[/red]")
    
    differences, unmatched_files = compare_json_files(date_str)
    display_results(differences, unmatched_files, date_str)

if __name__ == "__main__":
    main()