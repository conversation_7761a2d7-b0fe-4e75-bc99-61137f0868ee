import csv
import re
import os

import fitz  # Import PyMuPDF
import pandas as pd


def extract_pending_mdl_data(pdf_path, csv_path):
    # Load existing data from CSV
    existing_data = {}
    try:
        with open(csv_path, mode='r', newline='') as file:
            reader = csv.DictReader(file)
            for row in reader:
                existing_data[row['mdl_num']] = row
    except FileNotFoundError:
        # If the file doesn't exist, we'll create it later
        pass

    # Open the provided PDF file
    document = fitz.open(pdf_path)

    # Regex pattern to extract MDL number, litigation, actions now pending, and total actions
    regex_pattern = re.compile(
        r"(MDL -\d{4})\s+(IN RE:.+?)\s+([\d,]+)\s+([\d,]+)"
    )

    # Read through each page of the PDF
    for page_num in range(len(document)):
        page = document.load_page(page_num)
        text = page.get_text()
        matches = regex_pattern.finditer(text)
        for match in matches:
            mdl_num = re.findall(r'-\d{4}', match.group(1))[0][1:]
            if mdl_num in existing_data:
                description = existing_data[mdl_num].get('description', '')
                short_summary = existing_data[mdl_num].get('short_summary', '')
            else:
                description = ''
                short_summary = ''

            data = {
                'mdl_num': mdl_num,
                'litigation': match.group(2),
                'actions_now_pending': match.group(3).replace(',', ''),
                'total_actions': match.group(4).replace(',', ''),
                'description': description,
                'short_summary': short_summary
            }
            existing_data[mdl_num] = data  # Update or add new data

    new_csv_path = csv_path.replace('_.backup', '')
    # Write the extracted/updated data to a CSV file
    with open(new_csv_path, mode='w', newline='') as file:
        fieldnames = ['mdl_num', 'litigation', 'actions_now_pending', 'total_actions', 'description', 'short_summary']
        writer = csv.DictWriter(file, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(existing_data.values())  # Write all data, including updates and new entries


def check_csv_for_inconsistent_lines(csv_path):
    with open(csv_path, mode='r', newline='') as file:
        reader = csv.reader(file)
        header = next(reader)
        expected_num_fields = len(header)

        inconsistent_lines = []

        for line_num, row in enumerate(reader, start=2):  # start=2 to account for the header line
            if len(row) != expected_num_fields:
                inconsistent_lines.append((line_num, row))

        if inconsistent_lines:
            print("Inconsistent lines found:")
            for line_num, row in inconsistent_lines:
                print(f"Line {line_num}: {row}")
        else:
            print("No inconsistent lines found.")


if __name__ == "__main__":
    # Define your PDF and CSV file paths
    pdf_path = 'data/mdls/mdls_by_district/Pending_MDL_Dockets_By_District-August-1-2024.pdf'
    full_pdf_path = os.path.join(os.getcwd(), '..', pdf_path)
    csv_path = 'data/mdls/mdl_pending_by_actions_backup.csv'
    full_csv_path = os.path.join(os.getcwd(), '..', csv_path)

    # Call the function with your file paths
    extract_pending_mdl_data(full_pdf_path, full_csv_path)

    # Check the CSV file for inconsistent lines
    check_csv_for_inconsistent_lines(os.path.join(os.getcwd(), '..', 'data/mdls/mdl_pending_by_actions.csv'))

    # Read the CSV file, skipping bad lines
    try:
        df = pd.read_csv(os.path.join(os.getcwd(), '..', 'data/mdls/mdl_pending_by_actions.csv'), on_bad_lines='skip')
        print(df.head())
    except pd.errors.ParserError as e:
        print(f"Error reading CSV file: {e}")
