#!/usr/bin/env python3
import pandas as pd
import re
import json
from collections import defaultdict
from datetime import datetime
import os
from rich.console import Console
from rich.table import Table


def normalize_attorney_name(name_str):
    if not name_str or pd.isna(name_str):
        return "Unknown Attorney"

    name_str = str(name_str).strip()  # Ensure it's a string and strip whitespace
    if not name_str:
        return "Unknown Attorney"

    # Step 1: Handle "Last, First M." or "Last, First" format
    if ',' in name_str:
        parts = [p.strip() for p in name_str.split(',', 1)]
        if len(parts) == 2 and parts[0] and parts[1]:  # Ensure both parts are non-empty
            last_name = parts[0]
            first_middle_parts = parts[1].split()

            # Reconstruct first_middle carefully, especially with middle initials
            first_name_normalized = ""
            if len(first_middle_parts) > 0:
                first_name_normalized = first_middle_parts[0]
                if len(first_middle_parts) > 1:  # Potentially middle name/initial
                    # Check if the second part is a middle initial (e.g., "<PERSON><PERSON>")
                    if len(first_middle_parts[1]) == 2 and first_middle_parts[1].endswith('.'):
                        first_name_normalized += " " + first_middle_parts[1]
                        # If there's more after initial (unlikely for "Last, First M." but handle)
                        if len(first_middle_parts) > 2:
                            first_name_normalized += " " + " ".join(first_middle_parts[2:])
                    elif len(first_middle_parts[1]) == 1 and first_middle_parts[
                        1].isalpha():  # Single letter middle name
                        first_name_normalized += " " + first_middle_parts[1] + "."  # Add period if missing
                        if len(first_middle_parts) > 2:
                            first_name_normalized += " " + " ".join(first_middle_parts[2:])
                    else:  # Assume it's a multi-part first name or middle name without initial format
                        first_name_normalized += " " + " ".join(first_middle_parts[1:])

            name_str = f"{first_name_normalized} {last_name}".strip()

    # Step 2: Clean up multiple spaces and spaces around periods (especially for middle initials)
    name_str = re.sub(r'\s+\.', '.', name_str)  # "James ." -> "James."
    name_str = re.sub(r'\.\s+', '. ', name_str)  # "James.Ferraro" -> "James. Ferraro" (if period was part of name)
    # but mainly targets "James. M." -> "James. M. "
    name_str = re.sub(r'\s{2,}', ' ', name_str).strip()  # Consolidate multiple spaces

    # Step 3: Ensure middle initials have a period if they are single uppercase letters
    # (This might be too aggressive if names genuinely lack periods, but common for legal)
    # Example: "James M Ferraro" -> "James M. Ferraro"
    parts = name_str.split()
    if len(parts) > 1:  # At least first and last name
        reconstructed_parts = [parts[0]]
        for i in range(1, len(parts) - 1):  # Middle parts
            part = parts[i]
            if len(part) == 1 and part.isupper() and not part.endswith('.'):
                reconstructed_parts.append(part + ".")
            else:
                reconstructed_parts.append(part)
        reconstructed_parts.append(parts[-1])  # Add last name
        name_str = " ".join(reconstructed_parts)

    # Final check for names like "James . Ferraro" becoming "James. Ferraro"
    # We want "James M. Ferraro" or "James Ferraro"
    # If a part ends with a period and is not a recognized suffix (Jr., Sr., II, III, IV),
    # and the next part starts with a capital letter, ensure a space.
    name_str = re.sub(r'([A-Za-z])\.([A-Za-z])', r'\1. \2',
                      name_str)  # "James.Ferraro" -> "James. Ferraro" (incorrect); "M.Ferraro" -> "M. Ferraro"

    # Correct "First. Last" to "First Last" if the period was just from cleanup
    # unless it's a known initialism like St.
    # This is tricky. Let's be more conservative.
    # What we really want is "First M. Last"
    # Let's re-evaluate "James . Ferraro"
    # Initial: "James . Ferraro"
    # After sub(r'\s+\.', '.'): "James. Ferraro" -> This is incorrect for "James Ferraro" if "." was an artifact.
    # If the original was "Ferraro, James .", then:
    #   parts[0] = "Ferraro", parts[1] = "James ."
    #   first_middle_parts = ["James", "."]
    #   first_name_normalized = "James ."
    #   name_str = "James . Ferraro"
    #   After re.sub(r'\s+\.', '.', name_str): "James. Ferraro"
    #   After re.sub(r'\.\s+', '. ', name_str): "James. Ferraro" (no change if space was already there)
    #   After re.sub(r'([A-Za-z])\.([A-Za-z])', r'\1. \2', name_str): "James. Ferraro"

    # Let's specifically target "Name ." patterns that were not initials
    parts = name_str.split()
    if len(parts) > 1:
        cleaned_parts = []
        for i, part in enumerate(parts):
            if part == "." and i > 0 and not cleaned_parts[-1].endswith('.'):  # A standalone period
                continue  # Skip it
            elif part.endswith('.') and len(part) > 2 and not any(
                    part.upper().startswith(s) for s in ["JR", "SR", "II", "III", "IV"]):
                # If a word like "James." and not an initial, remove the period unless it's a recognized suffix
                # This might be too aggressive if names genuinely end with a period (e.g. some European names)
                # Let's be more specific about removing a period if it's only there due to " ." artifact
                pass  # Keep for now, rely on earlier "Last, First ." handling
            cleaned_parts.append(part)
        name_str = " ".join(cleaned_parts)

    # Final cleanup for "First ." -> "First" (if it was an artifact)
    name_str = re.sub(r'\s\.(?=\s|$)', '', name_str).strip()  # "James ." -> "James"

    # If name becomes just a period or empty, revert to Unknown Attorney
    if name_str == "." or not name_str:
        return "Unknown Attorney"

    return name_str


def parse_docket_entry(text):
    attorney_name = "Unknown Attorney"
    docket_num_str = None
    versus_str = "Unknown"

    attorney_end_markers_list = [
        r"\s*\d{1,2}:\d{2}-cv-\d{4,5}",
        r"\s*Modified on",
        r"\s*Assigned to",
        r"\s*Assigned",
        r"\s*Case #",
        r"\s*File No\.",
        r"\s*MDL Shell Case Entry",
        r"\s*MDL -",
        r"\s*\([a-z]{2,5},\s*,?\s*\)\s*(?:\.|\(Entered:|\(Main Document|\(Original Filename|\(date\)|\(see shell)",
        r"\s*\(sshe,|\s*\(kcar,|\s*\(cwil,|\s*\(jbin,|\s*\(rhei,|\s*\(bshr,",
        r"\s*\*\*\*",
        r"\s*\(Entered:",
        r"\s*\.$"
    ]
    attorney_end_pattern_str = r"(?:" + "|".join(attorney_end_markers_list) + ")"

    att_match1 = re.search(r"\)\s*([A-Za-zÀ-ÖØ-öø-ÿ,\s.'-]+?)\s*\)\s*" + attorney_end_pattern_str, text)
    if att_match1:
        attorney_name = att_match1.group(1).strip()
    else:
        att_match2 = re.search(r"([A-Za-zÀ-ÖØ-öø-ÿ,\s.'-]+?)\s*\)\s*" + attorney_end_pattern_str, text)
        if att_match2:
            candidate = att_match2.group(1).strip()
            if (' ' in candidate or ',' in candidate) and \
                    len(candidate) > 3 and len(candidate) < 70 and \
                    not any(kw.lower() in candidate.lower() for kw in [
                        "attachments", "summons", "exhibit", "filing fee",
                        "receipt number", "civil cover sheet", "mdl", "f/k/a",
                        "addendum", "et al", "inc.", "llc", "co.", "company",
                        "district", "authority", "county", "division", "proposed",
                        "additional defendants list", "answers", "blank", "corporate disclosure",
                        "original filename", "court name", "state court"
                    ]) and not re.match(r".*#\s*\d+", candidate) and \
                    not candidate.lower().startswith("case ") and \
                    not candidate.lower().startswith("main document") and \
                    not candidate.lower().endswith(" division") and \
                    not candidate.lower().endswith(" company"):
                attorney_name = candidate

    attorney_name = normalize_attorney_name(attorney_name)  # Normalization happens here

    # Docket Number Extraction
    docket_match = re.search(r'\b(\d{1,2}:\d{2}-cv-\d{4,5})\b', text)
    if docket_match:
        docket_num_str = docket_match.group(1)

    # Versus String Extraction
    versus_prefix_pattern = r"COMPLAINT\s+|NOTICE OF REMOVAL\s+|Letter from COMPLAINT\s*(?:.+?\s*Summons\s*)?"
    versus_suffix_pattern = r"\s*(?:\b(?:Charleston|South Carolina|Civil)\s+Division\b|\bMDL\b\s*-|\bCourt Name:|\(\s*Filing fee|\(\s*Attachments|\bFile No\.|\.$|\s+AND JURY DEMAND|\s+MDL\s+\d{4,}|$)"
    versus_search_pattern = rf"^(?:{versus_prefix_pattern})(.+?)\s+vs\s+(.+?)(?:{versus_suffix_pattern})"

    vs_match = re.search(versus_search_pattern, text, re.IGNORECASE)
    if vs_match:
        plaintiff = vs_match.group(1).strip()
        defendant_raw = vs_match.group(2).strip()

        defendant_clean = re.sub(r'\s*f/k/a.*$', '', defendant_raw, flags=re.IGNORECASE).strip()
        defendant_clean = re.sub(r'\s*\(f/k/a.*?\).*$', '', defendant_clean, flags=re.IGNORECASE).strip()
        defendant_clean = re.sub(r',\s*et al\.?$', '', defendant_clean, flags=re.IGNORECASE).strip()
        defendant_clean = re.sub(r'\s*\( Filing fee.*$', '', defendant_clean, flags=re.IGNORECASE).strip()
        defendant_clean = defendant_clean.rstrip('.').strip()

        if defendant_clean.endswith(' ('):
            defendant_clean = defendant_clean[:-2].strip()
        elif defendant_clean.endswith('('):
            defendant_clean = defendant_clean[:-1].strip()

        versus_str = f"{plaintiff} vs {defendant_clean}"
    elif text.startswith("Letter from COMPLAINT"):
        vs_match_letter = re.search(
            r"Letter from COMPLAINT\s*(?:.+?\s*Summons\s*)?(.+?)\s+vs\s+(.+?)(?:\s+Charleston Division|\.$|$)", text,
            re.IGNORECASE)
        if vs_match_letter:
            plaintiff = vs_match_letter.group(1).strip()
            defendant_raw = vs_match_letter.group(2).strip()
            defendant_clean = re.sub(r'\s*The 3M Company.*$', 'The 3M Company', defendant_raw,
                                     flags=re.IGNORECASE).strip()
            defendant_clean = re.sub(r',\s*et al\.?$', '', defendant_clean, flags=re.IGNORECASE).strip()
            if defendant_clean.endswith(' ('):
                defendant_clean = defendant_clean[:-2].strip()
            elif defendant_clean.endswith('('):
                defendant_clean = defendant_clean[:-1].strip()
            versus_str = f"{plaintiff} vs {defendant_clean}"

    return attorney_name, docket_num_str, versus_str


def main():
    downloads_dir = os.path.expanduser("~/Downloads")
    csv_filename = "AFFF-20250529.csv"
    filepath = os.path.join(downloads_dir, csv_filename)

    print(f"Attempting to read file from: {filepath}")

    if not os.path.exists(filepath):
        print(f"Error: File not found at {filepath}.")
        return

    try:
        df = pd.read_csv(filepath)
    except OSError as e:
        print(f"OSError reading CSV file: {e}")
        print("On macOS, ensure Terminal/IDE has Full Disk Access or move the file.")
        return
    except Exception as e:
        print(f"An unexpected error occurred reading CSV file: {e}")
        return

    attorney_filings_map = defaultdict(list)
    all_filings_for_stats = []

    print(f"Processing {len(df)} rows...")
    for index, row in df.iterrows():
        date_str = row.get('Date')
        docket_text = str(row.get('Docket Text', ''))

        if pd.isna(date_str) or not date_str:
            continue

        try:
            filing_date_dt = datetime.strptime(str(date_str).strip(), '%m/%d/%Y')
        except ValueError:
            continue

        filing_date_iso = filing_date_dt.strftime('%Y-%m-%d')

        attorney, docket_num, versus = parse_docket_entry(docket_text)

        is_docket_parsed = docket_num is not None
        docket_num_for_storage = docket_num if is_docket_parsed else f"NO_DOCKET_PARSED_ROW_{index + 2}"

        attorney_filings_map[attorney].append({
            "filing_date": filing_date_iso,
            "docket_num": docket_num_for_storage,
            "versus": versus if versus is not None else "Unknown"
        })

        if is_docket_parsed:
            all_filings_for_stats.append((filing_date_dt, docket_num))

    attorney_summary_stats = []
    for att, filings_list in attorney_filings_map.items():
        unique_dockets_for_attorney = set()
        for filing in filings_list:
            if not filing["docket_num"].startswith("NO_DOCKET_PARSED_ROW_"):
                unique_dockets_for_attorney.add(filing["docket_num"])

        attorney_summary_stats.append({
            "attorney_name": att,
            "total_unique_filings": len(unique_dockets_for_attorney),
            "total_entries_processed": len(filings_list)
        })

    attorney_summary_stats_sorted_alpha = sorted(attorney_summary_stats, key=lambda x: x["attorney_name"])

    console = Console()
    table = Table(title="Attorney Filing Summary", show_lines=True)
    table.add_column("Attorney Name", justify="left", style="cyan", no_wrap=False, min_width=20, max_width=40)
    table.add_column("Total Unique Filings", justify="right", style="magenta")
    table.add_column("Total Entries Processed", justify="right", style="green")

    attorney_summary_for_display = sorted(attorney_summary_stats, key=lambda x: x["total_unique_filings"], reverse=True)

    for item in attorney_summary_for_display:
        table.add_row(item["attorney_name"], str(item["total_unique_filings"]), str(item["total_entries_processed"]))

    console.print("\n--- Attorney Filing Summary (Sorted by Filings Descending) ---")
    console.print(table)

    filings_by_day = defaultdict(set)
    filings_by_week = defaultdict(set)
    filings_by_month = defaultdict(set)

    for date_dt, dn_str in all_filings_for_stats:
        day_key = date_dt.strftime('%Y-%m-%d')
        week_key = date_dt.strftime('%G-W%V')
        month_key = date_dt.strftime('%Y-%m')

        filings_by_day[day_key].add(dn_str)
        filings_by_week[week_key].add(dn_str)
        filings_by_month[month_key].add(dn_str)

    stats_by_day = {day: len(dockets) for day, dockets in sorted(filings_by_day.items())}
    stats_by_week = {week: len(dockets) for week, dockets in sorted(filings_by_week.items())}
    stats_by_month = {month: len(dockets) for month, dockets in sorted(filings_by_month.items())}

    sorted_attorney_filings_details = dict(sorted(attorney_filings_map.items()))

    results = {
        "attorney_filing_summary": attorney_summary_stats_sorted_alpha,
        "attorney_filings_details": sorted_attorney_filings_details,
        "overall_filing_stats": {
            "by_day": stats_by_day,
            "by_week": stats_by_week,
            "by_month": stats_by_month
        }
    }

    output_filename = "parsed_afff_filings_with_summary.json"
    output_filepath = os.path.join(downloads_dir, output_filename)

    print(f"\nAttempting to save JSON results to: {output_filepath}")

    try:
        with open(output_filepath, 'w') as f:
            json.dump(results, f, indent=4)
        print(f"Results successfully saved to {output_filepath}")
    except OSError as e:
        print(f"OSError writing JSON file: {e}")
        try:
            home_output_filepath = os.path.join(os.path.expanduser("~"), output_filename)
            print(f"Attempting fallback save to: {home_output_filepath}")
            with open(home_output_filepath, 'w') as f:
                json.dump(results, f, indent=4)
            print(f"Results successfully saved to {home_output_filepath}")
        except Exception as e_home:
            print(f"Fallback save to home directory also failed: {e_home}")
    except Exception as e:
        print(f"An unexpected error occurred writing JSON file: {e}")


if __name__ == "__main__":
    main()