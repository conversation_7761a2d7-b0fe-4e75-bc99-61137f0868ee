import json
import os
from pprint import pprint
from typing import Tuple

import pandas as pd

from pacer_manager import PacerManager


def summarize_mdl_filings_for_date(date_str: str, pacer_manager: PacerManager) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Compare local JSON files against DynamoDB records for a given date.

    Args:
        date_str: Date in YYYYMMDD format
        pacer_manager: Instance of PacerManager class

    Returns:
        Tuple of two DataFrames:
        - First DataFrame contains records found in both local and DynamoDB
        - Second DataFrame contains records found only in local files
    """
    base_path = f"/Users/<USER>/PycharmProjects/mt_competitive_analysis/data/{date_str}/dockets"

    if not os.path.exists(base_path):
        raise ValueError(f"Directory does not exist: {base_path}")

    local_records = []

    # Read all local JSON files
    for filename in os.listdir(base_path):
        if not filename.endswith(".json"):
            continue

        file_path = os.path.join(base_path, filename)
        try:
            with open(file_path, 'r') as file:
                data = json.load(file)

                # Extract required fields with default values
                record = {
                    'court_id': data.get('court_id', 'NA'),
                    'docket_num': data.get('docket_num', 'NA'),
                    'mdl_num': data.get('mdl_num', 'NA'),
                    'title': data.get('title', 'NA'),
                    'versus': data.get('versus', 'NA'),
                    'is_transferred': bool(data.get('is_transferred', False)),
                    'source': 'local',
                    'filename': filename
                }
                local_records.append(record)

        except json.JSONDecodeError as e:
            print(f"Error reading JSON from {filename}: {e}")

    # Convert local records to DataFrame
    local_df = pd.DataFrame(local_records)

    # Check each local record in DynamoDB
    matched_records = []
    unmatched_records = []

    for _, record in local_df.iterrows():
        dynamo_items = pacer_manager.query_pacer_by_court_id_and_docket_num(
            record['court_id'],
            record['docket_num']
        )

        if dynamo_items:
            # Record exists in DynamoDB
            for item in dynamo_items:
                matched_record = {
                    'court_id': item.get('CourtId', 'NA'),
                    'docket_num': item.get('DocketNum', 'NA'),
                    'mdl_num': item.get('MdlNum', 'NA'),
                    'title': item.get('Title', 'NA'),
                    'versus': item.get('Versus', 'NA'),
                    'is_transferred': bool(item.get('IsTransferred', False)),
                    'source': 'both',
                    'local_filename': record['filename']
                }
                matched_records.append(matched_record)
        else:
            # Record only exists locally
            unmatched_record = record.to_dict()
            unmatched_record['source'] = 'local_only'
            unmatched_records.append(unmatched_record)

    # Create DataFrames for matched and unmatched records
    matched_df = pd.DataFrame(matched_records) if matched_records else pd.DataFrame()
    unmatched_df = pd.DataFrame(unmatched_records) if unmatched_records else pd.DataFrame()

    # Sort DataFrames by court_id and docket_num
    if not matched_df.empty:
        matched_df.sort_values(['court_id', 'docket_num'], inplace=True)
    if not unmatched_df.empty:
        unmatched_df.sort_values(['court_id', 'docket_num'], inplace=True)

    # Add summary statistics
    print(f"\nSummary for {date_str}:")
    print(f"Total local records: {len(local_df)}")
    print(f"Records found in DynamoDB: {len(matched_df)}")
    print(f"Records only in local files: {len(unmatched_df)}")

    return matched_df, unmatched_df


def main():
    from config import load_config
    config = load_config('01/01/70')
    pacer_db = PacerManager(config)
    response = summarize_mdl_filings_for_date('20250102', pacer_db)
    pprint(response)


if __name__ == "__main__":
    main()
