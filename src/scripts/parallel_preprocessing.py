#!/usr/bin/env python
"""Parallel preprocessing for NER and embeddings with progress tracking"""

import asyncio
import concurrent.futures
from typing import List, Dict, Any, Tuple
import threading
from rich.progress import Progress, SpinnerColumn, BarColumn, TextColumn, TimeRemainingColumn, TimeElapsedColumn
from rich.console import Console
import time
import psutil
import logging

logger = logging.getLogger(__name__)
console = Console()


class ParallelPreprocessor:
    """Handles parallel preprocessing of NER and embeddings with memory management"""
    
    def __init__(self, hybrid_classifier, max_workers: int = None):
        self.classifier = hybrid_classifier
        self.forced_max_workers = max_workers
        self.max_parallel_tasks = self._determine_safe_parallelism()
        
    def _determine_safe_parallelism(self) -> int:
        """Determine safe level of parallelism based on available resources"""
        # If max_workers is explicitly set, use it
        if self.forced_max_workers is not None:
            logger.info(f"Using explicitly set max_workers: {self.forced_max_workers}")
            return self.forced_max_workers
            
        cpu_count = psutil.cpu_count(logical=False) or 1
        available_memory_gb = psutil.virtual_memory().available / (1024**3)
        
        # Conservative approach based on memory
        if available_memory_gb < 8:
            max_parallel = 1  # Sequential only
            logger.warning(f"Low memory ({available_memory_gb:.1f}GB), using sequential processing")
        elif available_memory_gb < 16:
            max_parallel = min(2, cpu_count)
            logger.info(f"Moderate memory ({available_memory_gb:.1f}GB), using {max_parallel} parallel tasks")
        else:
            max_parallel = min(4, cpu_count)  # Cap at 4 to avoid diminishing returns
            logger.info(f"Sufficient memory ({available_memory_gb:.1f}GB), using {max_parallel} parallel tasks")
            
        return max_parallel
    
    def preprocess_with_progress(self, ads_data_list: List[Dict[str, Any]]) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        Run NER and embedding preprocessing in parallel with progress bars
        Returns: (embedding_results, ner_results)
        """
        num_ads = len(ads_data_list)
        
        # Prepare texts for processing
        texts_for_processing = []
        valid_indices = []
        
        for idx, ad_data in enumerate(ads_data_list):
            raw_text, _ = self.classifier._prepare_ad_text_for_processing(
                ad_data, self.classifier.text_processing_fields, 
                self.classifier.invalid_summary_strings
            )
            if raw_text and raw_text.strip():
                texts_for_processing.append((idx, raw_text, ad_data))
                valid_indices.append(idx)
        
        num_valid_texts = len(texts_for_processing)
        
        if num_valid_texts == 0:
            console.print("[yellow]No valid texts to process[/yellow]")
            return {}, {}
        
        console.print(f"[cyan]Processing {num_valid_texts} valid texts from {num_ads} ads[/cyan]")
        
        # Results storage
        embedding_results = {}
        ner_results = {}
        
        # Thread-safe progress tracking
        embedding_progress = 0
        ner_progress = 0
        progress_lock = threading.Lock()
        
        def update_embedding_progress(count: int):
            nonlocal embedding_progress
            with progress_lock:
                embedding_progress += count
        
        def update_ner_progress(count: int):
            nonlocal ner_progress
            with progress_lock:
                ner_progress += count
        
        # Process in parallel with progress bars
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TextColumn("({task.completed}/{task.total})"),
            TimeElapsedColumn(),
            TimeRemainingColumn(),
            console=console
        ) as progress:
            
            # Create progress tasks
            overall_task = progress.add_task("[bold]Overall Progress", total=num_valid_texts * 2)
            embedding_task = progress.add_task("[cyan]Embeddings", total=num_valid_texts)
            ner_task = progress.add_task("[green]NER Processing", total=num_valid_texts)
            
            # Force truly sequential processing when max_workers=1
            if self.max_parallel_tasks == 1 or self.forced_max_workers == 1:
                # Sequential processing when max_workers=1
                console.print("[yellow]Max workers = 1: Using truly sequential processing (embeddings first, then NER)[/yellow]")
                
                # Process embeddings FIRST
                if self.classifier.embedder:
                    console.print("[cyan]Processing embeddings sequentially...[/cyan]")
                    for idx, text, ad_data in texts_for_processing:
                        try:
                            embedding = self.classifier.embedder.encode(text, show_progress=False)
                            embedding_results[idx] = embedding
                            progress.update(embedding_task, advance=1)
                            progress.update(overall_task, advance=1)
                        except Exception as e:
                            logger.error(f"Embedding error for ad {idx}: {e}")
                
                # Process NER SECOND
                if self.classifier.ner_model:
                    console.print("[green]Processing NER sequentially...[/green]")
                    for idx, text, ad_data in texts_for_processing:
                        try:
                            entities = self.classifier._get_or_compute_ner_entities(text, ad_data)
                            ner_results[idx] = entities
                            progress.update(ner_task, advance=1)
                            progress.update(overall_task, advance=1)
                        except Exception as e:
                            logger.error(f"NER error for ad {idx}: {e}")
                            
            elif self.max_parallel_tasks > 1 and self.classifier.embedder and self.classifier.ner_model:
                # Parallel processing
                # Use 2 workers if we have at least 2 parallel tasks allowed (one for NER, one for embeddings)
                executor_workers = min(2, self.max_parallel_tasks)
                logger.info(f"Using ThreadPoolExecutor with {executor_workers} workers for parallel NER and embeddings")
                with concurrent.futures.ThreadPoolExecutor(max_workers=executor_workers) as executor:
                    
                    # Embedding processing function
                    def process_embeddings_batch(batch_data):
                        batch_texts = [text for _, text, _ in batch_data]
                        try:
                            # Use the embedder's batch processing
                            embeddings = self.classifier.embedder.encode(
                                batch_texts, 
                                batch_size=min(64, len(batch_texts)),
                                show_progress=False  # We have our own progress
                            )
                            
                            # Store results
                            for (idx, text, _), embedding in zip(batch_data, embeddings):
                                embedding_results[idx] = embedding
                            
                            # Update progress
                            update_embedding_progress(len(batch_data))
                            progress.update(embedding_task, advance=len(batch_data))
                            progress.update(overall_task, advance=len(batch_data))
                            
                        except Exception as e:
                            logger.error(f"Error in embedding batch: {e}")
                    
                    # NER processing function
                    def process_ner_batch(batch_data):
                        for idx, text, ad_data in batch_data:
                            try:
                                # Use existing NER processing
                                entities = self.classifier._get_or_compute_ner_entities(text, ad_data)
                                ner_results[idx] = entities
                                
                                # Update progress
                                update_ner_progress(1)
                                progress.update(ner_task, advance=1)
                                progress.update(overall_task, advance=1)
                                
                            except Exception as e:
                                logger.error(f"Error in NER processing for ad {idx}: {e}")
                    
                    # Split data into batches
                    batch_size = max(10, num_valid_texts // 20)  # 5% chunks
                    batches = [texts_for_processing[i:i + batch_size] 
                              for i in range(0, num_valid_texts, batch_size)]
                    
                    # Submit tasks
                    embedding_futures = []
                    ner_futures = []
                    
                    for batch in batches:
                        # Embeddings can be processed in larger batches
                        embedding_futures.append(
                            executor.submit(process_embeddings_batch, batch)
                        )
                        
                        # NER needs individual processing
                        ner_futures.append(
                            executor.submit(process_ner_batch, batch)
                        )
                    
                    # Wait for completion with timeout
                    timeout = 300 * (num_valid_texts / 1000)  # 5 minutes per 1000 texts
                    
                    for future in concurrent.futures.as_completed(
                        embedding_futures + ner_futures, timeout=timeout
                    ):
                        try:
                            future.result()
                        except Exception as e:
                            logger.error(f"Task failed: {e}")
                            
            else:
                # Sequential processing (low memory or missing components)
                console.print("[yellow]Using sequential processing due to resource constraints[/yellow]")
                
                # Process embeddings
                if self.classifier.embedder:
                    for idx, text, ad_data in texts_for_processing:
                        try:
                            embedding = self.classifier.embedder.encode(text, show_progress=False)
                            embedding_results[idx] = embedding
                            progress.update(embedding_task, advance=1)
                            progress.update(overall_task, advance=1)
                        except Exception as e:
                            logger.error(f"Embedding error for ad {idx}: {e}")
                
                # Process NER
                if self.classifier.ner_model:
                    for idx, text, ad_data in texts_for_processing:
                        try:
                            entities = self.classifier._get_or_compute_ner_entities(text, ad_data)
                            ner_results[idx] = entities
                            progress.update(ner_task, advance=1)
                            progress.update(overall_task, advance=1)
                        except Exception as e:
                            logger.error(f"NER error for ad {idx}: {e}")
            
            # Final statistics
            progress.console.print(f"\n[green]✓ Completed:[/green]")
            progress.console.print(f"  • Embeddings: {len(embedding_results)}/{num_valid_texts}")
            progress.console.print(f"  • NER: {len(ner_results)}/{num_valid_texts}")
            
            # Memory usage report
            memory_after = psutil.virtual_memory()
            progress.console.print(f"  • Memory usage: {memory_after.percent:.1f}% "
                                 f"({memory_after.available / (1024**3):.1f}GB available)")
        
        return embedding_results, ner_results


def parallel_preprocess_ads(classifier, ads_data_list: List[Dict[str, Any]], max_workers: int = None) -> None:
    """Main entry point for parallel preprocessing"""
    preprocessor = ParallelPreprocessor(classifier, max_workers=max_workers)
    
    start_time = time.time()
    embedding_results, ner_results = preprocessor.preprocess_with_progress(ads_data_list)
    elapsed = time.time() - start_time
    
    console.print(f"\n[bold green]Preprocessing completed in {elapsed:.1f} seconds[/bold green]")
    
    if len(ads_data_list) > 0:
        rate = len(ads_data_list) / elapsed
        console.print(f"[cyan]Processing rate: {rate:.1f} ads/second[/cyan]")
    
    # Store results back in classifier caches if needed
    # The caches should already be updated by the individual processing functions
    
    return embedding_results, ner_results