#!/usr/bin/env python3
"""
Master script to calculate and analyze AFFF (MDL 2873) filing metrics,
including docket counts and plaintiff numbers, fetching data from the database
and providing interactive analysis options.
"""

import os
import sys
import argparse
from datetime import datetime, date
from typing import Optional, List, Dict, Any, Tuple
from collections import defaultdict
import json
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
import seaborn as sns
from datetime import datetime, date, timedelta

# Import the project root handling function
try:
    from utils import get_project_root
except ImportError:
    # Fall back to direct import if utils.py is not available
    try:
        from src.lib.config import PROJECT_ROOT
    except ImportError:
        # If can't import, try to load from environment or use a fallback
        try:
            from dotenv import load_dotenv
            load_dotenv()
            PROJECT_ROOT = os.getenv('PROJECT_ROOT', os.path.expanduser('~/PycharmProjects/lexgenius'))
        except ImportError:
            PROJECT_ROOT = os.path.expanduser('~/PycharmProjects/lexgenius')
else:
    PROJECT_ROOT = get_project_root()

# Add project root to sys.path to allow importing src modules
# Assumes script is located at project_root/scripts/calculate_afff_metrics.py
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

try:
    from rich.console import Console
    from rich.table import Table
    from rich.prompt import Prompt
    from rich.panel import Panel
    from src.lib.pacer_manager import PacerManager
    from src.lib.config import load_config
except ImportError as e:
    print(f"Error importing necessary libraries.")
    print(f"Please ensure 'rich', 'pandas', 'matplotlib', 'seaborn' are installed (`pip install rich pandas matplotlib seaborn`)")
    print(f"Also ensure the project structure allows importing `src.lib.pacer_manager` and `src.lib.config`.")
    print(f"Import Error: {e}")
    sys.exit(1)

# --- Configuration and Constants ---
console = Console()

# Load configuration using the provided function
try:
    # Note: The '01/01/70' date string seems unusual for config loading,
    # but keeping it as per the original calculate_afff_num_plaintiffs.py
    config = load_config('01/01/70')
    console.print("[dim]Configuration loaded successfully.[/dim]")
except Exception as e:
    console.print(f"[bold red]Error loading configuration: {e}[/bold red]")
    # Configuration is likely essential for PacerManager
    sys.exit(1)

MDL_NUM = '2873'  # AFFF MDL Number

# --- Helper Functions ---

def parse_arguments():
    """Parses command-line arguments."""
    parser = argparse.ArgumentParser(description="Calculate and analyze AFFF (MDL 2873) filing metrics.")
    parser.add_argument("--start-date", required=True, help="Start date for analysis in YYYYMMDD format.")
    parser.add_argument("--end-date", default=None, help="End date for analysis in YYYDMMDD format (optional, defaults to today).")
    parser.add_argument("--local", action="store_true", help="Use local DynamoDB instance.")
    parser.add_argument("--port", type=int, default=8000, help="Port for local DynamoDB instance (default: 8000).")
    parser.add_argument("--chart-output", default="afff_filings_chart.png", help="Output path for the daily filing chart (default: afff_filings_chart.png).")
    return parser.parse_args()

def format_date_yyyymmdd(date_str: str) -> Optional[str]:
    """Validate and format date string to YYYYMMDD."""
    try:
        datetime.strptime(date_str, '%Y%m%d')
        return date_str # Already in correct format if parsing succeeded
    except ValueError:
        return None

def format_date_display(date_yyyymmdd: str) -> str:
    """Format YYYYMMDD string for display (e.g., YYYY-MM-DD)."""
    try:
        return datetime.strptime(date_yyyymmdd, '%Y%m%d').strftime('%Y-%m-%d')
    except ValueError:
        return date_yyyymmdd # Return original if formatting fails

def process_dockets(items: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]], List[Dict[str, Any]]]:
    """
    Processes the raw docket items:
    1. Converts TransferredIn to boolean.
    2. Identifies and separates transferred-in duplicates based on Transferor info.
    3. Normalizes NumPlaintiffs ('0' or None becomes 1) and calculates Int version for summation.

    Returns:
        Tuple: (initial_items_processed, filtered_items, dropped_items)
    """
    initial_items_processed = []
    # Using sets for faster lookup of keys and indices
    potential_transfers_keys = set() # Keys: (TransferorCourtId, TransferorDocketNum)
    original_dockets_keys = set()    # Keys: (CourtId, DocketNum)
    original_dockets_key_to_index = {} # Map key to index in initial_items_processed
    dropped_indices = set() # Indices in initial_items_processed to drop
    dropped_items = [] # List of items that were dropped

    # First pass: Process TransferredIn, normalize NumPlaintiffs, calculate Int version, and collect keys/indices
    for index, item in enumerate(items):
        processed_item = item.copy() # Work on a copy

        # Convert TransferredIn (Handles potential variations like '1', 1, True, 'True')
        transferred_in_val = processed_item.get('TransferredIn')
        processed_item['TransferredIn'] = bool(transferred_in_val and str(transferred_in_val).lower() in ('1', 'true'))

        # Normalize NumPlaintiffs ('0' or None becomes '1') and calculate NumPlaintiffsInt
        num_plaintiffs_raw = processed_item.get('NumPlaintiffs')
        if num_plaintiffs_raw is None or str(num_plaintiffs_raw).strip() == '' or str(num_plaintiffs_raw).strip() == '0':
            processed_item['NumPlaintiffsDisplay'] = '1' # For display if needed
            processed_item['NumPlaintiffsInt'] = 1
        else:
            # Keep original for 'NumPlaintiffsDisplay' but try to convert to int
            processed_item['NumPlaintiffsDisplay'] = str(num_plaintiffs_raw)
            try:
                processed_item['NumPlaintiffsInt'] = int(num_plaintiffs_raw)
            except (ValueError, TypeError):
                console.print(f"[yellow]Warning: Could not convert NumPlaintiffs '{num_plaintiffs_raw}' to int for docket {processed_item.get('CourtId')}-{processed_item.get('DocketNum')}. Defaulting to 1 for summation.[/yellow]")
                processed_item['NumPlaintiffsInt'] = 1


        initial_items_processed.append(processed_item)

        # Collect potential transfer and original docket keys
        transferor_court_id = processed_item.get('TransferorCourtId')
        transferor_docket_num = processed_item.get('TransferorDocketNum')
        court_id = processed_item.get('CourtId')
        docket_num = processed_item.get('DocketNum')

        if transferor_court_id and transferor_docket_num:
            transfer_key = (str(transferor_court_id), str(transferor_docket_num))
            potential_transfers_keys.add(transfer_key)

        if court_id and docket_num:
            original_key = (str(court_id), str(docket_num))
            original_dockets_keys.add(original_key)
            original_dockets_key_to_index[original_key] = index


    # Second pass: Identify items to drop (originals that have corresponding transfers)
    # An original docket is dropped if its key matches a transferor key in the dataset.
    for original_key in original_dockets_keys:
        if original_key in potential_transfers_keys:
            # This original docket key appears as a TransferorCourtId/TransferorDocketNum
            # in *some* other item in the dataset. We consider this original docket
            # as a "source" that was transferred out, and we want to count it
            # only where it landed (the item with the matching Transferor info).
            # So, we drop the original item from our count.
            original_index_to_drop = original_dockets_key_to_index[original_key]
            if original_index_to_drop not in dropped_indices:
                 dropped_indices.add(original_index_to_drop)
                 # Append the item as it was processed in the first pass (includes NumPlaintiffsInt)
                 dropped_items.append(initial_items_processed[original_index_to_drop])
                 # console.print(f"[dim]DEBUG: Marking original {original_key} at index {original_index_to_drop} for drop (matches a transfer key in the dataset).[/dim]") # Debug

    # Third pass: Create filtered list excluding dropped indices
    filtered_items = [item for index, item in enumerate(initial_items_processed) if index not in dropped_indices]

    # Sort lists for consistent viewing (optional)
    initial_items_processed.sort(key=lambda x: (x.get('FilingDate', ''), x.get('CourtId', ''), x.get('DocketNum', '')))
    filtered_items.sort(key=lambda x: (x.get('FilingDate', ''), x.get('CourtId', ''), x.get('DocketNum', '')))
    dropped_items.sort(key=lambda x: (x.get('FilingDate', ''), x.get('CourtId', ''), x.get('DocketNum', '')))

    return initial_items_processed, filtered_items, dropped_items


def calculate_and_display_sum(console: Console, items: List[Dict[str, Any]], description: str):
    """Calculates and displays the sum of NumPlaintiffsInt for a list of items."""
    if not items:
        console.print(f"[bold yellow]No items to sum for '{description}'.[/bold yellow]")
        return
    total_plaintiffs = sum(item.get('NumPlaintiffsInt', 0) for item in items)
    console.print(f"\n[bold green]Total Plaintiffs ({description}): {total_plaintiffs}[/bold green]\n")


def display_table(console: Console, title: str, items: List[Dict[str, Any]], columns: Optional[List[str]] = None):
    """Displays a list of dictionaries as a table using Rich."""
    if not items:
        console.print(f"[bold yellow]No items to display for '{title}'.[/bold yellow]")
        return

    table = Table(title=title, show_header=True, header_style="bold magenta")

    # Define default columns or use provided ones
    if columns:
        display_columns = columns
    else:
         # Attempt to determine columns from the first item if not specified
         if items:
             display_columns = list(items[0].keys())
         else:
             console.print(f"[bold yellow]Cannot determine columns for empty list for '{title}'.[/bold yellow]")
             return

    # Ensure 'NumPlaintiffsDisplay' is used if NumPlaintiffs is in the list
    if 'NumPlaintiffs' in display_columns and 'NumPlaintiffsDisplay' in items[0]:
        # Replace 'NumPlaintiffs' with 'NumPlaintiffsDisplay' for better normalized output
        display_columns = [col if col != 'NumPlaintiffs' else 'NumPlaintiffsDisplay' for col in display_columns]
        if 'NumPlaintiffsInt' in display_columns: # Also remove Int version if main was requested
             display_columns.remove('NumPlaintiffsInt')

    # Add columns to table
    for col in display_columns:
        # Use a friendly name for NumPlaintiffsDisplay column header
        header_name = "NumPlaintiffs" if col == 'NumPlaintiffsDisplay' else col
        # Right align numeric/boolean columns potentially
        justify = "right" if col in ('NumPlaintiffsDisplay', 'NumPlaintiffsInt', 'TransferredIn') else "left"
        style = "green" if col in ('NumPlaintiffsDisplay', 'NumPlaintiffsInt') else "cyan" if col == 'FilingDate' else None
        table.add_column(header_name, justify=justify, style=style)


    for item in items:
        row_data = []
        for col in display_columns:
            # Use NumPlaintiffsDisplay value if showing plaintiffs and it exists
            if col == 'NumPlaintiffsDisplay' and 'NumPlaintiffsDisplay' in item:
                 value = item['NumPlaintiffsDisplay']
            else:
                 value = item.get(col)

            # Convert non-string types for display
            if isinstance(value, (dict, list)):
                row_data.append(json.dumps(value, indent=2))
            elif isinstance(value, bool):
                 row_data.append("[green]True[/green]" if value else "[red]False[/red]")
            elif value is None:
                 row_data.append("[dim]None[/dim]")
            else:
                 row_data.append(str(value))
        table.add_row(*row_data)

    console.print(table)


def display_plaintiffs_over_time(console: Console, items: List[Dict[str, Any]]):
    """Displays FilingDate and NumPlaintiffsInt for each item."""
    if not items:
        console.print("[bold yellow]No items to display plaintiff counts for.[/bold yellow]")
        return

    table = Table(title="Plaintiff Counts per Docket (Filtered Data)", show_header=True, header_style="bold magenta")
    table.add_column("FilingDate", style="cyan")
    table.add_column("CourtId", style="dim")
    table.add_column("DocketNum", style="dim")
    table.add_column("NumPlaintiffs", justify="right", style="green") # Use Int value for accuracy

    # Sort by date for clarity
    items_sorted = sorted(items, key=lambda x: x.get('FilingDate', ''))

    for item in items_sorted:
        table.add_row(
            item.get('FilingDate', '[dim]N/A[/dim]'),
            item.get('CourtId', '[dim]N/A[/dim]'),
            item.get('DocketNum', '[dim]N/A[/dim]'),
            str(item.get('NumPlaintiffsInt', '[dim]N/A[/dim]')) # Use the integer version
        )
    console.print(table)


def display_plaintiff_sum_range(console: Console, items: List[Dict[str, Any]], start_date_fmt: str, end_date_fmt: str):
    """Calculates and displays the sum of NumPlaintiffsInt for the filtered set within the date range."""
    if not items:
        console.print(f"[bold yellow]No items to sum plaintiffs for date range {format_date_display(start_date_fmt)} to {format_date_display(end_date_fmt)}.[/bold yellow]")
        return

    total_plaintiffs = sum(item.get('NumPlaintiffsInt', 0) for item in items) # Use the integer version
    console.print(f"\n[bold green]Total Plaintiffs (Filtered Data) for Date Range {format_date_display(start_date_fmt)} to {format_date_display(end_date_fmt)}: {total_plaintiffs}[/bold green]\n")


def prepare_daily_docket_data(items: List[Dict[str, Any]]) -> pd.DataFrame:
    """
    Prepare daily docket counts and cumulative counts from item list using pandas.
    Operates on the FILTERED list for consistency.
    """
    if not items:
        return pd.DataFrame(columns=['Date', 'Daily_Filings', 'Cumulative_Filings'])

    console.print("[yellow]Preparing daily docket count data...[/yellow]")
    # Use pandas for easy grouping and cumulative sum
    df = pd.DataFrame(items)

    # Ensure FilingDate is datetime and handle potential errors/missing dates
    # Assuming FilingDate is in YYYYMMDD format from the database
    df['Date'] = pd.to_datetime(df.get('FilingDate'), format='%Y%m%d', errors='coerce')
    df = df.dropna(subset=['Date']) # Drop items with unparseable dates

    if df.empty:
         console.print("[yellow]No valid dates found in items for daily count calculation.[/yellow]")
         return pd.DataFrame(columns=['Date', 'Daily_Filings', 'Cumulative_Filings'])

    # Group by date and count
    daily_counts = df.groupby(df['Date'].dt.date).size().reset_index(name='Daily_Filings')

    # Ensure the Date column in daily_counts is datetime for merging/sorting/plotting
    daily_counts['Date'] = pd.to_datetime(daily_counts['Date'])

    # Sort by date
    daily_counts = daily_counts.sort_values('Date').reset_index(drop=True)

    # Calculate cumulative filings
    daily_counts['Cumulative_Filings'] = daily_counts['Daily_Filings'].cumsum()

    console.print(f"[green]Prepared daily counts for {len(daily_counts)} unique dates.[/green]")
    return daily_counts


def display_daily_docket_counts(console: Console, df_daily: pd.DataFrame, title: str):
     """Displays daily and cumulative docket counts using rich."""
     if df_daily.empty:
         console.print(f"[bold yellow]No daily docket counts to display for '{title}'.[/bold yellow]")
         return

     table = Table(title=title, show_header=True, header_style="bold magenta")
     table.add_column("Filing Date", style="cyan")
     table.add_column("Daily Filings", justify="right", style="green")
     table.add_column("Cumulative Filings", justify="right", style="blue")

     total_filings = df_daily['Daily_Filings'].sum()

     # Display summary panel above the table
     console.print(Panel.fit(
         f"[bold blue]Total Dockets in Period: {total_filings}",
         title="Daily Docket Summary (Filtered Data)",
         border_style="blue"
     ))

     for index, row in df_daily.iterrows():
         table.add_row(
             row['Date'].strftime("%Y-%m-%d"),
             str(row['Daily_Filings']),
             str(row['Cumulative_Filings'])
         )

     console.print(table)


def create_dual_axis_chart(console: Console, df_daily: pd.DataFrame, output_path: str):
    """Create a dual-axis chart with cumulative line and daily filing bars."""
    if df_daily.empty:
         console.print("[bold yellow]No data to create chart.[/bold yellow]")
         return

    console.print(f"[yellow]Creating chart and saving to {output_path}...[/yellow]")
    # Set style
    plt.style.use('seaborn-v0_8-whitegrid')
    sns.set_style("whitegrid", {'grid.color': '.9'})

    # Create figure and axis
    fig, ax1 = plt.subplots(figsize=(12, 6))

    # Plot cumulative line on primary y-axis
    color1 = '#115197'  # Dark blue
    ax1.plot(df_daily['Date'], df_daily['Cumulative_Filings'],
                   color=color1, linewidth=2, zorder=2, label='Cumulative Filings')
    ax1.set_ylabel('Cumulative Filings', color=color1)
    ax1.tick_params(axis='y', labelcolor=color1)
    # REMOVE ha='right' from here. Let autofmt_xdate handle alignment.
    ax1.tick_params(axis='x', rotation=45) # Rotate x-axis labels using tick_params


    # Create secondary y-axis for daily filings
    ax2 = ax1.twinx()
    color2 = '#C4C4C4'  # Light gray

    # Use plt.bar on ax2, manually calculating width.
    # Using pandas plot.bar can sometimes be tricky with dual axes and specific customizations.
    # The original code's attempt to use plt.bar manually after clearing the pandas plot.bar
    # was on the right track for legend handling across axes.
    # Let's refine the manual bar plotting for clarity.

    # Calculate width based on date differences, ensuring it's not zero or negative
    # If dates are very sparse, width might need manual tuning.
    # For simplicity, let's pick a default width that usually looks okay.
    bar_width = 0.8 # Experiment with this value if bars are too wide or too thin

    # Ensure dates are numerical positions for plotting bars accurately,
    # but let the axis formatter handle the date display.
    x_positions = df_daily['Date'] # Matplotlib handles datetime objects directly for plotting

    bars = ax2.bar(x_positions, df_daily['Daily_Filings'],
                   color=color2, alpha=0.7, zorder=1, label='Daily Filings', width=bar_width)

    ax2.set_ylabel('Daily Filings')
    ax2.tick_params(axis='y')

    # Add legend - need to manually handle since they are on different axes
    lines, labels = ax1.get_legend_handles_labels()
    bars_handles, bar_labels = ax2.get_legend_handles_labels()

    # Combine legends
    ax1.legend(lines + bars_handles, labels + bar_labels, loc='upper left')

    # Customize x-axis on the primary axis (ax1) as ax2 shares it
    # Use a more robust date locator and formatter
    ax1.xaxis.set_major_locator(MaxNLocator(nbins=10)) # Auto number of ticks, max 10
    ax1.xaxis.set_major_formatter(plt.matplotlib.dates.DateFormatter('%Y-%m-%d')) # Format dates nicely

    # Use autofmt_xdate AFTER setting formatter and locator, as it adjusts rotation AND alignment
    fig.autofmt_xdate() # Auto format date labels to prevent overlap and set alignment

    # Set title and labels
    title_range = f"{df_daily['Date'].min().strftime('%Y-%m-%d')} to {df_daily['Date'].max().strftime('%Y-%m-%d')}" if not df_daily.empty else "No Data"
    plt.title(f'AFFF MDL Filing Trends\n({title_range})', pad=20, fontsize=14, fontweight='bold')
    ax1.set_xlabel('Filing Date') # Set xlabel on the primary axis


    # Add grid lines only for primary axis or customize
    ax1.grid(True, which='both', linestyle='--', linewidth=0.5)
    ax2.grid(False) # Disable secondary grid to avoid clutter

    # Adjust layout - often helps after autofmt_xdate
    fig.tight_layout()


    # Save plot
    try:
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        console.print(f"[green]Chart saved successfully to {output_path}[/green]")
    except Exception as e:
         console.print(f"[red]Error saving chart: {str(e)}[/red]")
    finally:
         plt.close(fig) # Close figure to free memory


# --- Main execution function ---
def analyze_local_json_filings(console: Console, start_date_str: str, end_date_str: str) -> None:
    """Analyze local MDL 2873 JSON filings from a date range and display results."""

    try:
        start_dt = datetime.strptime(start_date_str, '%Y%m%d')
        end_dt = datetime.strptime(end_date_str, '%Y%m%d')
    except ValueError:
        console.print("[red]Error: Invalid date format provided to analyze_local_json_filings.[/red]")
        return

    # Initialize aggregate counters
    total_transferred = {"cases": 0, "plaintiffs": 0}
    total_pending_cto = {"cases": 0, "plaintiffs": 0}
    total_direct = {"cases": 0, "plaintiffs": 0}

    # Track filenames for each category
    all_transferred_files = []
    all_pending_files = []
    all_direct_files = []

    processed_dates_count = 0

    current_dt = start_dt
    while current_dt <= end_dt:
        current_date_loop_str = current_dt.strftime('%Y%m%d')
        # Construct path using PROJECT_ROOT
        base_path = os.path.join(PROJECT_ROOT, "data", current_date_loop_str, "dockets")

        if not os.path.exists(base_path):
            # Optionally log if a specific date's directory is missing
            # console.print(f"[dim]Info: Directory not found for date {current_date_loop_str}: {base_path}[/dim]")
            current_dt += timedelta(days=1)
            continue

        processed_dates_count += 1
        # console.print(f"[dim]Processing local JSON files for {current_date_loop_str}...[/dim]")

        for filename in os.listdir(base_path):
            if not filename.endswith('.json'):
                continue

            file_path = os.path.join(base_path, filename)

            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)

                if data.get('mdl_num') != MDL_NUM:  # Use global MDL_NUM constant
                    continue

                num_plaintiffs_raw = data.get('num_plaintiffs')
                if num_plaintiffs_raw is None or str(num_plaintiffs_raw).strip() == '' or str(
                        num_plaintiffs_raw).strip() == '0':
                    num_plaintiffs = 1
                else:
                    try:
                        num_plaintiffs = int(num_plaintiffs_raw)
                        if num_plaintiffs < 1:  # Ensure at least 1 plaintiff
                            num_plaintiffs = 1
                    except (ValueError, TypeError):
                        console.print(
                            f"[yellow]Warning: Could not convert num_plaintiffs '{num_plaintiffs_raw}' to int for local file {current_date_loop_str}/{filename}. Defaulting to 1.[/yellow]")
                        num_plaintiffs = 1

                base_name = filename.replace('.json', '')
                file_identifier = f"{current_date_loop_str}/{base_name}"

                if data.get('transferred_in'):
                    total_transferred["cases"] += 1
                    total_transferred["plaintiffs"] += num_plaintiffs
                    all_transferred_files.append(file_identifier)
                elif data.get('pending_cto'):
                    total_pending_cto["cases"] += 1
                    total_pending_cto["plaintiffs"] += num_plaintiffs
                    all_pending_files.append(file_identifier)
                else:
                    total_direct["cases"] += 1
                    total_direct["plaintiffs"] += num_plaintiffs
                    all_direct_files.append(file_identifier)

            except Exception as e:
                console.print(
                    f"[red]Error processing local file {filename} for date {current_date_loop_str}: {str(e)}[/red]")

        current_dt += timedelta(days=1)

    if processed_dates_count == 0:
        console.print(
            f"[yellow]No local JSON data found for the date range {format_date_display(start_date_str)} to {format_date_display(end_date_str)}.[/yellow]")
        console.print(
            f"[dim]Checked base path pattern: /Users/<USER>/PycharmProjects/lexgenius/data/YYYYMMDD/dockets[/dim]")
        return

    table_title = (
        f"Local MDL {MDL_NUM} JSON Filing Analysis for {format_date_display(start_date_str)} to {format_date_display(end_date_str)}")
    table = Table(
        title=table_title,
        show_header=True,
        header_style="bold magenta"
    )

    table.add_column("Category", style="cyan")
    table.add_column("Cases", justify="right", style="green")
    table.add_column("Total Plaintiffs", justify="right", style="yellow")

    table.add_row(
        "Transferred In (Local JSON)",
        str(total_transferred["cases"]),
        str(total_transferred["plaintiffs"])
    )
    table.add_row(
        "Pending CTO (Local JSON)",
        str(total_pending_cto["cases"]),
        str(total_pending_cto["plaintiffs"])
    )
    table.add_row(
        "Direct Filings (Local JSON)",
        str(total_direct["cases"]),
        str(total_direct["plaintiffs"])
    )

    grand_total_cases = total_transferred["cases"] + total_pending_cto["cases"] + total_direct["cases"]
    grand_total_plaintiffs = total_transferred["plaintiffs"] + total_pending_cto["plaintiffs"] + total_direct[
        "plaintiffs"]

    table.add_row(
        "Total (Local JSON)",
        str(grand_total_cases),
        str(grand_total_plaintiffs)
    )

    console.print("\n")
    console.print(Panel.fit("Local MDL 2873 JSON Analysis Results", style="bold blue"))
    console.print(table)

    if all_transferred_files:
        console.print("\n[bold cyan]Transferred In Files (Local JSON):[/bold cyan]")
        console.print(", ".join(sorted(list(set(all_transferred_files)))))

    if all_pending_files:
        console.print("\n[bold cyan]Pending CTO Files (Local JSON):[/bold cyan]")
        console.print(", ".join(sorted(list(set(all_pending_files)))))

    if all_direct_files:
        console.print("\n[bold cyan]Direct Filing Files (Local JSON):[/bold cyan]")
        console.print(", ".join(sorted(list(set(all_direct_files)))))


def main():
    args = parse_arguments()
    # console is already a global variable in the script

    start_date_fmt = format_date_yyyymmdd(args.start_date)
    if not start_date_fmt:
        console.print(f"[bold red]Error: Invalid start date format. Please use YYYYMMDD.[/bold red]")
        sys.exit(1)

    if args.end_date:
        end_date_fmt = format_date_yyyymmdd(args.end_date)
        if not end_date_fmt:
            console.print(f"[bold red]Error: Invalid end date format. Please use YYYYMMDD.[/bold red]")
            sys.exit(1)
    else:
        end_date_fmt = date.today().strftime('%Y%m%d')
        console.print(f"[dim]End date not provided, using today: {format_date_display(end_date_fmt)}[/dim]")

    if start_date_fmt > end_date_fmt:
        console.print(
            f"[bold red]Error: Start date ({format_date_display(start_date_fmt)}) cannot be after end date ({format_date_display(end_date_fmt)}).[/bold red]")
        sys.exit(1)

    console.print(
        f"[bold blue]Analyzing AFFF (MDL {MDL_NUM}) Filings from {format_date_display(start_date_fmt)} to {format_date_display(end_date_fmt)}[/bold blue]")
    console.print(f"Attempting to initialize PacerManager (Local: {args.local}, Port: {args.port})...")

    try:
        pacer_manager = PacerManager(config=config, use_local=args.local, local_port=args.port)
        console.print("[green]PacerManager initialized successfully.[/green]")
    except Exception as e:
        console.print(f"[bold red]Error initializing PacerManager: {e}[/bold red]")
        console.print_exception(show_locals=False)
        sys.exit(1)

    console.print(f"[yellow]Fetching AFFF ({MDL_NUM}) dockets from database...[/yellow]")
    try:
        raw_items = pacer_manager.get_mdl_dockets_by_date_range(MDL_NUM, start_date_fmt, end_date_fmt)
        console.print(f"[green]Fetched {len(raw_items)} raw items from database.[/green]")
    except Exception as e:
        console.print(f"[bold red]Error fetching dockets from database: {e}[/bold red]")
        console.print_exception(show_locals=False)
        sys.exit(1)

    initial_items = []
    filtered_items = []
    dropped_items = []

    if not raw_items:
        console.print("[yellow]No dockets found in database for the specified criteria.[/yellow]")
    else:
        console.print("[yellow]Processing database dockets (handling transfers, normalizing plaintiffs)...[/yellow]")
        initial_items, filtered_items, dropped_items = process_dockets(raw_items)
        console.print(
            f"[green]Database docket processing complete. Initial Items: {len(initial_items)}, Filtered Items: {len(filtered_items)}, Dropped Items: {len(dropped_items)}[/green]")

    display_cols = [
        "FilingDate", "CourtId", "DocketNum", "NumPlaintiffs", "TransferredIn",
        "TransferorCourtId", "TransferorDocketNum", "Plaintiff", "Defendant", "LawFirm", "AddedOn"
    ]

    while True:
        console.print("\n[bold cyan]Choose an analysis option:[/bold cyan]")
        console.print("--- Database Docket Analysis (MDL 2873) ---")
        console.print("1. View Initial DB Items (raw fetched, includes potential transfers)")
        console.print("2. View Filtered DB Items (transfers removed - likely unique MDL cases)")
        console.print("3. View Dropped DB Items (identified as original dockets transferred elsewhere)")
        console.print("4. View Daily DB Docket Counts (Filtered Data)")
        console.print("5. View DB Plaintiff Counts per Docket (Filtered Data)")
        console.print("6. View Total DB Plaintiffs Summary (Filtered Data)")
        console.print("7. Create Daily DB Filing Chart (Filtered Data)")
        console.print("--- Local JSON File Analysis (MDL 2873) ---")
        console.print("8. Analyze Local JSON Filings (Transferred, Pending CTO, Direct)")
        console.print("--- General ---")
        console.print("q. Quit")

        choice = Prompt.ask("Enter your choice", choices=["1", "2", "3", "4", "5", "6", "7", "8", "q"], default="q")

        if choice == '1':
            display_table(console, f"Initial DB Items ({len(initial_items)})", initial_items, columns=display_cols)
            calculate_and_display_sum(console, initial_items, "Initial DB Items")
        elif choice == '2':
            display_table(console, f"Filtered DB Items ({len(filtered_items)})", filtered_items, columns=display_cols)
            calculate_and_display_sum(console, filtered_items, "Filtered DB Items")
        elif choice == '3':
            display_table(console, f"Dropped DB Items ({len(dropped_items)})", dropped_items, columns=display_cols)
            calculate_and_display_sum(console, dropped_items, "Dropped DB Items")
        elif choice == '4':
            if filtered_items:
                df_daily_db = prepare_daily_docket_data(filtered_items)
                display_daily_docket_counts(console, df_daily_db,
                                            f"Daily DB Docket Counts ({format_date_display(start_date_fmt)} to {format_date_display(end_date_fmt)})")
            else:
                console.print("[yellow]No filtered DB data to display daily counts.[/yellow]")
        elif choice == '5':
            display_plaintiffs_over_time(console, filtered_items)
        elif choice == '6':
            display_plaintiff_sum_range(console, filtered_items, start_date_fmt, end_date_fmt)
        elif choice == '7':
            if filtered_items:
                df_daily_db_chart = prepare_daily_docket_data(filtered_items)
                chart_path = args.chart_output
                if chart_path == "afff_filings_chart.png":
                    chart_path = f'afff_filings_chart_{start_date_fmt}_{end_date_fmt}.png'
                create_dual_axis_chart(console, df_daily_db_chart, chart_path)
            else:
                console.print("[yellow]No filtered DB data to create chart.[/yellow]")
        elif choice == '8':
            analyze_local_json_filings(console, start_date_fmt, end_date_fmt)
        elif choice == 'q':
            console.print("[bold blue]Exiting analysis.[/bold blue]")
            break

if __name__ == "__main__":
    main()


# To Use the Script:
#
# Save: Save the code block above as calculate_afff_metrics.py in your project's scripts directory (assuming your project structure is project_root/scripts/ and project_root/src/lib/).
#
# Install Libraries: Make sure you have rich, pandas, matplotlib, and seaborn installed (pip install rich pandas matplotlib seaborn).
#
# Configuration: Ensure your src/lib/config.py and database setup for PacerManager are correctly configured and accessible. The script relies on load_config('01/01/70') as per the original source, and assumes PacerManager can initialize with that config and the local/port settings.
#
# Run: Execute the script from your terminal, providing the required --start-date argument in YYYYMMDD format.
#
# Example (fetching data from a production/remote DB):
#
# python scripts/calculate_afff_metrics.py --start-date 20230101 --end-date 20231231
# IGNORE_WHEN_COPYING_START
# content_copy
# download
# Use code with caution.
# Bash
# IGNORE_WHEN_COPYING_END
#
# Example (fetching data from a local DB):
#
# python scripts/calculate_afff_metrics.py --start-date 20240101 --local --port 8000
# IGNORE_WHEN_COPYING_START
# content_copy
# download
# Use code with caution.
# Bash
# IGNORE_WHEN_COPYING_END
#
# Example (saving chart to a specific path):
#
# python scripts/calculate_afff_metrics.py --start-date 20230101 --end-date 20231231 --chart-output my_afff_chart.png
# IGNORE_WHEN_COPYING_START
# content_copy
# download
# Use code with caution.
# Bash
# IGNORE_WHEN_COPYING_END
#
# Interact: The script will fetch and process data, then present you with an interactive menu to choose which analysis or view you want to see.