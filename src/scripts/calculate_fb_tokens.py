# --- START OF FILE src/scripts/calculate_fb_tokens.py ---
import boto3
import logging
import os
import argparse
import sys
from decimal import Decimal
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

from botocore.exceptions import ClientError
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, BarColumn, TextColumn, TimeElapsedColumn, MofNCompleteColumn
from rich.table import Table

# Determine project root assuming script is in src/scripts/
_SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
_PROJECT_ROOT = os.path.dirname(os.path.dirname(_SCRIPT_DIR))
# Add project root to path if not already there (helps with imports like src.lib)
if _PROJECT_ROOT not in sys.path:
    sys.path.insert(0, _PROJECT_ROOT)

# Local imports (should work now with sys.path adjusted)
try:
    from src.lib.config import load_config
    from src.lib.dynamodb_base_manager import <PERSON>DbBaseManager, convert_decimals
    from src.scripts.deepseek_tokenizer_loader import load_deepseek_tokenizer
except ImportError as e:
    print(f"Error importing local modules: {e}")
    print("Ensure you are running this script from the project root directory using:")
    print("python -m src.scripts.calculate_fb_tokens")
    print(f"Current sys.path: {sys.path}")
    exit(1)


# --- Configuration ---
# DB related (used only if not --prompts-only)
DEFAULT_AD_ARCHIVE_TABLE_NAME = "FBAdArchive"
DEFAULT_IMAGE_TEXT_TABLE_NAME = "ImageTextTable" # <<< Verify this name in your local DB!
IMAGE_TEXT_PK_FIELD = "PHash"
IMAGE_TEXT_ATTRIBUTE = "ImageText"
AD_ARCHIVE_PHASH_FIELD = "PHash"
FIELDS_TO_TOKENIZE = ['Title', 'Body', 'Summary', 'PageName']
MAX_LOOKUP_WORKERS = 10

# Prompt related (used only if --prompts-only)
# Fixed path relative to the determined project root
FIXED_PROMPT_DIR = os.path.join(_PROJECT_ROOT, "src", "config", "fb_ad_categorizer_prompts")

# General
DEFAULT_TOKENIZER_DIR = os.path.join(_PROJECT_ROOT, "src", "scripts") # Default assumes tokenizer files are with scripts
DEFAULT_CONFIG_ID = '01/01/70'


# --- Setup ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)
console = Console()

# --- Helper Functions ---

def get_image_text(image_text_table, phash: str) -> str | None:
    """Fetches ImageText from the ImageTextTable using PHash."""
    if not phash or not isinstance(phash, str) or not phash.strip():
        return None
    try:
        response = image_text_table.get_item(
            Key={IMAGE_TEXT_PK_FIELD: phash}
        )
        item = response.get('Item')
        if item:
            item = convert_decimals(item)
            image_text = item.get(IMAGE_TEXT_ATTRIBUTE)
            return image_text if isinstance(image_text, str) and image_text.strip() else None
        else:
            # This is common if not all ads have image text, keep as debug
            logger.debug(f"No item found in {image_text_table.name} for {IMAGE_TEXT_PK_FIELD}={phash}")
            return None
    except ClientError as e:
        # Log actual errors, but maybe not ResourceNotFound if table name was wrong initially
        if e.response['Error']['Code'] == 'ResourceNotFoundException':
             logger.warning(f"ResourceNotFoundException for PHash {phash} in {image_text_table.name} - table might not exist or key incorrect.")
        else:
             logger.error(f"DynamoDB error fetching {IMAGE_TEXT_PK_FIELD} {phash} from {image_text_table.name}: {e.response['Error']['Message']}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error fetching image text for {phash}: {e}", exc_info=True)
        return None

# --- Prompt Token Calculation Function ---
def calculate_prompt_tokens(tokenizer, prompt_dir: str):
    """Finds all .md files in the fixed prompt_dir, reads them, and calculates total tokens."""
    console.print(Panel(f"Calculating Tokens for Prompts\n"
                        f"Prompt Directory: [bold cyan]{prompt_dir}[/]",
                        title="Prompt Token Calculation Mode", border_style="blue"))

    # Use the absolute path determined earlier
    absolute_prompt_dir = os.path.abspath(prompt_dir)

    if not os.path.isdir(absolute_prompt_dir):
        console.print(f"[bold red]Error:[/bold red] Prompt directory not found: '{absolute_prompt_dir}'")
        console.print(f"Note: Expected location based on script path: {FIXED_PROMPT_DIR}")
        return

    total_prompt_tokens = 0
    processed_files_count = 0
    files_details = [] # Store (filename, tokens)

    console.print(f"Scanning for '.md' files in '{absolute_prompt_dir}'...")
    try:
        # Use os.walk to recursively find files
        for root, _, files in os.walk(absolute_prompt_dir):
            for filename in files:
                if filename.lower().endswith(".md"):
                    file_path = os.path.join(root, filename)
                    # Use relative path from the specified prompt_dir for cleaner output
                    relative_path = os.path.relpath(file_path, absolute_prompt_dir)
                    processed_files_count += 1
                    logger.info(f"Processing prompt file: {file_path}")
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()

                        if content.strip(): # Only tokenize if there's content
                             tokens = tokenizer.encode(content)
                             num_tokens = len(tokens)
                             total_prompt_tokens += num_tokens
                             files_details.append((relative_path, num_tokens))
                        else:
                             files_details.append((relative_path, 0))
                             logger.warning(f"Prompt file is empty: {file_path}")

                    except FileNotFoundError:
                         # This shouldn't happen with os.walk unless file vanished mid-scan
                         logger.error(f"File vanished during processing: {file_path}")
                         files_details.append((relative_path, "Error - Vanished"))
                    except IOError as e:
                         logger.error(f"Error reading file {file_path}: {e}")
                         files_details.append((relative_path, "Error - Reading"))
                    except Exception as e:
                         logger.error(f"Error tokenizing file {file_path}: {e}", exc_info=True)
                         files_details.append((relative_path, "Error - Tokenizing"))

        if processed_files_count == 0:
             console.print("[yellow]Warning: No '.md' files found in the specified directory.[/yellow]")
             return # Exit gracefully if no files found


        console.print(f"Scan complete. Found and processed {processed_files_count} '.md' files.")

        # Print Summary Table
        table = Table(title="Prompt File Token Counts", show_header=True, header_style="bold magenta")
        table.add_column("File Path", style="dim", width=60, no_wrap=True)
        table.add_column("Tokens", justify="right")

        # Sort by path for consistent output
        files_details.sort(key=lambda x: x[0])

        for path, num_tokens in files_details:
             if isinstance(num_tokens, int):
                 table.add_row(path, f"{num_tokens:,}")
             else:
                 # Display errors clearly
                 table.add_row(path, f"[bold red]{num_tokens}[/bold red]")

        console.print(table)

        # Print Final Summary Panel
        console.print(Panel(f"Prompt Token Calculation Summary\n\n"
                            f"Directory Scanned: [bold yellow]{absolute_prompt_dir}[/]\n"
                            f"Total '.md' Files Processed: [bold yellow]{processed_files_count:,}[/]\n\n"
                            f"Total Tokens Calculated for Prompts: [bold green]{total_prompt_tokens:,}[/]",
                            title="Final Summary (Prompts)", border_style="green"))

    except Exception as e:
        logger.error(f"An unexpected error occurred during prompt calculation: {e}", exc_info=True)
        console.print(f"[bold red]An unexpected error occurred:[/bold red] {e}")


# --- DynamoDB Token Calculation Function ---
def calculate_db_tokens(config: dict, tokenizer, ad_table, image_text_table):
    """Scans the Ad Archive, looks up ImageText, and calculates total tokens."""
    console.print(Panel(f"Starting Token Calculation (DynamoDB Mode)\n"
                        f"Ad Archive Table: [bold cyan]{ad_table.name}[/]\n"
                        f"Image Text Table: [bold cyan]{image_text_table.name}[/]",
                        title="Configuration", border_style="blue"))

    total_items_scanned = 0
    items_with_phashes = 0
    items_with_image_text_found = 0
    total_tokens = 0
    scan_kwargs = {}
    processed_items = 0
    estimated_total = 1 # Avoid division by zero if count fails

    # Use rich progress bar
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        MofNCompleteColumn(), # Use Items: N/M format
        TextColumn("[progress.percentage]{task.percentage:>3.1f}%"),
        TextColumn("Tokens: {task.fields[tokens]}"),
        TimeElapsedColumn(),
        console=console,
        transient=False # Keep visible after completion
    ) as progress:

        # Estimate total items
        try:
            console.print("Estimating item count (this might take a moment)...")
            if ad_table:
                # Add ProvisionedThroughputExceededException handling if needed
                estimated_total = ad_table.item_count
                console.print(f"Table '{ad_table.name}' contains approximately {estimated_total:,} items.")
            else:
                # This case should not be reached if connection succeeded
                raise ValueError("Ad table object not initialized.")
        except (ClientError, Exception) as e:
             estimated_total = 1000000 # Default large number if count fails
             logger.warning(f"Could not get item count for {ad_table.name if ad_table else 'N/A'}: {e}. Using default estimate.")
             console.print(f"[yellow]Could not get exact item count for '{ad_table.name if ad_table else 'N/A'}'. Using estimated total for progress.[/yellow]")


        scan_task = progress.add_task(
            f"Scanning [cyan]{ad_table.name}[/]",
            total=max(1, estimated_total), # Ensure total is at least 1
            tokens="0"
        )

        with ThreadPoolExecutor(max_workers=MAX_LOOKUP_WORKERS) as executor:
            futures = {} # Store future -> original item text parts mapping

            done = False
            while not done:
                try:
                    # Add Segment/TotalSegments for parallel scan if table is huge and local setup allows
                    response = ad_table.scan(**scan_kwargs)
                except ClientError as e:
                    logger.error(f"DynamoDB scan failed on table {ad_table.name}: {e}", exc_info=True)
                    console.print(f"[bold red]Error during table scan:[/bold red] {e.response['Error']['Message']}")
                    # Allow processing of already submitted futures before exiting scan loop
                    done = True
                    response = {} # Ensure loop processes futures and exits cleanly


                items = response.get('Items', [])
                # Check for completion based on LastEvaluatedKey
                last_evaluated_key = response.get('LastEvaluatedKey')
                if not last_evaluated_key and not items and not futures and not scan_kwargs.get('ExclusiveStartKey'):
                     logger.info(f"Scan of {ad_table.name} appears complete (no items, no LEK, no pending futures).")
                     done = True

                total_items_scanned += len(items)

                # Submit lookup tasks for items with PHash
                for raw_item in items:
                    item = convert_decimals(raw_item) # Use imported converter
                    phash = item.get(AD_ARCHIVE_PHASH_FIELD)

                    text_parts = []
                    for field in FIELDS_TO_TOKENIZE:
                        value = item.get(field)
                        if isinstance(value, str) and value.strip():
                            text_parts.append(value)
                        elif isinstance(value, (int, float)): # Handle accidental numbers
                            text_parts.append(str(value))

                    if phash and isinstance(phash, str) and phash.strip():
                        items_with_phashes += 1
                        future = executor.submit(get_image_text, image_text_table, phash)
                        futures[future] = text_parts
                    else:
                        # Process immediately if no PHash
                        full_text = " ".join(text_parts)
                        item_tokens = 0
                        if full_text:
                            try:
                                # Use the passed tokenizer object
                                item_tokens = len(tokenizer.encode(full_text))
                                total_tokens += item_tokens
                            except Exception as e:
                                 logger.warning(f"Tokenization error for item without PHash (ID: {item.get('AdArchiveID', 'N/A')}): {e}", exc_info=True)

                        processed_items += 1
                        progress.update(scan_task, advance=1, tokens=f"{total_tokens:,}")


                # Process completed futures as they finish
                # Use as_completed for better handling of futures finishing out of order
                # Check only if there are pending futures
                if futures:
                     try:
                        # Use timeout=0 for non-blocking check of completed futures
                        for future in as_completed(futures, timeout=0):
                              text_parts = futures.pop(future) # Remove processed future
                              try:
                                  image_text = future.result() # Get result (might raise exception)
                                  if image_text:
                                      items_with_image_text_found += 1
                                      text_parts.append(image_text)

                                  full_text = " ".join(text_parts)
                                  item_tokens = 0
                                  if full_text:
                                      try:
                                          # Use the passed tokenizer object
                                          item_tokens = len(tokenizer.encode(full_text))
                                          total_tokens += item_tokens
                                      except Exception as e:
                                           logger.warning(f"Tokenization error for item with PHash lookup: {e}", exc_info=True)

                                  processed_items += 1
                                  progress.update(scan_task, advance=1, tokens=f"{total_tokens:,}")

                              except Exception as e:
                                  # Log error from future.result() or subsequent processing
                                  logger.error(f"Error processing future result: {e}", exc_info=True)
                                  processed_items += 1 # Still count as processed (though failed)
                                  progress.update(scan_task, completed=processed_items, tokens=f"{total_tokens:,}")
                     except TimeoutError:
                          # No futures completed within the timeout(0), totally fine, continue scanning
                          pass

                # Prepare for the next scan iteration ONLY if not already exiting loop
                if not done:
                    if last_evaluated_key:
                        scan_kwargs['ExclusiveStartKey'] = last_evaluated_key
                        # Dynamically update progress total if estimate was low
                        if total_items_scanned > progress.tasks[scan_task].total:
                             progress.update(scan_task, total=total_items_scanned + estimated_total // 10)
                    else:
                        # No LastEvaluatedKey, scan of this segment/page is done.
                        # If there are no pending futures, the whole process is done.
                        if not futures:
                            logger.info(f"Scan page finished (no LEK) and no pending futures. Marking as done.")
                            done = True
                        else:
                             logger.debug(f"Scan page finished (no LEK), but {len(futures)} futures pending. Waiting for completion.")
                             # Loop will continue to process remaining futures in the as_completed block


            # --- After Scan Loop ---
            # Ensure any remaining futures are processed if scan loop exited early (e.g., due to error)
            if futures:
                 console.print(f"[yellow]Scan loop exited, processing {len(futures)} remaining futures...[/yellow]")
                 for future in as_completed(futures): # Wait for remaining ones
                     text_parts = futures.pop(future, None) # Pop safely
                     if text_parts is None: # Should not happen with as_completed if pop worked before
                           logger.warning("Attempted to process an already processed or invalid future.")
                           continue
                     try:
                         image_text = future.result()
                         if image_text:
                             items_with_image_text_found += 1
                             text_parts.append(image_text)
                         full_text = " ".join(text_parts)
                         item_tokens = 0
                         if full_text:
                              try:
                                   item_tokens = len(tokenizer.encode(full_text))
                                   total_tokens += item_tokens
                              except Exception as e:
                                   logger.warning(f"Tokenization error for final item: {e}")
                         processed_items += 1
                         progress.update(scan_task, advance=1, tokens=f"{total_tokens:,}")
                     except Exception as e:
                         logger.error(f"Error processing final future result: {e}", exc_info=True)
                         processed_items += 1
                         progress.update(scan_task, completed=processed_items, tokens=f"{total_tokens:,}")


            # Ensure final progress reflects actual processed count and total
            final_processed = processed_items
            final_total = max(final_processed, estimated_total, total_items_scanned)
            progress.update(scan_task, completed=final_processed, total=final_total, tokens=f"{total_tokens:,}")


    # Print DB Mode Summary
    console.print(Panel(f"DynamoDB Scan Complete!\n\n"
                        f"Total Items Scanned from '{ad_table.name}': [bold yellow]{total_items_scanned:,}[/]\n"
                        f"Items Processed (Tokenized or Attempted): [bold yellow]{processed_items:,}[/]\n"
                        f"Items with '{AD_ARCHIVE_PHASH_FIELD}' field present: [bold yellow]{items_with_phashes:,}[/]\n"
                        f"Items where '{IMAGE_TEXT_ATTRIBUTE}' was found via PHash in '{image_text_table.name}': [bold yellow]{items_with_image_text_found:,}[/]\n\n"
                        f"Fields Tokenized per item: [bold cyan]{', '.join(FIELDS_TO_TOKENIZE)} + {IMAGE_TEXT_ATTRIBUTE} (if found)[/]\n\n"
                        f"Total Tokens Calculated (DB Mode): [bold green]{total_tokens:,}[/]",
                        title="Token Calculation Summary (DB Mode)", border_style="green"))


# --- Main Execution Block ---
if __name__ == '__main__':
    parser = argparse.ArgumentParser(
        description="Calculate tokens for FB Ad Archive data in DynamoDB or for local prompt files.",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter # Show defaults in help
    )
    parser.add_argument(
        '--prompts-only',
        action='store_true',
        help=f"Calculate tokens only for .md files in the fixed directory '{FIXED_PROMPT_DIR}', skipping DynamoDB."
    )
    parser.add_argument(
        '--tokenizer-dir',
        default=DEFAULT_TOKENIZER_DIR,
        help="Directory containing the tokenizer files."
    )
    # Arguments relevant only for DB mode
    parser.add_argument(
        '--config-id',
        default=DEFAULT_CONFIG_ID,
        help="Configuration identifier for loading settings (used for DB connection)."
    )
    parser.add_argument(
        '--ad-table',
        default=DEFAULT_AD_ARCHIVE_TABLE_NAME,
        help="Name of the main Ad Archive DynamoDB table."
    )
    parser.add_argument(
        '--image-table',
        default=DEFAULT_IMAGE_TEXT_TABLE_NAME,
        help="Name of the Image Text DynamoDB table."
    )

    args = parser.parse_args()

    script_logger = logging.getLogger(__name__)

    # 1. Load Tokenizer (needed for both modes)
    tokenizer = None
    absolute_tokenizer_dir = os.path.abspath(args.tokenizer_dir)
    try:
        console.print(f"Loading tokenizer from: {absolute_tokenizer_dir}...")
        tokenizer = load_deepseek_tokenizer(absolute_tokenizer_dir)
        console.print("[green]Tokenizer loaded successfully.[/green]")
    except FileNotFoundError as e:
         console.print(f"[bold red]Error:[/bold red] Tokenizer files not found at '{absolute_tokenizer_dir}'. {e}")
         exit(1) # Exit if tokenizer fails to load
    except Exception as e:
        script_logger.error(f"Failed to load tokenizer from {absolute_tokenizer_dir}: {e}", exc_info=True)
        console.print(f"[bold red]Error loading tokenizer:[/bold red] {e}")
        exit(1)


    # 2. Branch based on mode
    if args.prompts_only:
        # --- Prompt Calculation Mode ---
        # Pass the fixed directory path
        calculate_prompt_tokens(tokenizer, FIXED_PROMPT_DIR)

    else:
        # --- DynamoDB Calculation Mode ---
        try:
            # Load app config (only needed for DB mode)
            script_logger.info(f"Loading configuration for: {args.config_id}")
            # Ensure load_config can find its files relative to project root or its own location
            app_config = load_config(args.config_id)

            # Connect to DynamoDB using DynamoDbBaseManager
            console.print("Connecting to DynamoDB via Base Manager...")
            dynamodb_resource = None
            ad_table_obj = None
            image_text_table_obj = None

            try:
                # Instantiate the manager - it should handle endpoint logic internally
                # Use table names from args
                base_manager = DynamoDbBaseManager(app_config, table_name=args.ad_table, use_local=True)
                if not hasattr(base_manager, 'dynamodb') or not base_manager.dynamodb:
                     raise ConnectionError("DynamoDbBaseManager failed to initialize the DynamoDB resource.")
                dynamodb_resource = base_manager.dynamodb
                console.print(f"[green]DynamoDB resource acquired. Endpoint: {base_manager.dynamodb.meta.client.meta.endpoint_url}[/green]")

                # Get table objects
                ad_table_obj = dynamodb_resource.Table(args.ad_table)
                image_text_table_obj = dynamodb_resource.Table(args.image_table)

                # Check access by trying to load metadata (includes existence check)
                console.print(f"Checking access to table: '{ad_table_obj.name}'...")
                ad_table_obj.load()
                console.print(f"Checking access to table: '{image_text_table_obj.name}'...")
                image_text_table_obj.load() # <<< This will fail if image_table name is wrong!
                console.print(f"[green]Successfully connected to tables '{ad_table_obj.name}' and '{image_text_table_obj.name}'.[/green]")

            except ClientError as e:
                # Provide more specific feedback for ResourceNotFoundException
                table_name_in_error = args.ad_table if args.ad_table in str(e) else args.image_table
                if e.response['Error']['Code'] == 'ResourceNotFoundException':
                     logger.error(f"DynamoDB ResourceNotFoundException: Table '{table_name_in_error}' not found.", exc_info=True)
                     console.print(f"[bold red]DynamoDB Error:[/bold red] Table '{table_name_in_error}' not found. Please check the table name argument (`--ad-table` or `--image-table`) and ensure the table exists in your local DynamoDB instance.")
                else:
                     logger.error(f"DynamoDB client error during connection/table check: {e}", exc_info=True)
                     console.print(f"[bold red]DynamoDB Error:[/bold red] Could not access tables. Check names/permissions/endpoint. Details: {e.response['Error']['Message']}")
                exit(1) # Exit if DB connection fails
            except (ConnectionError, AttributeError, Exception) as e:
                 logger.error(f"Failed to initialize DynamoDB connection via BaseManager: {e}", exc_info=True)
                 console.print(f"[bold red]Connection Error:[/bold red] Could not initialize DynamoDB connection. Details: {e}")
                 exit(1) # Exit if DB connection fails

            # Run the DB token calculation
            calculate_db_tokens(app_config, tokenizer, ad_table_obj, image_text_table_obj)

        except ImportError as e:
            script_logger.error(f"Import error (DB Mode): {e}. Make sure required libraries and local modules are installed/accessible.", exc_info=True)
            console.print(f"[bold red]Import Error:[/bold red] {e}.")
        except Exception as e:
            script_logger.error(f"An unexpected error occurred during DB token calculation setup: {e}", exc_info=True)
            console.print(f"[bold red]An unexpected error occurred (DB Mode):[/bold red] {e}")

    script_logger.info("Script finished.")
# --- END OF FILE src/scripts/calculate_fb_tokens.py ---