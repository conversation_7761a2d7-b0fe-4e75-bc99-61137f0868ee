#!/usr/bin/env python3
"""
Process Review Cases Script

This script processes review cases collected during PACER scraping. It loads cases from
the review_cases_all.json file for a specific date, filters them based on various criteria,
and allows the user to review and select which cases to keep. The final list is saved to
review_cases_final.json.

Usage:
    python -m src.scripts.process_review_cases --date YYYYMMDD
    python -m src.scripts.process_review_cases --log-level INFO --date YYYYMMDD
    python -m src.scripts.process_review_cases --date YYYYMMDD --dicts

Arguments:
    --date: Date in YYYYMMDD format (e.g., 20240601 for June 1, 2024)
    --log-level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
    --dicts: If present, save the final cases as a list of dictionaries instead of list of lists.
"""

import logging
import os
import json
from datetime import datetime
import sys
import argparse

# Add the project root to the Python path if running as a script
if __name__ == "__main__":
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    sys.path.insert(0, project_root)

from src.lib.pacer_manager import PacerManager
from rich.console import Console
from rich.panel import Panel
from rich.progress import track
from rich.table import Table
from rich import print as rprint

console = Console()
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
DATA_PATH = os.path.join(PROJECT_ROOT, 'data')


def get_valid_date(date_input: str = None):
    """Get and validate date input in YYYYMMDD format.

    Args:
        date_input (str, optional): Date string in YYYYMMDD format. If None, prompts user for input.

    Returns:
        str: Path to the logs directory for the specified date.

    Raises:
        SystemExit: If date format is invalid.
    """
    if not date_input:
        date_input = console.input("[cyan]Enter the date in YYYYMMDD format:[/cyan] ")

    # Validate format: must be 8 digits
    if not (date_input and date_input.isdigit() and len(date_input) == 8):
        console.print("[red]Invalid date format. Please enter in YYYYMMDD format (8 digits).[/red]")
        sys.exit(1)

    try:
        date_obj = datetime.strptime(date_input, "%Y%m%d")
        return os.path.join(DATA_PATH, date_obj.strftime("%Y%m%d"), "logs")
    except ValueError:
        console.print("[red]Invalid date. Please enter a valid date in YYYYMMDD format.[/red]")
        sys.exit(1)


def load_json_files(directory):
    """Load and combine all JSON files from the directory."""
    all_cases = []
    try:
        # Try new filename first, then legacy filename
        file_path = os.path.join(directory, "review_cases_all.json")
        legacy_file_path = os.path.join(directory, "irrelevant_cases_all.json")

        if os.path.exists(file_path):
            with open(file_path, 'r') as f:
                data = json.load(f)
                if isinstance(data, list):
                    all_cases.extend(data)
                else:
                    all_cases.append(data)  # Should ideally be a list, but handle single object
        elif os.path.exists(legacy_file_path):
            console.print(f"[yellow]Using legacy file: {legacy_file_path}[/yellow]")
            with open(legacy_file_path, 'r') as f:
                data = json.load(f)
                if isinstance(data, list):
                    all_cases.extend(data)
                else:
                    all_cases.append(data)  # Should ideally be a list, but handle single object
        else:
            console.print(f"[red]File not found: {file_path} or {legacy_file_path}[/red]")
    except json.JSONDecodeError:
        console.print(f"[red]Error reading {file_path}. Skipping...[/red]")
    except Exception as e:
        console.print(f"[red]Unexpected error reading {file_path}: {str(e)}[/red]")

    return all_cases


def deduplicate_cases(cases):
    """Remove duplicates based on court_id and docket_num."""
    seen = set()
    unique_cases = []
    for case in cases:
        # Ensure case is a dictionary and has the required keys
        if not isinstance(case, dict):
            logging.warning(f"Skipping non-dictionary item during deduplication: {case}")
            continue
        court_id = case.get("court_id")
        docket_num = case.get("docket_num")

        if court_id is None or docket_num is None:
            logging.warning(f"Skipping case with missing court_id or docket_num: {case}")
            continue

        case_key = (court_id, docket_num)
        if case_key not in seen:
            seen.add(case_key)
            unique_cases.append(case)
    return unique_cases


def filter_excluded_keywords(cases):
    """Filter out cases containing excluded keywords."""
    exclude_keywords = {
        "target", "walmart", "wal-mart", "lowes", "home depot", "walgreens", "sams west",
        "united states of america", "cvs", "carnival cruise lines", "menard", "sam's club",
        "lowe's", "royal carribbean cruises", "chipotle grill", "dolgen", "sam's east", "sams east",
        "dollar general", "safeway", "stop & shop supermarket", "celebrity cruises", "publix",
        "united states federal", "costco", "the cheesecake factory", "burlington stores",
        "social security administration",
        "opened in error", "Kohl's", "kgrp, llc", "kwik trip", "circle k stores", "otis elevator",
        "the tjx companies", "chipotle mexican grill", "fiesta mart", 'kroger', "smith's food & drug",
        'publix super markets', 'caesars atlantic city', 'city of chicago', 'olive garden holdings',
        'dollar tree stores', 'united statees of america', 'united states postal service', "united states",
        "hobby lobby stores", 'carnival corporation', 'transit authority', "Albertsons", "Albertson's LLC",
        "sam's west", "michaels companies", "winn dixie", "dollar tree", "dillards", "tjx companies",
        "quicktrip corporation", "quicktrip", "jo-ann stores", "joann stores", "speedway llc", "QuickTrip Corporation",
        "family dollar", "cracker barrel", "mini mart", "mini-mart", "michaels stores", "Alerbertson's LLC",
        'tj maxx', 'us marshals service', "walgreen co.", "bjs wholesale club", "cook county sheriffs office",
        "aldi inc", "sams club", "quiktrip"
    }

    return [
        case for case in cases
        if isinstance(case, dict) and case.get("versus") and not any(  # Check if case is dict
            keyword in case["versus"].lower() for keyword in exclude_keywords
        )
    ]


def filter_existing_dockets(cases, pacer_manager, filing_date):  # filing_date seems unused here
    """Filter out cases that already exist in the database."""
    non_existing_cases = []
    console.print(f"[cyan]Checking {len(cases)} cases against database...[/cyan]")
    with console.status("[cyan]Checking for existing dockets...[/cyan]") as status:
        for case in track(cases, description="Checking DB"):
            if not isinstance(case, dict) or not case.get("docket_num") or not case.get("court_id"):
                logging.warning(f"Skipping case with invalid format or missing keys for DB check: {case}")
                continue
            exists = pacer_manager.check_docket_exists_by_id_num(
                docket_num=case['docket_num'],
                court_id=case['court_id']
            )
            if not exists:
                non_existing_cases.append(case)
            else:
                logging.debug(f"Case {case['court_id']}/{case['docket_num']} exists in DB. Filtering out.")

    console.print(f"[green]Found {len(cases) - len(non_existing_cases)} existing cases in DB[/green]")
    console.print(f"[blue]Remaining cases after DB check: {len(non_existing_cases)}[/blue]")
    return non_existing_cases


def main(args, config):
    """Main function to process review cases for a specific date.

    This function processes review cases by:
    1. Validating the date and finding the corresponding logs directory
    2. Loading review cases from JSON files
    3. Removing duplicates and filtering by excluded keywords
    4. Checking if cases already exist in the database
    5. Allowing user to review and select cases to keep
    6. Saving the final list of cases (as dicts) to a JSON file

    Args:
        args: Command line arguments including date and log level
        config: Application configuration
    """
    date_path = get_valid_date(args.date)
    date_str = os.path.basename(os.path.dirname(date_path))  # YYYYMMDD
    console.print(f"[cyan]Processing review cases for date: {date_str}[/cyan]")

    if not os.path.isdir(date_path):
        console.print(f"[red]Logs directory not found: {date_path}[/red]")
        sys.exit(1)

    pacer_manager = PacerManager(config)
    console.print("[cyan]Loading review cases from 'review_cases_all.json'...[/cyan]")
    all_cases = load_json_files(date_path)
    console.print(f"[green]Total cases loaded: {len(all_cases)}[/green]")
    if not all_cases:
        console.print("[yellow]No cases to process. Exiting.[/yellow]")
        return

    console.print("[cyan]Removing duplicates...[/cyan]")
    unique_cases = deduplicate_cases(all_cases)
    console.print(f"[green]Cases after deduplication: {len(unique_cases)}[/green]")

    console.print("[cyan]Filtering by excluded keywords...[/cyan]")
    filtered_by_keywords = filter_excluded_keywords(unique_cases)
    console.print(f"[green]Cases after keyword filtering: {len(filtered_by_keywords)}[/green]")

    cases_to_review = filter_existing_dockets(filtered_by_keywords, pacer_manager, date_str)

    final_cases = []  # Will store list of dicts
    total_to_review = len(cases_to_review)
    if not total_to_review:
        console.print("[yellow]No cases remaining after filtering. Exiting review process.[/yellow]")
    else:
        console.print(f"[cyan]Starting review of {total_to_review} cases...[/cyan]")

    for idx, case in enumerate(cases_to_review, 1):
        console.rule(f"[yellow]Reviewing Case {idx} of {total_to_review}[/yellow]")

        table = Table(show_header=False, box=None, padding=(0, 1))
        if isinstance(case, dict):
            display_order = ["court_id", "docket_num", "versus", "_reason_review", "_reason_irrelevant", "filing_date", "added_date_iso"]
            for key in display_order:
                if key in case and case[key]:
                    table.add_row(f"[bold cyan]{key.replace('_', ' ').title()}[/bold cyan]", str(case[key]))

            other_fields = {k: v for k, v in case.items() if v and k not in display_order}
            for key, value in other_fields.items():
                table.add_row(f"[cyan]{key.replace('_', ' ').title()}[/cyan]", str(value))
        else:
            table.add_row("[red]Error[/red]", "Case data is not in the expected dictionary format.")

        console.print(table)

        response = console.input("\n[yellow]Keep this case? (Y/N) [N]:[/yellow] ").strip().upper() or "N"
        if response == "Y":
            # Default to saving as a dictionary
            final_cases.append({"court_id": case.get("court_id"), "docket_num": case.get("docket_num")})

    # Default output filename reflects the dict structure
    output_filename = "review_cases_final.json"
    output_path = os.path.join(date_path, output_filename)

    if final_cases:
        save_response = console.input(
            f"\n[yellow]Save the {len(final_cases)} selected cases to '{output_filename}'? (Y/N) [Y]:[/yellow] ").strip().upper() or "Y"
        if save_response != "N":
            with open(output_path, 'w') as f:
                json.dump(final_cases, f, indent=4)
            console.print(f"[green]Selected cases saved to {output_path}[/green]")
            console.print(f"[green]Saved {len(final_cases)} cases.[/green]")
        else:
            console.print("[yellow]File save canceled.[/yellow]")
    else:
        console.print(
            f"[yellow]No cases were selected to keep. No output file generated as '{output_filename}'.[/yellow]")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Process irrelevant cases with configurable logging level and date"
    )
    parser.add_argument(
        "--log-level",
        type=str,
        default="CRITICAL",
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
        help="Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)"
    )
    parser.add_argument(
        "--date",
        type=str,
        help="Date in YYYYMMDD format (e.g., 20240601 for June 1, 2024). If not provided, will prompt for input."
    )
    # The --dicts flag is removed as saving as dicts is now default.
    # If you still want it for explicit clarity (though it wouldn't change behavior), you can keep it:
    # parser.add_argument(
    #     "--dicts",
    #     action="store_true",
    #     help="Explicitly save final cases as a list of dictionaries (this is now the default)."
    # )
    args = parser.parse_args()

    numeric_level = getattr(logging, args.log_level.upper(), None)
    logging.basicConfig(level=numeric_level, format="%(levelname)s: %(message)s")
    logger = logging.getLogger(__name__)

    try:
        from src.lib.config import load_config

        effective_date_for_config = args.date if args.date else datetime.now().strftime("%Y%m%d")
        try:
            config_date_mmddyy = datetime.strptime(effective_date_for_config, "%Y%m%d").strftime("%m/%d/%y")
        except ValueError:
            logger.critical(f"Invalid effective date for config: {effective_date_for_config}")
            sys.exit(1)

        config = load_config(end_date=config_date_mmddyy)
    except ImportError:
        logger.critical("Failed to import load_config. Ensure src.lib.config is correct.")
        sys.exit(1)
    except Exception as e:
        logger.critical(f"Failed to load configuration: {e}")
        sys.exit(1)

    main(args, config)