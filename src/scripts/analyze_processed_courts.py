#!/usr/bin/env python3

import argparse
import os

from rich import box
from rich.console import Console
from rich.table import Table

# Initialize the console for rich formatting
console = Console()


def analyze_logs(date):
    log_file_path = os.path.join('data', date, 'logs', 'processed_courts.log')

    if not os.path.exists(log_file_path):
        console.print(f"[red]Log file not found: {log_file_path}[/red]")
        return

    with open(log_file_path, 'r') as file:
        logs = file.readlines()

    if not logs:
        console.print(f"[yellow]Log file is empty: {log_file_path}[/yellow]")
        return

    table_title = f"Processed Courts Logs for {date}"
    # Common table options, show_footer removed here, will be handled manually if needed
    table_options = {
        "show_header": True,
        "header_style": "bold cyan",
        "show_lines": True,
        "box": box.ROUNDED  # Assumes `from rich import box` is present
    }

    first_line_content = logs[0].strip()

    if ',' in first_line_content and len(first_line_content.split(',')) > 1:
        # CSV-aware path
        table = Table(title=table_title, **table_options)

        csv_headers = first_line_content.split(',')
        num_data_columns = len(csv_headers)

        column_sums = [0.0] * num_data_columns
        # First data column (e.g., CourtID) is not for summing.
        # Subsequent columns are initially candidates.
        can_sum_column = ([False] + [True] * (num_data_columns - 1)) if num_data_columns > 0 else []

        # Define table columns (no footer attributes here)
        table.add_column("Line #", style="dim white", width=7, justify="right")

        if num_data_columns > 0:
            table.add_column(csv_headers[0], justify="left", style="green", min_width=10)

        for i in range(1, num_data_columns):
            table.add_column(csv_headers[i], justify="right", style="blue", min_width=6)

        # Process log entries (data rows)
        # logs[0] is the header. Data/special lines start from logs[1:].
        for line_number_in_file, log_entry_str in enumerate(logs[1:], start=2):
            stripped_log_entry = log_entry_str.strip()
            parts = stripped_log_entry.split(',')

            if len(parts) == num_data_columns:
                table.add_row(str(line_number_in_file), *parts)
                for i in range(num_data_columns):
                    if i < len(can_sum_column) and can_sum_column[i]:
                        try:
                            value = float(parts[i])
                            column_sums[i] += value
                        except ValueError:
                            can_sum_column[i] = False
            else:
                # Special line or malformed CSV line
                row_data_for_special_line = [str(line_number_in_file), stripped_log_entry]
                # Pad with empty strings for the remaining data columns
                padding_count = num_data_columns - 1 if num_data_columns > 0 else 0
                row_data_for_special_line.extend([""] * padding_count)
                table.add_row(*row_data_for_special_line)

        # Manually add repeated headers row (before totals)
        if num_data_columns > 0:  # Only add if there were CSV headers
            repeated_headers_row_data = [""] + csv_headers  # Empty for "Line #" col
            table.add_row(*repeated_headers_row_data, style="bold cyan")

        # Manually add Totals row
        if num_data_columns > 0:  # Only add if there were CSV headers
            totals_row_content = ["Totals"]
            for i in range(num_data_columns):
                if i < len(can_sum_column) and can_sum_column[i]:
                    # Display sum as integer if it's whole, else two decimal places
                    if column_sums[i] == int(column_sums[i]):
                        totals_row_content.append(str(int(column_sums[i])))
                    else:
                        totals_row_content.append(f"{column_sums[i]:.2f}")
                else:
                    totals_row_content.append(csv_headers[i])  # Repeat header name
            table.add_row(*totals_row_content, style="bold")

    else:
        # Fallback path: first line is not a CSV header
        table = Table(title=table_title, **table_options)
        table.add_column("Line #", style="dim white", width=7, justify="right")
        table.add_column("Log Entry", min_width=20)

        for line_number, log_entry_str in enumerate(logs, start=1):
            table.add_row(str(line_number), log_entry_str.strip())

    console.print(table)

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Analyze processed courts logs.')
    parser.add_argument('--date', type=str, required=True, help='Date in YYYYMMDD format')
    args = parser.parse_args()
    
    analyze_logs(args.date)
