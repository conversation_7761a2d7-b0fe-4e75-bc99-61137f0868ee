#!/usr/bin/env python3
"""
Count Unique PHash Primary Keys in FBImageHash Table

This script connects to a local DynamoDB Docker instance and counts
the number of unique PHash primary keys in the FBImageHash table.
"""

import argparse
import boto3
import logging
import sys
from collections import Counter
from rich.console import Console
from rich.progress import Progress
from rich.table import Table
from typing import Dict, List, Set, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("count_unique_phash")
console = Console()

def get_local_dynamodb_resource(port: int, region: str):
    """Initializes and returns a DynamoDB resource connected to localhost."""
    endpoint_url = f'http://localhost:{port}'
    logger.info(f"Connecting to local DynamoDB at {endpoint_url} (Region: {region})")
    try:
        session = boto3.Session(aws_access_key_id='dummy', aws_secret_access_key='dummy', region_name=region)
        resource = session.resource('dynamodb', endpoint_url=endpoint_url)
        list(resource.tables.limit(1))  # Verify connection
        logger.info("DynamoDB resource created and connection verified.")
        return resource
    except Exception as e:
        logger.error(f"Failed to create/verify DynamoDB resource for endpoint {endpoint_url}: {e}", exc_info=True)
        raise

def scan_table(table, progress=None):
    """Scan the entire table with pagination."""
    items = []
    scan_kwargs = {}
    
    if progress:
        # If we don't know the total, we'll update as we go
        task = progress.add_task("[cyan]Scanning table...", total=None)
    
    try:
        done = False
        start_key = None
        scanned_count = 0
        
        while not done:
            if start_key:
                scan_kwargs['ExclusiveStartKey'] = start_key
            response = table.scan(**scan_kwargs)
            items.extend(response.get('Items', []))
            
            # Update progress
            scanned_count += response.get('ScannedCount', 0)
            if progress:
                progress.update(task, completed=len(items), total=scanned_count)
            
            start_key = response.get('LastEvaluatedKey')
            done = start_key is None
            
        if progress:
            progress.update(task, completed=len(items), total=scanned_count)
            
        return items
    except Exception as e:
        logger.error(f"Error scanning table: {e}", exc_info=True)
        if progress:
            progress.stop()
        raise

def count_unique_phash(items: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Count unique PHash values and collect statistics."""
    if not items:
        return {
            "total_items": 0,
            "unique_phash_count": 0,
            "phash_frequency": Counter(),
            "most_common": []
        }
    
    # Extract PHash values
    phash_values = [item.get('PHash') for item in items if 'PHash' in item]
    
    # Count frequencies
    phash_counter = Counter(phash_values)
    
    return {
        "total_items": len(items),
        "unique_phash_count": len(phash_counter),
        "phash_frequency": phash_counter,
        "most_common": phash_counter.most_common(10)  # Top 10 most common hashes
    }

def display_results(stats: Dict[str, Any]):
    """Display the results in a formatted table."""
    console.print(f"\n[bold green]FBImageHash Table Statistics[/bold green]")
    console.print(f"Total items: [cyan]{stats['total_items']:,}[/cyan]")
    console.print(f"Unique PHash values: [cyan]{stats['unique_phash_count']:,}[/cyan]")
    
    # Calculate duplication rate
    if stats['total_items'] > 0:
        duplication_rate = (stats['total_items'] - stats['unique_phash_count']) / stats['total_items'] * 100
        console.print(f"Duplication rate: [cyan]{duplication_rate:.2f}%[/cyan]")
    
    # Display most common hashes if any duplicates exist
    if stats['most_common'] and stats['most_common'][0][1] > 1:
        console.print("\n[bold]Most Common PHash Values:[/bold]")
        table = Table(show_header=True, header_style="bold")
        table.add_column("PHash", style="dim")
        table.add_column("Count", justify="right")
        table.add_column("% of Total", justify="right")
        
        for phash, count in stats['most_common']:
            if count > 1:  # Only show duplicates
                percentage = (count / stats['total_items']) * 100
                table.add_row(
                    str(phash)[:20] + "..." if len(str(phash)) > 23 else str(phash),
                    str(count),
                    f"{percentage:.2f}%"
                )
        
        console.print(table)

def main():
    parser = argparse.ArgumentParser(description="Count unique PHash primary keys in FBImageHash table")
    parser.add_argument("--port", type=int, default=8000, help="Port for local DynamoDB instance")
    parser.add_argument("--region", type=str, default="us-east-1", help="AWS region name")
    parser.add_argument("--table", type=str, default="FBImageHash", help="DynamoDB table name")
    args = parser.parse_args()
    
    try:
        # Connect to local DynamoDB
        dynamodb = get_local_dynamodb_resource(args.port, args.region)
        table = dynamodb.Table(args.table)
        
        # Verify table exists
        try:
            table.table_status
            console.print(f"[green]Connected to table: {args.table}[/green]")
        except Exception as e:
            console.print(f"[bold red]Error: Table '{args.table}' not found or not accessible.[/bold red]")
            logger.error(f"Table error: {e}", exc_info=True)
            return
        
        # Scan table with progress bar
        with Progress() as progress:
            console.print(f"[yellow]Scanning table '{args.table}'...[/yellow]")
            items = scan_table(table, progress)
        
        # Count unique PHash values
        stats = count_unique_phash(items)
        
        # Display results
        display_results(stats)
        
    except Exception as e:
        console.print(f"[bold red]Error: {str(e)}[/bold red]")
        logger.error(f"Script error: {e}", exc_info=True)
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
