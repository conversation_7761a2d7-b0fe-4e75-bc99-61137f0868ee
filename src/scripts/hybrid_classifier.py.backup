#!/usr/bin/env python
import argparse
import asyncio
import dataclasses
import hashlib
import json
import logging
import multiprocessing as mp
import os
import pickle
import re
import sys
import time
import warnings

# Suppress repetitive spaCy warnings and tokenizer multiprocessing warnings
warnings.filterwarnings("ignore", message=".*rule-based lemmatizer did not find POS annotation.*")
warnings.filterwarnings("ignore", message=".*The rule-based lemmatizer did not find POS annotation.*")
warnings.filterwarnings("ignore", category=UserWarning, module="spacy")
import os
os.environ["PYTHONWARNINGS"] = "ignore::UserWarning:spacy"
os.environ["TOKENIZERS_PARALLELISM"] = "false"  # Prevent tokenizer fork warnings
from concurrent.futures import ProcessPoolExecutor, as_completed
from collections import Counter, defaultdict
from functools import lru_cache
from pathlib import Path
from typing import Any, Dict, List, Optional, Pattern, Set, Tuple, Union
import numpy as np
import pandas as pd
import torch
import yaml
from rich import box
from rich.align import Align
# Rich integration
from rich.console import Console
from rich.logging import RichHandler
from rich.panel import Panel
from rich.progress import BarColumn, Progress, SpinnerColumn, TextColumn, TimeRemainingColumn, TimeElapsedColumn, \
    TaskProgressColumn, track  # Add this with other rich imports
from rich.prompt import Prompt, IntPrompt, Confirm
from rich.table import Table
from rich.text import Text

# Add law firms manager import
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from lib.law_firms_manager import LawFirmsManager

# Optional NLP/ML libraries
try:
    from sentence_transformers import SentenceTransformer

    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False
    SentenceTransformer = None  # Placeholder

try:
    import spacy

    SPACY_AVAILABLE = True
except ImportError:
    SPACY_AVAILABLE = False
    spacy = None  # Placeholder

# Optional LLM libraries
try:
    from transformers import pipeline  # Corrected import

    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False
    pipeline = None

try:
    import mlx
    import mlx.core as mx
    import mlx.nn as mnn
    from mlx_lm import load, generate

    MLX_AVAILABLE = True
except ImportError:
    MLX_AVAILABLE = False

try:
    from llama_cpp import Llama

    LLAMA_CPP_AVAILABLE = True
except ImportError:
    LLAMA_CPP_AVAILABLE = False

_ollama_import_logged = False  # Module-level flag

try:
    import ollama

    OLLAMA_AVAILABLE = True
    if not _ollama_import_logged and os.getenv(
            "SPACY_WORKER_ID") is None:  # crude check, or use multiprocessing.current_process().name
        # Only print in the main process, not in spaCy workers
        # or more simply, if you only want it once per program lifetime:
        # if not _ollama_import_logged:
        print(f"DEBUG: Ollama import successful, version: {getattr(ollama, '__version__', 'unknown')}")
        _ollama_import_logged = True
except ImportError as e:
    OLLAMA_AVAILABLE = False
    print(f"DEBUG: Ollama import failed: {e}")
except Exception as e:
    OLLAMA_AVAILABLE = False
    print(f"DEBUG: Ollama import error: {e}")

# --- Logger setup with Rich ---
logger = logging.getLogger("HybridClassifier")
if not logger.handlers:
    log_handler = RichHandler(rich_tracebacks=True, show_time=True, show_level=True, show_path=False,
                              console=Console(stderr=True))
    log_formatter = logging.Formatter('%(name)s - %(message)s')
    log_handler.setFormatter(log_formatter)
    logger.addHandler(log_handler)
    logger.setLevel(logging.INFO)
    logger.propagate = False

# Prefer rapidfuzz over fuzzywuzzy for better performance
FUZZYWUZZY_AVAILABLE = False
FUZZY_BACKEND = None

try:
    # Try rapidfuzz first (faster)
    from rapidfuzz import fuzz, process
    from rapidfuzz.utils import default_process as full_process

    FUZZYWUZZY_AVAILABLE = True
    FUZZY_BACKEND = "rapidfuzz"
    logger.debug("Using rapidfuzz for fuzzy matching (faster)")
except ImportError:
    try:
        # Fallback to fuzzywuzzy
        from fuzzywuzzy import fuzz, process
        from fuzzywuzzy.utils import full_process

        FUZZYWUZZY_AVAILABLE = True
        FUZZY_BACKEND = "fuzzywuzzy"
        logger.debug("Using fuzzywuzzy for fuzzy matching (slower fallback)")
    except ImportError:
        FUZZYWUZZY_AVAILABLE = False
        FUZZY_BACKEND = None
        logger.warning("No fuzzy matching library available. Install rapidfuzz for best performance:")
        logger.warning("  pip install rapidfuzz")
        logger.warning("  OR pip install fuzzywuzzy[speedup] (slower fallback)")


# --- Helper Functions (from vector_clusterer.py and others) ---

def _normalize_text_for_matching(text: str) -> str:
    """
    Normalizes text for consistent rule matching: lowercases, removes punctuation,
    and consolidates whitespace.
    """
    if not isinstance(text, str):
        return ""
    text = text.lower()
    text = re.sub(r'[^\w\s]', ' ', text)  # Keep alphanumeric and spaces
    text = re.sub(r'\s+', ' ', text).strip()
    return text


def _compile_term_pattern(terms: List[str], term_type_description: str) -> Optional[re.Pattern]:
    """
    Compiles a regex pattern from a list of terms.
    Assumes input 'terms' are already normalized (lowercased, no punctuation).
    Uses \b for whole-word matching.
    """
    if not terms:
        return None
    try:
        # Filter out empty strings that might result from normalization of empty inputs
        valid_terms = [term for term in terms if term]
        if not valid_terms:
            return None
        pattern_str = r'\b(?:' + '|'.join(re.escape(term) for term in valid_terms) + r')\b'
        pattern = re.compile(pattern_str,
                             re.IGNORECASE)  # IGNORECASE might be redundant if terms are pre-lowercased, but safe
        logger.debug(f"Compiled regex pattern for {len(valid_terms)} {term_type_description}.")
        return pattern
    except Exception as e:
        logger.error(f"Failed to compile regex for {term_type_description}: {e}", exc_info=True)
        return None


# --- Dataclasses ---
@dataclasses.dataclass
class ClassificationResult:
    """Result of classification with confidence scores"""
    ad_id: str
    campaign: str
    company: Optional[str]
    original_name: Optional[str]  # Original company name before fuzzy matching
    confidence: float
    method: str  # 'rule', 'embedding', 'llm', 'llm_enhanced', 'llm_company_created', 'llm_failed', 'hybrid', 'none'
    needs_review: bool
    rule_source: Optional[str]  # 'manual_config', 'auto_generated'
    rule_confidence: float = 0.0
    embedding_confidence: float = 0.0
    llm_confidence: float = 0.0
    dbscan_cluster: Optional[int] = None  # DBSCAN cluster ID for 'Other' campaigns
    details: Dict[str, Any] = dataclasses.field(default_factory=dict)


# --- Core Components ---

class AdCampaignMatcher:
    """
    Handles matching of ad text against a predefined set of campaign rules.
    Adapted from vector_clusterer.py.
    """

    def __init__(self, known_campaigns: List[Dict[str, Any]], logger_instance: logging.Logger):
        self.known_campaigns = known_campaigns  # These are pre-processed campaigns with compiled patterns
        self.logger = logger_instance

        # Pre-segregate rules for efficiency, mirroring logic from vector_clusterer.py
        # This specific prioritization might need to be made more generic or configurable.
        self.data_breach_rules = [c for c in self.known_campaigns if c['LitigationName'] == "Data Breach"]
        self.etsy_privacy_rules = [c for c in self.known_campaigns if
                                   c['LitigationName'] == "Etsy Privacy Litigation"]
        self.general_privacy_rules = [c for c in self.known_campaigns if c['LitigationName'] == "Privacy Violation"]

        other_rule_litigation_names = ["Data Breach", "Etsy Privacy Litigation", "Privacy Violation"]
        self.remaining_rules_for_dynamic_check = sorted(
            [c for c in self.known_campaigns if c['LitigationName'] not in other_rule_litigation_names],
            key=lambda x: (
                len(x.get('trigger_pattern').pattern if x.get('trigger_pattern') else ''),
                # Specificity by num trigger terms
                len(x.get('include_or_pattern').pattern if x.get('include_or_pattern') else ''),  # and include terms
                len(x.get('exclude_pattern').pattern if x.get('exclude_pattern') else '')
            ),
            reverse=True
        )

        self.prioritized_rule_sets_for_match = [
            ("Data Breach Rules", self.data_breach_rules),
            ("Etsy Privacy Rules", self.etsy_privacy_rules),
            ("General Privacy Rules", self.general_privacy_rules),
            ("Other Campaign Rules (Dynamic Selection)", self.remaining_rules_for_dynamic_check)
        ]
        self.logger.info(f"AdCampaignMatcher initialized with {len(self.known_campaigns)} campaigns, "
                         f"{len(self.remaining_rules_for_dynamic_check)} in dynamic set.")

    def _matches_rules_for_campaign(self, campaign_config_entry: Dict[str, Any], text_to_match: str) -> bool:
        """Checks if the given text matches all rules for a single campaign configuration."""
        trigger_pattern: Optional[Pattern] = campaign_config_entry.get('trigger_pattern')
        include_or_pattern: Optional[Pattern] = campaign_config_entry.get('include_or_pattern')
        exclude_pattern: Optional[Pattern] = campaign_config_entry.get('exclude_pattern')

        if not trigger_pattern or not trigger_pattern.search(text_to_match):
            return False
        if include_or_pattern and not include_or_pattern.search(text_to_match):
            return False
        if exclude_pattern and exclude_pattern.search(text_to_match):
            return False
        return True

    def _calculate_specificity_score(self, campaign_config_entry: Dict[str, Any], text_to_match: str) -> int:
        """Calculates a specificity score for a match."""
        trigger_pattern: Optional[Pattern] = campaign_config_entry.get('trigger_pattern')
        include_or_pattern: Optional[Pattern] = campaign_config_entry.get('include_or_pattern')

        matched_trigger_terms_list = trigger_pattern.findall(text_to_match) if trigger_pattern else []
        matched_include_terms_list = include_or_pattern.findall(text_to_match) if include_or_pattern else []

        specificity_score = 0
        if include_or_pattern and matched_include_terms_list:
            specificity_score += 1000  # High bonus for rules with includes, if they matched

        specificity_score += len(set(matched_trigger_terms_list))  # Number of unique trigger terms matched
        specificity_score += len(set(matched_include_terms_list))  # Number of unique include terms matched
        return specificity_score

    def match_ad_text(self, text_to_match: str, ad_id_for_log: str = "N/A") -> Tuple[Optional[str], float]:
        """
        Attempts to match the given normalized text against campaign rules.
        Returns (LitigationName, confidence_score), or (None, 0.0).
        Confidence is simplified here; could be enhanced by specificity score.
        """
        matched_campaign_name: Optional[str] = None
        best_score_for_match = 0.0  # Using specificity score as confidence proxy

        if not text_to_match.strip():
            logger.debug(f"AD_ID: {ad_id_for_log} - AdCampaignMatcher: Empty text_to_match.")
            return None, 0.0

        for pass_name, current_rule_set in self.prioritized_rule_sets_for_match:
            if matched_campaign_name and pass_name != "Other Campaign Rules (Dynamic Selection)":  # Found in fixed priority
                break

            logger.debug(f"AD_ID: {ad_id_for_log} - AdCampaignMatcher attempting pass: {pass_name}")

            if pass_name == "Other Campaign Rules (Dynamic Selection)":
                potential_matches: List[Tuple[int, str]] = []  # (specificity_score, campaign_name)
                for campaign_config_entry in current_rule_set:
                    campaign_name_from_config = campaign_config_entry['LitigationName']
                    if self._matches_rules_for_campaign(campaign_config_entry, text_to_match):
                        score = self._calculate_specificity_score(campaign_config_entry, text_to_match)
                        potential_matches.append((score, campaign_name_from_config))
                        logger.debug(
                            f"AD_ID: {ad_id_for_log} - Rule '{campaign_name_from_config}': POTENTIAL MATCH (Score: {score}).")

                if potential_matches:
                    potential_matches.sort(key=lambda x: x[0], reverse=True)
                    # If we already have a match from a previous pass, only override if this is significantly better.
                    # For now, any match from dynamic pass overrides None.
                    if not matched_campaign_name or potential_matches[0][0] > best_score_for_match:
                        matched_campaign_name = potential_matches[0][1]
                        best_score_for_match = potential_matches[0][0]
                        logger.debug(
                            f"AD_ID: {ad_id_for_log} - BEST MATCH IN '{pass_name}': {matched_campaign_name} (Score: {best_score_for_match})")
                # This pass continues to iterate all its rules to find the *best* among them
                # It doesn't break early unless a match was found in prior fixed passes.
            else:  # Fixed-priority passes
                for campaign_config_entry in current_rule_set:
                    campaign_name_from_config = campaign_config_entry['LitigationName']
                    if self._matches_rules_for_campaign(campaign_config_entry, text_to_match):
                        matched_campaign_name = campaign_name_from_config
                        # For fixed priority, any match is taken. Specificity can be used to break ties if multiple rules in fixed set match.
                        # For simplicity, first match in fixed set is taken.
                        best_score_for_match = self._calculate_specificity_score(campaign_config_entry,
                                                                                 text_to_match)  # Or a fixed high score
                        logger.debug(
                            f"AD_ID: {ad_id_for_log} - EXACT MATCHED: {campaign_name_from_config} (Pass: {pass_name}, Score: {best_score_for_match})")
                        break  # Matched in this fixed-priority rule set

            if matched_campaign_name and pass_name != "Other Campaign Rules (Dynamic Selection)":  # If matched in fixed, break outer.
                break

        # Rule matches always have 0.98 confidence as per requirement
        final_confidence = 0.98 if matched_campaign_name else 0.0

        if not matched_campaign_name:
            logger.debug(f"AD_ID: {ad_id_for_log} - AdCampaignMatcher: No campaign rule matched.")

        return matched_campaign_name, final_confidence


class M4OptimizedEmbedder:
    """Embeddings optimized for M4 with MPS acceleration (from hybrid_campaign_classifier_m4.py)"""

    def __init__(self, model_name: str = 'all-MiniLM-L6-v2', cache_file: Optional[str] = "embedding_cache.pkl"):
        if not SENTENCE_TRANSFORMERS_AVAILABLE:
            logger.error("SentenceTransformers library not available. Embedder cannot function.")
            raise ImportError("SentenceTransformers library is required for M4OptimizedEmbedder.")

        if torch.backends.mps.is_available() and torch.backends.mps.is_built():
            self.device = torch.device("mps")
            logger.info("MPS available. Using Apple Metal for embeddings.")
        else:
            self.device = torch.device("cpu")
            logger.info("MPS not available. Using CPU for embeddings.")
            if sys.platform == "darwin":
                logger.warning("Running on macOS but MPS not available/built. Check PyTorch installation.")

        self.model_name = model_name
        self.model = SentenceTransformer(model_name, device=self.device.type)
        self.cache_file = Path(cache_file) if cache_file else None
        self._cache: Dict[str, np.ndarray] = {}
        self._new_embeddings_count = 0
        self._save_interval = 200  # Larger interval for better performance with 128GB RAM
        self._cache_hits_count = 0  # For diagnostics
        self._cache_misses_count = 0  # For diagnostics
        self._load_cache()

    def _get_hash(self, text: str) -> str:
        return hashlib.md5(text.encode('utf-8')).hexdigest()

    def _load_cache(self):
        if self.cache_file and self.cache_file.exists():
            try:
                with open(self.cache_file, 'rb') as f:
                    self._cache = pickle.load(f)
                logger.info(f"M4Embedder: Loaded {len(self._cache)} embeddings from cache: {self.cache_file}")
            except Exception as e:
                logger.warning(f"M4Embedder: Could not load embedding cache from {self.cache_file}: {e}")
                self._cache = {}
        else:
            if self.cache_file:
                logger.info(f"M4Embedder: No existing cache found at {self.cache_file}, will create new.")
            else:
                logger.info("M4Embedder: No cache file specified. Embeddings will not be persisted across runs.")
            self._cache = {}

    def save_cache(self, force=False):
        if self.cache_file and (force or self._new_embeddings_count >= self._save_interval):
            try:
                self.cache_file.parent.mkdir(parents=True, exist_ok=True)
                with open(self.cache_file, 'wb') as f:
                    pickle.dump(self._cache, f)
                logger.info(
                    f"M4Embedder: Saved {len(self._cache)} embeddings to cache: {self.cache_file}. Hits: {self._cache_hits_count}, Misses: {self._cache_misses_count}")
                self._new_embeddings_count = 0
            except Exception as e:
                logger.error(f"M4Embedder: Could not save embedding cache to {self.cache_file}: {e}")

    @lru_cache(maxsize=10240)
    def _encode_single_text_with_lru(self, text: str) -> np.ndarray:
        text_hash = self._get_hash(text)
        if text_hash in self._cache:
            self._cache_hits_count += 1
            # logger.debug(f"M4Embedder (single LRU): Cache HIT for text hash {text_hash[:8]} (text: '{text[:50]}...')")
            return self._cache[text_hash]

        self._cache_misses_count += 1
        # logger.debug(f"M4Embedder (single LRU): Cache MISS for text hash {text_hash[:8]} (text: '{text[:50]}...'). Encoding.")
        embedding = self.model.encode([text], convert_to_numpy=True, show_progress_bar=False)[0]
        self._cache[text_hash] = embedding
        self._new_embeddings_count += 1
        if self._new_embeddings_count >= self._save_interval: self.save_cache()
        return embedding

    def encode(self, texts: Union[str, List[str]], batch_size: int = 32, show_progress: bool = False) -> np.ndarray:
        if isinstance(texts, str):
            return self._encode_single_text_with_lru(texts)

        final_embeddings_list = [None] * len(texts)
        texts_to_encode_on_device = []
        indices_for_device_encoding = []  # Original indices of texts that need encoding

        for i, text in enumerate(texts):
            if not text or not text.strip():  # Handle empty strings explicitly
                # logger.debug(f"M4Embedder: Empty string at index {i}, will use zero vector.")
                # Determine expected embedding dimension
                # This is a common hack; ideally, model should expose output dim.
                # Using a common default and checking first real embedding.
                expected_dim = self.model.get_sentence_embedding_dimension() if hasattr(self.model,
                                                                                        'get_sentence_embedding_dimension') else 384
                final_embeddings_list[i] = np.zeros(expected_dim, dtype=np.float32)
                continue

            text_hash = self._get_hash(text)
            if text_hash in self._cache:
                self._cache_hits_count += 1
                # logger.debug(f"M4Embedder (batch): Cache HIT for text hash {text_hash[:8]} (text: '{text[:30]}...') at index {i}")
                final_embeddings_list[i] = self._cache[text_hash]
            else:
                # logger.debug(f"M4Embedder (batch): Cache MISS for text hash {text_hash[:8]} (text: '{text[:30]}...') at index {i}. Queuing for encoding.")
                self._cache_misses_count += 1
                texts_to_encode_on_device.append(text)
                indices_for_device_encoding.append(i)

        if texts_to_encode_on_device:
            logger.info(f"M4Embedder: Encoding {len(texts_to_encode_on_device)} texts on device (batch)...")
            try:
                device_encoded_embeddings = self.model.encode(
                    texts_to_encode_on_device,
                    batch_size=batch_size,
                    convert_to_numpy=True,
                    show_progress_bar=show_progress,
                    device=self.device.type
                )

                for original_idx, embedding, text_for_cache in zip(indices_for_device_encoding,
                                                                   device_encoded_embeddings,
                                                                   texts_to_encode_on_device):
                    final_embeddings_list[original_idx] = embedding
                    self._cache[self._get_hash(text_for_cache)] = embedding
                    self._new_embeddings_count += 1

                if self._new_embeddings_count >= self._save_interval:
                    self.save_cache()  # Save if threshold met
            except Exception as e:
                logger.error(f"M4Embedder: Error during SentenceTransformer batch encoding: {e}", exc_info=True)
                # Fallback for failed batch items: use zero vectors to avoid crashing
                expected_dim = self.model.get_sentence_embedding_dimension() if hasattr(self.model,
                                                                                        'get_sentence_embedding_dimension') else 384
                for original_idx in indices_for_device_encoding:
                    if final_embeddings_list[
                        original_idx] is None:  # Only fill if not already filled (e.g. by empty string check)
                        final_embeddings_list[original_idx] = np.zeros(expected_dim, dtype=np.float32)

        # Verify all embeddings are ndarrays and have consistent shapes
        # This part is crucial for preventing downstream errors with np.array(final_embeddings_list)
        final_dim = None
        for i, emb in enumerate(final_embeddings_list):
            if emb is None:  # Should ideally not happen if empty strings are handled
                logger.error(
                    f"M4Embedder: Found None embedding at index {i} for text '{texts[i][:50]}...'. Using zero vector.")
                # Fallback to a dimension (e.g. first valid embedding or model's stated dim)
                if final_dim is None and hasattr(self.model, 'get_sentence_embedding_dimension'):
                    final_dim = self.model.get_sentence_embedding_dimension()
                elif final_dim is None:  # Try to infer from any other non-None embedding
                    for e_ in final_embeddings_list:
                        if e_ is not None: final_dim = e_.shape[0]; break
                final_dim = final_dim or 384  # Ultimate fallback dimension
                final_embeddings_list[i] = np.zeros(final_dim, dtype=np.float32)

            if not isinstance(emb, np.ndarray):
                logger.warning(f"M4Embedder: Embedding at index {i} is not an ndarray (type: {type(emb)}). Converting.")
                try:
                    final_embeddings_list[i] = np.array(emb, dtype=np.float32)
                except Exception as conversion_err:
                    logger.error(
                        f"M4Embedder: Failed to convert embedding at index {i} to ndarray: {conversion_err}. Using zero vector.")
                    dim_to_use = final_dim or (self.model.get_sentence_embedding_dimension() if hasattr(self.model,
                                                                                                        'get_sentence_embedding_dimension') else 384)
                    final_embeddings_list[i] = np.zeros(dim_to_use, dtype=np.float32)

            if final_dim is None and final_embeddings_list[
                i] is not None:  # Set expected dimension from first valid embedding
                final_dim = final_embeddings_list[i].shape[0]

            if final_embeddings_list[i] is not None and final_embeddings_list[i].shape[0] != final_dim:
                logger.error(
                    f"M4Embedder: Shape mismatch at index {i}. Expected dim {final_dim}, got {final_embeddings_list[i].shape}. Text: '{texts[i][:50]}...'. Using zero vector of dim {final_dim}.")
                final_embeddings_list[i] = np.zeros(final_dim, dtype=np.float32)

        try:
            return np.array(final_embeddings_list, dtype=np.float32)
        except ValueError as e:
            logger.error(
                f"M4Embedder: Final np.array conversion failed due to inconsistent shapes/types: {e}. Details: {[(type(e), e.shape if hasattr(e, 'shape') else 'N/A') for e in final_embeddings_list[:5]]}",
                exc_info=True)
            # This should be rare if the loop above correctly standardizes everything
            # As a last resort, filter out problematic ones or return empty array of correct shape
            valid_embs = [emb for emb in final_embeddings_list if
                          isinstance(emb, np.ndarray) and emb.shape == (final_dim,)]
            if valid_embs:
                logger.warning(
                    f"M4Embedder: Returning {len(valid_embs)} valid embeddings out of {len(texts)} due to final conversion error.")
                return np.array(valid_embs, dtype=np.float32)
            else:
                logger.error("M4Embedder: No valid embeddings to return after final conversion error.")
                dim_to_use = final_dim or (self.model.get_sentence_embedding_dimension() if hasattr(self.model,
                                                                                                    'get_sentence_embedding_dimension') else 384)
                return np.array([]).reshape(0, dim_to_use)


class LocalLLMClassifier:
    """Local LLM classifier with response caching"""

    def __init__(self, backend: str = 'ollama', model_path: Optional[str] = None,
                 device: Optional[str] = None, cache_file: Optional[
                str] = None):  # Removed timeout, max_retries, retry_delay as they are not used here
        self.backend = backend
        self.model_path = model_path
        self.model: Any = None
        self.tokenizer: Any = None
        self.device = device  # For 'transformers' or 'mlx' if they need explicit device setting
        self.cache_file = cache_file or "llm_response_cache.pkl"
        self._response_cache = {}
        self._cache_hits = 0
        self._cache_misses = 0
        self._load_cache()
        self._initialize_model()

    def _initialize_model(self):
        if self.backend == 'ollama' and OLLAMA_AVAILABLE:
            logger.info("Initializing Ollama client for default host http://127.0.0.1:11434.")
            if not self.model_path:
                logger.error(
                    "Ollama model name/tag (e.g., 'llama3:8b') must be provided for Ollama backend via --llm-model.")
                self.model = None  # Mark as not initialized
                self.model_path = None  # Mark as not usable
                return

            try:
                ollama_host_env = os.environ.get('OLLAMA_HOST')
                ollama_models_env = os.environ.get('OLLAMA_MODELS')
                # logger.info(f"Python os.environ OLLAMA_HOST: '{ollama_host_env}' (Client will use explicit host if different)")
                # logger.info(f"Python os.environ OLLAMA_MODELS: '{ollama_models_env}' (This tells Ollama where to look for models)")

                self.model = ollama.Client(host='http://127.0.0.1:11434')  # Store the client instance
                logger.info(f"LocalLLMClassifier: Testing Ollama connection. Target model: '{self.model_path}'")

                models_info_raw = self.model.list()
                # logger.info(f"RAW OLLAMA CLIENT.LIST() RESPONSE TYPE: {type(models_info_raw)}")
                # logger.info(f"RAW OLLAMA CLIENT.LIST() RESPONSE CONTENT: {str(models_info_raw)[:1000]}") # Log snippet

                actual_model_names_from_client = []
                if hasattr(models_info_raw, 'models') and isinstance(models_info_raw.models, list):
                    for model_obj in models_info_raw.models:
                        if hasattr(model_obj, 'model'): actual_model_names_from_client.append(model_obj.model)
                elif isinstance(models_info_raw, dict) and 'models' in models_info_raw:
                    for m_data in models_info_raw.get('models', []):
                        if isinstance(m_data, dict) and m_data.get('name'): actual_model_names_from_client.append(
                            m_data.get('name'))

                # logger.info(f"MODELS FOUND BY PYTHON CLIENT on http://127.0.0.1:11434 (parsed): {actual_model_names_from_client}")

                model_exists = any(
                    self.model_path == name or self.model_path.split(':')[0] == name.split(':')[0] for name in
                    actual_model_names_from_client)

                if not model_exists:
                    logger.error(
                        f"Ollama model '{self.model_path}' NOT FOUND. Available: {actual_model_names_from_client}")
                    self.model = None;
                    self.model_path = None;
                    return
                logger.info(f"✅ Ollama connection successful and model '{self.model_path}' IS AVAILABLE.")

            except ollama.ResponseError as e:
                logger.error(
                    f"❌ Ollama API ResponseError: {getattr(e, 'error', str(e))} (Status: {getattr(e, 'status_code', 'N/A')})")
                self.model = None;
                self.model_path = None
            except ollama.ConnectionError as e:
                logger.error(
                    f"❌ Ollama ConnectionError: Could not connect to Ollama server at http://127.0.0.1:11434. Details: {e}")
                self.model = None;
                self.model_path = None
            except Exception as e:
                logger.error(f"❌ Ollama initialization failed with an unexpected error: {type(e).__name__} - {e}",
                             exc_info=True)
                self.model = None;
                self.model_path = None


        elif self.backend == 'mlx' and MLX_AVAILABLE:
            logger.info("Initializing MLX model...")
            if not self.model_path: self.model_path = "mlx-community/Nous-Hermes-2-Mistral-7B-DPO-4bit-MLX"  # Default if not set
            try:
                self.model, self.tokenizer = load(self.model_path)
                logger.info(f"MLX model {self.model_path} loaded.")
            except Exception as e:
                logger.error(f"Failed to load MLX model {self.model_path}: {e}", exc_info=True)
                self.model = None;
                self.tokenizer = None

        elif self.backend == 'llama_cpp' and LLAMA_CPP_AVAILABLE:
            logger.info("Initializing llama.cpp model...")
            if not self.model_path:
                logger.error("Model path required for llama.cpp backend.")
                self.model = None;
                return
            try:
                # Default n_gpu_layers to a high number to offload as much as possible.
                # Users can configure this more specifically if needed via advanced options or direct modification.
                self.model = Llama(model_path=self.model_path, n_ctx=2048, n_gpu_layers=99, verbose=False)
                logger.info(f"llama.cpp model {self.model_path} loaded.")
            except Exception as e:
                logger.error(f"Failed to load llama.cpp model {self.model_path}: {e}", exc_info=True)
                self.model = None

        elif self.backend == 'transformers' and TRANSFORMERS_AVAILABLE:
            logger.info("Initializing Hugging Face Transformers model...")
            if not self.model_path: self.model_path = "facebook/bart-large-mnli"  # Default zero-shot model

            # Determine device for transformers pipeline
            resolved_device_idx = -1  # Default to CPU
            if self.device:  # If a device string like "mps" or "cuda:0" is passed
                if self.device == "mps" and torch.backends.mps.is_available():
                    logger.info("Transformers: Using MPS device specified.")
                    # For pipeline, device can be "mps". Actual index not needed.
                    # The pipeline handles torch.device(self.device)
                elif "cuda" in self.device and torch.cuda.is_available():
                    try:
                        resolved_device_idx = int(self.device.split(':')[-1]) if ':' in self.device else 0
                        logger.info(f"Transformers: Using CUDA device {resolved_device_idx} specified.")
                    except ValueError:
                        logger.warning(
                            f"Could not parse CUDA device index from '{self.device}'. Defaulting to cuda:0 if available, else CPU.")
                        resolved_device_idx = 0 if torch.cuda.is_available() else -1
                else:  # Unrecognized device or specified device not available
                    logger.warning(
                        f"Device '{self.device}' not recognized or unavailable. Using default logic (CUDA > MPS > CPU).")
                    if torch.cuda.is_available():
                        resolved_device_idx = 0
                    elif torch.backends.mps.is_available() and sys.platform == "darwin":
                        self.device = "mps"  # Pipeline can take "mps"
                    # else resolved_device_idx remains -1 for CPU

            else:  # No device string passed, auto-detect
                if torch.cuda.is_available():
                    resolved_device_idx = 0
                elif torch.backends.mps.is_available() and sys.platform == "darwin":
                    self.device = "mps"

            pipeline_device_arg = self.device if self.device == "mps" else resolved_device_idx

            try:
                self.model = pipeline("zero-shot-classification", model=self.model_path, device=pipeline_device_arg)
                logger.info(f"Transformers pipeline {self.model_path} loaded on device: {self.model.device}.")
            except Exception as e:
                logger.error(f"Failed to load Transformers model {self.model_path}: {e}", exc_info=True)
                self.model = None
        else:
            if self.backend not in ['ollama', 'mlx', 'llama_cpp', 'transformers']:
                logger.warning(
                    f"LLM Backend '{self.backend}' not recognized or its library not available. LLM features disabled.")
            elif not OLLAMA_AVAILABLE and self.backend == 'ollama':
                logger.warning(
                    f"Ollama backend selected, but Ollama library not available or connection failed. LLM features disabled.")

            self.model = None  # Ensure model is None if not initialized

    def _load_cache(self):
        if self.cache_file and Path(self.cache_file).exists():  # Ensure cache_file is not None
            try:
                with open(self.cache_file, 'rb') as f:
                    self._response_cache = pickle.load(f)
                logger.info(f"Loaded {len(self._response_cache)} cached LLM responses from {self.cache_file}")
            except Exception as e:
                logger.warning(f"Failed to load LLM cache from {self.cache_file}: {e}")
                self._response_cache = {}
        else:
            if self.cache_file:
                logger.info(f"LLM response cache file not found: {self.cache_file}. A new cache will be created.")
            else:
                logger.info("No LLM response cache file specified. Caching will be in-memory only for this session.")
            self._response_cache = {}

    def _save_cache(self):
        if not self.cache_file:  # Do not attempt to save if no cache file is specified
            return
        try:
            Path(self.cache_file).parent.mkdir(parents=True, exist_ok=True)
            with open(self.cache_file, 'wb') as f:
                pickle.dump(self._response_cache, f)
            # logger.debug(f"Saved {len(self._response_cache)} LLM responses to cache: {self.cache_file}")
        except Exception as e:
            logger.error(f"Failed to save LLM cache to {self.cache_file}: {e}")

    def _get_cache_key(self, text: str, candidate_labels: List[str], context: Optional[str] = None,
                       enhanced: bool = False) -> str:
        key_data = {
            'text': text[:1500],  # Slightly longer than typical max_text_len for safety
            'labels': sorted(candidate_labels),
            'context': context,
            'enhanced': enhanced,
            'model': self.model_path,  # Model path is crucial for cache specificity
            'backend': self.backend,
            'type': 'classification'
        }
        return hashlib.md5(json.dumps(key_data, sort_keys=True).encode()).hexdigest()

    def _get_generic_llm_cache_key(self, messages: List[Dict[str, str]], call_options: Optional[Dict] = None) -> str:
        sorted_options_str = None
        if call_options:
            try:  # Ensure options are serializable and sorted for consistency
                sorted_options_str = json.dumps(call_options, sort_keys=True)
            except TypeError:  # Fallback if options are not directly JSON serializable (e.g., contain unhashable items)
                sorted_options_str = str(sorted(call_options.items())) if isinstance(call_options, dict) else str(
                    call_options)

        key_data = {
            'messages': json.dumps(messages, sort_keys=True),
            # Ensure messages list of dicts is consistently serialized
            'options': sorted_options_str,
            'model': self.model_path,
            'backend': self.backend,
            'type': 'generic_chat'
        }
        return hashlib.md5(json.dumps(key_data, sort_keys=True).encode()).hexdigest()

    def classify(self, text: str, candidate_labels: List[str], context: Optional[str] = None) -> Tuple[
        Optional[str], float]:
        # Check if LLM is usable (model initialized and model_path set for Ollama)
        if not self.model_path and self.backend == 'ollama':  # Ollama needs model_path, model is the client
            logger.debug(f"Ollama LLM not ready for classification: model_path not set.")
            return None, 0.0
        if self.backend != 'ollama' and not self.model:  # Other backends store model instance in self.model
            logger.debug(f"{self.backend} LLM not ready for classification: model not initialized.")
            return None, 0.0
        if not candidate_labels:
            logger.warning("LLM classify called with no candidate_labels.")
            return None, 0.0

        max_text_len = 1000  # Max length of text to send to LLM for this specific task
        text_to_classify = text[:max_text_len]

        cache_key = self._get_cache_key(text_to_classify, candidate_labels, context, enhanced=False)
        if cache_key in self._response_cache:
            self._cache_hits += 1
            # logger.debug(f"LLM cache hit for: {text_to_classify[:50]}... Labels: {candidate_labels[:3]}")
            return self._response_cache[cache_key]

        self._cache_misses += 1
        result_tuple = (None, 0.0)

        try:
            if self.backend == 'ollama':  # self.model is ollama.Client here
                system_prompt = f"You are a text classification assistant. Classify the provided 'Ad text' into one of the following categories: {', '.join(candidate_labels)}. Respond ONLY with the single most appropriate category name from this list. No explanations or other text."
                user_prompt = f"Ad text: \"{text_to_classify}\"\nCategory:"
                messages = [{'role': 'system', 'content': system_prompt}, {'role': 'user', 'content': user_prompt}]

                response = self.model.chat(model=self.model_path, messages=messages, options={'temperature': 0.0})
                response_text = response['message']['content'].strip()

                best_match_label = None
                # Prioritize exact, case-insensitive match
                for label in candidate_labels:
                    if label.lower() == response_text.lower():
                        best_match_label = label;
                        break
                if not best_match_label:  # Fallback to substring if no exact match
                    for label in candidate_labels:
                        if label.lower() in response_text.lower():
                            if not best_match_label or response_text.lower().startswith(
                                    label.lower()):  # Prefer starts_with
                                best_match_label = label
                            if response_text.lower().startswith(label.lower()): break  # Found a good prefix match

                if best_match_label:
                    result_tuple = (best_match_label, 0.70)  # Default confidence for LLM match
                else:
                    logger.debug(
                        f"Ollama response '{response_text}' (model: {self.model_path}) did not match labels: {candidate_labels}")

            elif self.backend == 'mlx' and self.model and self.tokenizer:
                prompt = f"Classify: \"{text_to_classify}\" Categories: {', '.join(candidate_labels)}.\nCategory:"
                response_text = generate(self.model, self.tokenizer, prompt=prompt, max_tokens=25,
                                         temp=0.0).strip()  # Increased max_tokens slightly
                for label in candidate_labels:
                    if label.lower() in response_text.lower(): result_tuple = (label, 0.75); break

            elif self.backend == 'llama_cpp' and self.model:
                prompt = f"Classify: \"{text_to_classify}\" Categories: {', '.join(candidate_labels)}.\nCategory:"
                response = self.model(prompt, max_tokens=25, temperature=0.0,
                                      stop=["\n", ".", ","])  # Added more stop tokens
                response_text = response['choices'][0]['text'].strip()
                for label in candidate_labels:
                    if label.lower() in response_text.lower(): result_tuple = (label, 0.75); break

            elif self.backend == 'transformers' and self.model:
                hypothesis_template = "This ad is about {}."
                hf_result = self.model(text_to_classify, candidate_labels, hypothesis_template=hypothesis_template,
                                       multi_label=False)
                if hf_result and 'labels' in hf_result and 'scores' in hf_result and hf_result['labels']:
                    result_tuple = (hf_result['labels'][0], float(hf_result['scores'][0]))

        except Exception as e:
            logger.error(f"LLM classification error (backend: {self.backend}, model: {self.model_path or 'N/A'}): {e}",
                         exc_info=False)  # exc_info=False for brevity in main log

        self._response_cache[cache_key] = result_tuple
        if self._cache_misses % 100 == 0: self._save_cache()  # Save cache periodically after misses (optimized for 128GB RAM)
        return result_tuple

    def classify_with_enhanced_prompt(self, text: str, candidate_labels: List[str], context: str = "general") -> Tuple[
        Optional[str], float]:
        if not self.model_path and self.backend == 'ollama':
            logger.debug(f"Ollama LLM not ready for enhanced classification: model_path not set.")
            return None, 0.0
        if self.backend != 'ollama' and not self.model:
            logger.debug(f"{self.backend} LLM not ready for enhanced classification: model not initialized.")
            return None, 0.0
        if not candidate_labels:
            logger.warning("Enhanced LLM classify called with no candidate_labels.")
            return None, 0.0

        max_text_len = 1200
        text_to_classify = text[:max_text_len]

        cache_key = self._get_cache_key(text_to_classify, candidate_labels, context, enhanced=True)
        if cache_key in self._response_cache:
            self._cache_hits += 1
            return self._response_cache[cache_key]

        self._cache_misses += 1
        result_tuple = (None, 0.0)

        try:
            if self.backend == 'ollama':
                system_prompt = f"""You are an expert legal advertising classifier. Analyze the given ad text and classify it into one of these specific litigation categories: {', '.join(candidate_labels)}.
Context: This is a legal advertisement for mass tort, class action, or personal injury cases.
Instructions:
1. Identify key indicators: product names, medical devices, drugs, injuries, conditions, companies.
2. If multiple types are mentioned, choose the most prominent.
3. Respond ONLY with the exact category name from the list.
4. If genuinely uncertain or not about litigation, respond with "Other".
Examples:
- "If you used Roundup and developed cancer..." → Roundup
- "Defective hip implants causing pain..." → (specific MDL name if in list)
- "Car accident? Call now!" → Other (not mass tort)"""  # Ensure examples reflect candidate_labels structure
                user_prompt = f"Analyze this ad text:\n\n\"{text_to_classify}\"\n\nMost appropriate litigation category:"
                messages = [{'role': 'system', 'content': system_prompt}, {'role': 'user', 'content': user_prompt}]

                response = self.model.chat(model=self.model_path, messages=messages, options={'temperature': 0.0})
                response_text = response['message']['content'].strip()

                best_match_label = None;
                confidence_score = 0.5
                for label in candidate_labels:  # Prioritize exact match for enhanced prompt
                    if label.lower() == response_text.lower():
                        best_match_label = label;
                        confidence_score = 0.85;
                        break
                if not best_match_label:  # Fallback
                    for label in candidate_labels:
                        if label.lower() in response_text.lower():
                            if not best_match_label or response_text.lower().startswith(label.lower()):
                                best_match_label = label;
                                confidence_score = 0.75 if response_text.lower().startswith(label.lower()) else 0.65
                            if response_text.lower().startswith(label.lower()): break

                if best_match_label:
                    result_tuple = (best_match_label, confidence_score)
                else:
                    logger.debug(
                        f"Enhanced LLM response '{response_text}' (model: {self.model_path}) did not match labels: {candidate_labels}")
            else:  # Fallback for non-Ollama backends
                logger.debug(
                    f"Enhanced prompt not specifically implemented for backend '{self.backend}'. Falling back to standard classify.")
                return self.classify(text, candidate_labels, context)  # Use standard classify as fallback

        except Exception as e:
            logger.error(
                f"Enhanced LLM classification error (backend: {self.backend}, model: {self.model_path or 'N/A'}): {e}",
                exc_info=False)

        self._response_cache[cache_key] = result_tuple
        if self._cache_misses % 100 == 0: self._save_cache()  # Save cache periodically (optimized for 128GB RAM)
        return result_tuple

    def execute_cached_llm_call(self, messages: List[Dict[str, str]], call_options: Optional[Dict] = None) -> Optional[
        Dict]:
        if not self.model_path and self.backend == 'ollama':
            logger.debug(f"Ollama LLM not ready for generic call: model_path not set.")
            return None
        if self.backend != 'ollama' and not self.model:
            logger.debug(f"{self.backend} LLM not ready for generic call: model not initialized.")
            return None

        current_call_options = call_options or {}
        cache_key = self._get_generic_llm_cache_key(messages, current_call_options)

        if cache_key in self._response_cache:
            self._cache_hits += 1
            return self._response_cache[cache_key]

        self._cache_misses += 1
        llm_response_data: Optional[Dict] = None

        try:
            if self.backend == 'ollama':
                # logger.debug(f"Making Ollama API call (generic). Model: {self.model_path}. Options: {current_call_options}")
                llm_response_data = self.model.chat(model=self.model_path, messages=messages,
                                                    options=current_call_options)
            # Add MLX, Llama.cpp, Transformers backends here if RuleImprover needs them for generic calls
            # For now, focusing on Ollama as per original structure for RuleImprover's LLM usage
            else:
                logger.warning(f"Generic LLM call not implemented for backend '{self.backend}' yet. No call made.")
                return None
        except Exception as e:
            logger.error(f"Generic LLM call error (backend: {self.backend}, model: {self.model_path or 'N/A'}): {e}",
                         exc_info=False)
            return None

        if llm_response_data is not None:
            self._response_cache[cache_key] = llm_response_data
            if self._cache_misses % 100 == 0: self._save_cache()  # Optimized for 128GB RAM
        return llm_response_data


# The `classify_ad` method was ERRONEOUSLY placed here. It has been removed.
# The main classification pipeline logic is now correctly orchestrated by
# `HybridClassifier.classify_batch`.


class CompanyNameNormalizer:
    """Handles fuzzy matching and normalization of company names"""

    def __init__(self, similarity_threshold: float = 85):
        self.similarity_threshold = similarity_threshold
        self.known_companies: Dict[str, str] = {}  # normalized_name -> canonical_name
        self.company_aliases: Dict[str, List[str]] = {}  # canonical_name -> [aliases]

    def _normalize_company_name(self, name: str) -> str:
        """Normalize company name for comparison"""
        if not name:
            return ""
        # Remove common suffixes and normalize
        normalized = name.lower().strip()
        # Remove common corporate suffixes
        suffixes_to_remove = [
            ', inc.', ', inc', ' inc.', ' inc',
            ', llc', ' llc', ', corp.', ' corp.',
            ', corporation', ' corporation',
            ', ltd.', ' ltd', ', co.', ' co',
            '?<br', '?', '<br>'
        ]
        for suffix in suffixes_to_remove:
            if normalized.endswith(suffix):
                normalized = normalized[:-len(suffix)].strip()
        return normalized

    def add_or_match_company(self, company_name: str) -> Tuple[str, str]:
        """
        Add company or find fuzzy match.
        Returns (canonical_name, original_name)
        """
        if not company_name or not company_name.strip():
            return None, None

        original_name = company_name.strip()
        normalized = self._normalize_company_name(original_name)

        if not normalized:
            return None, None

        if not FUZZYWUZZY_AVAILABLE:
            # If fuzzy matching not available, use exact matching
            canonical = self.known_companies.get(normalized, original_name)
            self.known_companies[normalized] = canonical
            return canonical, original_name

        # Check for exact match first
        if normalized in self.known_companies:
            canonical = self.known_companies[normalized]
            if canonical not in self.company_aliases:
                self.company_aliases[canonical] = []
            if original_name not in self.company_aliases[canonical]:
                self.company_aliases[canonical].append(original_name)
            return canonical, original_name

        # Find fuzzy matches
        if self.known_companies:
            try:
                # Use the most appropriate scorer based on fuzzy library
                if FUZZY_BACKEND == "rapidfuzz":
                    # rapidfuzz has optimized scorers
                    from rapidfuzz.fuzz import WRatio as scorer
                else:
                    # fuzzywuzzy fallback
                    scorer = fuzz.ratio

                match, score = process.extractOne(
                    normalized,
                    self.known_companies.keys(),
                    scorer=scorer,
                    score_cutoff=self.similarity_threshold  # Pre-filter for performance
                )

                if match and score >= self.similarity_threshold:
                    canonical = self.known_companies[match]
                    if canonical not in self.company_aliases:
                        self.company_aliases[canonical] = []
                    if original_name not in self.company_aliases[canonical]:
                        self.company_aliases[canonical].append(original_name)
                    # Also map this normalized name to the canonical
                    self.known_companies[normalized] = canonical
                    return canonical, original_name
            except Exception as e:
                logger.debug(f"Fuzzy matching error for '{company_name}': {e}")

        # No match found, add as new canonical company
        canonical = original_name
        self.known_companies[normalized] = canonical
        self.company_aliases[canonical] = [original_name]
        return canonical, original_name

    def get_company_stats(self) -> Dict[str, Any]:
        """Get statistics about company normalization"""
        total_aliases = sum(len(aliases) for aliases in self.company_aliases.values())
        return {
            'unique_companies': len(self.company_aliases),
            'total_name_variants': total_aliases,
            'avg_variants_per_company': total_aliases / max(1, len(self.company_aliases))
        }


class HybridClassifier:
    """
    Consolidated Hybrid Campaign Classifier.
    """

    def __init__(self,
                 campaign_config_path: str,
                 text_processing_fields: List[str],
                 embedder_model_name: str = 'all-MiniLM-L6-v2',
                 embedder_cache_file: Optional[str] = "embedding_cache.pkl",
                 company_ner_model: str = "en_core_web_trf",
                 use_llm: bool = False,
                 llm_backend: str = 'ollama',
                 llm_model_path: Optional[str] = None,
                 llm_cache_file: Optional[str] = "llm_response_cache.pkl",
                 enhanced_llm: bool = True,
                 llm_timeout: int = 120,
                 llm_max_retries: int = 3,
                 llm_retry_delay: int = 5,
                 ner_cache_file: str = "ner_results_cache.pkl",
                 log_level: str = "INFO",
                 company_similarity_threshold: float = 85,
                 skip_terms_file: Optional[str] = None,
                 improve_rules_active: bool = False,
                 max_workers: Optional[int] = None,
                 parallel_chunk_size: int = 100,
                 succinct_names: bool = True):

        logger.setLevel(log_level.upper())
        self.console = Console()

        # USE EXACT YAML FIELD NAMES - DO NOT LOWERCASE
        self.text_processing_fields = text_processing_fields  # Keep exact YAML capitalization
        logger.info(f"HybridClassifier initialized. Text processing fields (exact YAML): {self.text_processing_fields}")

        self.improve_rules_active = improve_rules_active
        self.skip_terms_file = skip_terms_file
        self.succinct_names = succinct_names

        _invalid_summary_strings_input = ['NA', 'SKIPPED', '', 'None', 'null', "Summary generation failed",
                                          "no summary available"]
        self.invalid_summary_strings = {s.lower() for s in _invalid_summary_strings_input if isinstance(s, str)}

        self.skip_terms = []
        if skip_terms_file and os.path.exists(skip_terms_file):
            try:
                with open(skip_terms_file, 'r', encoding='utf-8') as f:
                    self.skip_terms = json.load(f)
                logger.info(f"📋 Loaded {len(self.skip_terms)} skip terms from {skip_terms_file}")
            except Exception as e:
                logger.warning(f"Failed to load skip terms from {skip_terms_file}: {e}")
        elif skip_terms_file:
            logger.warning(f"Skip terms file not found: {skip_terms_file}")

        self.campaign_config_path = Path(campaign_config_path)
        self.known_campaigns_processed: List[Dict[str, Any]] = []
        self._load_and_process_campaign_config()

        if not self.known_campaigns_processed:
            logger.warning("No campaign rules loaded. Rule-based matching will be ineffective.")
        self.ad_campaign_matcher = AdCampaignMatcher(self.known_campaigns_processed, logger)

        self.campaign_prototypes: Dict[str, np.ndarray] = {}
        if SENTENCE_TRANSFORMERS_AVAILABLE:
            actual_embedder_cache_file = embedder_cache_file or f"embedding_cache_{embedder_model_name.replace('/', '_')}.pkl"
            self.embedder = M4OptimizedEmbedder(model_name=embedder_model_name, cache_file=actual_embedder_cache_file)
            if self.embedder:
                self._generate_campaign_prototypes()
        else:
            logger.error("SentenceTransformers not installed. Embedding features will be disabled.")
            self.embedder = None

        self.use_llm = use_llm
        self.enhanced_llm = enhanced_llm
        self.llm_classifier: Optional[LocalLLMClassifier] = None
        if self.use_llm:
            actual_llm_cache_file = llm_cache_file or "llm_response_cache.pkl"
            self.llm_classifier = LocalLLMClassifier(
                backend=llm_backend, model_path=llm_model_path, cache_file=actual_llm_cache_file
            )

        self.ner_model = None
        self.ner_cache_file = Path(ner_cache_file) if ner_cache_file else None
        # CRITICAL NER CACHE KEY CHANGE: Key is raw_text, Value: entities_dict (MATCHING .BAK.PY)
        self._ner_results_cache: Dict[str, Dict[str, List[str]]] = {}
        self._ner_cache_hits = 0
        self._ner_cache_misses = 0
        if self.ner_cache_file and self.ner_cache_file.exists():
            try:
                with open(self.ner_cache_file, 'rb') as f:
                    self._ner_results_cache = pickle.load(f)
                logger.info(
                    f"Loaded {len(self._ner_results_cache)} NER results from cache: {self.ner_cache_file} (EXPECTING TEXT-KEYED CACHE)")
            except Exception as e:
                logger.warning(
                    f"Could not load NER results cache from {self.ner_cache_file}: {e}. A new text-keyed cache will be created.")
        elif self.ner_cache_file:
            logger.info(
                f"NER results cache file not found: {self.ner_cache_file}. A new text-keyed cache will be created.")
        else:
            logger.info("No NER cache file specified. NER results will not be persisted.")

        if SPACY_AVAILABLE:
            try:
                self.ner_model = spacy.load(company_ner_model, disable=["parser", "tagger"])
                logger.info(f"spaCy NER model '{company_ner_model}' loaded.")
            except OSError:
                logger.warning(
                    f"spaCy model '{company_ner_model}' not found. Try 'python -m spacy download {company_ner_model}'.")
            except Exception as e:
                logger.error(f"Error loading spaCy model '{company_ner_model}': {e}", exc_info=True)
        else:
            logger.warning("spaCy library not available. NER features disabled.")

        self.law_firm_suffixes = {"llp", "llc", "pc", "pllc", "attorney", "attorneys", "law", "legal", "lawyer",
                                  "lawyers", "advocates", "p.a.", "p.c.", "l.l.p.", "l.l.c."}
        self.law_firm_keywords = {"law office", "legal group", "law firm", "injury lawyer", "trial lawyers"}
        self.company_similarity_threshold = company_similarity_threshold
        self.company_normalizer = CompanyNameNormalizer(similarity_threshold=company_similarity_threshold)
        
        # Initialize law firms manager to load known law firms
        self.law_firms_manager = None
        self.known_law_firms = set()
        self.law_firm_fuzzy_threshold = 85  # Threshold for fuzzy matching law firms
        try:
            # Initialize with minimal config for law firms access
            law_firms_config = {
                'dynamo_table': 'LawFirms',
                'use_local': False  # Use production DynamoDB for law firms
            }
            self.law_firms_manager = LawFirmsManager(law_firms_config)
            # Load all law firm names for filtering
            law_firms_records = self.law_firms_manager.get_all_records(
                projection_expression='#name',
                expression_attribute_names={'#name': 'Name'}
            )
            self.known_law_firms = {record.get('Name', '').strip().lower() 
                                    for record in law_firms_records 
                                    if record.get('Name')}
            logger.info(f"Loaded {len(self.known_law_firms)} law firm names for NER filtering")
        except Exception as e:
            logger.warning(f"Failed to load law firms for NER filtering: {e}")
            self.known_law_firms = set()

        if FUZZYWUZZY_AVAILABLE:
            logger.info(
                f"🔍 Fuzzy matching enabled using {FUZZY_BACKEND} (similarity threshold: {company_similarity_threshold}%)")
        else:
            logger.warning("⚠️  Fuzzy matching disabled - company name normalization will use exact matching only")

        self.sklearn_available = False
        self.KMeans = None;
        self.silhouette_score = None;
        self.StandardScaler = None
        self.DBSCAN = None;
        self.NearestNeighbors = None
        if self.improve_rules_active:
            try:
                from sklearn.cluster import KMeans, DBSCAN
                from sklearn.metrics import silhouette_score
                from sklearn.preprocessing import StandardScaler
                from sklearn.neighbors import NearestNeighbors
                self.KMeans = KMeans;
                self.DBSCAN = DBSCAN;
                self.NearestNeighbors = NearestNeighbors
                self.silhouette_score = silhouette_score;
                self.StandardScaler = StandardScaler
                self.sklearn_available = True
                logger.info("scikit-learn found for RuleImprover.")
            except ImportError:
                logger.warning("scikit-learn not found for RuleImprover. Clustering features disabled.")

        # Parallel processing configuration - optimized for M4 Mac with 128GB RAM
        self.max_workers = max_workers or min(mp.cpu_count(), 24)  # Higher cap for M4 Mac
        self.parallel_chunk_size = parallel_chunk_size
        logger.info(f"Parallel processing configured: {self.max_workers} workers, chunk size: {self.parallel_chunk_size}")

    def _create_worker_config(self) -> Dict[str, Any]:
        """Create a serializable configuration for worker processes"""
        return {
            'campaign_config_path': str(self.campaign_config_path),
            'text_processing_fields': self.text_processing_fields,
            'embedder_model_name': getattr(self.embedder, 'model_name', 'all-MiniLM-L6-v2') if self.embedder else 'all-MiniLM-L6-v2',
            'embedder_cache_file': str(self.embedder.cache_file) if self.embedder and self.embedder.cache_file else None,
            'company_ner_model': 'en_core_web_trf',  # Default model
            'use_llm': self.use_llm,
            'llm_backend': getattr(self.llm_classifier, 'backend', 'ollama') if self.llm_classifier else 'ollama',
            'llm_model_path': getattr(self.llm_classifier, 'model_path', None) if self.llm_classifier else None,
            'llm_cache_file': getattr(self.llm_classifier, 'cache_file', None) if self.llm_classifier else None,
            'enhanced_llm': self.enhanced_llm,
            'ner_cache_file': str(self.ner_cache_file) if self.ner_cache_file else None,
            'company_similarity_threshold': self.company_similarity_threshold,
            'skip_terms_file': self.skip_terms_file,
            'improve_rules_active': False,  # Disable rule improvement in workers
            'max_workers': 1,  # Single worker per process
            'parallel_chunk_size': 1  # Process one at a time in worker
        }

    @staticmethod
    def _worker_process_ads(ads_chunk: List[Dict[str, Any]], worker_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Worker function for parallel ad processing.
        Returns serialized ClassificationResult dictionaries.
        """
        try:
            # Create a classifier instance in the worker process
            worker_classifier = HybridClassifier(**worker_config)
            
            results = []
            for ad_data in ads_chunk:
                try:
                    result = worker_classifier._classify_single_ad_item(ad_data)
                    # Convert to dict for serialization
                    result_dict = dataclasses.asdict(result)
                    results.append(result_dict)
                except Exception as e:
                    ad_id = ad_data.get('AdArchiveID', 'unknown')
                    logger.error(f"Worker error processing ad {ad_id}: {e}")
                    # Return empty result on error
                    empty_result = ClassificationResult(
                        ad_id=str(ad_id),
                        campaign="Other",
                        confidence=0.0,
                        method="error",
                        needs_review=True,
                        details={"error": str(e)}
                    )
                    results.append(dataclasses.asdict(empty_result))
            
            return results
            
        except Exception as e:
            logger.error(f"Worker process error: {e}")
            return []

    @staticmethod
    def _prepare_ad_text_for_processing(  # Updated to use exact YAML fields
            ad_data: Dict[str, Any],
            text_processing_fields: List[str],  # EXPECTS exact YAML field names (with capitalization)
            invalid_summary_strings_set: set
    ) -> Tuple[str, str]:
        raw_parts = []
        ad_id_for_log = ad_data.get('AdArchiveID', str(ad_data.get('ad_id', 'UnknownID_In_Prep')))

        for field_name in text_processing_fields:  # Use exact YAML field names
            value = ad_data.get(field_name)  # Direct lookup with exact capitalization

            if value is None:
                continue

            # Simple string conversion and strip
            value_str = str(value).strip()

            if not value_str:
                continue

            raw_parts.append(value_str)

        raw_combined_text = " ".join(raw_parts)  # Simple space separator
        normalized_combined_text = _normalize_text_for_matching(raw_combined_text)

        if not raw_combined_text and text_processing_fields:  # Log if empty after trying all fields
            logger.warning(
                f"AD_ID {ad_id_for_log} - _prepare_ad_text (NEW YAML logic): FINAL RAW TEXT IS EMPTY from fields: {text_processing_fields}.")

        return raw_combined_text, normalized_combined_text

    def _get_or_compute_ner_entities(self, text_to_analyze: str, ad_data_context: Dict[str, Any]) -> Dict[
        str, List[str]]:
        """
        Retrieves NER entities from cache or computes them.
        CRITICAL CHANGE: Uses the full `text_to_analyze` (raw_text) as the cache key, matching .bak.py.
        `ad_data_context` is used by `_extract_entities_from_doc`.
        """
        if not self.ner_model or not text_to_analyze or not text_to_analyze.strip():  # Ensure text is not just whitespace
            return {}

        # CRITICAL: Use the raw text itself as the cache key, like in .bak.py
        # Ensure no leading/trailing whitespace on key if text_to_analyze might have it
        # and .bak.py implicitly stripped keys or text before keying.
        # Assuming text_to_analyze from _prepare_ad_text_for_processing is already stripped.
        cache_key = text_to_analyze

        if self.ner_cache_file and cache_key in self._ner_results_cache:
            self._ner_cache_hits += 1
            return self._ner_results_cache[cache_key]

        self._ner_cache_misses += 1

        entities_by_type = defaultdict(list)
        try:
            max_spacy_len = getattr(self.ner_model, 'max_length', 1000000)
            # spaCy processes the text, which should be the original raw_text if it's for NER
            doc = self.ner_model(text_to_analyze[:max_spacy_len])
            entities_by_type = self._extract_entities_from_doc(doc, ad_data_context)
        except Exception as e:
            ad_id_log = ad_data_context.get('AdArchiveID', 'Unknown_in_NER_compute')
            logger.error(f"AD_ID: {ad_id_log} - spaCy NER processing error: {e}", exc_info=False)

        result_entities = dict(entities_by_type)
        if self.ner_cache_file:
            self._ner_results_cache[cache_key] = result_entities
        return result_entities

    def _classify_single_ad_item(self, ad_data_item: Dict[str, Any]) -> ClassificationResult:
        """
        Classifies a single ad item, mirroring .bak.py's classify_ad structure.
        This is the primary classification logic per ad.
        """
        ad_id = str(ad_data_item.get('AdArchiveID', ad_data_item.get('ad_id', f'unknown_single_{id(ad_data_item)}')))

        # 1. Prepare text - USES CORRECTED _prepare_ad_text_for_processing
        # self.text_processing_fields are already lowercased from __init__
        raw_text, normalized_text = self._prepare_ad_text_for_processing(
            ad_data_item,
            self.text_processing_fields,
            self.invalid_summary_strings
        )

        current_details = {
            "raw_text_snippet": raw_text[:200] if raw_text else "EMPTY_RAW_TEXT_FROM_PREP",
            "normalized_text_snippet": normalized_text[:200] if normalized_text else "EMPTY_NORM_TEXT_FROM_PREP",
            "extracted_entities": {}
        }

        # 2. NER Processing - USES CORRECTED _get_or_compute_ner_entities (with raw_text as key)
        all_extracted_entities: Dict[str, List[str]] = {}
        text_for_ner = raw_text  # Always use raw_text from consistent prep for NER key
        if self.ner_model and text_for_ner and text_for_ner.strip():
            try:
                # Pass ad_data_item as context for _extract_entities_from_doc
                all_extracted_entities = self._get_or_compute_ner_entities(text_for_ner, ad_data_item)
                current_details['extracted_entities'] = all_extracted_entities
            except Exception as ner_error:
                logger.error(f"AD_ID: {ad_id} - Error in _get_or_compute_ner_entities: {ner_error}", exc_info=False)

        # Initialize results based on .bak.py structure
        final_campaign: str = "Other"
        final_confidence: float = 0.0
        final_method: str = "none"
        rule_source_val: Optional[str] = None

        rule_confidence_val: float = 0.0  # Specificity score from rules
        embedding_confidence_val: float = 0.0  # Similarity score from embeddings
        llm_confidence_val: float = 0.0  # Confidence from LLM

        # 3. Rule-based matching (operates on normalized_text)
        if normalized_text and self.ad_campaign_matcher:
            campaign_rule, conf_rule_match = self.ad_campaign_matcher.match_ad_text(normalized_text,
                                                                                    ad_id_for_log=ad_id)
            rule_confidence_val = conf_rule_match
            if campaign_rule and campaign_rule != "Other":
                final_campaign = campaign_rule
                final_confidence = conf_rule_match  # Using specificity score as confidence proxy, as per AdCampaignMatcher
                final_method = "rule"
                rule_source_val = "manual_config"

                # 4. Skip term matching (only if no specific rule matched)
        # This part comes from the current version and seems fine to keep if final_method is not "rule"
        if final_method != "rule" and self.skip_terms and raw_text:
            for skip_term in self.skip_terms:
                if skip_term.lower() in raw_text.lower():
                    # If a skip term is found, it's "General" and review is not needed.
                    companies_skip, original_names_skip = self.extract_company_info(ad_data_item, "General",
                                                                                 all_extracted_entities)
                    # For compatibility, use first company if multiple found
                    company_skip = companies_skip[0] if companies_skip else None
                    original_name_skip = original_names_skip[0] if original_names_skip else None
                    current_details["final_extracted_companies"] = companies_skip
                    current_details["final_original_company_names"] = original_names_skip
                    current_details["final_extracted_company"] = company_skip  # Keep for backward compatibility
                    current_details["final_original_company_name"] = original_name_skip  # Keep for backward compatibility
                    return ClassificationResult(ad_id=ad_id, campaign="General", company=company_skip,
                                                original_name=original_name_skip,
                                                confidence=1.0, method="skip_terms", needs_review=False,
                                                # Skip terms are definitive
                                                rule_source=rule_source_val, rule_confidence=rule_confidence_val,
                                                embedding_confidence=embedding_confidence_val,
                                                llm_confidence=llm_confidence_val,
                                                details=current_details)

        # 5. Embedding-based matching (if not strongly rule-classified)  
        # Since rules always return 99% confidence, always proceed to embeddings for better accuracy
        # Only skip embeddings if rule found something AND it's not "Other"
        proceed_to_embedding = (final_method != "rule") or \
                               (final_method == "rule" and final_campaign == "Other")

        if self.embedder and self.campaign_prototypes and proceed_to_embedding:
            if raw_text:  # Embeddings operate on raw_text
                ad_embedding = self.embedder.encode(raw_text, show_progress=False)
                if ad_embedding is not None and ad_embedding.ndim > 0 and np.any(ad_embedding):
                    best_sim = -1.0
                    campaign_embed_match = None
                    valid_proto_data = {name: emb for name, emb in self.campaign_prototypes.items() if
                                        emb is not None and emb.ndim > 0 and np.any(emb)}
                    if valid_proto_data:
                        proto_names_list = list(valid_proto_data.keys())
                        proto_embs_array = np.array(list(valid_proto_data.values()))
                        if proto_embs_array.ndim == 2 and ad_embedding.ndim == 1 and proto_embs_array.shape[1] == \
                                ad_embedding.shape[0]:
                            ad_embedding_norm = np.linalg.norm(ad_embedding)
                            if ad_embedding_norm > 1e-8:
                                proto_embs_norms = np.linalg.norm(proto_embs_array, axis=1)
                                valid_indices_mask = proto_embs_norms > 1e-8
                                if np.any(valid_indices_mask):
                                    active_proto_embs = proto_embs_array[valid_indices_mask];
                                    active_proto_norms = proto_embs_norms[valid_indices_mask]
                                    active_proto_names = [proto_names_list[i] for i, keep in
                                                          enumerate(valid_indices_mask) if keep]
                                    if active_proto_embs.size > 0:
                                        sims = np.dot(active_proto_embs, ad_embedding) / (
                                                    active_proto_norms * ad_embedding_norm)
                                        if len(sims) > 0:
                                            idx_max_sim = np.argmax(sims);
                                            best_sim = sims[idx_max_sim]
                                            if idx_max_sim < len(active_proto_names): campaign_embed_match = \
                                            active_proto_names[idx_max_sim]

                    embedding_confidence_val = float(best_sim) if best_sim > -1.0 else 0.0
                    # .bak.py condition: confidence_embed > 0.6
                    if campaign_embed_match and campaign_embed_match != "Other" and embedding_confidence_val > 0.60:
                        # Check if this campaign has exclude terms that match the ad text
                        exclude_match = False
                        for campaign_data in self.known_campaigns_processed:
                            if campaign_data.get('LitigationName') == campaign_embed_match:
                                exclude_pattern = campaign_data.get('exclude_pattern')
                                if exclude_pattern and normalized_text and exclude_pattern.search(normalized_text):
                                    logger.debug(f"AD_ID: {ad_id} - Embedding match '{campaign_embed_match}' rejected due to exclude term match")
                                    exclude_match = True
                                break
                        
                        if not exclude_match and (embedding_confidence_val > final_confidence or final_campaign == "Other"):  # Or if current is "Other"
                            final_campaign = campaign_embed_match
                            final_confidence = embedding_confidence_val
                            final_method = "embedding"
                            rule_source_val = None
                            # else: logger.debug(f"AD_ID: {ad_id} - No raw text for embedding in _classify_single_ad_item.")

        # 6. Standard LLM-based classification (if enabled and not confidently classified by previous methods)
        # .bak.py condition: (final_method == "none" or (final_method == "embedding" and final_confidence < 0.65))
        proceed_to_llm = (final_method == "none") or \
                         (final_method == "embedding" and final_confidence < 0.65) or \
                         (final_method == "rule" and rule_confidence_val < 0.65 and final_campaign == "Other")

        if self.use_llm and self.llm_classifier and proceed_to_llm:
            candidate_campaigns_for_llm = list(self.campaign_prototypes.keys())
            if raw_text and candidate_campaigns_for_llm:
                campaign_llm_match, conf_llm_match = self.llm_classifier.classify(raw_text, candidate_campaigns_for_llm)
                llm_confidence_val = conf_llm_match
                # .bak.py condition: confidence_llm > 0.5
                if campaign_llm_match and campaign_llm_match != "Other" and llm_confidence_val > 0.50:
                    if llm_confidence_val > final_confidence or final_campaign == "Other":
                        final_campaign = campaign_llm_match
                        final_confidence = llm_confidence_val
                        final_method = "llm"
                        rule_source_val = None
                elif final_method == "none":  # LLM also failed or disabled
                    final_method = "llm_failed"
                    # Try individual ad campaign suggestion as fallback ONLY if LLM is enabled
                    if self.use_llm and self.llm_classifier and raw_text and raw_text.strip():
                        suggested_campaign, suggested_confidence = self._suggest_individual_campaign(raw_text, all_extracted_entities)
                        if suggested_campaign and suggested_campaign != "Other":
                            final_campaign = suggested_campaign
                            final_confidence = suggested_confidence
                            final_method = "llm_suggestion"
            # else conditions for no raw_text or no candidates already logged by classify_batch's pre-check

        # Company extraction, happens for all outcomes
        companies, original_names = self.extract_company_info(ad_data_item, final_campaign, all_extracted_entities)
        # For compatibility, use first company if multiple found
        company = companies[0] if companies else None
        original_name = original_names[0] if original_names else None
        current_details["final_extracted_companies"] = companies
        current_details["final_original_company_names"] = original_names
        current_details["final_extracted_company"] = company  # Keep for backward compatibility
        current_details["final_original_company_name"] = original_name  # Keep for backward compatibility

        # Determine 'needs_review' based on .bak.py logic
        needs_review = (final_method != "rule") and \
                       (final_confidence < 0.7 or (final_method == "none" and final_campaign == "Other"))
        if final_method == "skip_terms": needs_review = False  # Skip terms are definitive
        if final_method == "rule" and final_confidence >= 0.85: needs_review = False  # Strong rule match
        if final_campaign == "Other" and final_method != "skip_terms": needs_review = True

        return ClassificationResult(
            ad_id=ad_id, campaign=final_campaign, company=company, original_name=original_name,
            confidence=final_confidence, method=final_method, needs_review=needs_review,
            rule_source=rule_source_val, rule_confidence=rule_confidence_val,
            embedding_confidence=embedding_confidence_val, llm_confidence=llm_confidence_val,
            details=current_details
        )

    def _h_get_optimal_k_for_kmeans(self, embeddings_scaled: np.ndarray, max_k: int = 10) -> int:
        if not self.sklearn_available or self.silhouette_score is None or self.KMeans is None:
            heuristic_k = int(max(2, min(5, np.ceil(np.sqrt(len(embeddings_scaled)) / 2.5))))
            logger.debug(f"KMeans (helper): sklearn not fully available, using heuristic K={heuristic_k}")
            return heuristic_k
        if len(embeddings_scaled) < 4:
            logger.debug(
                f"KMeans (helper): Not enough samples ({len(embeddings_scaled)}) for silhouette. Returning K=1.")
            return 1 if len(embeddings_scaled) > 0 else 0

        upper_k_bound = min(max_k + 1, len(embeddings_scaled))
        k_range = range(2, upper_k_bound)

        if not list(k_range):
            logger.debug(
                f"KMeans (helper): k_range empty. n_samples={len(embeddings_scaled)}, max_k_to_test={upper_k_bound - 1}. Returning K=1.")
            return 1 if len(embeddings_scaled) > 0 else 0

        silhouette_scores = []
        for k_val in k_range:
            try:
                kmeans = self.KMeans(n_clusters=k_val, random_state=42, n_init='auto')
                cluster_labels = kmeans.fit_predict(embeddings_scaled)
                if len(set(cluster_labels)) > 1:
                    score = self.silhouette_score(embeddings_scaled, cluster_labels)
                    silhouette_scores.append(score)
                else:
                    silhouette_scores.append(-1)
            except Exception as e:
                logger.warning(f"Error calculating silhouette score for K={k_val} (KMeans helper): {e}");
                silhouette_scores.append(-1)

        if not silhouette_scores or max(silhouette_scores) <= -1 + 1e-9:
            heuristic_k = int(max(2, min(5, np.ceil(np.sqrt(len(embeddings_scaled)) / 2.5))))
            logger.debug(
                f"KMeans (helper): Silhouette scores not helpful (max: {max(silhouette_scores) if silhouette_scores else 'N/A'}), using heuristic K={heuristic_k}")
            return heuristic_k

        optimal_k = k_range[np.argmax(silhouette_scores)]
        logger.info(f"Optimal K for KMeans (helper): {optimal_k} (Max Silhouette: {max(silhouette_scores):.3f})")
        return optimal_k

    def _h_estimate_dbscan_eps(self, embeddings_scaled: np.ndarray, min_samples_dbscan: int,
                               percentile: float = 80.0) -> Optional[float]:
        if not self.sklearn_available or not self.NearestNeighbors or len(embeddings_scaled) < min_samples_dbscan:
            logger.warning(
                f"DBSCAN eps (helper): NearestNeighbors not available or insufficient samples ({len(embeddings_scaled)} < {min_samples_dbscan}).")
            return None
        try:
            k_for_eps_calc = min_samples_dbscan
            nn = self.NearestNeighbors(n_neighbors=k_for_eps_calc, metric='euclidean')
            nn.fit(embeddings_scaled)
            distances, _ = nn.kneighbors(embeddings_scaled)

            if distances.shape[1] < k_for_eps_calc:
                logger.warning(
                    f"DBSCAN eps (helper): Requested {k_for_eps_calc} neighbors, but data only supports {distances.shape[1]}. Using {distances.shape[1]}.")
                k_for_eps_calc = distances.shape[1]
                if k_for_eps_calc == 0: return None

            kth_distances = np.sort(distances[:, k_for_eps_calc - 1])
            eps_value = np.percentile(kth_distances, percentile)
            logger.info(
                f"Estimated DBSCAN eps (helper): {eps_value:.4f} (using {k_for_eps_calc}-th neighbor distances at {percentile}th percentile)")

            if eps_value <= 1e-6:
                logger.warning(
                    f"DBSCAN eps (helper): Estimated eps ({eps_value:.4f}) is very small. DBSCAN might not be effective or may be sensitive.")
                non_zero_kth_distances = kth_distances[kth_distances > 1e-6]
                if len(non_zero_kth_distances) > 0:
                    num_to_avg = max(1, int(len(non_zero_kth_distances) * 0.10))
                    adjusted_eps = np.mean(np.sort(non_zero_kth_distances)[:num_to_avg]) * 1.5
                    logger.info(
                        f"DBSCAN eps (helper): Adjusted eps based on smallest non-zero k-th distances to: {adjusted_eps:.4f}")
                    if adjusted_eps > 1e-5:
                        eps_value = adjusted_eps
                    else:
                        return None
                else:
                    logger.warning(
                        "DBSCAN eps (helper): All k-th distances are zero or near-zero. Cannot determine suitable eps.")
                    return None
            return eps_value
        except Exception as e:
            logger.error(f"Error during DBSCAN eps estimation (helper): {e}", exc_info=True)
            return None

    def _h_get_frequent_entities_from_samples(self,
                                              list_of_entity_dicts: List[Dict[str, List[str]]],
                                              min_entity_occurrence: int = 2,
                                              top_n_entities: int = 3
                                              ) -> Tuple[List[str], List[str]]:
        org_counts = Counter()
        prod_counts = Counter()

        for entities_for_one_ad in list_of_entity_dicts:
            if isinstance(entities_for_one_ad, dict):
                for org_entity in entities_for_one_ad.get("ORG", []): org_counts[org_entity] += 1
                for prod_entity in entities_for_one_ad.get("PRODUCT", []): prod_counts[prod_entity] += 1

        frequent_orgs = [org for org, count in org_counts.most_common(top_n_entities * 2) if
                         count >= min_entity_occurrence][:top_n_entities]
        frequent_prods = [prod for prod, count in prod_counts.most_common(top_n_entities * 2) if
                          count >= min_entity_occurrence][:top_n_entities]
        return frequent_orgs, frequent_prods

    # classify_ad will now be a simpler helper, or its logic absorbed into classify_batch stages.
    # For this radical refactor, let's assume classify_batch takes over orchestration.


    def _load_and_process_campaign_config(self):
        if not self.campaign_config_path.exists():
            logger.error(f"Campaign config file not found: {self.campaign_config_path}")
            return
        try:
            with open(self.campaign_config_path, 'r', encoding='utf-8') as f:
                raw_campaign_data = json.load(f)
        except Exception as e:
            logger.error(f"Error loading or parsing campaign config {self.campaign_config_path}: {e}")
            return
        if not isinstance(raw_campaign_data, list):
            logger.error("Campaign config must be a JSON list of campaign objects.")
            return
        processed_campaigns = []
        for campaign_dict in raw_campaign_data:
            current_campaign_data = campaign_dict.copy()
            litigation_name = current_campaign_data.get('LitigationName')
            if not litigation_name:
                logger.warning(f"Skipping campaign entry due to missing 'LitigationName': {current_campaign_data}")
                continue
            triggers_raw = current_campaign_data.get("triggers", [])
            if not triggers_raw or not isinstance(triggers_raw, list) or not all(
                    isinstance(t, str) for t in triggers_raw):
                logger.warning(f"Campaign '{litigation_name}' has invalid/missing triggers. Skipping.")
                continue
            processed_trigger_terms = [_normalize_text_for_matching(t) for t in triggers_raw if t and t.strip()]
            if not processed_trigger_terms:
                logger.warning(
                    f"Campaign '{litigation_name}' has no valid trigger terms after normalization. Skipping.")
                continue
            current_campaign_data['trigger_pattern'] = _compile_term_pattern(processed_trigger_terms,
                                                                             f"triggers for {litigation_name}")
            if not current_campaign_data['trigger_pattern']:
                logger.warning(f"Failed to compile trigger pattern for '{litigation_name}'. Skipping this campaign.")
                continue
            includes_raw = current_campaign_data.get("include", [])
            current_campaign_data['include_or_pattern'] = None
            if isinstance(includes_raw, list) and all(isinstance(i, str) for i in includes_raw):
                processed_include_terms = [_normalize_text_for_matching(i) for i in includes_raw if i and i.strip()]
                if processed_include_terms:
                    current_campaign_data['include_or_pattern'] = _compile_term_pattern(processed_include_terms,
                                                                                        f"includes for {litigation_name}")
            excludes_raw = current_campaign_data.get("exclude", [])
            current_campaign_data['exclude_pattern'] = None
            if isinstance(excludes_raw, list) and all(isinstance(e, str) for e in excludes_raw):
                processed_exclude_terms = [_normalize_text_for_matching(e) for e in excludes_raw if e and e.strip()]
                if processed_exclude_terms:
                    current_campaign_data['exclude_pattern'] = _compile_term_pattern(processed_exclude_terms,
                                                                                     f"excludes for {litigation_name}")
            processed_campaigns.append(current_campaign_data)
        self.known_campaigns_processed = processed_campaigns
        logger.info(
            f"Loaded and processed {len(self.known_campaigns_processed)} campaigns from {self.campaign_config_path}")

    def _generate_campaign_prototypes(self):
        if not self.embedder or not self.known_campaigns_processed:
            logger.warning("Skipping campaign prototype generation: Embedder or known campaigns not available.")
            return
        campaign_texts_for_proto = []
        campaign_names_for_proto = []
        for campaign_data in self.known_campaigns_processed:
            name = campaign_data.get('LitigationName')
            if not name:
                logger.warning(f"Skipping prototype for campaign due to missing 'LitigationName': {campaign_data}")
                continue
            triggers = campaign_data.get('triggers', [])
            includes = campaign_data.get('include', [])
            proto_text_parts = [t for t in triggers if isinstance(t, str) and t.strip()]
            proto_text_parts.extend([i for i in includes if isinstance(i, str) and i.strip()])
            campaign_text_for_embedding = name
            if proto_text_parts:
                campaign_text_for_embedding += " " + " ".join(proto_text_parts)
            logger.debug(
                f"Preparing prototype embedding for campaign '{name}' using text: '{campaign_text_for_embedding[:150]}...'")
            campaign_texts_for_proto.append(campaign_text_for_embedding)
            campaign_names_for_proto.append(name)
        if campaign_texts_for_proto:
            logger.info(f"Generating prototype embeddings for {len(campaign_texts_for_proto)} campaigns...")
            try:
                embeddings = self.embedder.encode(campaign_texts_for_proto, show_progress=False)
                successful_embeddings = 0
                failed_embeddings = 0
                for campaign_name, emb in zip(campaign_names_for_proto, embeddings):
                    if emb is not None and hasattr(emb, 'shape') and emb.shape[0] > 0 and np.any(emb != 0):
                        self.campaign_prototypes[campaign_name] = emb
                        successful_embeddings += 1
                    else:
                        logger.warning(f"Failed to generate valid embedding for campaign prototype: {campaign_name}.")
                        failed_embeddings += 1
                logger.info(f"Successfully generated {successful_embeddings} campaign prototype embeddings.")
                if failed_embeddings > 0:
                    logger.warning(f"Failed to generate embeddings for {failed_embeddings} campaign prototypes.")
            except Exception as e:
                logger.error(f"Error during batch embedding of campaign prototypes: {e}", exc_info=True)
        else:
            logger.warning("No valid texts found to generate campaign prototypes.")

    def _is_law_firm_or_legal_service(self, name: str, text_for_context: str = "") -> bool:
        """Checks if a name likely refers to a law firm or legal service."""
        if not name:
            return False
        name_lower = name.lower()
        text_lower = text_for_context.lower() if text_for_context else ""

        # Check for common law firm suffixes in the name itself
        for suffix in self.law_firm_suffixes:
            if f" {suffix}" in name_lower or name_lower.endswith(suffix) or f"{suffix}." in name_lower:
                return True
        # Check for common law firm keywords in the name
        for keyword in self.law_firm_keywords:
            if keyword in name_lower:
                return True

        # Contextual check: if the name appears in ad text that also contains legal keywords
        # This helps if the firm name itself is generic but context implies legal service
        if text_lower:  # only if context is provided
            if name in text_for_context:  # Check if the exact name is in the broader text
                if any(keyword in text_lower for keyword in self.law_firm_keywords) or \
                        any(suffix in text_lower for suffix in
                            self.law_firm_suffixes):  # And context contains legal terms
                    return True
        return False

    def _get_frequent_entities_for_company_extraction(self,
                                                      entities: Dict[str, List[str]],
                                                      entity_type: str = "ORG",
                                                      min_occurrences: int = 1,
                                                      # For single ad, this is effectively always met if entity exists
                                                      top_n: int = 3) -> List[str]:
        """
        Helper to get frequent/relevant entities of a specific type (e.g., ORG) from a single ad's NER results.
        For a single ad, "frequent" is less relevant than "plausible".
        """
        if not entities or entity_type not in entities:
            return []

        potential_companies = []
        # Filter out overly generic or common non-company organizations
        # This list can be expanded based on data observation
        common_non_companies_lowercase = {
            "facebook", "meta", "google", "news", "corp", "inc", "llc", "llp", "pc",
            "government", "department", "university", "college", "hospital",
            "state of", "county of", "city of", "united states", "congress"
                                                                 "center", "federal", "national", "group", "institute",
            "association"
        }

        for ent_text in entities[entity_type]:
            if not ent_text or not isinstance(ent_text, str):
                continue

            ent_lower = ent_text.lower()

            # Basic length and complexity checks
            if not (2 < len(ent_text) < 70):  # Avoid extremely short or long strings
                continue
            if len(ent_text.split()) > 6:  # Avoid overly long phrases
                continue
            if ent_lower in common_non_companies_lowercase:
                continue
            if any(non_comp_kw in ent_lower for non_comp_kw in
                   [" news", " media", " daily", " times", " post", " journal"]):  # common media suffixes
                if not self._is_law_firm_or_legal_service(ent_text):  # unless it explicitly looks like a law firm
                    continue

            potential_companies.append(ent_text)

        # Currently, this just returns filtered list; if scoring/ranking were added, top_n would apply here.
        # For now, it will return up to all plausible ORGs found.
        return potential_companies[:top_n]

    def _suggest_individual_campaign(self, raw_text: str, all_extracted_entities: Dict[str, List[str]]) -> Tuple[str, float]:
        """
        Uses LLM to suggest a specific campaign name for an individual ad when standard classification fails.
        Returns (suggested_campaign_name, confidence_score)
        """
        if not self.llm_classifier or not self.llm_classifier.model:
            return "Other", 0.0
            
        # Extract entity hints for the prompt
        frequent_orgs_hint = []
        frequent_prods_hint = []
        
        if all_extracted_entities:
            orgs = all_extracted_entities.get("ORG", [])
            if orgs:
                frequent_orgs_hint = orgs[:3]  # Top 3 organizations
                
            products = all_extracted_entities.get("PRODUCT", [])
            if products:
                frequent_prods_hint = products[:3]  # Top 3 products

        # Truncate text for LLM prompt
        text_for_analysis = raw_text[:1000] if len(raw_text) > 1000 else raw_text
        
        # Get known campaigns for matching
        known_campaigns = []
        if hasattr(self, 'campaign_rules') and self.campaign_rules:
            known_campaigns = [rule.get('LitigationName', '') for rule in self.campaign_rules if rule.get('LitigationName')]
            known_campaigns = [name for name in known_campaigns if name not in ['Other', 'General']]
        
        # Build the campaign suggestion prompt (try to match known campaigns first)
        if hasattr(self, 'succinct_names') and self.succinct_names:
            prompt_text = (
                "You are an expert legal advertising analyst. Your task is to classify an ad into an existing litigation campaign.\n"
                "FIRST, try to match the ad to one of these KNOWN CAMPAIGNS:\n"
            )
            if known_campaigns:
                for i, campaign in enumerate(known_campaigns[:20]):  # Show first 20 to avoid token limit
                    prompt_text += f"{i+1}. {campaign}\n"
                prompt_text += "\nIf the ad clearly matches one of these campaigns, use that EXACT name.\n"
            
            prompt_text += (
                "If NO known campaign matches, create a SHORT campaign name: 'Company/Product + Core Issue + Lawsuit'.\n"
                "Respond with JSON: {\"campaign_name\": \"exact_name\", \"confidence\": 0.0-1.0}\n\n"
            )
        else:
            prompt_text = (
                "You are an expert legal advertising analyst. Your task is to identify a specific litigation campaign by analyzing an ad text.\n"
                "The following text is from an online advertisement that could not be classified into predefined legal litigation categories. "
                "Based SOLELY on the provided text and contextual hints, suggest:\n"
                "1. A CONCISE and SPECIFIC name for a potential litigation campaign (e.g., 'XYZ Drug Heart Problems Lawsuit', 'Faulty ABC Product Injuries'). AVOID overly generic names like 'Injury Claims' or 'Consumer Protection'.\n"
                "2. A confidence score between 0.0 and 1.0 indicating how certain you are about this classification.\n\n"
            )
        
        # Add entity hints if available
        if frequent_orgs_hint or frequent_prods_hint:
            prompt_text += "Contextual Hints from Named Entity Recognition (These might be central to the litigation theme):\n"
            if frequent_orgs_hint: 
                prompt_text += f"- Potentially relevant organizations: {', '.join(frequent_orgs_hint)}\n"
            if frequent_prods_hint: 
                prompt_text += f"- Potentially relevant products/services: {', '.join(frequent_prods_hint)}\n"
        
        prompt_text += (
            "\nReview the following examples of input ad text snippets and desired JSON output (notice the specificity):\n\n"
            "Example 1:\nInput Ad Text Snippet: \"Our investigation suggests that the Ram Promaster 2022-2023 models are incapable of actually activating the 8th and 9th gears under normal driving conditions.\"\nCorresponding Suggested JSON:\n{\"campaign_name\": \"Ram Promaster Transmission Defect Lawsuit\", \"confidence\": 0.85}\n\n"
            "Example 2:\nInput Ad Text Snippet: \"Nitrous oxide addiction can start small — but lead to serious health consequences and a lasting impact on your life. If you or someone you care about is struggling with this addiction and experiencing harm, we're here to help. Legal options may be available.\"\nCorresponding Suggested JSON:\n{\"campaign_name\": \"Nitrous Oxide Inhalant Addiction Harm\", \"confidence\": 0.75}\n\n"
            "Example 3:\nInput Ad Text Snippet: \"Sharpen App users: If you accessed study videos through iOS, Android, or Chrome starting 10/11/2022 and had an account, you could be eligible for compensation. Tap 'Apply Now' to find out if you're eligible. Bursor & Fisher, P.A. ...\"\nCorresponding Suggested JSON:\n{\"campaign_name\": \"Sharpen App User Data Privacy Claim\", \"confidence\": 0.90}\n\n"
            "Example 4:\nInput Ad Text Snippet: \"Ticketmaster customers—were you charged more than expected for tickets? You're not alone, and these practices could be illegal. We recently filed a case in California and we are now looking for participants in Maryland. ... Tycko & Zavareei LLP ...\"\nCorresponding Suggested JSON:\n{\"campaign_name\": \"Ticketmaster Excessive Fees Lawsuit (Maryland)\", \"confidence\": 0.88}\n\n"
            "IMPORTANT: Respond ONLY with a single valid JSON object containing the keys 'campaign_name' (a string) and 'confidence' (a number between 0.0 and 1.0). Ensure the campaign name is specific and distinguishing. Do not include any other text, explanations, or apologies.\n\n"
            f"Text to analyze for campaign suggestion:\n\"{text_for_analysis}\""
        )
        
        try:
            # Use the LLM to generate campaign suggestion
            messages = [{'role': 'user', 'content': prompt_text}]
            
            # For Ollama with format="json" support
            response = self.llm_classifier.model.chat(
                model=self.llm_classifier.model_path,
                messages=messages,
                format="json",
                options={"temperature": 0.3}  # Lower temperature for more consistent results
            )
            
            if response and hasattr(response, 'message') and hasattr(response.message, 'content'):
                import json
                try:
                    result = json.loads(response.message.content)
                    campaign_name = result.get('campaign_name', 'Other')
                    confidence = float(result.get('confidence', 0.0))
                    
                    # Validate confidence range
                    confidence = max(0.0, min(1.0, confidence))
                    
                    logger.info(f"Individual campaign suggestion: '{campaign_name}' (confidence: {confidence:.2f})")
                    return campaign_name, confidence
                    
                except (json.JSONDecodeError, ValueError, KeyError) as e:
                    logger.warning(f"Failed to parse LLM campaign suggestion JSON: {e}")
                    return "Other", 0.0
            else:
                logger.warning("No valid response from LLM for campaign suggestion")
                return "Other", 0.0
                
        except Exception as e:
            # Check if it's a connection error (Ollama not running)
            if "connection" in str(e).lower() or "refused" in str(e).lower():
                logger.warning(f"Ollama connection unavailable for campaign suggestion: {e}")
            else:
                logger.error(f"Error during individual campaign suggestion: {e}")
            return "Other", 0.0

    def extract_company_info(self, ad_data: Dict[str, Any], campaign_name: str,
                             all_extracted_entities: Dict[str, List[str]]) -> Tuple[Optional[List[str]], Optional[List[str]]]:
        """
        Extracts target defendant company names from ad data or NER results.
        Filters out law firms and legal services, focusing on actual defendant companies.
        Returns (canonical_company_names_list, original_company_names_list).
        """
        defendant_companies: List[str] = []
        
        # Legal acronyms to exclude
        legal_acronyms = {'llc', 'llp', 'lp', 'pc', 'pllc', 'pa', 'esq', 'ltd', 'limited', 
                         'corporation', 'corp', 'inc', 'incorporated', 'co', 'company'}

        # Get NER entities
        raw_text_for_context, _ = self._prepare_ad_text_for_processing(
            ad_data, self.text_processing_fields, self.invalid_summary_strings
        )

        # Get all ORG entities
        all_orgs = all_extracted_entities.get("ORG", [])
        
        if all_orgs:
            # Filter out law firms and legal services
            for org in all_orgs:
                if not org or not org.strip():
                    continue
                    
                org_clean = org.strip()
                org_lower = org_clean.lower()
                
                # Skip if it's a law firm based on suffixes/keywords
                if self._is_law_firm_or_legal_service(org_clean, raw_text_for_context):
                    logger.debug(f"AD_ID {ad_data.get('AdArchiveID', '')} - Skipping law firm: {org_clean}")
                    continue
                
                # Skip if it's in the law firms database (fuzzy match)
                if self.law_firms_manager:
                    try:
                        # Search for the organization in law firms database
                        law_firm_results = self.law_firms_manager.search_law_firms(org_clean)
                        if law_firm_results:
                            # Found in law firms database, skip it
                            logger.debug(f"AD_ID {ad_data.get('AdArchiveID', '')} - Found in law firms DB: {org_clean}")
                            continue
                    except Exception as e:
                        logger.warning(f"Error searching law firms database: {e}")
                
                # Remove legal acronyms from the end of the company name
                words = org_clean.split()
                while words and words[-1].lower().replace('.', '') in legal_acronyms:
                    words.pop()
                
                if words:  # If there's still something left after removing legal acronyms
                    cleaned_company = ' '.join(words)
                    
                    # Additional filters for common non-company entities
                    if any(keyword in cleaned_company.lower() for keyword in 
                          ['court', 'judge', 'attorney', 'lawyer', 'legal', 'law office',
                           'law group', 'law firm', 'associates', 'partners']):
                        continue
                    
                    # Check if it's a valid company name (not too short, not just initials)
                    if len(cleaned_company) > 2 and not cleaned_company.isupper() or len(cleaned_company.split()) > 1:
                        defendant_companies.append(cleaned_company)
                        logger.debug(f"AD_ID {ad_data.get('AdArchiveID', '')} - Found defendant company: {cleaned_company}")

        # If no companies found from NER, check if campaign name might be the company
        if not defendant_companies and campaign_name and \
                campaign_name not in ["Other", "General", "Data Breach", "Privacy Violation", "Unknown"]:
            # Check if campaign name is likely a company/product name
            if len(campaign_name.split()) <= 4 and not any(kw in campaign_name.lower() for kw in
                                                           ["litigation", "lawsuit", "claims", "settlement", "injury",
                                                            "cancer", "recall", "defect", "syndrome", "exposure",
                                                            "disorder", "victims", "compensation"]):
                defendant_companies.append(campaign_name)
                logger.debug(f"AD_ID {ad_data.get('AdArchiveID', '')} - Company inferred from campaign name: {campaign_name}")

        # Normalize the found companies
        if defendant_companies:
            canonical_names = []
            original_names = []
            
            # Remove duplicates while preserving order
            seen = set()
            unique_companies = []
            for company in defendant_companies:
                normalized = self.company_normalizer._normalize_company_name(company)
                if normalized not in seen:
                    seen.add(normalized)
                    unique_companies.append(company)
            
            for company in unique_companies:
                canonical_name, _ = self.company_normalizer.add_or_match_company(company)
                if canonical_name:
                    canonical_names.append(canonical_name)
                    original_names.append(company)
            
            if canonical_names:
                logger.info(f"AD_ID {ad_data.get('AdArchiveID', '')} - Extracted defendant companies: {canonical_names}")
                return canonical_names, original_names

        logger.debug(f"AD_ID {ad_data.get('AdArchiveID', '')} - No defendant company information extracted.")
        return None, None

    def _extract_entities_from_doc(self, doc: Any, ad_data_context: Dict[str, Any]) -> Dict[str, List[str]]:
        """
        Extracts entities from a spaCy Doc object.
        'ad_data_context' is passed for potential future use but not used in this basic version.
        """
        entities_by_type = defaultdict(list)
        if not doc or not hasattr(doc, 'ents'):
            logger.debug(
                f"AD_ID {ad_data_context.get('AdArchiveID', 'Unknown')} - _extract_entities_from_doc: Invalid or empty doc object received.")
            return {}

        try:
            for ent in doc.ents:
                # Standard list of interesting entity types, can be configured if needed
                if ent.label_ in ["ORG", "PERSON", "PRODUCT", "LAW", "LOC", "GPE", "NORP", "FAC", "EVENT",
                                  "WORK_OF_ART", "MONEY", "QUANTITY", "DATE", "TIME"]:
                    entities_by_type[ent.label_].append(ent.text.strip())

            # Deduplicate and sort entities for consistency
            for ent_type in entities_by_type:
                entities_by_type[ent_type] = sorted(list(set(entities_by_type[ent_type])))

        except Exception as e:
            ad_id = ad_data_context.get('AdArchiveID', 'Unknown_Context_ID')
            logger.warning(
                f"AD_ID: {ad_id} - Error extracting entities from spaCy doc in _extract_entities_from_doc: {e}",
                exc_info=False)
            return {}  # Return empty if error during extraction

        return dict(entities_by_type)

    def _batch_process_ner(self, ads_data_list_batch: List[Dict[str, Any]], pipe_batch_size: int = 32,
                           n_process: int = 1):
        """
        Pre-warms the NER cache using .bak.py approach with text_hash->ad_ids mapping 
        and ner_{ad_id}_{text_hash} cache keys for compatibility.
        """
        if not self.ner_model:
            logger.info("NER model not available, skipping batch NER pre-warming.")
            return

        # Build mapping like .bak.py: text_hash -> list of ad_ids with that text
        text_hash_to_ad_ids_map: Dict[str, List[str]] = defaultdict(list)
        unique_text_hashes_for_pipe: Set[str] = set()
        texts_for_ner_pipe_input: List[str] = []  # Actual text strings for spaCy
        initial_hits_in_prep_phase = 0

        if self.ner_cache_file:
            logger.info(
                f"NER_CACHE_INIT_STATUS (Warming): Cache file is '{self.ner_cache_file}'. Loaded cache size: {len(self._ner_results_cache)} items.")
        else:
            logger.info("NER_CACHE_INIT_STATUS (Warming): No NER cache file specified.")

        # Stage 1: Build text_hash mapping and identify what needs processing
        composite_hits = 0
        raw_text_hits = 0
        
        for ad_data in ads_data_list_batch:
            raw_text, _ = self._prepare_ad_text_for_processing(
                ad_data, self.text_processing_fields, self.invalid_summary_strings
            )
            
            if raw_text and raw_text.strip():
                ad_id_val = ad_data.get('AdArchiveID', ad_data.get('ad_id', 'unknown_ad_id_in_bnp_prep'))
                ad_id_for_cache = str(ad_id_val)
                
                max_len_for_hash = 10000  # Consistent with individual NER method
                text_for_hash_key_gen = raw_text[:max_len_for_hash]
                current_text_hash = hashlib.md5(text_for_hash_key_gen.encode('utf-8')).hexdigest()
                
                # Map this ad_id to this text_hash
                text_hash_to_ad_ids_map[current_text_hash].append(ad_id_for_cache)
                
                # Check if this specific ad_id + text_hash combo is already cached (both formats)
                specific_cache_key = f"ner_{ad_id_for_cache}_{current_text_hash}"
                raw_text_cache_key = raw_text
                
                # Debug: Log cache key checking for first few items
                if initial_hits_in_prep_phase < 5:
                    composite_in_cache = specific_cache_key in self._ner_results_cache
                    raw_in_cache = raw_text_cache_key in self._ner_results_cache
                    logger.debug(f"Cache check for AD_ID {ad_id_for_cache}: composite_key='{specific_cache_key[:50]}...', in_cache={composite_in_cache}, raw_key='{raw_text_cache_key[:50]}...', in_cache={raw_in_cache}")
                
                # Check for both composite and raw text cache entries
                cache_hit = False
                hit_type = None
                if self.ner_cache_file:
                    if specific_cache_key in self._ner_results_cache:
                        cache_hit = True
                        hit_type = "composite"
                        composite_hits += 1
                    elif raw_text_cache_key in self._ner_results_cache:
                        # Copy from raw text cache to composite cache for future efficiency
                        cached_result = self._ner_results_cache[raw_text_cache_key]
                        self._ner_results_cache[specific_cache_key] = cached_result
                        cache_hit = True
                        hit_type = "raw_text"
                        raw_text_hits += 1
                
                if cache_hit:
                    initial_hits_in_prep_phase += 1
                
                # If this unique text content hasn't been queued for spaCy yet:
                if current_text_hash not in unique_text_hashes_for_pipe:
                    # Check if we can use cached results for this text (prioritize raw text cache for efficiency)
                    needs_spacy_processing = True
                    if self.ner_cache_file:
                        # First check if we have raw text cache (most efficient - covers all ad_ids with this text)
                        if raw_text_cache_key in self._ner_results_cache:
                            needs_spacy_processing = False
                            # Populate composite cache entries for all ad_ids with this text for future efficiency
                            temp_associated_ad_ids = text_hash_to_ad_ids_map.get(current_text_hash, [])
                            cached_result = self._ner_results_cache[raw_text_cache_key]
                            for temp_ad_id in temp_associated_ad_ids:
                                composite_key = f"ner_{temp_ad_id}_{current_text_hash}"
                                if composite_key not in self._ner_results_cache:
                                    self._ner_results_cache[composite_key] = cached_result
                            
                            # Log this raw text cache utilization
                            if len(temp_associated_ad_ids) > 1:
                                logger.debug(f"Raw text cache hit covers {len(temp_associated_ad_ids)} ad_ids with hash {current_text_hash[:8]}...")
                        else:
                            # Fallback: check if ALL ad_ids for this text hash have composite cache entries
                            all_cached_for_this_hash = True
                            temp_associated_ad_ids = text_hash_to_ad_ids_map.get(current_text_hash, [])
                            for temp_ad_id in temp_associated_ad_ids:
                                composite_key = f"ner_{temp_ad_id}_{current_text_hash}"
                                if composite_key not in self._ner_results_cache:
                                    all_cached_for_this_hash = False
                                    break
                            if all_cached_for_this_hash:
                                needs_spacy_processing = False
                    
                    if needs_spacy_processing:
                        texts_for_ner_pipe_input.append(raw_text)
                        unique_text_hashes_for_pipe.add(current_text_hash)

        if initial_hits_in_prep_phase > 0:
            logger.info(
                f"Batch NER Prep (Cache Warming): {initial_hits_in_prep_phase} AD_ID-text combinations found in cache ({composite_hits} composite keys, {raw_text_hits} raw text keys).")

        if not texts_for_ner_pipe_input:
            logger.info("Batch NER (Cache Warming): No new unique texts need processing by spaCy for warming.")
            return

        logger.info(f"Batch NER (Cache Warming): Starting spaCy for {len(texts_for_ner_pipe_input)} unique texts.")

        max_spacy_len = getattr(self.ner_model, 'max_length', 1000000)
        truncated_texts_for_pipe = [text[:max_spacy_len] for text in texts_for_ner_pipe_input]
        newly_cached_items_count = 0

        try:
            # Process texts with spaCy and map results back to all associated ad_ids
            # Force n_process=1 to avoid multiprocessing issues with large transformer models
            docs_with_texts = zip(
                self.ner_model.pipe(truncated_texts_for_pipe, batch_size=pipe_batch_size, n_process=1),
                texts_for_ner_pipe_input
            )

            for doc, processed_text in track(
                    docs_with_texts,
                    description="[cyan]NER Warming (spaCy pipe)...",
                    total=len(texts_for_ner_pipe_input),
                    console=self.console,
                    transient=True
            ):
                # Extract entities from this doc
                # Use first ad_data that maps to this text for context (any will do)
                text_hash_of_processed_text = hashlib.md5(processed_text[:10000].encode('utf-8')).hexdigest()
                associated_ad_ids = text_hash_to_ad_ids_map.get(text_hash_of_processed_text, [])
                
                # Use the first associated ad_id's data for context
                temp_ad_data_for_context = {}
                if associated_ad_ids:
                    # Find the ad_data for context - use any ad with this text
                    for ad_data in ads_data_list_batch:
                        if str(ad_data.get('AdArchiveID', ad_data.get('ad_id', ''))) == associated_ad_ids[0]:
                            temp_ad_data_for_context = ad_data
                            break

                entities = self._extract_entities_from_doc(doc, temp_ad_data_for_context)

                # Save cache entries for all ad_ids associated with this text_hash
                if self.ner_cache_file:
                    for ad_id_to_cache_for in associated_ad_ids:
                        key_to_store = f"ner_{ad_id_to_cache_for}_{text_hash_of_processed_text}"
                        if key_to_store not in self._ner_results_cache:
                            self._ner_results_cache[key_to_store] = entities
                            newly_cached_items_count += 1
                        elif self._ner_results_cache[key_to_store] != entities:
                            self._ner_results_cache[key_to_store] = entities
                            logger.debug(f"Overwrote existing cache for {key_to_store} as new NER result differed.")

        except Exception as e:
            logger.error(f"Batch NER (Cache Warming): Error during spaCy nlp.pipe(): {e}", exc_info=True)

        if self.ner_cache_file and newly_cached_items_count > 0:
            try:
                with open(self.ner_cache_file, 'wb') as f:
                    pickle.dump(self._ner_results_cache, f)
                logger.info(
                    f"Batch NER (Cache Warming): Saved {newly_cached_items_count} new items to NER cache. Total cache size: {len(self._ner_results_cache)}.")
            except Exception as e:
                logger.error(f"Batch NER (Cache Warming): Failed to save NER cache: {e}")

    def _classify_ads_parallel(self, ads_data_list: List[Dict[str, Any]]) -> List[ClassificationResult]:
        """
        Process ads in parallel using multiprocessing for M4 Mac optimization.
        """
        # For large datasets (>10K), use sequential processing to avoid multiprocessing overhead
        # Multiprocessing creates too much overhead when each worker needs to load full models
        if len(ads_data_list) > 10000:
            self.console.print(f"[yellow]Using optimized sequential processing for large dataset ({len(ads_data_list)} ads)[/yellow]")
            return self._classify_ads_sequential(ads_data_list)
        elif len(ads_data_list) < self.parallel_chunk_size * 2:
            # For small datasets, use sequential processing to avoid overhead
            self.console.print(f"[yellow]Using sequential processing for {len(ads_data_list)} ads (below parallel threshold)[/yellow]")
            return self._classify_ads_sequential(ads_data_list)

        # Split ads into chunks for parallel processing
        chunk_size = max(self.parallel_chunk_size, len(ads_data_list) // (self.max_workers * 4))
        chunks = [ads_data_list[i:i + chunk_size] for i in range(0, len(ads_data_list), chunk_size)]
        
        self.console.print(f"[cyan]Parallel processing: {len(chunks)} chunks across {self.max_workers} workers (chunk size: {chunk_size})[/cyan]")
        
        # Create worker configuration
        worker_config = self._create_worker_config()
        
        results = []
        completed_ads = 0
        total_ads = len(ads_data_list)

        # Progress tracking with comprehensive display
        progress_cols = [
            TextColumn("[bold blue]{task.description}"),
            BarColumn(bar_width=None),
            TextColumn("[progress.percentage]{task.percentage:>3.1f}%"),
            TextColumn("({task.completed}/{task.total})"),
            TimeRemainingColumn(),
            TimeElapsedColumn()
        ]
        
        with Progress(*progress_cols, console=self.console, transient=False, refresh_per_second=2) as progress_bar:
            task_parallel = progress_bar.add_task("Classifying Ads", total=total_ads)
            
            try:
                with ProcessPoolExecutor(max_workers=self.max_workers) as executor:
                    # Submit all chunks
                    future_to_chunk = {
                        executor.submit(HybridClassifier._worker_process_ads, chunk, worker_config): chunk
                        for chunk in chunks
                    }
                    
                    # Collect results as they complete
                    for future in as_completed(future_to_chunk):
                        chunk = future_to_chunk[future]
                        try:
                            chunk_results = future.result()
                            chunk_size = len(chunk_results)
                            
                            # Convert back to ClassificationResult objects
                            for result_dict in chunk_results:
                                result = ClassificationResult(**result_dict)
                                results.append(result)
                            
                            completed_ads += chunk_size
                            # Update progress with entire chunk at once
                            progress_bar.update(task_parallel, advance=chunk_size)
                                
                        except Exception as e:
                            logger.error(f"Error processing chunk with {len(chunk)} ads: {e}")
                            # Add error results for failed chunk
                            for ad_data in chunk:
                                ad_id = ad_data.get('AdArchiveID', 'unknown')
                                error_result = ClassificationResult(
                                    ad_id=str(ad_id),
                                    campaign="Other",
                                    confidence=0.0,
                                    method="parallel_error",
                                    needs_review=True,
                                    details={"error": str(e)}
                                )
                                results.append(error_result)
                                completed_ads += 1
                                progress_bar.update(task_parallel, advance=1)
                                
            except Exception as e:
                logger.error(f"Fatal error in parallel processing: {e}")
                # Fallback to sequential processing
                self.console.print(f"[red]Parallel processing failed, falling back to sequential[/red]")
                return self._classify_ads_sequential(ads_data_list)
        
        self.console.print(f"[green]Parallel processing complete: {completed_ads} ads processed[/green]")
        return results

    def _classify_ads_sequential(self, ads_data_list: List[Dict[str, Any]]) -> List[ClassificationResult]:
        """
        Optimized sequential processing with batched progress updates.
        """
        results = []
        total_ads = len(ads_data_list)
        
        progress_cols = [
            TextColumn("[bold blue]{task.description}"),
            BarColumn(bar_width=None),
            TextColumn("[progress.percentage]{task.percentage:>3.1f}%"),
            TextColumn("({task.completed}/{task.total})"),
            TimeRemainingColumn(),
            TimeElapsedColumn()
        ]
        
        # Process in batches for better progress display and memory management
        batch_size = 256  # Update progress every 256 ads (matches config batch_size)
        
        with Progress(*progress_cols, console=self.console, transient=False, refresh_per_second=2) as progress_bar:
            task_classify = progress_bar.add_task("Classifying Ads", total=total_ads)
            
            for i, ad_data_item in enumerate(ads_data_list):
                result = self._classify_single_ad_item(ad_data_item)
                results.append(result)
                
                # Update progress every batch_size items or at the end
                if (i + 1) % batch_size == 0 or (i + 1) == total_ads:
                    # Calculate how many items to advance (remaining items in current batch)
                    items_processed_in_batch = min(batch_size, (i + 1) % batch_size or batch_size)
                    progress_bar.update(task_classify, advance=items_processed_in_batch)
                    
        return results

    def classify_batch(self,
                       ads_data_list: List[Dict[str, Any]],  # EXPECTS DEDUPLICATED LIST from main_async
                       batch_size: int = 32,  # batch_size is for LLM/embedder internal, and for Rich progress display
                       output_csv: Optional[str] = None,
                       output_deduplicated_csv: Optional[str] = None,  # This will be applied *after* all classification
                       deduplication_fields: Optional[List[str]] = None) -> List[ClassificationResult]:

        if not ads_data_list:
            logger.warning("classify_batch called with empty ads_data_list.")
            return []

        num_ads_total = len(ads_data_list)
        self.console.print(f"[bold blue]Starting BATCH classification for {num_ads_total} unique ads.[/bold blue]")

        # Optional: NER Pre-warming with optimized multiprocessing for M4 Mac
        if self.ner_model and getattr(self, '_batch_process_ner', None):  # Check if method exists
            self.console.print(f"[cyan]Pre-Stage: Attempting NER cache warming for {num_ads_total} ads...[/cyan]")
            # Use single process for spaCy to avoid multiprocessing issues with transformer models
            # Transformer models like en_core_web_trf are too memory-intensive for multiprocessing
            self._batch_process_ner(ads_data_list, pipe_batch_size=min(32, batch_size),
                                    n_process=1)  # Stable single-process mode
            self.console.print(f"[green]Pre-Stage: NER cache warming attempt complete.[/green]\n")

        # Optional: Embedding Pre-warming for all texts
        if self.embedder and hasattr(self.embedder, 'encode'):
            self.console.print(f"[cyan]Pre-Stage: Pre-encoding texts for embeddings ({num_ads_total} ads)...[/cyan]")
            all_raw_texts_for_embedding = []
            for ad_data_item_pre_emb in ads_data_list:
                raw_text_pre_emb, _ = self._prepare_ad_text_for_processing(
                    ad_data_item_pre_emb, self.text_processing_fields, self.invalid_summary_strings
                )
                if raw_text_pre_emb and raw_text_pre_emb.strip():
                    all_raw_texts_for_embedding.append(raw_text_pre_emb)

            if all_raw_texts_for_embedding:
                # Optimize batch size for M4 Mac with 128GB RAM - use much larger batches
                optimized_batch_size = min(batch_size * 8, 2048)  # Much larger batches for better GPU utilization
                self.embedder.encode(all_raw_texts_for_embedding, batch_size=optimized_batch_size, show_progress=True)
                self.console.print(f"[green]Pre-Stage: Embedding pre-encoding complete.[/green]\n")

        # Main classification with parallel processing
        self.console.print(f"[cyan]Main Classification Pass: Processing {num_ads_total} ads with parallel optimization...[/cyan]")
        
        results = self._classify_ads_parallel(ads_data_list)
        
        # Incremental save for large datasets
        if output_csv and len(results) > 5000:
            logger.info(f"Saving {len(results)} results to {output_csv}...")
            self.save_results_to_csv(results, ads_data_list, output_csv)

        self.console.print(f"[green]Main Classification Pass Complete. {len(results)} ads processed.[/green]\n")

        # --- Stage 4 from current code: LLM for "Other" items using DBSCAN/Clustering (if improve_rules_active) ---
        # This now runs AFTER all ads have a first-pass classification.
        if self.use_llm and self.llm_classifier and self.improve_rules_active:
            other_items_for_llm_enhancement = []
            # We need to associate the ClassificationResult with its original ad_data for text
            # Create a map from ad_id to its original ad_data_item
            ad_data_map_for_llm = {str(ad.get('AdArchiveID', ad.get('ad_id', f"map_fallback_{idx}"))): ad for idx, ad in
                                   enumerate(ads_data_list)}

            for res_idx, res_item in enumerate(results):
                if res_item.campaign == "Other" and not res_item.needs_review:  # Or some other condition to select "Other" items
                    # Retrieve original ad_data for text. The ClassificationResult's details has snippets.
                    original_ad_data_for_res = ad_data_map_for_llm.get(res_item.ad_id)
                    if original_ad_data_for_res:
                        # We need raw_text and all_extracted_entities for clustering prompt
                        # Re-prepare text (or ensure it was stored in ClassificationResult.details)
                        temp_raw_text, _ = self._prepare_ad_text_for_processing(
                            original_ad_data_for_res, self.text_processing_fields, self.invalid_summary_strings)

                        # Ensure NER entities are available (they should be in res_item.details['extracted_entities'])
                        temp_entities = res_item.details.get('extracted_entities', {})

                        if temp_raw_text and temp_raw_text.strip():
                            other_items_for_llm_enhancement.append({
                                "ad_id": res_item.ad_id,  # Link back to the result
                                "raw_text": temp_raw_text,
                                "all_extracted_entities": temp_entities,
                                "result_index": res_idx  # Index in the main 'results' list
                            })

            num_other_for_llm = len(other_items_for_llm_enhancement)
            self.console.print(
                f"[cyan]Post-Processing: LLM Clustering for {num_other_for_llm} 'Other' ads (Improve Rules Active).[/cyan]")

            if self.sklearn_available and num_other_for_llm >= 10 and self.embedder:
                # Perform DBSCAN clustering on 'Other' items
                logger.info(f"Starting DBSCAN clustering for {num_other_for_llm} 'Other' items")
                
                # Get embeddings for clustering
                other_texts_for_embedding = [item['raw_text'] for item in other_items_for_llm_enhancement]
                other_embeddings = self.embedder.encode(other_texts_for_embedding, show_progress=False)
                
                if other_embeddings is not None and len(other_embeddings) > 0 and other_embeddings.ndim == 2:
                    # Scale embeddings
                    scaler = self.StandardScaler()
                    embeddings_scaled = scaler.fit_transform(other_embeddings)
                    
                    cluster_labels = None
                    used_method = None
                    
                    # Try DBSCAN first
                    if self.DBSCAN and self.NearestNeighbors:
                        MIN_SAMPLES_PARAM = 4
                        EPS_ESTIMATION_MIN_SAMPLES = MIN_SAMPLES_PARAM
                        EPS_PERCENTILE = 80.0
                        
                        eps_val = self._h_estimate_dbscan_eps(embeddings_scaled, EPS_ESTIMATION_MIN_SAMPLES, EPS_PERCENTILE)
                        if eps_val and eps_val > 1e-6:
                            dbscan = self.DBSCAN(eps=eps_val, min_samples=MIN_SAMPLES_PARAM, metric='euclidean')
                            labels_dbscan = dbscan.fit_predict(embeddings_scaled)
                            n_clusters_dbscan = len(set(labels_dbscan)) - (1 if -1 in labels_dbscan else 0)
                            
                            if n_clusters_dbscan > 0 and n_clusters_dbscan < len(other_items_for_llm_enhancement) * 0.8:
                                cluster_labels = labels_dbscan
                                used_method = "DBSCAN"
                                logger.info(f"DBSCAN found {n_clusters_dbscan} clusters. Eps={eps_val:.3f}, MinPts={MIN_SAMPLES_PARAM}")
                            else:
                                logger.info(f"DBSCAN produced trivial clustering ({n_clusters_dbscan} clusters). Trying KMeans.")
                    
                    # Fall back to KMeans if DBSCAN didn't work
                    if used_method is None and self.KMeans:
                        k_optimal = self._h_get_optimal_k_for_kmeans(embeddings_scaled)
                        if k_optimal > 1 and k_optimal < len(other_items_for_llm_enhancement) * 0.8:
                            kmeans = self.KMeans(n_clusters=k_optimal, random_state=42, n_init='auto')
                            labels_kmeans = kmeans.fit_predict(embeddings_scaled)
                            cluster_labels = labels_kmeans
                            used_method = "KMeans"
                            logger.info(f"KMeans found {k_optimal} clusters.")
                    
                    # Assign cluster IDs to results
                    if cluster_labels is not None:
                        for i, item in enumerate(other_items_for_llm_enhancement):
                            result_idx = item["result_index"]
                            cluster_id = int(cluster_labels[i])
                            # Only assign positive cluster IDs (DBSCAN uses -1 for noise)
                            if cluster_id >= 0:
                                results[result_idx].dbscan_cluster = cluster_id
                                logger.debug(f"Assigned {used_method} cluster {cluster_id} to ad {item['ad_id']}")
                        
                        # Log cluster statistics
                        unique_clusters = set(cluster_labels)
                        if -1 in unique_clusters:
                            unique_clusters.remove(-1)
                        logger.info(f"{used_method} clustering complete: {len(unique_clusters)} clusters assigned to 'Other' items")
                        self.console.print(
                            f"[green]  {used_method} clustering: Assigned {len(unique_clusters)} clusters to 'Other' items.[/green]")
                    else:
                        logger.info("No suitable clusters found for 'Other' items")
                        self.console.print(
                            f"[yellow]  No suitable clusters found for 'Other' items.[/yellow]")
                else:
                    logger.warning("Could not generate valid embeddings for 'Other' items")
                    self.console.print(
                        f"[yellow]  Could not generate embeddings for clustering 'Other' items.[/yellow]")

            elif not self.sklearn_available:
                self.console.print(
                    f"[yellow]  (Improve Rules Active: scikit-learn not available, cannot cluster 'Other' items).[/yellow]")
            elif not self.embedder:
                self.console.print(
                    f"[yellow]  (Improve Rules Active: Embedder not available, cannot cluster 'Other' items).[/yellow]")
            elif num_other_for_llm < 10:
                self.console.print(
                    f"[yellow]  (Improve Rules Active: Less than 10 'Other' items ({num_other_for_llm}), skipping clustering).[/yellow]")

        # --- Enhanced LLM for remaining "Other" (if not improve_rules_active or if still "Other") ---
        if self.use_llm and self.llm_classifier and self.enhanced_llm:
            self.console.print(f"[cyan]Post-Processing: Enhanced LLM for remaining 'Other' ads.[/cyan]")
            llm_enhanced_updated_count = 0
            progress_cols = [
                TextColumn("[bold blue]{task.description}"),
                BarColumn(bar_width=None),
                TextColumn("[progress.percentage]{task.percentage:>3.1f}%"),
                TextColumn("({task.completed}/{task.total})"),
                TimeRemainingColumn(),
                TimeElapsedColumn()
            ]
            with Progress(*progress_cols, console=self.console, transient=False, refresh_per_second=4) as progress_bar_llm_enhance:
                # Collect items that are still "Other" and might benefit
                items_for_enhanced_llm_pass = [(idx, res) for idx, res in enumerate(results) if res.campaign == "Other"]
                task_llm_enhance = progress_bar_llm_enhance.add_task("LLM Enhanced Processing",
                                                                     total=len(items_for_enhanced_llm_pass))

                for res_idx, res_item in items_for_enhanced_llm_pass:
                    original_ad_data = ads_data_list[res_idx]  # Assuming ads_data_list corresponds to results by index
                    raw_text_for_llm, _ = self._prepare_ad_text_for_processing(
                        original_ad_data, self.text_processing_fields, self.invalid_summary_strings
                    )
                    if raw_text_for_llm and raw_text_for_llm.strip():
                        candidate_campaigns_for_llm = list(self.campaign_prototypes.keys())
                        if candidate_campaigns_for_llm:
                            enhanced_campaign, enhanced_conf = self.llm_classifier.classify_with_enhanced_prompt(
                                raw_text_for_llm, candidate_campaigns_for_llm, context="legal_ad_classification"
                            )
                            if enhanced_campaign and enhanced_campaign != "Other" and enhanced_conf > 0.65:  # Threshold
                                results[res_idx].campaign = enhanced_campaign
                                results[res_idx].confidence = enhanced_conf
                                results[res_idx].method = "llm_enhanced"
                                llm_enhanced_updated_count += 1
                    progress_bar_llm_enhance.update(task_llm_enhance, advance=1)
            self.console.print(
                f"[green]Post-Processing: Enhanced LLM updated {llm_enhanced_updated_count} 'Other' ads.[/green]\n")

        # Final save of all results
        if output_csv:
            self.save_results_to_csv(results, ads_data_list, output_csv)
        if output_deduplicated_csv and deduplication_fields:
            # This deduplication is on the *final classified results*, not the input list.
            self.save_deduplicated_results_to_csv(results, ads_data_list, output_deduplicated_csv, deduplication_fields)

        return results

    def close(self):
        if self.llm_classifier and hasattr(self.llm_classifier, '_cache_hits') and hasattr(self.llm_classifier,
                                                                                           '_cache_misses'):
            total_requests = self.llm_classifier._cache_hits + self.llm_classifier._cache_misses
            if total_requests > 0:
                hit_rate = (self.llm_classifier._cache_hits / total_requests) * 100
                log_msg = f"LLM Cache Stats: {self.llm_classifier._cache_hits} hits, {self.llm_classifier._cache_misses} misses ({hit_rate:.1f}% hit rate)"
                self.console.print(f"[cyan]{log_msg}[/cyan]")
            else:
                self.console.print(f"[yellow]LLM Cache Stats: No LLM requests were made or tracked.[/yellow]")

        if self.embedder and hasattr(self.embedder, 'save_cache'):
            self.embedder.save_cache(force=True)

        if self.ner_cache_file and hasattr(self, '_ner_results_cache') and self._ner_results_cache:
            try:
                self.ner_cache_file.parent.mkdir(parents=True, exist_ok=True)
                with open(self.ner_cache_file, 'wb') as f:
                    pickle.dump(self._ner_results_cache, f)
                logger.info(
                    f"Saved NER results cache ({len(self._ner_results_cache)} items, Hits: {self._ner_cache_hits}, Misses: {self._ner_cache_misses}) to {self.ner_cache_file} on close.")
            except Exception as e:
                logger.error(f"Failed to save NER cache on close: {e}")
        logger.info("HybridClassifier resources potentially cleaned up or saved.")

    def save_results_to_csv(self,
                            results: List[ClassificationResult],
                            original_ads_data: List[Dict[str, Any]],
                            filepath: str):
        if not results:
            logger.info("No results to save to CSV.")
            return

        data_to_save = []
        ads_data_map = {str(ad.get('AdArchiveID', ad.get('ad_id'))): ad for ad in original_ads_data if
                        ad.get('AdArchiveID') or ad.get('ad_id')}

        for res in results:
            row = dataclasses.asdict(res)
            original_ad = ads_data_map.get(str(res.ad_id))
            raw_text_for_output = ""
            if original_ad:
                raw_text_for_output, _ = HybridClassifier._prepare_ad_text_for_processing(
                    original_ad, self.text_processing_fields, self.invalid_summary_strings
                )
            row['raw_text_combined'] = raw_text_for_output
            # Include DBSCAN cluster for 'Other' campaigns
            if res.campaign == "Other" and res.dbscan_cluster is not None:
                row['DBSCAN_Cluster'] = res.dbscan_cluster
            else:
                row['DBSCAN_Cluster'] = None
            if isinstance(row['details'], dict):
                for k, v in row['details'].items():
                    row[f"detail_{k}"] = str(v)
            del row['details']
            data_to_save.append(row)

        df = pd.DataFrame(data_to_save)
        
        # Sort by campaign first, then by DBSCAN_Cluster for 'Other' campaigns
        if 'DBSCAN_Cluster' in df.columns:
            # Create a sorting key where 'Other' campaigns with DBSCAN clusters are sorted by cluster ID
            df['_sort_key'] = df.apply(
                lambda row: (row['campaign'], row['DBSCAN_Cluster'] if row['campaign'] == 'Other' and pd.notna(row['DBSCAN_Cluster']) else float('inf')),
                axis=1
            )
            df = df.sort_values(by=['_sort_key'])
            df = df.drop(columns=['_sort_key'])
        
        try:
            Path(filepath).parent.mkdir(parents=True, exist_ok=True)
            df.to_csv(filepath, index=False, encoding='utf-8')
            self.console.print(f"[green]✓ Classification results saved to {filepath}[/green]")
        except Exception as e:
            self.console.print(f"[red]Error saving results to CSV {filepath}: {e}[/red]")
            logger.error(f"Error saving results to CSV {filepath}: {e}", exc_info=True)

    def save_deduplicated_results_to_csv(self,
                                         results: List[ClassificationResult],
                                         original_ads_data: List[Dict[str, Any]],
                                         filepath: str,
                                         deduplication_fields: List[str]):
        if not results:
            logger.info("No results to deduplicate and save.")
            return
        if not deduplication_fields:
            logger.warning("No deduplication fields provided. Cannot deduplicate.")
            self.save_results_to_csv(results, original_ads_data, filepath)
            return

        df_data = []
        ads_data_map = {str(ad.get('AdArchiveID', ad.get('ad_id'))): ad for ad in original_ads_data if
                        ad.get('AdArchiveID') or ad.get('ad_id')}

        for res in results:
            row = dataclasses.asdict(res)
            original_ad = ads_data_map.get(str(res.ad_id))
            raw_text_for_output = ""
            dedup_text_parts = []

            if original_ad:
                raw_text_for_output, _ = HybridClassifier._prepare_ad_text_for_processing(
                    original_ad, self.text_processing_fields, self.invalid_summary_strings
                )
                for field_name in deduplication_fields:
                    field_val = original_ad.get(field_name)
                    if isinstance(field_val,
                                  str) and field_val.strip() and field_val.lower() not in self.invalid_summary_strings:
                        dedup_text_parts.append(field_val.strip())

            row['raw_text_combined'] = raw_text_for_output
            row['deduplication_key_text'] = _normalize_text_for_matching(" ".join(dedup_text_parts))
            
            # Include DBSCAN cluster for 'Other' campaigns
            if res.campaign == "Other" and res.dbscan_cluster is not None:
                row['DBSCAN_Cluster'] = res.dbscan_cluster
            else:
                row['DBSCAN_Cluster'] = None

            if isinstance(row['details'], dict):
                for k, v in row['details'].items():
                    row[f"detail_{k}"] = str(v)
            del row['details']
            df_data.append(row)

        df = pd.DataFrame(df_data)
        if df.empty:
            logger.info("DataFrame for deduplication is empty.")
            return

        logger.info(f"Attempting deduplication on {len(df)} results using fields: {deduplication_fields}")
        df.sort_values(by='confidence', ascending=False, inplace=True)
        deduplicated_df = df.drop_duplicates(subset=['deduplication_key_text'], keep='first')
        num_dropped = len(df) - len(deduplicated_df)
        self.console.print(
            f"[cyan]Deduplication: Kept {len(deduplicated_df)} unique ads, removed {num_dropped} duplicates.[/cyan]")

        if 'deduplication_key_text' in deduplicated_df.columns:
            deduplicated_df = deduplicated_df.drop(columns=['deduplication_key_text'])
            
        # Sort by campaign first, then by DBSCAN_Cluster for 'Other' campaigns
        if 'DBSCAN_Cluster' in deduplicated_df.columns:
            # Create a sorting key where 'Other' campaigns with DBSCAN clusters are sorted by cluster ID
            deduplicated_df['_sort_key'] = deduplicated_df.apply(
                lambda row: (row['campaign'], row['DBSCAN_Cluster'] if row['campaign'] == 'Other' and pd.notna(row['DBSCAN_Cluster']) else float('inf')),
                axis=1
            )
            deduplicated_df = deduplicated_df.sort_values(by=['_sort_key'])
            deduplicated_df = deduplicated_df.drop(columns=['_sort_key'])

        try:
            Path(filepath).parent.mkdir(parents=True, exist_ok=True)
            deduplicated_df.to_csv(filepath, index=False, encoding='utf-8')
            self.console.print(f"[green]✓ Deduplicated classification results saved to {filepath}[/green]")
        except Exception as e:
            self.console.print(f"[red]Error saving deduplicated results to CSV {filepath}: {e}[/red]")
            logger.error(f"Error saving deduplicated results to CSV {filepath}: {e}", exc_info=True)


# --- Workflow Components (Adapted from hybrid_campaign_classifier_m4.py) ---

class OptimizedDynamoDBLoader:
    """Parallel DynamoDB loader - Scans ALL attributes."""

    def __init__(self, table_name: str = 'FBAdArchive', region: str = 'us-east-1',
                 endpoint_url: Optional[str] = None, max_workers: Optional[int] = 16,
                 image_hash_table: str = 'FBImageHash'):  # Removed text_fields_to_project
        self.table_name = table_name
        self.image_hash_table = image_hash_table
        self.region = region
        self.endpoint_url = endpoint_url
        self.max_workers = max_workers or (os.cpu_count() or 4)
        self.console = Console()

        logger.info(f"DynamoDBLoader initialized to scan ALL attributes from table: {self.table_name}")

        try:
            import boto3
            from boto3.dynamodb.conditions import Key, Attr
            self.boto3 = boto3
            self.Key = Key
            self.Attr = Attr
        except ImportError:
            logger.error("Boto3 is not installed. DynamoDB loading will not work.")
            self.boto3 = None
            self.Key = None
            self.Attr = None

    def _convert_decimals(self, obj):
        if isinstance(obj, self.boto3.dynamodb.types.Decimal):
            return float(obj)
        if isinstance(obj, list):
            return [self._convert_decimals(i) for i in obj]
        if isinstance(obj, dict):
            return {k: self._convert_decimals(v) for k, v in obj.items()}
        return obj

    def load_data_parallel(self, limit: Optional[int] = None, start_date: Optional[str] = None) -> pd.DataFrame:
        if not self.boto3:
            return pd.DataFrame()

        dynamodb_args = {'region_name': self.region}
        if self.endpoint_url:
            dynamodb_args['endpoint_url'] = self.endpoint_url
            logger.info(f"Using DynamoDB endpoint: {self.endpoint_url}")

        dynamodb = self.boto3.resource('dynamodb', **dynamodb_args)
        table = dynamodb.Table(self.table_name)

        items = []
        scan_kwargs = {}  # No ProjectionExpression, scan all attributes

        if start_date:
            if not self.Attr:
                logger.error("Boto3 Attr not available for filtering")
            else:
                scan_kwargs['FilterExpression'] = self.Attr('StartDate').gte(start_date)
                logger.info(f"Filtering ads with StartDate >= {start_date}")
                logger.debug(f"Filter expression: {scan_kwargs['FilterExpression']}")

        if limit:
            scan_kwargs['Limit'] = limit

        logger.info(f"Scan parameters (will fetch all attributes): {scan_kwargs}")

        total_items_in_table = 0
        try:
            dynamodb_client = self.boto3.client('dynamodb', **dynamodb_args)
            table_info = dynamodb_client.describe_table(TableName=self.table_name)
            total_items_in_table = table_info.get('Table', {}).get('ItemCount', 0)
            if total_items_in_table == 0:
                count_response = table.scan(Select='COUNT')
                total_items_in_table = count_response.get('Count', 0)
        except Exception as e:
            logger.warning(f"Could not get total item count: {e}")
            total_items_in_table = 0

        try:
            dynamo_progress_columns = [
                TextColumn("[bold blue]{task.description}"),
                BarColumn(bar_width=None),
                TextColumn("[progress.percentage]{task.percentage:>3.1f}%"),
                TextColumn("({task.completed}/{task.total})"),
                TextColumn("•"),
                TextColumn("[progress.percentage]{task.completed}/{task.total}") if limit else TextColumn(
                    "[progress.percentage]{task.completed} items"),
                TextColumn("•"),
                TimeElapsedColumn(),
                TextColumn("•"),
                TimeRemainingColumn() if limit else TextColumn("Scanning..."),
            ]

            with Progress(*dynamo_progress_columns, console=self.console, transient=True) as progress:
                est_total = limit if limit else None
                task_desc = f"[cyan]Scanning DynamoDB table '{self.table_name}' (all attributes)..."
                if limit: task_desc += f" (limit {limit})"
                if start_date: task_desc += f" (StartDate >= {start_date})"

                scan_task = progress.add_task(task_desc, total=est_total if (est_total and est_total > 0) else None)

                response = table.scan(**scan_kwargs)
                batch_items = response['Items']
                items.extend(batch_items)
                progress.update(scan_task, advance=len(batch_items))

                if len(items) == len(batch_items) and start_date and batch_items:
                    logger.info(f"First batch: {len(batch_items)} items retrieved with StartDate filter")
                    sample_dates = [item.get('StartDate', 'NO_DATE') for item in batch_items[:3]]
                    logger.info(f"Sample StartDates from first batch: {sample_dates}")

                while 'LastEvaluatedKey' in response and (not limit or len(items) < limit):
                    scan_kwargs['ExclusiveStartKey'] = response['LastEvaluatedKey']
                    if limit and limit - len(items) < scan_kwargs.get('Limit', float('inf')):
                        scan_kwargs['Limit'] = limit - len(items)

                    response = table.scan(**scan_kwargs)
                    batch_items = response['Items']
                    items.extend(batch_items)
                    progress.update(scan_task, advance=len(batch_items))

            if limit and len(items) > limit:
                items = items[:limit]

            processed_items = [self._convert_decimals(item) for item in items]
            df = pd.DataFrame(processed_items)

            if df.empty:
                self.console.print(f"[yellow]Warning: No data loaded from DynamoDB table '{self.table_name}'.[/yellow]")
                return df

            selected_count = len(df)
            if total_items_in_table > 0:
                percentage = (selected_count / total_items_in_table) * 100
                self.console.print(
                    f"[green]✓ Selected {selected_count:,} items ({percentage:.1f}% of {total_items_in_table:,} total items)[/green]")
            else:
                self.console.print(f"[green]✓ Selected {selected_count:,} items for processing[/green]")

            filter_info_parts = []
            if start_date: filter_info_parts.append(f"StartDate >= {start_date}")
            if limit: filter_info_parts.append(f"Limited to {limit:,} items")
            if filter_info_parts:
                self.console.print(f"[dim]Applied filters: {', '.join(filter_info_parts)}[/dim]")
            else:
                logger.info("No filters applied - processing all items")

            # Ensure AdArchiveID exists, it's critical.
            # It should be returned by scan all if it exists in items.
            if 'AdArchiveID' not in df.columns:
                if 'ad_id' in df.columns:  # common alternative name
                    df.rename(columns={'ad_id': 'AdArchiveID'}, inplace=True)
                    logger.info("Renamed 'ad_id' column to 'AdArchiveID'.")
                else:  # If neither exists, then it's problematic.
                    logger.error(
                        "CRITICAL: 'AdArchiveID' (or 'ad_id') column not found in any loaded DynamoDB items. Cannot proceed reliably.")
                    # Depending on strictness, might want to return empty df or raise error.
                    # For now, generating dummy IDs to allow processing, but this is a data issue.
                    df['AdArchiveID'] = [f"generated_id_{i}" for i in range(len(df))]
                    logger.warning("Generated dummy 'AdArchiveID' values as it was missing.")

            # Log first few items' keys to verify all attributes are loaded
            if not df.empty:
                logger.debug(f"Sample keys from first loaded DynamoDB item: {list(df.iloc[0].index)}")

            return df

        except Exception as e:
            self.console.print(f"[bold red]Error loading data from DynamoDB: {e}[/bold red]")
            logger.error(f"DynamoDB load error: {e}", exc_info=True)
            return pd.DataFrame()


class RuleImprover:
    """Analyze results and suggest rule improvements, including clustering and LLM analysis."""

    def __init__(self,
                 campaign_config_path: Path,
                 embedder: Optional[M4OptimizedEmbedder],
                 llm_classifier: Optional[LocalLLMClassifier],
                 text_processing_fields: List[str],
                 invalid_summary_strings: set,
                 logger_instance: logging.Logger):
        self.campaign_config_path = campaign_config_path
        self.embedder = embedder
        self.llm_classifier = llm_classifier
        self.text_processing_fields = text_processing_fields
        self.invalid_summary_strings = invalid_summary_strings
        self.logger = logger_instance
        self.console = Console()
        self.campaign_rules = self._load_rules()

        self.sklearn_available = False
        self.KMeans = None
        self.silhouette_score = None
        self.StandardScaler = None
        self.DBSCAN = None
        self.NearestNeighbors = None

        try:
            from sklearn.cluster import KMeans, DBSCAN
            from sklearn.metrics import silhouette_score
            from sklearn.preprocessing import StandardScaler
            from sklearn.neighbors import NearestNeighbors
            import numpy as np  # Ensure numpy is available if used directly here

            self.KMeans = KMeans
            self.DBSCAN = DBSCAN
            self.NearestNeighbors = NearestNeighbors
            self.silhouette_score = silhouette_score
            self.StandardScaler = StandardScaler
            self.sklearn_available = True
            self.logger.info("scikit-learn found. Clustering (KMeans, DBSCAN) for rule improvement is available.")
        except ImportError:
            self.logger.warning(
                "scikit-learn not found. Advanced rule improvement via clustering will be unavailable.")
            self.sklearn_available = False
        global np  # Make numpy available for NpEncoder if it's defined late
        if 'np' not in globals() and self.sklearn_available:  # cheap way to ensure np is global if sklearn is used
            pass

    def _load_rules(self) -> List[Dict[str, Any]]:
        if not self.campaign_config_path.exists():
            self.logger.error(f"Campaign config file not found at {self.campaign_config_path} for RuleImprover.")
            return []
        try:
            with open(self.campaign_config_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            if isinstance(data, list):
                return data
            else:
                self.logger.error(f"Campaign config at {self.campaign_config_path} is not a list.")
                return []
        except json.JSONDecodeError:
            self.logger.error(f"Error decoding JSON from {self.campaign_config_path}")
            return []
        except Exception as e:
            self.logger.error(f"Failed to load campaign rules from {self.campaign_config_path}: {e}")
            return []

    def save_improved_config(self, suggestions: Dict, output_path_str: str):
        output_path = Path(output_path_str)
        self.console.print(
            f"[yellow]Saving rule suggestions to {output_path}. Manual review and integration needed.[/yellow]")
        try:
            output_path.parent.mkdir(parents=True, exist_ok=True)
            with open(output_path, 'w', encoding='utf-8') as f:
                class NpEncoder(json.JSONEncoder):
                    def default(self, obj):
                        if isinstance(obj, np.integer):
                            return int(obj)
                        if isinstance(obj, np.floating):
                            return float(obj)
                        if isinstance(obj, np.ndarray):
                            return obj.tolist()
                        if isinstance(obj, (Path)):
                            return str(obj)
                        return super(NpEncoder, self).default(obj)

                json.dump(suggestions, f, indent=2, cls=NpEncoder, ensure_ascii=False)
            self.console.print(f"[green]✓ Saved rule suggestions to {output_path}[/green]")
        except Exception as e:
            self.console.print(f"[red]Error saving suggestions to {output_path}: {e}[/red]")
            self.logger.error(f"Error saving rule suggestions to {output_path}: {e}", exc_info=True)

    def _get_optimal_k_for_kmeans(self, embeddings_scaled: np.ndarray, max_k: int = 10) -> int:
        if not self.sklearn_available or self.silhouette_score is None or self.KMeans is None:
            heuristic_k = int(max(2, min(5, np.ceil(np.sqrt(len(embeddings_scaled)) / 2.5))))
            self.logger.debug(f"KMeans: sklearn not fully available, using heuristic K={heuristic_k}")
            return heuristic_k
        if len(embeddings_scaled) < 4:
            self.logger.debug(f"KMeans: Not enough samples ({len(embeddings_scaled)}) for silhouette. Returning K=1.")
            return 1 if len(embeddings_scaled) > 0 else 0

        upper_k_bound = min(max_k + 1, len(embeddings_scaled))
        k_range = range(2, upper_k_bound)

        if not list(k_range):
            self.logger.debug(
                f"KMeans: k_range empty for silhouette. n_samples={len(embeddings_scaled)}, max_k_to_test={upper_k_bound - 1}. Returning K=1.")
            return 1 if len(embeddings_scaled) > 0 else 0

        silhouette_scores = []
        for k_val in k_range:
            try:
                kmeans = self.KMeans(n_clusters=k_val, random_state=42, n_init='auto')
                cluster_labels = kmeans.fit_predict(embeddings_scaled)
                if len(set(cluster_labels)) > 1:
                    score = self.silhouette_score(embeddings_scaled, cluster_labels)
                    silhouette_scores.append(score)
                else:
                    silhouette_scores.append(-1)
            except Exception as e:
                self.logger.warning(f"Error calculating silhouette score for K={k_val} (KMeans): {e}");
                silhouette_scores.append(-1)

        if not silhouette_scores or max(silhouette_scores) <= -1:
            heuristic_k = int(max(2, min(5, np.ceil(np.sqrt(len(embeddings_scaled)) / 2.5))))
            self.logger.debug(f"KMeans: Silhouette scores not helpful, using heuristic K={heuristic_k}")
            return heuristic_k

        optimal_k = k_range[np.argmax(silhouette_scores)]
        self.logger.info(f"Optimal K for KMeans: {optimal_k} (Max Silhouette: {max(silhouette_scores):.3f})")
        return optimal_k

    def _estimate_dbscan_eps(self, embeddings_scaled: np.ndarray, min_samples: int, percentile: float = 80.0) -> \
            Optional[float]:
        if not self.NearestNeighbors or len(embeddings_scaled) < min_samples:
            self.logger.warning(
                f"DBSCAN: NearestNeighbors not available or insufficient samples ({len(embeddings_scaled)} < {min_samples}) for eps estimation.")
            return None
        try:
            k_for_eps_calc = min_samples

            nn = self.NearestNeighbors(n_neighbors=k_for_eps_calc, metric='euclidean')
            nn.fit(embeddings_scaled)
            distances, _ = nn.kneighbors(embeddings_scaled)

            if distances.shape[1] < k_for_eps_calc:
                self.logger.warning(
                    f"DBSCAN eps: Requested {k_for_eps_calc} neighbors, but data only supports {distances.shape[1]}. Using {distances.shape[1]}.")
                k_for_eps_calc = distances.shape[1]
                if k_for_eps_calc == 0: return None

            kth_distances = np.sort(distances[:, k_for_eps_calc - 1])
            eps_value = np.percentile(kth_distances, percentile)
            self.logger.info(
                f"Estimated DBSCAN eps: {eps_value:.4f} (using {k_for_eps_calc}-th neighbor distances at {percentile}th percentile)")

            if eps_value <= 1e-6:
                self.logger.warning(
                    f"Estimated eps ({eps_value:.4f}) is very small or zero. DBSCAN might not be effective.")
                non_zero_kth_distances = kth_distances[kth_distances > 1e-6]
                if len(non_zero_kth_distances) > 0:
                    num_to_avg = max(1, int(len(non_zero_kth_distances) * 0.15))
                    eps_value = np.mean(np.sort(non_zero_kth_distances)[:num_to_avg]) * 1.5
                    self.logger.info(f"Adjusted eps based on smallest non-zero k-th distances to: {eps_value:.4f}")
                    if eps_value <= 1e-6:
                        self.logger.warning(f"Adjusted eps ({eps_value:.4f}) is still too small. DBSCAN may fail.")
                        return None
                else:
                    self.logger.warning(
                        "All k-th distances are zero or near-zero. Cannot determine a suitable eps for DBSCAN.")
                    return None
            return eps_value
        except Exception as e:
            self.logger.error(f"Error during DBSCAN eps estimation: {e}", exc_info=True)
            return None

    def _get_frequent_entities_from_samples(self,
                                            sample_items_for_llm: List[Dict[str, Any]],
                                            min_entity_occurrence: int = 2,
                                            top_n_entities: int = 3) -> Tuple[List[str], List[str]]:
        org_counts = Counter()
        prod_counts = Counter()

        for item_data in sample_items_for_llm:
            details = item_data.get('classification_result_details', {})
            entities = details.get('extracted_entities', {})

            for org_entity in entities.get("ORG", []): org_counts[org_entity] += 1
            for prod_entity in entities.get("PRODUCT", []): prod_counts[prod_entity] += 1

        frequent_orgs = [org for org, count in org_counts.most_common(top_n_entities) if count >= min_entity_occurrence]
        frequent_prods = [prod for prod, count in prod_counts.most_common(top_n_entities) if
                          count >= min_entity_occurrence]

        return frequent_orgs, frequent_prods

    def analyze_and_suggest(self, classified_ads: List[ClassificationResult],
                            ads_data_list_param: List[Dict[str, Any]]) -> Dict:
        suggestions = {
            "new_triggers": defaultdict(Counter),
            "potential_new_campaigns_ngram": Counter(),
            "potential_new_campaigns_llm_cluster": []
        }
        stopwords = set([
            "a", "an", "the", "is", "are", "was", "were", "be", "been", "being", "have", "has", "had", "do", "does",
            "did", "will", "would", "should", "can", "could", "may", "might", "must", "and", "but", "or", "nor", "for",
            "so",
            "yet", "if", "then", "else", "when", "where", "why", "how", "what", "which", "who", "whom", "whose",
            "this", "that", "these", "those", "am", "i", "you", "he", "she", "it", "we", "they", "me", "him", "her",
            "us", "them", "my", "your", "his", "its", "our", "their", "mine", "yours", "hers", "ours", "theirs",
            "to", "of", "in", "on", "at", "by", "from", "with", "about", "above", "after", "again", "against", "all",
            "any", "both", "each", "few", "more", "most", "other", "some", "such", "no", "not", "only", "own", "same",
            "than", "too", "very", "s", "t", "just", "don", "now", "d", "ll", "m", "o", "re", "ve", "y",
            "ad", "advertisement", "advertising", "attorney", "attorneys", "lawyer", "lawyers", "law", "legal", "firm",
            "llp", "llc", "pc", "call", "contact", "click", "visit", "learn", "more", "apply", "submit", "find", "out",
            "free",
            "consultation", "evaluation", "case", "review", "claim", "claims", "lawsuit", "litigation", "settlement",
            "compensation", "entitled",
            "eligible", "rights", "options", "help", "assistance", "confidential", "risk", "obligation", "no", "fee",
            "unless",
            "win", "recover", "injured", "injury", "harm", "suffered", "affected", "victim", "victims", "important",
            "notice",
            "attention", "act", "now", "today", "limited", "time", "deadline", "sponsored", "results", "message",
            "information",
            "you've", "we're", "they're", "it's", "don't", "qualified", "experienced", "professional", "group",
            "center", "financial", "medical", "treatment", "diagnosis", "condition", "health", "doctor", "hospital",
            "insurance",
            "investigation", "seeking", "participants", "if", "may", "possible", "potential", "serious", "significant",
            "get", "up", "down", "over", "under", "again", "further", "then", "once", "here", "there", "when", "where",
            "why", "how", "all", "any", "both", "each", "few", "more", "most", "other", "some", "such", "no", "nor",
            "not",
            "only", "own", "same", "so", "than", "too", "very", "s", "t", "can", "will", "just", "don", "should",
            "january", "february", "march", "april", "may", "june", "july", "august", "september", "october",
            "november", "december", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday",
            "amp", "ll", "inc", "corp", "ltd", "co", "etc", "eg", "ie", "vs"
        ])

        ads_data_map_by_id = {str(ad.get('AdArchiveID', ad.get('ad_id', ''))): ad
                              for ad in ads_data_list_param
                              if ad.get('AdArchiveID') or ad.get('ad_id')}

        other_items_data = []

        for result in classified_ads:
            current_ad_dict = ads_data_map_by_id.get(str(result.ad_id))
            if not current_ad_dict:
                self.logger.warning(
                    f"RuleImprover: Ad data for ad_id {result.ad_id} not found in ads_data_map. Skipping.")
                continue

            ad_content_raw, ad_content_norm = HybridClassifier._prepare_ad_text_for_processing(
                current_ad_dict,
                self.text_processing_fields,
                self.invalid_summary_strings
            )

            words = re.findall(r'\b[a-z][a-z0-9\'-]*[a-z0-9]\b', ad_content_norm)
            meaningful_words = [w for w in words if w not in stopwords and len(w) > 2]

            if result.campaign == "Other":
                if ad_content_raw.strip():
                    other_items_data.append({
                        "id": result.ad_id,
                        "raw_text": ad_content_raw,
                        "normalized_text": ad_content_norm,
                        "classification_result_details": result.details
                    })
                elif result.campaign == "Other":
                    self.logger.debug(f"RuleImprover: Ad {result.ad_id} is 'Other' but has no processable raw text.")

                if meaningful_words and len(meaningful_words) > 1:
                    for j in range(len(meaningful_words) - 1):
                        bigram = tuple(sorted((meaningful_words[j], meaningful_words[j + 1])))
                        suggestions["potential_new_campaigns_ngram"][bigram] += 1

            elif result.needs_review and meaningful_words:
                current_campaign_triggers = []
                for camp_rule in self.campaign_rules:
                    if camp_rule.get("LitigationName") == result.campaign:
                        current_campaign_triggers = [_normalize_text_for_matching(t) for t in
                                                     camp_rule.get("triggers", [])]
                        break
                for word in meaningful_words:
                    if word not in current_campaign_triggers:
                        suggestions["new_triggers"][result.campaign][word] += 1

        suggestions["potential_new_campaigns_ngram"] = {
            " ".join(phrase_tuple): count
            for phrase_tuple, count in suggestions["potential_new_campaigns_ngram"].most_common(10)
            if count > 2
        }
        suggestions["new_triggers"] = {
            camp: [wc[0] for wc in words.most_common(5) if wc[1] > 1]
            for camp, words in suggestions["new_triggers"].items()
        }

        MIN_OTHER_ITEMS_FOR_CLUSTERING = 10
        MIN_CLUSTER_SIZE_FOR_LLM_ANALYSIS = 3
        DBSCAN_MIN_SAMPLES_PARAM = 4
        DBSCAN_EPS_ESTIMATION_MIN_SAMPLES = DBSCAN_MIN_SAMPLES_PARAM
        DBSCAN_EPS_PERCENTILE = 80.0

        if self.embedder and self.sklearn_available and len(other_items_data) >= MIN_OTHER_ITEMS_FOR_CLUSTERING:
            self.logger.info(f"RuleImprover: Found {len(other_items_data)} 'Other' items with text for clustering.")
            other_texts_for_embedding = [item['raw_text'] for item in other_items_data]

            try:
                other_embeddings = self.embedder.encode(other_texts_for_embedding, show_progress=False)

                if other_embeddings is not None and len(other_embeddings) > 0 and other_embeddings.ndim == 2:
                    scaler = self.StandardScaler()
                    embeddings_scaled = scaler.fit_transform(other_embeddings)

                    cluster_labels_for_llm = None
                    used_method_for_llm = None

                    if self.DBSCAN and self.NearestNeighbors:
                        eps_val = self._estimate_dbscan_eps(embeddings_scaled, DBSCAN_EPS_ESTIMATION_MIN_SAMPLES,
                                                            DBSCAN_EPS_PERCENTILE)
                        if eps_val and eps_val > 1e-6:
                            dbscan = self.DBSCAN(eps=eps_val, min_samples=DBSCAN_MIN_SAMPLES_PARAM, metric='euclidean')
                            labels_dbscan = dbscan.fit_predict(embeddings_scaled)
                            n_clusters_dbscan = len(set(labels_dbscan)) - (1 if -1 in labels_dbscan else 0)
                            if n_clusters_dbscan > 0 and n_clusters_dbscan < len(other_items_data) * 0.8:
                                cluster_labels_for_llm = labels_dbscan
                                used_method_for_llm = "DBSCAN"
                                self.logger.info(
                                    f"DBSCAN found {n_clusters_dbscan} clusters. Eps={eps_val:.3f}, MinPts={DBSCAN_MIN_SAMPLES_PARAM}")
                            else:
                                self.logger.info(
                                    f"DBSCAN produced trivial clustering ({n_clusters_dbscan} clusters). Trying KMeans.")
                        else:
                            self.logger.info("DBSCAN eps estimation failed or eps too small. Trying KMeans.")

                    if used_method_for_llm is None and self.KMeans:
                        k_optimal = self._get_optimal_k_for_kmeans(embeddings_scaled)
                        if k_optimal > 1 and k_optimal < len(other_items_data) * 0.8:
                            kmeans = self.KMeans(n_clusters=k_optimal, random_state=42, n_init='auto')
                            labels_kmeans = kmeans.fit_predict(embeddings_scaled)
                            cluster_labels_for_llm = labels_kmeans
                            used_method_for_llm = "KMeans"
                            self.logger.info(f"KMeans found {k_optimal} clusters.")
                        else:
                            self.logger.info(
                                f"KMeans optimal K is {k_optimal}, not suitable for LLM analysis of distinct groups.")

                    if cluster_labels_for_llm is not None and used_method_for_llm:
                        unique_cluster_ids = sorted(
                            [l for l in set(cluster_labels_for_llm) if l != -1])
                        for cluster_id in unique_cluster_ids:
                            member_indices = [i for i, label in enumerate(cluster_labels_for_llm) if
                                              label == cluster_id]
                            if len(member_indices) >= MIN_CLUSTER_SIZE_FOR_LLM_ANALYSIS:
                                self.logger.info(
                                    f"{used_method_for_llm} Cluster {cluster_id}: Analyzing with LLM ({len(member_indices)} items).")

                                sample_indices = np.random.choice(member_indices, size=min(len(member_indices), 5),
                                                                  replace=False)
                                sample_items_for_entities = [other_items_data[idx] for idx in sample_indices]
                                sample_texts_for_llm_prompt = [item['raw_text'][:500] for item in
                                                               sample_items_for_entities]

                                frequent_orgs_hint, frequent_prods_hint = self._get_frequent_entities_from_samples(
                                    sample_items_for_entities)

                                can_use_llm_for_rule_improvement = (
                                        self.llm_classifier and self.llm_classifier.model and self.llm_classifier.model_path
                                )

                                if can_use_llm_for_rule_improvement:
                                    prompt_text = (
                                        "You are an expert legal advertising analyst. Your task is to identify potential new litigation campaigns by finding specific, distinguishing themes in ad texts.\n"
                                        "The following text snippets are from online advertisements categorized as 'Other' because they did not fit well into predefined legal litigation categories. "
                                        "They form a distinct group based on semantic clustering.\n\n"
                                        "Based SOLELY on the provided texts and contextual hints, suggest:\n"
                                        "1. A CONCISE and SPECIFIC name for a new potential litigation campaign (e.g., 'XYZ Drug Heart Problems Lawsuit', 'Faulty ABC Product Injuries'). AVOID overly generic names like 'Injury Claims' or 'Consumer Protection'.\n"
                                        "2. A list of 3-5 common and DISTINCTIVE keywords or phrases (1-3 words each) found in these texts that could serve as triggers for this new campaign. AVOID extremely common words like 'lawsuit', 'compensation', 'attorney', 'call', 'free', 'help', 'you', 'your', 'been', 'have', 'suffered', 'may', 'if', 'eligible', 'entitled' UNLESS they are part of a very specific product or company name.\n"
                                    )
                                    if frequent_orgs_hint or frequent_prods_hint:
                                        prompt_text += "\nContextual Hints from Named Entity Recognition within these samples (These might be central to the litigation theme):\n"
                                        if frequent_orgs_hint: prompt_text += f"- Potentially relevant organizations: {', '.join(frequent_orgs_hint)}\n"
                                        if frequent_prods_hint: prompt_text += f"- Potentially relevant products/services: {', '.join(frequent_prods_hint)}\n"
                                    prompt_text += (
                                        "\nReview the following examples of input ad text snippets and desired JSON output (notice the specificity):\n\n"
                                        "Example 1:\nInput Ad Text Snippet: \"Our investigation suggests that the Ram Promaster 2022-2023 models are incapable of actually activating the 8th and 9th gears under normal driving conditions.\"\nCorresponding Suggested JSON:\n{\"campaign_name\": \"Ram Promaster Transmission Defect Lawsuit\", \"trigger_keywords\": [\"Ram Promaster\", \"transmission defect\", \"gears not activating\", \"2022-2023 models\", \"gear shift problem\"]}\n\n"
                                        "Example 2:\nInput Ad Text Snippet: \"Nitrous oxide addiction can start small — but lead to serious health consequences and a lasting impact on your life. If you or someone you care about is struggling with this addiction and experiencing harm, we’re here to help. Legal options may be available.\"\nCorresponding Suggested JSON:\n{\"campaign_name\": \"Nitrous Oxide Inhalant Addiction Harm\", \"trigger_keywords\": [\"nitrous oxide\", \"whippits\", \"inhalant addiction\", \"neurological damage\", \"serious health effects\"]}\n\n"
                                        "Example 3:\nInput Ad Text Snippet: \"Sharpen App users: If you accessed study videos through iOS, Android, or Chrome starting 10/11/2022 and had an account, you could be eligible for compensation. Tap 'Apply Now' to find out if you're eligible. Bursor & Fisher, P.A. ...\"\nCorresponding Suggested JSON:\n{\"campaign_name\": \"Sharpen App User Data Privacy Claim\", \"trigger_keywords\": [\"Sharpen App\", \"study videos\", \"user data\", \"Bursor & Fisher\", \"iOS Android privacy\"]}\n\n"
                                        "Example 4:\nInput Ad Text Snippet: \"Ticketmaster customers—were you charged more than expected for tickets? You’re not alone, and these practices could be illegal. We recently filed a case in California and we are now looking for participants in Maryland. ... Tycko & Zavareei LLP ...\"\nCorresponding Suggested JSON:\n{\"campaign_name\": \"Ticketmaster Excessive Fees Lawsuit (Maryland)\", \"trigger_keywords\": [\"Ticketmaster\", \"junk fees\", \"hidden charges\", \"Maryland consumers\", \"Tycko & Zavareei\"]}\n\n"
                                        "IMPORTANT: Respond ONLY with a single valid JSON object containing the keys 'campaign_name' (a string) and 'trigger_keywords' (a list of strings). Ensure keywords are specific and distinguishing. Do not include any other text, explanations, or apologies.\n\n"
                                        "Texts to analyze for new campaign suggestion:\n"
                                    )
                                    for i, stxt in enumerate(
                                            sample_texts_for_llm_prompt): prompt_text += f"{i + 1}. \"{stxt}\"\n"

                                    messages = [{'role': 'user', 'content': prompt_text}]
                                    # For Ollama, format="json" is an option. For other backends, this might differ.
                                    # The execute_cached_llm_call will handle backend-specifics if expanded.
                                    # For now, RuleImprover's LLM use is Ollama-focused as per original code.
                                    llm_call_options = {'temperature': 0.1}
                                    if self.llm_classifier.backend == 'ollama':
                                        llm_call_options['format'] = 'json'

                                    response_dict = self.llm_classifier.execute_cached_llm_call(
                                        messages=messages,
                                        call_options=llm_call_options
                                    )

                                    if response_dict and response_dict.get('message') and response_dict['message'].get(
                                            'content'):
                                        llm_response_content = response_dict['message']['content']
                                        try:
                                            parsed_suggestion = json.loads(llm_response_content)
                                            if 'campaign_name' in parsed_suggestion and 'trigger_keywords' in parsed_suggestion:
                                                suggestions["potential_new_campaigns_llm_cluster"].append({
                                                    "method": used_method_for_llm,
                                                    "suggested_campaign_name": parsed_suggestion['campaign_name'],
                                                    "suggested_triggers": parsed_suggestion['trigger_keywords'],
                                                    "cluster_id": int(cluster_id), "cluster_size": len(member_indices),
                                                    "frequent_orgs_hint": frequent_orgs_hint,
                                                    "frequent_products_hint": frequent_prods_hint,
                                                    "sample_texts_provided_to_llm": sample_texts_for_llm_prompt
                                                })
                                            else:
                                                self.logger.warning(
                                                    f"LLM response for {used_method_for_llm} cluster {cluster_id} missing required keys. Response: {llm_response_content}")
                                        except json.JSONDecodeError:
                                            self.logger.error(
                                                f"Failed to parse LLM JSON for {used_method_for_llm} cluster {cluster_id}. Raw response: '{llm_response_content}'")
                                    elif response_dict is None:
                                        self.logger.error(
                                            f"LLM call failed for {used_method_for_llm} cluster {cluster_id} (returned None).")
                                    else:  # Response dict exists but message or content is missing
                                        self.logger.error(
                                            f"LLM response malformed for {used_method_for_llm} cluster {cluster_id}. Full response: {str(response_dict)[:500]}")
                                else:
                                    self.logger.info(
                                        f"LLM analysis for {used_method_for_llm} cluster {cluster_id} skipped: LLM not configured/ready for RuleImprover.")
                            else:
                                self.logger.debug(
                                    f"{used_method_for_llm} Cluster {cluster_id} too small ({len(member_indices)} items), skipping LLM analysis.")
                    else:
                        self.logger.info("No suitable clusters found by any method for LLM analysis.")
                else:
                    self.logger.warning(
                        "Could not generate valid embeddings for 'Other' items. Skipping clustering analysis.")
            except Exception as e:
                self.logger.error(f"Error during 'Other' items clustering or embedding process: {e}", exc_info=True)
        else:
            if not self.embedder:
                self.logger.info("RuleImprover: Embedder unavailable for clustering.")
            elif not self.sklearn_available:
                self.logger.info("RuleImprover: Scikit-learn unavailable for clustering.")
            else:
                self.logger.info(
                    f"RuleImprover: Too few 'Other' items ({len(other_items_data)}) for clustering. Min required: {MIN_OTHER_ITEMS_FOR_CLUSTERING}.")

        return suggestions


class ClassificationAnalyzer:
    """Analyze classification results (Adapted from hybrid_campaign_classifier_m4.py)"""

    def __init__(self):
        self.console = Console()

    def generate_report(self,
                        classification_results: List[ClassificationResult],
                        ads_data: List[Dict[str, Any]],
                        text_processing_fields: List[str],  # Added
                        invalid_summary_strings: set):  # Added
        num_total = len(classification_results)
        if num_total == 0:
            self.console.print("[yellow]No classification results to analyze.[/yellow]")
            return

        campaign_counts = Counter(res.campaign for res in classification_results)
        method_counts = Counter(res.method for res in classification_results)
        reviewed_needed_count = sum(1 for res in classification_results if res.needs_review)

        avg_confidence = np.mean([res.confidence for res in classification_results if res.campaign != "Other"]) if any(
            res.campaign != "Other" for res in classification_results) else 0

        self.console.print(
            Panel(f"[bold green]Classification Report[/bold green] (Total Ads: {num_total})", border_style="green"))

        camp_table = Table(title="Campaign Distribution", box=box.ROUNDED)
        camp_table.add_column("Campaign", style="cyan", overflow="fold")
        camp_table.add_column("Count", style="magenta", justify="right")
        camp_table.add_column("% of Total", style="green", justify="right")
        for campaign, count in campaign_counts.most_common():
            camp_table.add_row(campaign, str(count), f"{(count / num_total) * 100:.2f}%")
        self.console.print(camp_table)

        method_table = Table(title="Classification Method Usage", box=box.ROUNDED)
        method_table.add_column("Method", style="cyan")
        method_table.add_column("Count", style="magenta", justify="right")
        method_table.add_column("% of Total", style="green", justify="right")
        for method, count in method_counts.most_common():
            method_table.add_row(method, str(count), f"{(count / num_total) * 100:.2f}%")
        self.console.print(method_table)

        stats_panel = Panel(
            f"Average Confidence (excluding 'Other'): {avg_confidence:.3f}\n"
            f"Ads Flagged for Review: {reviewed_needed_count} ({(reviewed_needed_count / num_total) * 100:.2f}%)",
            title="Overall Statistics", border_style="blue"
        )
        self.console.print(stats_panel)

        self.console.print("\n[bold yellow]Sample Ads for Review (Campaign 'Other' or Needs Review):[/bold yellow]")
        review_samples = []
        for i, res in enumerate(classification_results):
            if res.campaign == "Other" or res.needs_review:
                # Use the static method to get the text
                raw_text, _ = HybridClassifier._prepare_ad_text_for_processing(
                    ads_data[i],
                    text_processing_fields,  # Pass through
                    invalid_summary_strings  # Pass through
                )
                review_samples.append({
                    "ID": res.ad_id, "Text": raw_text[:150] + "...",
                    "Assigned Campaign": res.campaign, "Conf": f"{res.confidence:.2f}", "Method": res.method
                })
            if len(review_samples) >= 10: break

        if review_samples:
            review_table = Table(title="Review Samples", box=box.SIMPLE)
            for col_name in review_samples[0].keys():
                review_table.add_column(col_name)
            for sample in review_samples:
                review_table.add_row(*sample.values())
            self.console.print(review_table)
        else:
            self.console.print("No ads found matching review criteria for sample display.")


# --- Interactive Menu System ---

class InteractiveConfig:
    """Interactive configuration manager for the classifier"""

    def __init__(self):
        self.console = Console()
        self.config = {
            'campaign_config': 'src/config/fb_ad_categorizer/campaign_config.json',
            'input_csv': None,
            'input_dynamodb_table': 'FBAdArchive',
            'output_csv': 'classified_ads_output.csv',
            'output_deduplicated_csv': None,
            'embedder_cache': 'embedding_cache.pkl',
            'aws_region': 'us-east-1',
            'local_dynamodb': True,
            'dynamodb_endpoint_url': 'http://localhost:8000',
            'limit': None,
            'text_fields': ['Title', 'Body', 'Summary', 'LinkDescription', 'PageName'],
            'embedder_model': 'all-MiniLM-L6-v2',
            'ner_model': 'en_core_web_trf',
            'use_llm': False,
            'llm_backend': 'ollama',
            'llm_model': None,
            'enhanced_llm': True,
            'batch_size': 512,  # Increased for M4 Mac with 128GB RAM
            'log_level': 'INFO',
            'improve_rules': False,
            'output_rule_suggestions': 'rule_suggestions.json',
            'start_date': None,
            'deduplication_fields': ['Title', 'Body']
        }

    def show_welcome(self):
        """Display welcome screen"""
        welcome_text = Text()
        welcome_text.append("🤖 Hybrid Legal Ad Classifier", style="bold cyan")
        welcome_text.append("\n\nInteractive Configuration & Processing Tool", style="green")
        welcome_text.append("\nM4 Optimized Version with Rich UI", style="dim")

        welcome_panel = Panel(
            Align.center(welcome_text),
            border_style="cyan",
            box=box.DOUBLE,
            padding=(1, 2)
        )

        self.console.print("\n")
        self.console.print(welcome_panel)
        self.console.print("\n")

    def show_main_menu(self):
        """Display main menu and get user choice"""
        menu_options = [
            "🔧 Configure Input/Output",
            "⚙️  Configure Processing Options",
            "🧠 Configure AI Models",
            "📊 View Current Configuration",
            "🚀 Start Processing",
            "❌ Exit"
        ]

        menu_panel = Panel(
            "\n".join([f"[bold cyan]{i + 1}.[/bold cyan] {option}" for i, option in enumerate(menu_options)]),
            title="[bold green]Main Menu[/bold green]",
            border_style="green",
            box=box.ROUNDED
        )

        self.console.print(menu_panel)

        choice = IntPrompt.ask(
            "\n[bold yellow]Select an option[/bold yellow]",
            choices=[str(i + 1) for i in range(len(menu_options))],
            default="5"
        )

        return choice

    def configure_input_output(self):
        """Configure input and output options"""
        self.console.print(Panel("[bold cyan]Input/Output Configuration[/bold cyan]", border_style="cyan"))

        # DynamoDB configuration (default and primary input)
        table_name = Prompt.ask(
            "\n[green]DynamoDB table name[/green]",
            default=self.config.get('input_dynamodb_table', "FBAdArchive")
        )
        self.config['input_dynamodb_table'] = table_name
        self.config['input_csv'] = None  # Always use DynamoDB

        use_local = Confirm.ask(
            "[green]Use local DynamoDB?[/green]",
            default=self.config['local_dynamodb']
        )
        self.config['local_dynamodb'] = use_local

        if use_local:
            endpoint = Prompt.ask(
                "[green]Local DynamoDB endpoint[/green]",
                default=self.config['dynamodb_endpoint_url']
            )
            self.config['dynamodb_endpoint_url'] = endpoint
        else:
            region = Prompt.ask(
                "[green]AWS region[/green]",
                default=self.config['aws_region']
            )
            self.config['aws_region'] = region

        # Start date filter
        set_start_date = Confirm.ask(
            "\n[yellow]Filter by StartDate?[/yellow]",
            default=bool(self.config['start_date'])
        )

        if set_start_date:
            start_date = Prompt.ask(
                "[green]Start date (YYYYMMDD format, e.g., 20240101)[/green]",
                default=self.config['start_date'] or ""
            )
            if start_date.strip():
                # Validate YYYYMMDD format
                date_str = start_date.strip()
                if len(date_str) == 8 and date_str.isdigit():
                    self.config['start_date'] = date_str
                else:
                    self.console.print("[red]Invalid date format. Please use YYYYMMDD (e.g., 20240101)[/red]")
                    self.config['start_date'] = None
            else:
                self.config['start_date'] = None
        else:
            self.config['start_date'] = None

        # Processing limit
        set_limit = Confirm.ask(
            "\n[yellow]Set processing limit?[/yellow]",
            default=bool(self.config['limit'])
        )

        if set_limit:
            limit = IntPrompt.ask(
                "[green]Number of ads to process[/green]",
                default=self.config['limit'] or 1000
            )
            self.config['limit'] = limit
        else:
            self.config['limit'] = None

        # Output configuration
        output_csv = Prompt.ask(
            "\n[green]Output CSV path[/green]",
            default=self.config['output_csv']
        )
        self.config['output_csv'] = output_csv

        create_dedup = Confirm.ask(
            "[yellow]Create deduplicated output?[/yellow]",
            default=bool(self.config['output_deduplicated_csv'])
        )

        if create_dedup:
            dedup_path = Prompt.ask(
                "[green]Deduplicated CSV path[/green]",
                default=self.config['output_deduplicated_csv'] or "classified_ads_deduplicated.csv"
            )
            self.config['output_deduplicated_csv'] = dedup_path
        else:
            self.config['output_deduplicated_csv'] = None

        self.console.print("[bold green]✓ Input/Output configuration updated![/bold green]\n")

    def configure_processing(self):
        """Configure processing options"""
        self.console.print(Panel("[bold cyan]Processing Configuration[/bold cyan]", border_style="cyan"))

        # Campaign config
        config_path = Prompt.ask(
            "\n[green]Campaign config JSON path[/green]",
            default=self.config['campaign_config']
        )
        self.config['campaign_config'] = config_path

        # Text fields
        self.console.print(f"\n[yellow]Current text fields:[/yellow] {', '.join(self.config['text_fields'])}")
        modify_fields = Confirm.ask(
            "[yellow]Modify text fields for processing?[/yellow]",
            default=False
        )

        if modify_fields:
            available_fields = ['Title', 'Body', 'Summary', 'LinkDescription', 'PageName', 'LawFirm']
            self.console.print(f"\n[dim]Available fields: {', '.join(available_fields)}[/dim]")
            fields_input = Prompt.ask(
                "[green]Enter comma-separated field names[/green]",
                default=",".join(self.config['text_fields'])
            )
            self.config['text_fields'] = [f.strip() for f in fields_input.split(',') if f.strip()]

        # Batch size
        batch_size = IntPrompt.ask(
            "\n[green]Batch size for processing[/green]",
            default=self.config['batch_size']
        )
        self.config['batch_size'] = batch_size

        # Log level
        log_level = Prompt.ask(
            "[green]Log level[/green]",
            choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
            default=self.config['log_level']
        )
        self.config['log_level'] = log_level

        # Rule improvement
        improve_rules = Confirm.ask(
            "\n[yellow]Enable rule improvement analysis?[/yellow]",
            default=self.config['improve_rules']
        )
        self.config['improve_rules'] = improve_rules

        if improve_rules:
            suggestions_path = Prompt.ask(
                "[green]Rule suggestions output path[/green]",
                default=self.config['output_rule_suggestions']
            )
            self.config['output_rule_suggestions'] = suggestions_path

        self.console.print("[bold green]✓ Processing configuration updated![/bold green]\n")

    def configure_models(self):
        """Configure AI model options"""
        self.console.print(Panel("[bold cyan]AI Models Configuration[/bold cyan]", border_style="cyan"))

        # Embedder model with predefined options
        self.console.print("\n[yellow]SentenceTransformer Embedding Models:[/yellow]")
        self.console.print("[dim]1. all-MiniLM-L6-v2 (Fast, 22M params) - Current default[/dim]")
        self.console.print("[dim]2. all-mpnet-base-v2 (Balanced, 109M params) - Recommended for M4[/dim]")
        self.console.print("[dim]3. all-roberta-large-v1 (Best accuracy, 355M params) - Highest quality[/dim]")
        self.console.print("[dim]4. Custom model name[/dim]")

        model_choice = Prompt.ask(
            "[green]Choose embedding model[/green]",
            choices=["1", "2", "3", "4"],
            default="1"
        )

        if model_choice == "1":
            self.config['embedder_model'] = 'all-MiniLM-L6-v2'
        elif model_choice == "2":
            self.config['embedder_model'] = 'all-mpnet-base-v2'
        elif model_choice == "3":
            self.config['embedder_model'] = 'all-roberta-large-v1'
        else:  # Custom
            embedder_model = Prompt.ask(
                "[green]Enter custom SentenceTransformer model name[/green]",
                default=self.config['embedder_model']
            )
            self.config['embedder_model'] = embedder_model

        # Embedder cache
        cache_path = Prompt.ask(
            "[green]Embedding cache file path[/green]",
            default=self.config['embedder_cache']
        )
        self.config['embedder_cache'] = cache_path

        # NER model
        ner_model = Prompt.ask(
            "[green]spaCy NER model name[/green]",
            default=self.config['ner_model']
        )
        self.config['ner_model'] = ner_model

        # LLM configuration
        use_llm = Confirm.ask(
            "\n[yellow]Enable LLM for classification refinement?[/yellow]",
            default=self.config['use_llm']
        )
        self.config['use_llm'] = use_llm

        if use_llm:
            llm_backend = Prompt.ask(
                "[green]LLM backend[/green]",
                choices=['ollama', 'mlx', 'llama_cpp', 'transformers'],
                default=self.config['llm_backend']
            )
            self.config['llm_backend'] = llm_backend

            model_help = {
                'ollama': 'Model tag (e.g., "llama3:8b")',
                'mlx': 'MLX model path or HuggingFace repo',
                'llama_cpp': 'Local .gguf file path',
                'transformers': 'HuggingFace model name'
            }

            llm_model = Prompt.ask(
                f"[green]LLM model ({model_help[llm_backend]})[/green]",
                default=self.config['llm_model'] or ""
            )
            if llm_model.strip():
                self.config['llm_model'] = llm_model

            # Enhanced LLM option
            enhanced_llm = Confirm.ask(
                "[yellow]Enable Enhanced LLM post-processing for 'Other' category items?[/yellow]\n"
                "[dim](Uses more sophisticated prompts to rescue mis-classified ads)[/dim]",
                default=self.config.get('enhanced_llm', True)
            )
            self.config['enhanced_llm'] = enhanced_llm

        self.console.print("[bold green]✓ AI models configuration updated![/bold green]\n")

    def configure_text_fields(self):
        """Configure text field options for input, processing, and deduplication"""
        self.console.print(Panel("[bold cyan]Text Fields Configuration[/bold cyan]", border_style="cyan"))

        # Processing text fields
        self.console.print(
            f"\n[yellow]Current processing text fields:[/yellow] {', '.join(self.config['text_fields'])}")
        modify_processing_fields = Confirm.ask(
            "[yellow]Modify text fields for processing (rules, embedding)?[/yellow]",
            default=False
        )

        if modify_processing_fields:
            available_fields = ['Title', 'Body', 'Summary', 'LinkDescription', 'PageName', 'LawFirm']
            self.console.print(f"\n[dim]Available fields: {', '.join(available_fields)}[/dim]")
            fields_input = Prompt.ask(
                "[green]Enter comma-separated field names for processing[/green]",
                default=",".join(self.config['text_fields'])
            )
            self.config['text_fields'] = [f.strip() for f in fields_input.split(',') if f.strip()]

        # Deduplication fields
        self.console.print(
            f"\n[yellow]Current deduplication fields:[/yellow] {', '.join(self.config['deduplication_fields'])}")
        modify_dedup_fields = Confirm.ask(
            "[yellow]Modify deduplication fields?[/yellow]",
            default=False
        )

        if modify_dedup_fields:
            available_dedup_fields = ['Title', 'Body', 'Summary', 'LawFirm']
            self.console.print(f"\n[dim]Available fields: {', '.join(available_dedup_fields)}[/dim]")
            dedup_fields_input = Prompt.ask(
                "[green]Enter comma-separated field names for deduplication[/green]",
                default=",".join(self.config['deduplication_fields'])
            )
            self.config['deduplication_fields'] = [f.strip() for f in dedup_fields_input.split(',') if f.strip()]

        self.console.print("[bold green]✓ Text fields configuration updated![/bold green]\n")

    def _get_embedder_info(self, model_name: str) -> str:
        """Get display info for embedding model"""
        model_info = {
            'all-MiniLM-L6-v2': 'Fast, 22M params',
            'all-mpnet-base-v2': 'Balanced, 109M params',
            'all-roberta-large-v1': 'Best accuracy, 355M params'
        }
        return model_info.get(model_name, 'Custom model')

    def show_current_config(self):
        """Display current configuration"""
        config_table = Table(title="Current Configuration", box=box.ROUNDED, border_style="blue")
        config_table.add_column("Setting", style="cyan", width=25)
        config_table.add_column("Value", style="green", overflow="fold")

        # Group settings by category
        categories = {
            "Input/Output": [
                ('DynamoDB Table', self.config['input_dynamodb_table']),
                ('Local DynamoDB', str(self.config['local_dynamodb'])),
                ('DynamoDB Endpoint', self.config['dynamodb_endpoint_url'] if self.config[
                    'local_dynamodb'] else f"AWS {self.config['aws_region']}"),
                ('Start Date Filter', self.config['start_date'] or 'None'),
                ('Processing Limit', str(self.config['limit']) if self.config['limit'] else 'No limit'),
                ('Output CSV', self.config['output_csv']),
                ('Deduplicated CSV', self.config['output_deduplicated_csv'] or 'None')
            ],
            "Processing": [
                ('Campaign Config', self.config['campaign_config']),
                ('Batch Size', str(self.config['batch_size'])),
                ('Log Level', self.config['log_level']),
                ('Improve Rules', str(self.config['improve_rules'])),
                ('Rule Suggestions Path',
                 self.config['output_rule_suggestions'] if self.config['improve_rules'] else 'N/A')
            ],
            "AI Models": [
                ('Embedder Model', self.config['embedder_model']),
                ('Embedder Info', self._get_embedder_info(self.config['embedder_model'])),
                ('NER Model', self.config['ner_model']),
                ('Use LLM', str(self.config['use_llm'])),
                ('LLM Backend', self.config['llm_backend'] if self.config['use_llm'] else 'N/A'),
                ('LLM Model', self.config['llm_model'] or 'None' if self.config['use_llm'] else 'N/A'),
                ('Enhanced LLM', str(self.config.get('enhanced_llm', True)) if self.config['use_llm'] else 'N/A')
            ],
            "Text Fields": [
                ('Processing Fields', ', '.join(self.config['text_fields'])),
                ('Deduplication Fields', ', '.join(self.config['deduplication_fields']))
            ]
        }

        for category, settings in categories.items():
            config_table.add_row(f"[bold yellow]{category}[/bold yellow]", "", style="bold")
            for setting, value in settings:
                config_table.add_row(f"  {setting}", str(value))
            config_table.add_row("", "")  # Spacer

        self.console.print("\n")
        self.console.print(config_table)
        self.console.print("\n")

        Prompt.ask("[dim]Press Enter to continue...[/dim]", default="")

    def to_args_namespace(self):
        """Convert config to argparse-like namespace"""

        class ArgsNamespace:
            pass

        args = ArgsNamespace()
        args.campaign_config_path = self.config['campaign_config']
        args.input_csv = self.config['input_csv']
        args.input_dynamodb_table_name = self.config['input_dynamodb_table']
        args.output_csv = self.config['output_csv']
        args.output_deduplicated_csv = self.config['output_deduplicated_csv']
        args.embedder_cache = self.config['embedder_cache']
        args.aws_region = self.config['aws_region']
        args.local_dynamodb = self.config['local_dynamodb']
        args.dynamodb_endpoint_url = self.config['dynamodb_endpoint_url']
        args.limit = self.config['limit']
        args.text_fields = self.config['text_fields']
        args.embedder_model = self.config['embedder_model']
        args.ner_model = self.config['ner_model']
        args.use_llm = self.config['use_llm']
        args.llm_backend = self.config['llm_backend']
        args.llm_model = self.config['llm_model']
        args.enhanced_llm = self.config.get('enhanced_llm', True)
        args.batch_size = self.config['batch_size']
        args.log_level = self.config['log_level']
        args.improve_rules = self.config['improve_rules']
        args.output_rule_suggestions = self.config['output_rule_suggestions']
        args.start_date = self.config['start_date']
        args.deduplication_fields = self.config['deduplication_fields']

        # Debug: Log the start_date being passed
        logger.info(f"Config start_date being passed to args: {self.config['start_date']}")

        return args


def run_interactive_mode():
    """Run the interactive configuration and processing mode"""
    config_manager = InteractiveConfig()
    config_manager.show_welcome()

    while True:
        try:
            choice = config_manager.show_main_menu()

            if choice == 1:
                config_manager.configure_input_output()
            elif choice == 2:
                config_manager.configure_processing()
            elif choice == 3:
                config_manager.configure_models()
            elif choice == 4:
                config_manager.show_current_config()
            elif choice == 5:
                # Start processing
                config_manager.console.print("\n[bold green]🚀 Starting classification process...[/bold green]\n")
                args = config_manager.to_args_namespace()

                # Run the main processing function
                if sys.platform == 'win32' and sys.version_info >= (3, 8):
                    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

                asyncio.run(main_async(args))

                # Ask if user wants to continue
                continue_processing = Confirm.ask(
                    "\n[yellow]Return to main menu?[/yellow]",
                    default=True
                )
                if not continue_processing:
                    break

            elif choice == 6:
                config_manager.console.print("\n[bold yellow]👋 Goodbye![/bold yellow]")
                break

        except KeyboardInterrupt:
            config_manager.console.print("\n\n[bold red]Process interrupted by user.[/bold red]")
            break
        except Exception as e:
            config_manager.console.print(f"\n[bold red]Error: {e}[/bold red]")
            logger.error(f"Interactive mode error: {e}", exc_info=True)

            continue_after_error = Confirm.ask(
                "[yellow]Continue anyway?[/yellow]",
                default=True
            )
            if not continue_after_error:
                break


# --- Main Execution ---
async def main_async(args):
    console = Console()
    console.print(Panel.fit(
        "[bold cyan]Hybrid Legal Ad Classifier[/bold cyan]",
        subtitle="M4 Optimized Version",
        box=box.DOUBLE
    ))

    start_time = time.time()
    logger.setLevel(args.log_level.upper())

    try:
        classifier = HybridClassifier(
            campaign_config_path=args.campaign_config_path,
            text_processing_fields=args.text_fields,
            embedder_model_name=args.embedder_model,
            embedder_cache_file=args.embedder_cache,
            company_ner_model=args.ner_model,
            use_llm=args.use_llm,
            llm_backend=args.llm_backend,
            llm_model_path=args.llm_model,
            llm_cache_file=getattr(args, 'llm_cache_file', 'llm_response_cache.pkl'),
            enhanced_llm=args.enhanced_llm,
            llm_timeout=getattr(args, 'llm_timeout', 120),
            llm_max_retries=getattr(args, 'llm_max_retries', 3),
            llm_retry_delay=getattr(args, 'llm_retry_delay', 5),
            ner_cache_file=getattr(args, 'ner_cache_file', 'ner_results_cache.pkl'),
            log_level=args.log_level.upper(),
            skip_terms_file=getattr(args, 'skip_terms_file', None),
            company_similarity_threshold=getattr(args, 'company_similarity_threshold', 85),
            improve_rules_active=args.improve_rules,
            max_workers=getattr(args, 'max_workers', None),
            parallel_chunk_size=getattr(args, 'parallel_chunk_size', 100)
        )
    except Exception as e:
        console.print(f"[bold red]Error initializing HybridClassifier: {e}[/bold red]")
        logger.critical(f"HybridClassifier init failed: {e}", exc_info=True)
        return

    ads_df = pd.DataFrame()
    if args.input_csv:
        console.print(f"[cyan]Loading data from CSV: {args.input_csv}[/cyan]")
        try:
            ads_df = pd.read_csv(args.input_csv, dtype={'AdArchiveID': str, 'ad_id': str})
            if 'ad_id' in ads_df.columns and 'AdArchiveID' not in ads_df.columns:
                ads_df.rename(columns={'ad_id': 'AdArchiveID'}, inplace=True)

            if 'AdArchiveID' not in ads_df.columns:
                logger.warning(
                    "AdArchiveID column not found in CSV, generating temporary IDs for deduplication uniqueness.")
                # Generate unique IDs for rows missing AdArchiveID for the purpose of deduplication key generation
                # This will be f"csv_temp_id_{index}"
            else:
                # Ensure AdArchiveID is string and handle NaNs before deduplication key generation
                ads_df['AdArchiveID'] = ads_df['AdArchiveID'].astype(str).fillna('')

            if args.limit and args.limit > 0:
                ads_df = ads_df.head(args.limit)
            console.print(f"[green]✓ Loaded {len(ads_df)} ads from CSV.[/green]")
        except FileNotFoundError:
            console.print(f"[bold red]Error: Input CSV file not found at {args.input_csv}[/bold red]")
            classifier.close()
            return
        except Exception as e:
            console.print(f"[bold red]Error loading CSV {args.input_csv}: {e}[/bold red]")
            classifier.close()
            return

    if args.input_dynamodb_table_name:
        console.print(f"[cyan]Loading data from DynamoDB table: {args.input_dynamodb_table_name}[/cyan]")
        loader = OptimizedDynamoDBLoader(
            table_name=args.input_dynamodb_table_name,
            region=args.aws_region,
            endpoint_url=args.dynamodb_endpoint_url if args.local_dynamodb else None
        )
        dynamo_ads_df = loader.load_data_parallel(limit=args.limit, start_date=getattr(args, 'start_date', None))

        if not dynamo_ads_df.empty:
            ads_df = dynamo_ads_df  # Prioritize DynamoDB data if loaded
            console.print(f"[green]✓ Loaded {len(ads_df)} ads from DynamoDB (all attributes).[/green]")
        elif ads_df.empty:  # No CSV data was loaded either
            console.print(
                f"[bold red]Failed to load data from DynamoDB or table is empty, and no CSV data provided. Exiting.[/bold red]")
            classifier.close()
            return

    if ads_df.empty:
        # This block for dummy data will only be reached if no input_csv AND no input_dynamodb_table_name were specified.
        # Or if both failed to load any data.
        if not args.input_csv and not args.input_dynamodb_table_name:
            console.print(f"[yellow]No input source (CSV/DynamoDB) specified. Using dummy data for testing.[/yellow]")
            dummy_data = [
                {'AdArchiveID': 'DUMMY_1', 'Title': 'Camp Lejeune Water Contamination Lawsuit',
                 'Body': 'Affected by Camp Lejeune water? Contact us now.', 'Summary': 'Camp Lejeune claims.',
                 'LawFirm': 'Law Firm X'},
                {'AdArchiveID': 'DUMMY_2', 'Title': 'RoundUp Weedkiller Cancer Claims',
                 'Body': 'Used RoundUp and developed Non-Hodgkins Lymphoma? You may be entitled to compensation.',
                 'LawFirm': 'Law Firm Y',
                 'LinkDescription': 'Learn more about RoundUp lawsuits', 'PageName': 'RoundUp Claims Page'},
                {'AdArchiveID': 'DUMMY_3', 'Title': 'Mesothelioma Asbestos Exposure Attorney',
                 'Body': 'Exposed to asbestos? Diagnosed with mesothelioma? Call for free consultation.',
                 'PageName': 'Best Asbestos Lawyers LLP', 'Summary': 'NA', 'LawFirm': 'Best Asbestos Lawyers LLP'}
            ]
            ads_df = pd.DataFrame(dummy_data)
        # Ensure AdArchiveID is string type for dummy data too.
        if 'AdArchiveID' in ads_df.columns:
            ads_df['AdArchiveID'] = ads_df['AdArchiveID'].astype(str)

        if args.limit and args.limit > 0: ads_df = ads_df.head(args.limit)
        if not ads_df.empty:
            console.print(f"[yellow]Loaded {len(ads_df)} dummy ads for testing.[/yellow]")

    if ads_df.empty:
        console.print("[bold red]No ad data to process after all loading attempts. Exiting.[/bold red]")
        classifier.close()
        return

    # Standardize AdArchiveID column: use 'ad_id' if 'AdArchiveID' is missing
    if 'AdArchiveID' not in ads_df.columns and 'ad_id' in ads_df.columns:
        ads_df.rename(columns={'ad_id': 'AdArchiveID'}, inplace=True)

    # Ensure AdArchiveID column exists, fill NaNs with empty string BEFORE to_dict for key generation
    if 'AdArchiveID' in ads_df.columns:
        ads_df['AdArchiveID'] = ads_df['AdArchiveID'].astype(str).fillna('')
    else:  # If neither AdArchiveID nor ad_id was present
        logger.warning(
            "AdArchiveID (or ad_id) column is missing. This might affect deduplication and processing if not handled by fallback ID generation later.")
        # We won't add a dummy AdArchiveID column here; let later logic handle it if needed for unique keying.

    # Fill NaN in all potential text columns (object type) with empty strings
    for col in ads_df.select_dtypes(include=['object']).columns:
        ads_df[col] = ads_df[col].fillna("").astype(str)

    logger.info(f"DataFrame columns before to_dict: {list(ads_df.columns)}")

    initial_ads_data_list = ads_df.to_dict(orient='records')
    num_before_dedup = len(initial_ads_data_list)

    # Deduplicate ads_data_list based on a consistently derived AdArchiveID
    unique_ads_map = {}
    ads_data_list = []  # This will be the deduplicated list

    for index, ad_item in enumerate(initial_ads_data_list):
        # Consistent AdArchiveID derivation:
        # Prefer 'AdArchiveID', then 'ad_id', then generate a unique fallback.
        # The .get('AdArchiveID', '') from df should already be a string due to astype(str).fillna('')
        # However, some items might still have '' if AdArchiveID was NaN/None and then filled.

        processed_ad_id = ad_item.get('AdArchiveID')  # Already string or empty
        if not processed_ad_id:  # If AdArchiveID is empty string after fillna
            # Try 'ad_id' if it existed and was different (less likely after rename, but for safety)
            processed_ad_id = ad_item.get('ad_id')
            if not processed_ad_id:  # If ad_id is also missing or empty
                processed_ad_id = f"generated_unique_id_for_row_{index}"
            else:  # ad_id was found and non-empty
                processed_ad_id = str(processed_ad_id)  # Ensure string
        # else: AdArchiveID was found and non-empty

        if processed_ad_id not in unique_ads_map:
            unique_ads_map[processed_ad_id] = ad_item
            ads_data_list.append(ad_item)

    num_after_dedup = len(ads_data_list)
    logger.info(f"Deduplicated ad list based on AdArchiveID (or fallback unique ID): "
                f"{num_before_dedup} items before, {num_after_dedup} items after.")
    if num_before_dedup > num_after_dedup:
        console.print(
            f"[yellow]Deduplication: Reduced ad list from {num_before_dedup} to {num_after_dedup} unique AdArchiveIDs (or fallbacks).[/yellow]")

    console.print(f"\n[bold green]🚀 Starting Classification Process[/bold green]")
    console.print(f"[cyan]Input data: {len(ads_data_list):,} ads for classification...[/cyan]")
    filter_summary_parts = []
    if hasattr(args, 'start_date') and args.start_date: filter_summary_parts.append(f"StartDate >= {args.start_date}")
    if args.limit: filter_summary_parts.append(
        f"Limited to {args.limit:,} items (Note: limit applied before deduplication if DynamoDB was source)")
    if filter_summary_parts: console.print(f"[dim]Applied filters: {', '.join(filter_summary_parts)}[/dim]")
    console.print()

    classification_results = classifier.classify_batch(ads_data_list, batch_size=args.batch_size,
                                                       output_csv=args.output_csv,
                                                       output_deduplicated_csv=args.output_deduplicated_csv,
                                                       deduplication_fields=args.deduplication_fields)

    if not classification_results:
        console.print(
            "[yellow]No classification results were generated. Skipping report and rule improvement.[/yellow]")
    else:
        analyzer = ClassificationAnalyzer()
        analyzer.generate_report(classification_results, ads_data_list,  # Pass deduplicated list for report context
                                 classifier.text_processing_fields, classifier.invalid_summary_strings)

        if args.use_llm and args.enhanced_llm and not classifier.improve_rules_active:
            enhanced_llm_rescued = sum(1 for r in classification_results if r.method == "llm_enhanced")
            # llm_company_created was removed, new name gen is llm_new_name
            llm_new_name_created = sum(1 for r in classification_results if r.method == "llm_new_name")
            total_llm_improvements_by_enhancer = enhanced_llm_rescued + llm_new_name_created

            if total_llm_improvements_by_enhancer > 0:
                console.print(f"\n[bold cyan]🧠 LLM Performance (direct ad classification/refinement):[/bold cyan]")
                console.print(
                    f"   • Re-classified into existing campaigns (enhanced prompt): {enhanced_llm_rescued} items")
                console.print(f"   • Generated new campaign names: {llm_new_name_created} items")
                console.print(
                    f"   • Total items updated by these LLM steps: {total_llm_improvements_by_enhancer} items")
            else:
                console.print(
                    f"\n[yellow]ℹ️  LLM for direct ad classification/refinement was enabled but made no changes this run (or all items were classified before this step).[/yellow]")

        if args.improve_rules:
            console.print("\n[cyan]Analyzing for rule improvement suggestions...[/cyan]")
            rule_improver = RuleImprover(
                campaign_config_path=Path(classifier.campaign_config_path),
                embedder=classifier.embedder,
                llm_classifier=classifier.llm_classifier,
                text_processing_fields=classifier.text_processing_fields,
                invalid_summary_strings=classifier.invalid_summary_strings,
                logger_instance=logger
            )
            # Pass the deduplicated ads_data_list which matches the classification_results
            suggestions = rule_improver.analyze_and_suggest(classification_results, ads_data_list)
            if args.output_rule_suggestions:
                rule_improver.save_improved_config(suggestions, args.output_rule_suggestions)
            else:
                console.print("[bold yellow]Rule Suggestions (JSON):[/bold yellow]")

                class NpEncoder(json.JSONEncoder):
                    def default(self, obj):
                        if isinstance(obj, np.integer): return int(obj)
                        if isinstance(obj, np.floating): return float(obj)
                        if isinstance(obj, np.ndarray): return obj.tolist()
                        if isinstance(obj, Path): return str(obj)
                        return super(NpEncoder, self).default(obj)

                console.print(json.dumps(suggestions, indent=2, cls=NpEncoder, ensure_ascii=False))

    classifier.close()
    end_time = time.time()
    processing_time = end_time - start_time
    console.print(f"\n[bold green]Total processing time: {processing_time:.2f} seconds.[/bold green]")
    if len(ads_data_list) > 0 and processing_time > 0:  # Use length of deduplicated list
        console.print(f"[bold cyan]Average speed: {len(ads_data_list) / processing_time:.2f} ads/second.[/bold cyan]")


def load_yaml_config(config_path: str) -> Dict[str, Any]:
    """Load configuration from YAML file."""
    config_file = Path(config_path)
    if not config_file.exists():
        raise FileNotFoundError(f"Config file not found: {config_path}")

    with open(config_file, 'r') as f:
        return yaml.safe_load(f)


def yaml_config_to_args(config: Dict[str, Any]) -> Dict[str, Any]:
    """Convert YAML config to args dictionary."""
    args = {}
    console = Console()
    console.print("[bold green]Configuration Loaded from YAML:[/bold green]")

    input_config = config.get('input', {})
    args['input_csv'] = input_config.get('csv_file')
    args['input_dynamodb_table_name'] = input_config.get('dynamodb_table_name')
    args['local_dynamodb'] = input_config.get('local_dynamodb', False)
    args['dynamodb_endpoint_url'] = input_config.get('dynamodb_endpoint_url')
    args['start_date'] = input_config.get('start_date')
    args['limit'] = input_config.get('limit')
    args['aws_region'] = config.get('aws', {}).get('region', 'us-east-1')

    campaigns_config = config.get('campaigns', {})
    args['campaign_config_path'] = campaigns_config.get('config_file',
                                                        'src/config/fb_ad_categorizer/campaign_config.json')
    args['skip_terms_file'] = campaigns_config.get('skip_terms_file')

    processing = config.get('processing', {})
    args['batch_size'] = processing.get('batch_size', 256)
    args['text_fields'] = processing.get('text_fields', ['Title', 'Body', 'Summary'])
    args['deduplication_fields'] = processing.get('deduplication_fields', ['Title', 'Body'])

    models_config = config.get('models', {})  # Renamed to avoid conflict
    embedder = models_config.get('embedder', {})
    args['embedder_model'] = embedder.get('model_name', 'all-MiniLM-L6-v2')
    args['embedder_cache'] = embedder.get('cache_file', 'embedding_cache.pkl')

    ner = models_config.get('ner', {})
    args['ner_model'] = ner.get('model_name', 'en_core_web_trf')
    args['ner_cache_file'] = ner.get('cache_file', 'ner_results_cache.pkl')

    llm = models_config.get('llm', {})
    args['use_llm'] = llm.get('enabled', False)
    args['llm_backend'] = llm.get('backend', 'ollama')
    args['llm_model'] = llm.get('model')  # This is model_path for LocalLLMClassifier
    args['llm_cache_file'] = llm.get('cache_file', 'llm_response_cache.pkl')  # Added this
    args['enhanced_llm'] = llm.get('enhanced_llm', True)
    args['llm_timeout'] = llm.get('timeout', 120)
    args['llm_max_retries'] = llm.get('max_retries', 3)
    args['llm_retry_delay'] = llm.get('retry_delay', 5)

    output = config.get('output', {})
    args['output_csv'] = output.get('csv_file', 'classified_ads.csv')
    args['output_deduplicated_csv'] = output.get('deduplicated_csv_file')  # Default to None if not present

    rules = config.get('rules', {})
    args['improve_rules'] = rules.get('improve_rules', False)
    args['output_rule_suggestions'] = rules.get('output_suggestions')

    args['log_level'] = config.get('logging', {}).get('level', 'INFO')
    args['interactive'] = False  # Not set from YAML

    # Log the loaded config for verification
    for key, value in args.items():
        if key not in ['interactive']:  # Don't log interactive for non-interactive runs
            console.print(f"  [dim]{key}:[/dim] {value}")
    console.print("[bold green]YAML configuration parsing complete.[/bold green]\n")
    return args


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Hybrid Legal Ad Classifier")

    # Config file
    parser.add_argument('--config', type=str,
                        help='YAML config file path')
    # Interactive mode
    parser.add_argument('--interactive', '-i', action='store_true',
                        help='Run in interactive mode')

    # Add all other arguments but without 'default=', as defaults will be handled by the script_defaults dict
    # This allows us to know if an arg was explicitly passed on CLI vs. taking a default.
    # For boolean flags, we'll use action='store_true' and action='store_false'.

    parser.add_argument('--campaign-config-path', type=str, help='Path to campaign configuration JSON file')
    parser.add_argument('--input-csv', type=str, help='Path to input CSV file with ads')
    parser.add_argument('--output-csv', type=str, help='Path to save comprehensive classified ads CSV')
    parser.add_argument('--output-deduplicated-csv', type=str, help='Optional: Path to save deduplicated CSV')
    parser.add_argument('--embedder-cache', type=str, help='Path for embedding cache file')
    parser.add_argument('--input-dynamodb-table-name', type=str, help='DynamoDB table name')
    parser.add_argument('--start-date', type=str, help='Filter ads by StartDate (YYYYMMDD)')
    parser.add_argument('--aws-region', type=str, help='AWS region for DynamoDB')

    # Tri-state booleans are tricky with argparse. A common pattern:
    # --feature / --no-feature. Default to None to see if user specified.
    parser.add_argument('--local-dynamodb', action=argparse.BooleanOptionalAction,
                        help='Use local DynamoDB (default: True if endpoint is localhost)')

    parser.add_argument('--dynamodb-endpoint-url', type=str, help='Endpoint URL for local DynamoDB')
    parser.add_argument('--limit', type=int, help='Limit number of ads to process')
    parser.add_argument('--text-fields', nargs='+', help="Fields for text processing")
    parser.add_argument('--deduplication-fields', nargs='+', help="Fields for deduplication")
    parser.add_argument('--embedder-model', type=str, help='SentenceTransformer model name')
    parser.add_argument('--ner-model', type=str, help='spaCy NER model name')
    parser.add_argument('--ner-cache-file', type=str, help='Path for NER results cache')

    parser.add_argument('--use-llm', action=argparse.BooleanOptionalAction, help='Enable LLM refinement')

    parser.add_argument('--llm-backend', choices=['mlx', 'llama_cpp', 'transformers', 'ollama'], help='LLM backend')
    parser.add_argument('--llm-model', type=str, help='LLM model path or name/tag')
    parser.add_argument('--llm-cache-file', type=str, help='LLM response cache file')

    parser.add_argument('--enhanced-llm', action=argparse.BooleanOptionalAction,
                        help='Enable enhanced LLM for "Other" items')

    parser.add_argument('--llm-timeout', type=int, help='Timeout for LLM requests')
    parser.add_argument('--llm-max-retries', type=int, help='LLM request retries')
    parser.add_argument('--llm-retry-delay', type=int, help='Delay between LLM retries')
    parser.add_argument('--batch-size', type=int, help='Batch size')
    parser.add_argument('--log-level', type=str, choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'])

    parser.add_argument('--improve-rules', action=argparse.BooleanOptionalAction,
                        help='Enable rule improvement analysis')

    parser.add_argument('--output-rule-suggestions', type=str, help='Path for rule suggestions')
    parser.add_argument('--skip-terms-file', type=str, help='Path to skip terms JSON file')
    parser.add_argument('--company-similarity-threshold', type=float, help='Company name fuzzy match threshold')
    
    # Parallel processing arguments for M4 Mac optimization
    parser.add_argument('--max-workers', type=int, help='Maximum number of parallel workers (default: auto-detect based on CPU cores)')
    parser.add_argument('--parallel-chunk-size', type=int, help='Chunk size for parallel processing (default: 100)')

    cli_args = parser.parse_args()

    # --- Configuration Loading Order ---
    # 1. Script hardcoded defaults
    # 2. Values from YAML file (if --config specified)
    # 3. Values from command-line arguments (override YAML and defaults)

    config = {
        'campaign_config_path': 'src/config/fb_ad_categorizer/campaign_config.json',
        'output_csv': 'classified_ads_output.csv',
        'output_deduplicated_csv': None,
        'embedder_cache': 'embedding_cache.pkl',
        'aws_region': 'us-east-1',
        'local_dynamodb': True,  # Default to True for local usage
        'dynamodb_endpoint_url': 'http://localhost:8000',
        'limit': None,
        'text_fields': ['Title', 'Body', 'Summary', 'LinkDescription', 'PageName'],
        'deduplication_fields': ['Title', 'Body'],
        'embedder_model': 'all-MiniLM-L6-v2',
        'ner_model': 'en_core_web_trf',
        'ner_cache_file': 'ner_results_cache.pkl',
        'use_llm': False,
        'llm_backend': 'ollama',
        'llm_model': None,
        'llm_cache_file': 'llm_response_cache.pkl',
        'enhanced_llm': True,
        'llm_timeout': 120,
        'llm_max_retries': 3,
        'llm_retry_delay': 5,
        'batch_size': 512,  # Increased for M4 Mac with 128GB RAM
        'log_level': 'INFO',
        'improve_rules': False,
        'output_rule_suggestions': 'rule_suggestions.json',
        'skip_terms_file': None,
        'company_similarity_threshold': 85,
        'max_workers': None,  # Auto-detect based on CPU cores
        'parallel_chunk_size': 500,  # Larger chunks for 128GB RAM
        'input_csv': None,
        'input_dynamodb_table_name': None,
        'start_date': None,
    }

    yaml_config_data = {}
    if cli_args.config:
        config_file_path = cli_args.config
        if not os.path.isabs(config_file_path) and not os.path.dirname(config_file_path):
            # If only a filename is given, assume it's in the standard config directory
            config_file_path = os.path.join("src", "config", "fb_ad_categorizer", config_file_path)

        if not os.path.exists(config_file_path):
            print(f"Error: YAML configuration file not found at '{config_file_path}'")
            sys.exit(1)
        print(f"Loading YAML configuration from: {config_file_path}")
        yaml_data_raw = load_yaml_config(config_file_path)  # Returns raw dict from YAML
        # yaml_config_to_args also prints, so we just need the dict
        yaml_config_data = yaml_config_to_args(yaml_data_raw)  # This will print the YAML content
        config.update(yaml_config_data)

    # Override with CLI arguments IF they were actually provided by the user
    # For action=argparse.BooleanOptionalAction, cli_args.arg will be True, False, or None
    for arg_name, cli_value in vars(cli_args).items():
        if arg_name in ['config', 'interactive']:  # Already handled or not for main_async
            continue

        # If the CLI value is not None, it means the user specified it
        # (either a value, or --flag / --no-flag for BooleanOptionalAction)
        if cli_value is not None:
            config[arg_name] = cli_value
        # If CLI value is None, but we had a YAML value, keep the YAML value.
        # If CLI value is None and no YAML value, the script default is already in 'config'.

    # Final check for local_dynamodb based on endpoint if not explicitly set
    # This is needed because cli_args.local_dynamodb will be None if neither --local-dynamodb nor --no-local-dynamodb is used.
    # In that case, we want YAML to take precedence, or infer from endpoint.
    if cli_args.local_dynamodb is None:  # Not set on CLI
        if yaml_config_data and 'local_dynamodb' in yaml_config_data:
            config['local_dynamodb'] = yaml_config_data['local_dynamodb']  # YAML takes precedence
        elif config.get('dynamodb_endpoint_url') and 'localhost' in config['dynamodb_endpoint_url']:
            config['local_dynamodb'] = True  # Infer if endpoint is localhost
        elif config.get('dynamodb_endpoint_url'):  # Endpoint set but not localhost
            config['local_dynamodb'] = False
        # If endpoint_url is also not set, it will keep the script default ('local_dynamodb': True)


    # Create a namespace object from the final config dict
    class ArgsNamespace:
        def __init__(self, **kwargs):
            self.__dict__.update(kwargs)

        def __getattr__(self, name):  # Default to None if an attribute is accessed but not set
            return self.__dict__.get(name)


    final_args_namespace = ArgsNamespace(**config)

    if cli_args.interactive or (len(sys.argv) == 1 and not cli_args.config):
        run_interactive_mode()
    else:
        if sys.platform == 'win32' and sys.version_info >= (3, 8):
            asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
        asyncio.run(main_async(final_args_namespace))
