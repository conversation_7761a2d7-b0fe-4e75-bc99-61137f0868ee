#!/usr/bin/env python3
"""
Template for scripts that need to access the project root.
This template shows how to use the utils.get_project_root() function
to avoid hardcoded paths.
"""

import os
import sys
from utils import get_project_root

# Get the project root path
PROJECT_ROOT = get_project_root()

def main():
    """Main function that demonstrates using PROJECT_ROOT."""
    
    # Example of constructing paths using PROJECT_ROOT
    data_dir = os.path.join(PROJECT_ROOT, "data")
    src_data_dir = os.path.join(PROJECT_ROOT, "src", "data")
    config_dir = os.path.join(PROJECT_ROOT, "src", "config")
    
    print(f"Project root: {PROJECT_ROOT}")
    print(f"Data directory: {data_dir}")
    print(f"Src data directory: {src_data_dir}")
    print(f"Config directory: {config_dir}")
    
    # Example of checking if a directory exists
    if not os.path.exists(data_dir):
        print(f"Directory does not exist: {data_dir}")
        return
    
    # Process a date-specific directory
    if len(sys.argv) > 1:
        date_str = sys.argv[1]
        date_dir = os.path.join(data_dir, date_str)
        if not os.path.exists(date_dir):
            print(f"Date directory does not exist: {date_dir}")
            return
        
        print(f"Date directory: {date_dir}")
        
        # Example of constructing a dockets path
        dockets_dir = os.path.join(date_dir, "dockets")
        if os.path.exists(dockets_dir):
            print(f"Dockets directory: {dockets_dir}")
            
            # Count files in the dockets directory
            files = [f for f in os.listdir(dockets_dir) if os.path.isfile(os.path.join(dockets_dir, f))]
            print(f"Number of files in dockets directory: {len(files)}")

if __name__ == "__main__":
    main()