#!/usr/bin/env python3
"""
Script to migrate data from a local DynamoDB table (FBImageHash) to an AWS DynamoDB table.

It loads data from the local table into a pandas DataFrame, then uploads it to AWS
using FBImageHashManager for batch insertion.

Key assumptions:
- Local table PK might be 'ImageHash', target AWS table PK is 'PHash'.
- Target AWS table SK is 'AdArchiveID'.
- FBImageHashManager is configured to use 'PHash' as PK and 'AdArchiveID' as SK.

Example Usage:
  python src/scripts/migrate_fb_hashes.py --local-endpoint http://localhost:8000 --aws-region us-east-1 --table-name FBImageHash
"""

import argparse
import logging
import os
import sys

import pandas as pd
from rich.logging import RichHandler
from rich.progress import (
    Progress,
    BarColumn,
    TextColumn,
    TimeRemainingColumn,
    TimeElapsedColumn,
    MofNCompleteColumn
)

# Add project root to Python path to allow importing from src.lib
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, PROJECT_ROOT)

try:
    from src.lib.fb_ads.image_utils import FBImageHashManager
    # DynamoDbBaseManager is implicitly used as FBImageHashManager inherits from it.
except ImportError as e:
    print(f"Error importing project modules: {e}. Ensure you are running from the project root or PYTHONPATH is set.")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
    datefmt="[%X]",
    handlers=[RichHandler(rich_tracebacks=True, show_path=False, show_level=True)]
)
logger = logging.getLogger("migrate_fb_hashes")

DEFAULT_LOCAL_DYNAMODB_ENDPOINT = "http://localhost:8000"
DEFAULT_AWS_REGION = "us-east-1"  # Default AWS region
DEFAULT_TABLE_NAME = "FBImageHash"
BATCH_UPLOAD_SIZE = 25  # DynamoDB BatchWriteItem limit

def load_local_table_to_df(table_name: str, local_endpoint_url: str) -> pd.DataFrame:
    """Loads all items from a local DynamoDB table into a pandas DataFrame."""
    logger.info(f"Connecting to local DynamoDB at {local_endpoint_url} for table '{table_name}'...")
    try:
        # Extract port from local_endpoint_url for FBImageHashManager
        try:
            local_port = int(local_endpoint_url.split(':')[-1])
        except ValueError:
            logger.error(f"Could not parse port from local_endpoint_url: {local_endpoint_url}. Expected format like http://localhost:8000")
            return pd.DataFrame()

        local_manager = FBImageHashManager(
            table_name=table_name,
            use_local=True,
            local_port=local_port,
            # region_name is not strictly needed for local if endpoint_url is full, but good to be explicit
            region_name='us-east-1' # Dummy region for local
        )

        if not local_manager.table_exists():
            logger.error(f"Local table '{table_name}' does not exist at {local_endpoint_url}.")
            return pd.DataFrame()

        logger.info(f"Scanning local table '{table_name}'... This may take a while.")

        items = []
        # scan_table is inherited from DynamoDbBaseManager, returns a generator
        # and handles Decimal conversion.
        all_items_iterator = local_manager.scan_table()

        with Progress(
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            MofNCompleteColumn(),
            TimeRemainingColumn(),
            TimeElapsedColumn(),
            transient=False
        ) as progress:
            # Get total count for progress bar if possible, otherwise it updates dynamically
            # total_items_count = local_manager.count_table_items()
            # scan_task = progress.add_task(f"Scanning local {table_name}", total=total_items_count if total_items_count > -1 else None)
            # For simplicity, let's not pre-count, progress bar will show N/unknown initially.
            scan_task = progress.add_task(f"Scanning local {table_name}", total=None)
            count = 0
            for item in all_items_iterator:
                items.append(item)
                count += 1
                progress.update(scan_task, advance=1, description=f"Scanning local {table_name} ({count} items)")
            progress.update(scan_task, total=count, completed=count, description=f"Scanned {count} items from local {table_name}")

        if not items:
            logger.warning(f"No items found in local table '{table_name}'.")
            return pd.DataFrame()

        logger.info(f"Successfully scanned {len(items)} items from local table '{table_name}'.")
        df = pd.DataFrame(items)
        logger.info(f"Loaded data into DataFrame. Shape: {df.shape}")

        # Ensure PK ('PHash') and SK ('AdArchiveID') are present for the upload.
        # FBImageHashManager is configured to use 'PHash' as PK.
        if 'PHash' not in df.columns:
            if 'ImageHash' in df.columns:
                logger.info("Renaming 'ImageHash' column to 'PHash' for AWS table compatibility.")
                df.rename(columns={'ImageHash': 'PHash'}, inplace=True)
            else:
                logger.error("'PHash' (or 'ImageHash' as fallback) column not found in the local data. Cannot proceed.")
                return pd.DataFrame()
        
        if 'AdArchiveID' not in df.columns:
            logger.error("'AdArchiveID' column (Sort Key) not found in the local data. Cannot proceed.")
            return pd.DataFrame()
            
        return df

    except Exception as e:
        logger.error(f"Error loading local table '{table_name}' to DataFrame: {e}", exc_info=True)
        return pd.DataFrame()

def upload_df_to_aws(df: pd.DataFrame, table_name: str, aws_region: str):
    """Uploads a DataFrame to an AWS DynamoDB table using FBImageHashManager."""
    if df.empty:
        logger.warning("DataFrame is empty. Nothing to upload.")
        return

    logger.info(f"Initializing FBHashManager for AWS DynamoDB table '{table_name}' in region '{aws_region}'...")
    try:
        aws_hash_manager = FBImageHashManager(
            table_name=table_name,
            region_name=aws_region,
            use_local=False
        )
        # FBImageHashManager's __init__ should verify table existence on AWS.

        logger.info(f"Preparing to upload {len(df)} items to AWS table '{table_name}'...")
        
        records_to_upload = df.to_dict('records')

        total_records = len(records_to_upload)
        successful_uploads = 0
        failed_uploads = 0

        with Progress(
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            MofNCompleteColumn(),
            TimeRemainingColumn(),
            TimeElapsedColumn(),
            transient=False
        ) as progress:
            upload_task = progress.add_task(f"Uploading to AWS {table_name}", total=total_records)

            for i in range(0, total_records, BATCH_UPLOAD_SIZE):
                batch = records_to_upload[i:i + BATCH_UPLOAD_SIZE]
                if not batch:
                    continue
                
                # batch_insert_items is inherited by FBImageHashManager from DynamoDbBaseManager
                # It returns (num_successful, num_failed_after_retries)
                succeeded, failed = aws_hash_manager.batch_insert_items(batch, batch_size=len(batch), disable_progress=True)
                
                successful_uploads += succeeded
                failed_uploads += failed
                
                progress.update(upload_task, advance=len(batch)) # Advance by batch size processed
                if failed > 0:
                    logger.warning(f"Batch ending at index {i+len(batch)-1}: {failed} items failed to upload after retries.")
        
        logger.info(f"Upload to AWS table '{table_name}' complete.")
        logger.info(f"Successfully uploaded: {successful_uploads} items")
        if failed_uploads > 0:
            logger.warning(f"Failed to upload: {failed_uploads} items. Check logs for details.")
        else:
            logger.info("All items processed successfully for upload.")

    except Exception as e:
        logger.error(f"Error uploading DataFrame to AWS table '{table_name}': {e}", exc_info=True)

def main():
    parser = argparse.ArgumentParser(description="Migrate DynamoDB FBImageHash table from local to AWS.")
    parser.add_argument(
        "--local-endpoint",
        type=str,
        default=DEFAULT_LOCAL_DYNAMODB_ENDPOINT,
        help=f"Endpoint URL for local DynamoDB. Default: {DEFAULT_LOCAL_DYNAMODB_ENDPOINT}"
    )
    parser.add_argument(
        "--aws-region",
        type=str,
        default=DEFAULT_AWS_REGION,
        help=f"AWS region for the target DynamoDB table. Default: {DEFAULT_AWS_REGION}"
    )
    parser.add_argument(
        "--table-name",
        type=str,
        default=DEFAULT_TABLE_NAME,
        help=f"Name of the DynamoDB table (assumed same for local and AWS). Default: {DEFAULT_TABLE_NAME}"
    )
    parser.add_argument(
        "--log-level",
        type=str,
        default="INFO",
        choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
        help="Set the logging level. Default: INFO"
    )

    args = parser.parse_args()

    logger.setLevel(args.log_level.upper())

    logger.info("Starting FBImageHash table migration script...")
    logger.info(f"  Local DynamoDB endpoint: {args.local_endpoint}")
    logger.info(f"  Target AWS region: {args.aws_region}")
    logger.info(f"  Table name: {args.table_name}")
    logger.info(f"  Log level: {args.log_level.upper()}")

    # Step 1: Load local table to DataFrame
    df = load_local_table_to_df(args.table_name, args.local_endpoint)

    if df.empty:
        logger.error("Failed to load data from local table, or table was empty. Exiting.")
        sys.exit(1)

    # Step 2: Upload DataFrame to AWS
    upload_df_to_aws(df, args.table_name, args.aws_region)

    logger.info("Migration script finished.")

if __name__ == "__main__":
    main()
