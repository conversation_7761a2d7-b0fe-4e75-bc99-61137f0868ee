#!/usr/bin/env python3
import argparse
import os
import sys
from datetime import datetime, date
from typing import Optional, List, Dict, Any, Tuple
from collections import defaultdict
import json # Added for potential config loading if needed

# Add project root to sys.path to allow importing src modules
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

try:
    from rich.console import Console
    from rich.table import Table
    from rich.prompt import Prompt
    from src.lib.pacer_manager import PacerManager
    from src.lib.config import load_config
except ImportError as e:
    print(f"Error importing necessary libraries. Make sure rich and project structure are correct: {e}")
    sys.exit(1)

# --- Configuration ---
# Load configuration using the provided function
try:
    config = load_config('01/01/70') # Using the specified date string
    print("Configuration loaded successfully.")
except Exception as e:
    print(f"Error loading configuration: {e}")
    sys.exit(1)


MDL_NUM = '2873'  # AFFF MDL Number

def parse_arguments():
    """Parses command-line arguments."""
    parser = argparse.ArgumentParser(description="Calculate and analyze AFFF plaintiff numbers for a given date range.")
    parser.add_argument("--start-date", required=True, help="Start date in YYYYMMDD format.")
    parser.add_argument("--end-date", default=None, help="End date in YYYYMMDD format (optional, defaults to today).")
    parser.add_argument("--local", action="store_true", help="Use local DynamoDB instance.")
    parser.add_argument("--port", type=int, default=8000, help="Port for local DynamoDB instance (default: 8000).")
    return parser.parse_args()

def format_date(date_str: str) -> Optional[str]:
    """Validate and format date string."""
    try:
        return datetime.strptime(date_str, '%Y%m%d').strftime('%Y%m%d')
    except ValueError:
        return None

def process_dockets(items: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]], List[Dict[str, Any]]]:
    """
    Processes the raw docket items:
    1. Converts TransferredIn to boolean.
    2. Identifies and separates transferred-in duplicates.
    3. Normalizes NumPlaintiffs in the filtered list.

    Returns:
        Tuple: (initial_items_processed, filtered_items, dropped_items)
    """
    initial_items_processed = []
    potential_transfers = {} # Key: (TransferorCourtId, TransferorDocketNum), Value: item index in initial_items_processed
    original_dockets = {}    # Key: (CourtId, DocketNum), Value: item index in initial_items_processed
    dropped_indices = set()
    dropped_items = []

    # First pass: Process TransferredIn, normalize NumPlaintiffs, calculate Int version, and index items
    for index, item in enumerate(items):
        processed_item = item.copy() # Avoid modifying original list directly yet

        # Convert TransferredIn
        transferred_in_val = processed_item.get('TransferredIn')
        processed_item['TransferredIn'] = bool(transferred_in_val and str(transferred_in_val) == '1')

        # Normalize NumPlaintiffs ('0' or None becomes '1') for initial calculation
        num_plaintiffs = processed_item.get('NumPlaintiffs')
        if num_plaintiffs is None or str(num_plaintiffs) == '0':
            processed_item['NumPlaintiffs'] = '1' # Normalize for display consistency if needed

        # Calculate NumPlaintiffsInt for summation - DO THIS FOR ALL ITEMS INITIALLY
        try:
            # Use the potentially normalized value
            processed_item['NumPlaintiffsInt'] = int(processed_item.get('NumPlaintiffs', 1))
        except (ValueError, TypeError):
             processed_item['NumPlaintiffsInt'] = 1 # Fallback

        initial_items_processed.append(processed_item)

        # Index potential transfers and original dockets
        transferor_court_id = processed_item.get('TransferorCourtId')
        transferor_docket_num = processed_item.get('TransferorDocketNum')
        court_id = processed_item.get('CourtId')
        docket_num = processed_item.get('DocketNum')

        if transferor_court_id and transferor_docket_num:
            potential_transfers[(transferor_court_id, transferor_docket_num)] = index

        if court_id and docket_num:
            original_dockets[(court_id, docket_num)] = index

    # Second pass: Identify items to drop and populate dropped_items list
    # Note: dropped_items will inherit NumPlaintiffsInt calculated in the first pass
    for transfer_key, transfer_index in potential_transfers.items():
        if transfer_key in original_dockets:
            original_index = original_dockets[transfer_key]
            # Check if already marked to avoid duplicates in dropped_items if logic allows
            if original_index not in dropped_indices:
                dropped_indices.add(original_index)
                # Append the item as it was processed in the first pass (including NumPlaintiffsInt)
                dropped_items.append(initial_items_processed[original_index])
                # print(f"DEBUG: Marking original index {original_index} for drop (matches transfer {transfer_key})") # Debug

    # Third pass: Create filtered list. NumPlaintiffsInt was already calculated in the first pass.
    filtered_items = []
    for index, item in enumerate(initial_items_processed):
        if index not in dropped_indices:
            # Item already has 'NumPlaintiffs' potentially normalized and 'NumPlaintiffsInt' calculated
            filtered_items.append(item)

    # Sort lists for consistent viewing (optional)
    initial_items_processed.sort(key=lambda x: (x.get('FilingDate', ''), x.get('CourtId', ''), x.get('DocketNum', '')))
    filtered_items.sort(key=lambda x: (x.get('FilingDate', ''), x.get('CourtId', ''), x.get('DocketNum', '')))
    dropped_items.sort(key=lambda x: (x.get('FilingDate', ''), x.get('CourtId', ''), x.get('DocketNum', '')))

    return initial_items_processed, filtered_items, dropped_items


def calculate_and_display_sum(console: Console, items: List[Dict[str, Any]], description: str):
    """Calculates and displays the sum of NumPlaintiffsInt for a list of items."""
    if not items:
        # No sum to display if no items
        return
    total_plaintiffs = sum(item.get('NumPlaintiffsInt', 0) for item in items)
    console.print(f"\n[bold green]Total Plaintiffs ({description}): {total_plaintiffs}[/bold green]\n")


def display_table(console: Console, title: str, items: List[Dict[str, Any]], columns: Optional[List[str]] = None):
    """Displays a list of dictionaries as a table using Rich."""
    if not items:
        console.print(f"[bold yellow]No items to display for '{title}'.[/bold yellow]")
        return

    table = Table(title=title, show_header=True, header_style="bold magenta")

    # Determine columns
    if columns:
        display_columns = columns
    else:
        # Use keys from the first item as default columns if not specified
        display_columns = list(items[0].keys())

    for col in display_columns:
        table.add_column(col)

    for item in items:
        row_data = []
        for col in display_columns:
            value = item.get(col)
            # Convert non-string types for display
            if isinstance(value, (dict, list)):
                row_data.append(json.dumps(value, indent=2))
            elif isinstance(value, bool):
                 row_data.append("[green]True[/green]" if value else "[red]False[/red]")
            elif value is None:
                 row_data.append("[dim]None[/dim]")
            else:
                 row_data.append(str(value))
        table.add_row(*row_data)

    console.print(table)

def display_plaintiffs_over_time(console: Console, items: List[Dict[str, Any]]):
    """Displays FilingDate and NumPlaintiffs."""
    if not items:
        console.print("[bold yellow]No filtered items to display plaintiff counts for.[/bold yellow]")
        return

    table = Table(title="Number of Plaintiffs by Filing Date", show_header=True, header_style="bold magenta")
    table.add_column("FilingDate", style="dim")
    table.add_column("NumPlaintiffs", justify="right")

    # Sort by date for clarity
    items.sort(key=lambda x: x.get('FilingDate', ''))

    for item in items:
        table.add_row(
            item.get('FilingDate', '[dim]N/A[/dim]'),
            str(item.get('NumPlaintiffsInt', '[dim]N/A[/dim]')) # Use the integer version
        )
    console.print(table)

def display_plaintiff_sum(console: Console, items: List[Dict[str, Any]], start_date: str, end_date: str):
    """Calculates and displays the sum of NumPlaintiffs for the filtered set within the date range."""
    if not items:
        console.print(f"[bold yellow]No filtered items to sum plaintiffs for date range {start_date} - {end_date}.[/bold yellow]")
        return

    total_plaintiffs = sum(item.get('NumPlaintiffsInt', 0) for item in items) # Use the integer version
    console.print(f"\n[bold green]Total Plaintiffs (Filtered) for Date Range {start_date} - {end_date}: {total_plaintiffs}[/bold green]\n")


def main():
    args = parse_arguments()
    console = Console()

    start_date_fmt = format_date(args.start_date)
    if not start_date_fmt:
        console.print(f"[bold red]Error: Invalid start date format. Please use YYYYMMDD.[/bold red]")
        sys.exit(1)

    if args.end_date:
        end_date_fmt = format_date(args.end_date)
        if not end_date_fmt:
            console.print(f"[bold red]Error: Invalid end date format. Please use YYYYMMDD.[/bold red]")
            sys.exit(1)
    else:
        end_date_fmt = date.today().strftime('%Y%m%d')
        console.print(f"[dim]End date not provided, using today: {end_date_fmt}[/dim]")

    if start_date_fmt > end_date_fmt:
        console.print(f"[bold red]Error: Start date ({start_date_fmt}) cannot be after end date ({end_date_fmt}).[/bold red]")
        sys.exit(1)

    console.print(f"Attempting to initialize PacerManager (Local: {args.local}, Port: {args.port})...")
    try:
        # Pass loaded config and local settings to PacerManager
        pacer_manager = PacerManager(config=config, use_local=False, local_port=args.port)
        console.print("[green]PacerManager initialized successfully.[/green]")
    except Exception as e:
        console.print(f"[bold red]Error initializing PacerManager: {e}[/bold red]")
        console.print_exception(show_locals=True) # More detailed error
        sys.exit(1)

    console.print(f"Fetching AFFF ({MDL_NUM}) dockets from {start_date_fmt} to {end_date_fmt}...")
    try:
        raw_items = pacer_manager.get_mdl_dockets_by_date_range(MDL_NUM, start_date_fmt, end_date_fmt)
        console.print(f"[green]Fetched {len(raw_items)} raw items.[/green]")
    except Exception as e:
        console.print(f"[bold red]Error fetching dockets: {e}[/bold red]")
        console.print_exception(show_locals=True)
        sys.exit(1)

    if not raw_items:
        console.print("[yellow]No dockets found for the specified criteria.[/yellow]")
        sys.exit(0)

    console.print("Processing dockets (handling transfers, normalizing plaintiffs)...")
    initial_items, filtered_items, dropped_items = process_dockets(raw_items)
    console.print(f"[green]Processing complete. Initial: {len(initial_items)}, Filtered: {len(filtered_items)}, Dropped: {len(dropped_items)}[/green]")

    # Define columns for display consistency
    display_cols = [
        "FilingDate", "DocketNum", "CourtId", "NumPlaintiffs", "TransferredIn",
        "TransferorCourtId", "TransferorDocketNum", "Plaintiff", "Defendant", "LawFirm", "AddedOn"
    ]

    while True:
        console.print("\n[bold cyan]Choose an option:[/bold cyan]")
        console.print("1. View initial items (raw fetched, TransferredIn processed)")
        console.print("2. View filtered items (transfers removed, NumPlaintiffs normalized)")
        console.print("3. View dropped items (identified as transfers)")
        console.print("4. View num_plaintiffs over date range (from filtered items)")
        console.print("5. View sum of num plaintiffs over date range (from filtered items)")
        console.print("q. Quit")

        choice = Prompt.ask("Enter your choice", choices=["1", "2", "3", "4", "5", "q"], default="q")

        if choice == '1':
            display_table(console, f"Initial Items ({len(initial_items)})", initial_items, columns=display_cols)
            calculate_and_display_sum(console, initial_items, "Initial")
        elif choice == '2':
            display_table(console, f"Filtered Items ({len(filtered_items)})", filtered_items, columns=display_cols)
            calculate_and_display_sum(console, filtered_items, "Filtered")
        elif choice == '3':
            display_table(console, f"Dropped Items ({len(dropped_items)})", dropped_items, columns=display_cols)
            calculate_and_display_sum(console, dropped_items, "Dropped")
        elif choice == '4':
            display_plaintiffs_over_time(console, filtered_items)
            # Optionally add sum here too? For now, keep it separate.
        elif choice == '5':
            # Pass date range to the summary function
            display_plaintiff_sum(console, filtered_items, start_date_fmt, end_date_fmt)
        elif choice == 'q':
            console.print("[bold blue]Exiting.[/bold blue]")
            break

if __name__ == "__main__":
    main()