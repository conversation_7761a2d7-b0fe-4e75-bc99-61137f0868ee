# OpenRouter configuration
api-key:
- openrouter=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
- gemini=AIzaSyCakfOTpzawR4suql4XIkl07-yFSWrKmi8
- openai=********************************************************************************************************************************************************************
- deepseek=***********************************
architect: true
model: gemini/gemini-2.5-pro-preview-03-25
editor-model: gemini/gemini-2.5-flash-preview-04-17
weak-model: openrouter/deepseek/deepseek-chat
multiline: true
watch-files: true
map-tokens: 8192
map-refresh: auto
dark-mode: false
pretty: true
cache-prompts: true
read: [CONVENTIONS.md]
