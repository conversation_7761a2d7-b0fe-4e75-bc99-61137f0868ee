#!/usr/bin/env python
"""
Extract semantic patterns from tight clusters to generate rule suggestions
"""

import argparse
import json
import logging
import os
import re
import sys
from collections import Counter, defaultdict
from pathlib import Path
from typing import Dict, List, Tuple, Set

import numpy as np
import pandas as pd
import yaml
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ClusterPatternExtractor:
    """Extract semantic patterns from clusters to suggest rules"""
    
    def __init__(self, csv_path: str, min_cluster_size: int = 10):
        self.csv_path = Path(csv_path)
        self.min_cluster_size = min_cluster_size
        self.df = self._load_csv()
        
    def _load_csv(self) -> pd.DataFrame:
        """Load the analyzed CSV with cluster information"""
        try:
            df = pd.read_csv(self.csv_path)
            logger.info(f"Loaded {len(df)} rows from {self.csv_path}")
            return df
        except Exception as e:
            logger.error(f"Failed to load CSV: {e}")
            return pd.DataFrame()
            
    def extract_patterns_from_clusters(self):
        """Extract semantic patterns from each cluster"""
        
        # Check for required columns
        if 'rule_subcluster_id' not in self.df.columns:
            logger.error("No 'rule_subcluster_id' column found. Run analyze_deduplicated_ads.py first.")
            return
            
        if 'raw_text_combined' not in self.df.columns:
            logger.error("No 'raw_text_combined' column found.")
            return
            
        # Group by cluster
        cluster_groups = self.df[self.df['rule_subcluster_id'].notna()].groupby('rule_subcluster_id')
        
        cluster_patterns = {}
        
        for cluster_id, cluster_df in cluster_groups:
            if len(cluster_df) < self.min_cluster_size:
                continue
                
            logger.info(f"\nAnalyzing cluster: {cluster_id} ({len(cluster_df)} items)")
            
            # Extract patterns for this cluster
            patterns = self._analyze_cluster_patterns(cluster_df, cluster_id)
            if patterns:
                cluster_patterns[cluster_id] = patterns
                
        # Save results
        self._save_pattern_analysis(cluster_patterns)
        
    def _analyze_cluster_patterns(self, cluster_df: pd.DataFrame, cluster_id: str) -> Dict:
        """Analyze patterns within a single cluster"""
        
        texts = cluster_df['raw_text_combined'].dropna().tolist()
        if not texts:
            return {}
            
        # 1. Extract common n-grams
        ngrams = self._extract_ngrams(texts, n_range=(2, 4))
        
        # 2. Find key phrases using TF-IDF
        key_phrases = self._extract_key_phrases(texts)
        
        # 3. Extract common patterns (phone numbers, URLs, CTAs)
        patterns = self._extract_regex_patterns(texts)
        
        # 4. Find distinguishing terms compared to other clusters
        distinguishing_terms = self._find_distinguishing_terms(texts, cluster_id)
        
        # 5. Extract entities if available
        entities = self._extract_common_entities(cluster_df)
        
        return {
            'cluster_size': len(cluster_df),
            'campaign': cluster_df['campaign'].iloc[0] if 'campaign' in cluster_df.columns else 'Unknown',
            'top_ngrams': ngrams[:20],
            'key_phrases': key_phrases[:15],
            'common_patterns': patterns,
            'distinguishing_terms': distinguishing_terms[:10],
            'common_entities': entities,
            'sample_texts': texts[:3]  # Include a few examples
        }
        
    def _extract_ngrams(self, texts: List[str], n_range: Tuple[int, int] = (2, 4)) -> List[Tuple[str, int]]:
        """Extract most common n-grams from texts"""
        
        # Combine all texts
        combined_text = ' '.join(texts).lower()
        
        # Remove common stop words and clean
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 
                     'of', 'with', 'by', 'from', 'about', 'as', 'is', 'was', 'are', 'were',
                     'been', 'be', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would',
                     'should', 'could', 'may', 'might', 'must', 'can', 'this', 'that', 'these',
                     'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'them', 'their'}
        
        words = re.findall(r'\b[a-z]+\b', combined_text)
        filtered_words = [w for w in words if w not in stop_words and len(w) > 2]
        
        # Generate n-grams
        all_ngrams = []
        for n in range(n_range[0], n_range[1] + 1):
            for i in range(len(filtered_words) - n + 1):
                ngram = ' '.join(filtered_words[i:i+n])
                all_ngrams.append(ngram)
                
        # Count and return most common
        ngram_counts = Counter(all_ngrams)
        return ngram_counts.most_common(50)
        
    def _extract_key_phrases(self, texts: List[str], max_features: int = 100) -> List[Tuple[str, float]]:
        """Extract key phrases using TF-IDF"""
        
        try:
            # Use TF-IDF to find important phrases
            vectorizer = TfidfVectorizer(
                ngram_range=(2, 4),
                max_features=max_features,
                stop_words='english',
                min_df=2
            )
            
            tfidf_matrix = vectorizer.fit_transform(texts)
            feature_names = vectorizer.get_feature_names_out()
            
            # Get average TF-IDF scores
            avg_scores = tfidf_matrix.mean(axis=0).A1
            
            # Sort by score
            phrase_scores = [(feature_names[i], avg_scores[i]) 
                           for i in avg_scores.argsort()[::-1]]
            
            return phrase_scores
            
        except Exception as e:
            logger.warning(f"TF-IDF extraction failed: {e}")
            return []
            
    def _extract_regex_patterns(self, texts: List[str]) -> Dict[str, List[str]]:
        """Extract common patterns like phone numbers, URLs, etc."""
        
        patterns = {
            'phone_numbers': [],
            'urls': [],
            'cta_phrases': [],
            'compensation_mentions': [],
            'time_limits': []
        }
        
        combined_text = ' '.join(texts)
        
        # Phone numbers
        phone_pattern = r'\b(?:\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})\b'
        phones = re.findall(phone_pattern, combined_text)
        patterns['phone_numbers'] = list(set([''.join(p) for p in phones]))[:5]
        
        # URLs
        url_pattern = r'https?://[^\s<>"{}|\\^`\[\]]+|www\.[^\s<>"{}|\\^`\[\]]+'
        urls = re.findall(url_pattern, combined_text, re.IGNORECASE)
        patterns['urls'] = list(set(urls))[:5]
        
        # Call-to-action phrases
        cta_pattern = r'(?:call|click|tap|visit|contact|learn more|apply now|get started|find out|check)\s+(?:now|today|us|here)'
        ctas = re.findall(cta_pattern, combined_text, re.IGNORECASE)
        patterns['cta_phrases'] = list(set(ctas))[:10]
        
        # Compensation mentions
        comp_pattern = r'(?:\$[\d,]+|\d+\s*(?:million|billion|thousand)|\bcompensation\b|\bsettlement\b|\bpayout\b)'
        comps = re.findall(comp_pattern, combined_text, re.IGNORECASE)
        patterns['compensation_mentions'] = list(set(comps))[:10]
        
        # Time limits
        time_pattern = r'(?:deadline|expires?|limited time|act now|before|until)\s*[^.!?]{0,50}'
        times = re.findall(time_pattern, combined_text, re.IGNORECASE)
        patterns['time_limits'] = list(set(times))[:10]
        
        return patterns
        
    def _find_distinguishing_terms(self, cluster_texts: List[str], cluster_id: str) -> List[Tuple[str, float]]:
        """Find terms that distinguish this cluster from others"""
        
        # Get all other texts
        other_mask = self.df['rule_subcluster_id'] != cluster_id
        other_texts = self.df[other_mask]['raw_text_combined'].dropna().tolist()
        
        if not other_texts:
            return []
            
        try:
            # Create TF-IDF for this cluster vs others
            vectorizer = TfidfVectorizer(
                ngram_range=(1, 3),
                max_features=200,
                stop_words='english'
            )
            
            # Combine texts
            all_texts = cluster_texts + other_texts
            labels = [1] * len(cluster_texts) + [0] * len(other_texts)
            
            tfidf_matrix = vectorizer.fit_transform(all_texts)
            feature_names = vectorizer.get_feature_names_out()
            
            # Calculate mean TF-IDF for cluster vs others
            cluster_mean = tfidf_matrix[:len(cluster_texts)].mean(axis=0).A1
            other_mean = tfidf_matrix[len(cluster_texts):].mean(axis=0).A1
            
            # Find most distinguishing terms (high in cluster, low in others)
            diff_scores = cluster_mean - other_mean
            top_indices = diff_scores.argsort()[::-1][:50]
            
            distinguishing = [(feature_names[i], diff_scores[i]) for i in top_indices]
            
            return distinguishing
            
        except Exception as e:
            logger.warning(f"Distinguishing terms extraction failed: {e}")
            return []
            
    def _extract_common_entities(self, cluster_df: pd.DataFrame) -> Dict[str, List[str]]:
        """Extract common entities from the cluster"""
        
        entities = defaultdict(list)
        
        # Check for entity columns
        if 'detail_extracted_entities' in cluster_df.columns:
            for _, row in cluster_df.iterrows():
                try:
                    if pd.notna(row['detail_extracted_entities']):
                        # Parse the entities (assuming they're stored as string representation of dict)
                        ent_data = eval(row['detail_extracted_entities'])
                        for ent_type, ent_list in ent_data.items():
                            entities[ent_type].extend(ent_list)
                except:
                    continue
                    
        # Get most common entities
        common_entities = {}
        for ent_type, ent_list in entities.items():
            if ent_list:
                counts = Counter(ent_list)
                common_entities[ent_type] = counts.most_common(10)
                
        return common_entities
        
    def _save_pattern_analysis(self, cluster_patterns: Dict):
        """Save the pattern analysis results"""
        
        # Create output directory
        output_dir = Path('cluster_pattern_analysis')
        output_dir.mkdir(exist_ok=True)
        
        # Save detailed JSON report
        output_file = output_dir / 'cluster_patterns.json'
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(cluster_patterns, f, indent=2, ensure_ascii=False)
        logger.info(f"Saved detailed pattern analysis to {output_file}")
        
        # Generate rule suggestions
        rule_suggestions = self._generate_rule_suggestions(cluster_patterns)
        
        # Save rule suggestions
        rules_file = output_dir / 'suggested_rules.json'
        with open(rules_file, 'w', encoding='utf-8') as f:
            json.dump(rule_suggestions, f, indent=2, ensure_ascii=False)
        logger.info(f"Saved rule suggestions to {rules_file}")
        
        # Generate human-readable report
        self._generate_readable_report(cluster_patterns, output_dir)
        
    def _generate_rule_suggestions(self, cluster_patterns: Dict) -> List[Dict]:
        """Generate rule suggestions based on patterns"""
        
        suggestions = []
        
        for cluster_id, patterns in cluster_patterns.items():
            # Skip noise clusters
            if 'NOISE' in cluster_id:
                continue
                
            campaign = patterns.get('campaign', 'Unknown')
            
            # Extract trigger terms from top n-grams and key phrases
            trigger_terms = set()
            
            # Add high-frequency n-grams
            for ngram, count in patterns.get('top_ngrams', [])[:10]:
                if count > 3:  # Appears in multiple ads
                    trigger_terms.add(ngram)
                    
            # Add high-scoring key phrases
            for phrase, score in patterns.get('key_phrases', [])[:10]:
                if score > 0.1:  # Significant TF-IDF score
                    trigger_terms.add(phrase)
                    
            # Add distinguishing terms
            for term, score in patterns.get('distinguishing_terms', [])[:5]:
                if score > 0.1:
                    trigger_terms.add(term)
                    
            # Extract exclude terms (terms that appear in other clusters but not this one)
            exclude_terms = set()
            # This would need more sophisticated analysis
            
            if trigger_terms:
                suggestion = {
                    'cluster_id': cluster_id,
                    'campaign': campaign,
                    'cluster_size': patterns['cluster_size'],
                    'suggested_triggers': list(trigger_terms),
                    'suggested_excludes': list(exclude_terms),
                    'common_patterns': patterns.get('common_patterns', {}),
                    'confidence': self._calculate_rule_confidence(patterns)
                }
                suggestions.append(suggestion)
                
        return suggestions
        
    def _calculate_rule_confidence(self, patterns: Dict) -> float:
        """Calculate confidence score for rule suggestion"""
        
        confidence = 0.5  # Base confidence
        
        # Increase confidence based on cluster size
        size = patterns.get('cluster_size', 0)
        if size > 50:
            confidence += 0.2
        elif size > 20:
            confidence += 0.1
            
        # Increase confidence if strong distinguishing terms
        distinguishing = patterns.get('distinguishing_terms', [])
        if distinguishing and distinguishing[0][1] > 0.3:
            confidence += 0.2
            
        return min(confidence, 0.95)
        
    def _generate_readable_report(self, cluster_patterns: Dict, output_dir: Path):
        """Generate a human-readable report"""
        
        report_file = output_dir / 'pattern_analysis_report.txt'
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("CLUSTER PATTERN ANALYSIS REPORT\n")
            f.write("=" * 80 + "\n\n")
            
            for cluster_id, patterns in cluster_patterns.items():
                f.write(f"\n{'='*60}\n")
                f.write(f"CLUSTER: {cluster_id}\n")
                f.write(f"Campaign: {patterns.get('campaign', 'Unknown')}\n")
                f.write(f"Size: {patterns.get('cluster_size', 0)} ads\n")
                f.write(f"{'='*60}\n\n")
                
                # Top phrases
                f.write("TOP PHRASES (by frequency):\n")
                for phrase, count in patterns.get('top_ngrams', [])[:15]:
                    f.write(f"  - '{phrase}' (appears {count} times)\n")
                f.write("\n")
                
                # Key distinguishing terms
                f.write("DISTINGUISHING TERMS (unique to this cluster):\n")
                for term, score in patterns.get('distinguishing_terms', [])[:10]:
                    f.write(f"  - '{term}' (score: {score:.3f})\n")
                f.write("\n")
                
                # Common patterns
                f.write("COMMON PATTERNS:\n")
                for pattern_type, items in patterns.get('common_patterns', {}).items():
                    if items:
                        f.write(f"  {pattern_type}:\n")
                        for item in items[:5]:
                            f.write(f"    - {item}\n")
                f.write("\n")
                
                # Common entities
                if patterns.get('common_entities'):
                    f.write("COMMON ENTITIES:\n")
                    for ent_type, entities in patterns['common_entities'].items():
                        f.write(f"  {ent_type}:\n")
                        for entity, count in entities[:5]:
                            f.write(f"    - {entity} ({count} occurrences)\n")
                    f.write("\n")
                
                # Sample texts
                f.write("SAMPLE TEXTS:\n")
                for i, text in enumerate(patterns.get('sample_texts', [])[:2]):
                    f.write(f"\nExample {i+1}:\n")
                    f.write(f"{text[:300]}...\n")
                
                f.write("\n" + "-"*60 + "\n")
                
        logger.info(f"Saved human-readable report to {report_file}")


def main():
    parser = argparse.ArgumentParser(description='Extract semantic patterns from clusters')
    parser.add_argument('--csv', type=str, required=True,
                       help='Path to CSV file with cluster analysis (output from analyze_deduplicated_ads.py)')
    parser.add_argument('--min-cluster-size', type=int, default=10,
                       help='Minimum cluster size to analyze (default: 10)')
    
    args = parser.parse_args()
    
    # Validate file exists
    if not Path(args.csv).exists():
        logger.error(f"CSV file not found: {args.csv}")
        sys.exit(1)
        
    # Extract patterns
    extractor = ClusterPatternExtractor(
        csv_path=args.csv,
        min_cluster_size=args.min_cluster_size
    )
    
    extractor.extract_patterns_from_clusters()
    
    logger.info("\n=== Pattern Extraction Complete ===")
    logger.info("Check the 'cluster_pattern_analysis' directory for results:")
    logger.info("  - cluster_patterns.json: Detailed pattern data")
    logger.info("  - suggested_rules.json: Rule suggestions for campaigns")
    logger.info("  - pattern_analysis_report.txt: Human-readable report")


if __name__ == '__main__':
    main()