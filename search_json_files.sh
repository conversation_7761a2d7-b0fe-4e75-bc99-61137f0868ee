#!/bin/bash

# Check for correct number of arguments
if [ "$#" -ne 2 ]; then
    echo "Usage: $0 FIELD VALUE"
    exit 1
fi

FIELD="$1"
VALUE="$2"
BASE_DIR="/Users/<USER>/PycharmProjects/mt_competitive_analysis/data"

# Find all JSON files under directories matching pattern: {BASE_DIR}/{YYYYMMDD}/dockets/...
find "$BASE_DIR" -type f -name "*.json" | while read -r file; do
    # Use jq to check for the field with the specified value; ignore errors for missing fields
    if jq -e ".${FIELD} == \"${VALUE}\"" "$file" >/dev/null 2>&1; then
        # Print path relative to the current working directory
        rel_path="${file/#$PWD\//}"
        echo "$rel_path"
    fi
done
