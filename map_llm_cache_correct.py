#!/usr/bin/env python3
"""
Map LLM cache using EXACT .bak logic to match existing cache keys.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

import pickle
import hashlib
import json
import boto3
from typing import Dict, Any, List, Tuple
from tqdm import tqdm

def load_all_ads():
    """Load ALL ads from FBAdArchive."""
    print("Loading ALL ads from FBAdArchive...")
    
    dynamodb = boto3.resource(
        'dynamodb',
        endpoint_url="http://localhost:8000",
        region_name='us-west-2',
        aws_access_key_id='fakeMyKeyId',
        aws_secret_access_key='fakeSecretAccessKey'
    )
    
    table = dynamodb.Table("FBAdArchive")
    
    all_ads = []
    scan_kwargs = {}
    
    while True:
        response = table.scan(**scan_kwargs)
        batch = response['Items']
        all_ads.extend(batch)
        
        if len(all_ads) % 10000 == 0:
            print(f"  Loaded {len(all_ads):,} ads...")
        
        if 'LastEvaluatedKey' not in response:
            break
        scan_kwargs['ExclusiveStartKey'] = response['LastEvaluatedKey']
    
    print(f"Total ads loaded: {len(all_ads):,}")
    return all_ads

def load_llm_cache():
    """Load existing LLM cache."""
    cache_file = "llm_response_cache.pkl"
    
    if not os.path.exists(cache_file):
        print(f"❌ LLM cache file not found: {cache_file}")
        return {}
    
    try:
        with open(cache_file, 'rb') as f:
            llm_cache = pickle.load(f)
        print(f"✅ Loaded LLM cache: {len(llm_cache):,} entries")
        return llm_cache
    except Exception as e:
        print(f"❌ Error loading LLM cache: {e}")
        return {}

def prepare_ad_text_bak_logic(ad_data: Dict[str, Any]) -> str:
    """
    EXACT text processing logic from .bak file.
    This matches the _prepare_ad_text_for_processing method.
    """
    # From .bak: text_processing_fields are lowercased YAML fields
    text_processing_fields = ["title", "body", "summary", "linkdescription", "pagename"]
    
    # From .bak: invalid summary strings 
    invalid_summary_strings = {s.lower() for s in ['NA', 'SKIPPED', '', 'None', 'null', "Summary generation failed"]}
    
    raw_parts = []
    
    # From .bak: create lowercase key mapping
    ad_data_lower_keys = {k.lower(): v for k, v in ad_data.items()}
    
    for field_name_config in text_processing_fields:  # field_name_config is already lowercased
        value = ad_data_lower_keys.get(field_name_config)
        
        if value is None:
            continue
        
        value_str = str(value).strip()
        
        if not value_str:
            continue
        
        # Special summary validation from .bak
        if field_name_config == 'summary':
            normalized_summary_content = value_str.lower()
            if normalized_summary_content in invalid_summary_strings:
                continue
        
        raw_parts.append(value_str)
    
    # From .bak: use " | " to join
    raw_combined_text = " | ".join(raw_parts)
    return raw_combined_text

def prepare_ad_text_new_logic(ad_data: Dict[str, Any]) -> str:
    """
    New YAML field logic for comparison.
    """
    YAML_FIELDS = ["Title", "Body", "Summary", "LinkDescription", "PageName"]
    
    text_parts = []
    for field_name in YAML_FIELDS:
        value = ad_data.get(field_name)
        if value is None:
            continue
        value_str = str(value).strip()
        if not value_str:
            continue
        text_parts.append(value_str)
    
    # New logic: use " " to join
    return " ".join(text_parts)

def get_llm_cache_key_bak_logic(text: str, candidate_labels: List[str], model_path: str = "deepseek-r1:8b-0528-qwen3-q8_0", backend: str = "ollama") -> str:
    """
    EXACT LLM cache key generation from .bak file.
    From _get_cache_key method in LLMClassifier.
    """
    max_text_len = 1000
    text_to_classify = text[:max_text_len] if len(text) > max_text_len else text
    
    key_data = {
        'text': text_to_classify,  # Limit text length for consistent keys (from .bak: text[:1000])
        'labels': sorted(candidate_labels),  # Sort for consistency
        'context': None,  # Default context
        'enhanced': False,  # Default enhanced
        'model': model_path,
        'backend': backend
    }
    return hashlib.md5(json.dumps(key_data, sort_keys=True).encode()).hexdigest()

def map_llm_cache_entries(ads: List[Dict[str, Any]], old_llm_cache: Dict[str, Any]) -> Tuple[Dict[str, Any], Dict[str, int]]:
    """Map LLM cache entries from old .bak format to new YAML format."""
    print(f"Mapping LLM cache entries for {len(ads):,} ads...")
    
    new_llm_cache = {}
    stats = {
        'total_ads': len(ads),
        'ads_with_text': 0,
        'old_keys_found': 0,
        'new_entries_created': 0,
        'no_text_ads': 0,
        'no_old_cache_entry': 0,
        'text_matches': 0,  # When old and new text are identical
        'text_differences': 0  # When old and new text differ
    }
    
    # Load campaign labels (from campaign_config.json) - extract LitigationName fields
    try:
        with open('src/config/fb_ad_categorizer/campaign_config.json', 'r') as f:
            campaign_config = json.load(f)
        # Extract LitigationName from each campaign (this matches .bak logic)
        candidate_labels = [campaign['LitigationName'] for campaign in campaign_config if 'LitigationName' in campaign]
        candidate_labels.append("Other")  # Add "Other" category
        print(f"  Using {len(candidate_labels)} campaign labels for cache key generation")
        print(f"  Labels: {candidate_labels[:5]}...")  # Show first 5
    except Exception as e:
        print(f"  Warning: Could not load campaign labels: {e}")
        candidate_labels = ["Other"]  # Default fallback
    
    for ad in tqdm(ads, desc="Mapping LLM cache"):
        # Extract text using both methods
        old_text = prepare_ad_text_bak_logic(ad)
        new_text = prepare_ad_text_new_logic(ad)
        
        if not new_text:
            stats['no_text_ads'] += 1
            continue
        
        stats['ads_with_text'] += 1
        
        # Track text differences
        if old_text == new_text:
            stats['text_matches'] += 1
        else:
            stats['text_differences'] += 1
        
        # Generate cache keys using EXACT .bak logic
        old_key = get_llm_cache_key_bak_logic(old_text, candidate_labels) if old_text else None
        new_key = get_llm_cache_key_bak_logic(new_text, candidate_labels)  # Use same logic but with new text
        
        # Check if old cache entry exists
        if old_key and old_key in old_llm_cache:
            # Copy old entry to new key
            new_llm_cache[new_key] = old_llm_cache[old_key]
            stats['old_keys_found'] += 1
            stats['new_entries_created'] += 1
        else:
            stats['no_old_cache_entry'] += 1
    
    return new_llm_cache, stats

def test_cache_key_generation(ads: List[Dict[str, Any]], llm_cache: Dict[str, Any], sample_size: int = 10):
    """Test cache key generation with sample ads to verify logic."""
    print(f"\nTesting cache key generation with {sample_size} ads...")
    
    # Load campaign labels - extract LitigationName fields
    try:
        with open('src/config/fb_ad_categorizer/campaign_config.json', 'r') as f:
            campaign_config = json.load(f)
        candidate_labels = [campaign['LitigationName'] for campaign in campaign_config if 'LitigationName' in campaign]
        candidate_labels.append("Other")  # Add "Other" category
    except:
        candidate_labels = ["Other"]
    
    test_ads = ads[:sample_size]
    matches_found = 0
    
    for i, ad in enumerate(test_ads):
        ad_id = ad.get('AdArchiveID', f'ad_{i}')
        old_text = prepare_ad_text_bak_logic(ad)
        
        if not old_text:
            print(f"  Ad {i+1} ({ad_id}): No text")
            continue
        
        # Generate cache key using EXACT .bak logic
        cache_key = get_llm_cache_key_bak_logic(old_text, candidate_labels)
        
        if cache_key in llm_cache:
            matches_found += 1
            value = llm_cache[cache_key]
            print(f"  ✅ Ad {i+1} ({ad_id}): MATCH found!")
            print(f"     Text: {old_text[:100]}...")
            print(f"     Key: {cache_key}")
            print(f"     Value: {value}")
        else:
            print(f"  ❌ Ad {i+1} ({ad_id}): No match")
            print(f"     Text: {old_text[:80]}...")
            print(f"     Key: {cache_key}")
    
    print(f"\nTest Results: {matches_found}/{len(test_ads)} ads have matching cache entries")
    return matches_found

def save_new_llm_cache(new_llm_cache: Dict[str, Any]):
    """Save the new LLM cache."""
    cache_file = "llm_response_cache_new.pkl"
    
    try:
        with open(cache_file, 'wb') as f:
            pickle.dump(new_llm_cache, f)
        print(f"✅ Saved new LLM cache: {cache_file} with {len(new_llm_cache):,} entries")
    except Exception as e:
        print(f"❌ Error saving LLM cache: {e}")

def update_yaml_config():
    """Update YAML config to use new LLM cache file."""
    config_path = "src/config/fb_ad_categorizer/hybrid_classifier_config.yml"
    
    try:
        with open(config_path, 'r') as f:
            content = f.read()
        
        # Update LLM cache file reference
        if 'cache_file: "llm_response_cache_new.pkl"' not in content:
            # Replace or add LLM cache file reference
            lines = content.split('\n')
            updated = False
            
            for i, line in enumerate(lines):
                if 'llm:' in line:
                    # Look for cache_file in the following lines
                    j = i + 1
                    while j < len(lines) and (lines[j].startswith('    ') or lines[j].strip() == ''):
                        if 'cache_file:' in lines[j]:
                            lines[j] = '    cache_file: "llm_response_cache_new.pkl"'
                            updated = True
                            break
                        j += 1
                    
                    if not updated:
                        # Add cache_file after llm section
                        lines.insert(j, '    cache_file: "llm_response_cache_new.pkl"')
                        updated = True
                    break
            
            if updated:
                with open(config_path, 'w') as f:
                    f.write('\n'.join(lines))
                print("✅ Updated YAML config")
        else:
            print("✅ YAML config already updated")
            
    except Exception as e:
        print(f"❌ Error updating YAML config: {e}")

def main():
    print("LLM CACHE MAPPING - EXACT .BAK LOGIC")
    print("=" * 60)
    
    # Step 1: Load data
    ads = load_all_ads()
    old_llm_cache = load_llm_cache()
    
    if not old_llm_cache:
        print("❌ No LLM cache to map")
        return
    
    # Step 2: Test cache key generation with sample
    matches = test_cache_key_generation(ads, old_llm_cache, sample_size=20)
    
    if matches == 0:
        print("\n❌ No matches found in test - check campaign_config.json or cache key logic")
        return
    
    print(f"\n✅ Found {matches} matches in test - proceeding with full mapping")
    
    # Step 3: Perform full mapping
    new_llm_cache, stats = map_llm_cache_entries(ads, old_llm_cache)
    
    # Step 4: Save new cache
    save_new_llm_cache(new_llm_cache)
    
    # Step 5: Update YAML config
    update_yaml_config()
    
    # Final summary
    print(f"\n{'='*60}")
    print("LLM CACHE MAPPING COMPLETED")
    print(f"{'='*60}")
    
    coverage_rate = (stats['old_keys_found'] / stats['ads_with_text'] * 100) if stats['ads_with_text'] > 0 else 0
    
    print(f"Original LLM cache: {len(old_llm_cache):,} entries")
    print(f"New LLM cache: {len(new_llm_cache):,} entries")
    print(f"Coverage rate: {coverage_rate:.2f}%")
    
    print(f"\nText Processing Analysis:")
    print(f"  Ads with identical old/new text: {stats['text_matches']:,}")
    print(f"  Ads with different old/new text: {stats['text_differences']:,}")
    
    if coverage_rate >= 10:
        print(f"\n✅ Good coverage rate! {coverage_rate:.1f}% of ads have LLM cache entries.")
    else:
        print(f"\n⚠️  Low coverage rate: {coverage_rate:.1f}%")
        print("   This may be expected if LLM was only used for specific cases.")

if __name__ == "__main__":
    main()