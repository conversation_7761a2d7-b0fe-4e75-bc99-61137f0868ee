{"analysis_date": "2025-05-31T21:22:05.915860", "reference_fields": ["AddedDate", "AddedDateIso", "AddedOn", "Allegations", "AssignedTo", "Attorney", "AttorneysGpt", "BaseFilename", "Cause", "CauseFromReport", "CourtId", "DateFiled", "Defendant", "DocketNum", "FilingDate", "Flags", "InitialFilingDate", "IsRemoval", "IsTransferred", "Juris<PERSON>", "JuryDemand", "LawFirm", "LawFirms", "MdlCat", "MdlNum", "NewFilename", "Nos", "NosFromReport", "Office", "OriginalFilename", "PendingCto", "Plaintiff", "PlaintiffsGpt", "ProcessingNotes", "S3Html", "S3Link", "SourcePage", "Summary", "Title", "TransferorCourtId", "TransferorCourtName", "TransferorDocketLawFirm", "TransferorDocketNum", "TransferredFrom", "TransferredIn", "Versus"], "summary": {"total_date_ranges": 26, "reference_fields_count": 46}, "date_range_analysis": {"2024-05": {"item_count": 2413, "missing_fields": ["AddedDateIso", "CauseFromReport", "InitialFilingDate", "LawFirms", "MdlCat", "NosFromReport", "PlaintiffsGpt", "ProcessingNotes", "SourcePage", "Summary", "TransferorCourtName", "TransferorDocketLawFirm"], "low_coverage_fields": {"TransferorCourtId": 0.12432656444260257, "Attorney": 0.16576875259013676, "TransferredFrom": 0.08288437629506838, "DateFiled": 0.12432656444260257, "IsRemoval": 0.12432656444260257, "AssignedTo": 0.16576875259013676, "S3Html": 0.12432656444260257, "AddedDate": 26.937422295897225, "TransferredIn": 0.24865312888520513, "AddedOn": 27.061748860339822, "PendingCto": 3.5640281806879406, "TransferorDocketNum": 0.12432656444260257, "Flags": 0.20721094073767096, "BaseFilename": 0.04144218814753419, "AttorneysGpt": 0.12432656444260257, "IsTransferred": 0.12432656444260257}, "total_fields_in_period": 134}, "2024-07": {"item_count": 2011, "missing_fields": ["AddedDateIso", "BaseFilename", "CauseFromReport", "LawFirms", "MdlCat", "NosFromReport", "PlaintiffsGpt", "ProcessingNotes", "SourcePage", "Summary", "TransferorDocketLawFirm"], "low_coverage_fields": {"TransferorCourtName": 7.409249129786176, "TransferorCourtId": 2.9835902536051715, "TransferredFrom": 9.099950273495773, "DateFiled": 41.7702635504724, "IsRemoval": 0.14917951268025859, "Allegations": 7.856787667826952, "S3Html": 41.621084037792144, "TransferredIn": 48.23470909995027, "InitialFilingDate": 0.04972650422675286, "TransferorDocketNum": 9.05022376926902, "IsTransferred": 41.96916956737941}, "total_fields_in_period": 118}, "2024-08": {"item_count": 3172, "missing_fields": ["AddedDateIso", "BaseFilename", "CauseFromReport", "IsTransferred", "LawFirms", "MdlCat", "NosFromReport", "PlaintiffsGpt", "ProcessingNotes", "SourcePage", "Summary", "TransferorDocketLawFirm"], "low_coverage_fields": {"TransferorCourtName": 3.562421185372005, "TransferorCourtId": 0.47288776796973514, "TransferredFrom": 4.098360655737705, "DateFiled": 10.403530895334175, "IsRemoval": 0.4098360655737705, "Allegations": 2.301387137452711, "S3Html": 9.993694829760404, "TransferredIn": 4.129886506935687, "InitialFilingDate": 0.15762925598991173, "TransferorDocketNum": 4.003783102143758, "Flags": 47.477931904161416}, "total_fields_in_period": 77}, "2024-09": {"item_count": 1795, "missing_fields": ["AddedDateIso", "BaseFilename", "CauseFromReport", "LawFirms", "MdlCat", "NosFromReport", "PlaintiffsGpt", "ProcessingNotes", "SourcePage", "Summary", "TransferorDocketLawFirm"], "low_coverage_fields": {"TransferorCourtName": 4.066852367688022, "TransferorCourtId": 4.011142061281337, "TransferredFrom": 3.0083565459610027, "Allegations": 4.735376044568245, "TransferredIn": 8.356545961002785, "InitialFilingDate": 7.186629526462395, "TransferorDocketNum": 8.356545961002785, "IsTransferred": 44.45682451253482}, "total_fields_in_period": 71}, "2024-10": {"item_count": 2701, "missing_fields": ["AddedDateIso", "BaseFilename", "CauseFromReport", "LawFirms", "MdlCat", "NosFromReport", "PlaintiffsGpt", "ProcessingNotes", "SourcePage", "Summary", "TransferorDocketLawFirm", "TransferredFrom"], "low_coverage_fields": {"TransferorCourtName": 10.070344316919659, "TransferorCourtId": 3.7023324694557576, "Allegations": 10.773787486116253, "InitialFilingDate": 9.329877823028507, "TransferorDocketNum": 11.14402073306183}, "total_fields_in_period": 67}, "2024-11": {"item_count": 1632, "missing_fields": ["AddedDateIso", "BaseFilename", "CauseFromReport", "LawFirms", "MdlCat", "NosFromReport", "PlaintiffsGpt", "ProcessingNotes", "SourcePage", "Summary", "TransferorDocketLawFirm", "TransferredFrom"], "low_coverage_fields": {"TransferorCourtName": 22.426470588235293, "TransferorCourtId": 3.2475490196078436, "Allegations": 18.38235294117647, "InitialFilingDate": 12.867647058823529, "PendingCto": 14.705882352941178, "TransferorDocketNum": 22.610294117647058}, "total_fields_in_period": 63}, "2025-01": {"item_count": 2227, "missing_fields": ["AddedDateIso", "BaseFilename", "CauseFromReport", "LawFirms", "MdlCat", "NosFromReport", "PlaintiffsGpt", "ProcessingNotes", "SourcePage", "Summary", "TransferorDocketLawFirm", "TransferredFrom"], "low_coverage_fields": {"TransferorCourtName": 7.678491243825775, "TransferorCourtId": 3.0534351145038165, "Allegations": 11.899416255051639, "InitialFilingDate": 6.8253255500673555, "TransferorDocketNum": 7.543780871127077}, "total_fields_in_period": 62}, "2025-03": {"item_count": 1968, "missing_fields": ["AddedDateIso", "BaseFilename", "CauseFromReport", "LawFirms", "MdlCat", "NosFromReport", "PlaintiffsGpt", "ProcessingNotes", "SourcePage", "Summary", "TransferorDocketLawFirm", "TransferredFrom"], "low_coverage_fields": {"TransferorCourtName": 7.113821138211382, "TransferorCourtId": 4.573170731707317, "Allegations": 10.010162601626016, "InitialFilingDate": 7.926829268292683, "TransferorDocketNum": 8.384146341463415}, "total_fields_in_period": 61}, "2025-04": {"item_count": 3120, "missing_fields": ["AddedDateIso", "BaseFilename", "CauseFromReport", "NosFromReport", "ProcessingNotes", "SourcePage", "TransferredFrom"], "low_coverage_fields": {"TransferorCourtName": 3.3653846153846154, "TransferorCourtId": 2.371794871794872, "Summary": 4.519230769230769, "TransferorDocketLawFirm": 7.211538461538461, "Allegations": 18.58974358974359, "OriginalFilename": 47.660256410256416, "InitialFilingDate": 4.134615384615384, "TransferorDocketNum": 4.455128205128205}, "total_fields_in_period": 67}, "2025-05": {"item_count": 3465, "missing_fields": [], "low_coverage_fields": {"TransferorCourtName": 2.0202020202020203, "TransferorCourtId": 1.3852813852813852, "Summary": 2.1933621933621934, "ProcessingNotes": 5.858585858585859, "TransferredFrom": 1.5584415584415585, "TransferorDocketLawFirm": 1.240981240981241, "InitialFilingDate": 1.2987012987012987, "TransferorDocketNum": 2.1067821067821066}, "total_fields_in_period": 69}, "2024-12": {"item_count": 1708, "missing_fields": ["AddedDateIso", "BaseFilename", "CauseFromReport", "MdlCat", "NosFromReport", "PlaintiffsGpt", "ProcessingNotes", "SourcePage", "Summary", "TransferorDocketLawFirm", "TransferredFrom"], "low_coverage_fields": {"TransferorCourtName": 9.543325526932083, "TransferorCourtId": 3.9227166276346606, "Allegations": 16.100702576112415, "LawFirms": 0.0585480093676815, "InitialFilingDate": 7.611241217798595, "TransferorDocketNum": 9.01639344262295}, "total_fields_in_period": 64}, "2024-04": {"item_count": 270, "missing_fields": ["AddedDateIso", "BaseFilename", "CauseFromReport", "LawFirms", "MdlCat", "NosFromReport", "PlaintiffsGpt", "ProcessingNotes", "SourcePage", "Summary", "TransferorCourtId", "TransferorDocketLawFirm"], "low_coverage_fields": {"TransferorCourtName": 0.3703703703703704, "Attorney": 1.8518518518518516, "TransferredFrom": 0.3703703703703704, "DateFiled": 1.8518518518518516, "IsRemoval": 1.8518518518518516, "AssignedTo": 1.4814814814814816, "S3Html": 1.8518518518518516, "AddedDate": 6.666666666666667, "TransferredIn": 2.2222222222222223, "AddedOn": 6.666666666666667, "Plaintiff": 35.18518518518518, "InitialFilingDate": 0.3703703703703704, "PendingCto": 0.3703703703703704, "TransferorDocketNum": 0.3703703703703704, "Flags": 1.8518518518518516, "AttorneysGpt": 1.8518518518518516, "IsTransferred": 1.4814814814814816}, "total_fields_in_period": 88}, "2024-06": {"item_count": 1661, "missing_fields": ["AddedDateIso", "BaseFilename", "CauseFromReport", "InitialFilingDate", "LawFirms", "MdlCat", "NosFromReport", "PlaintiffsGpt", "ProcessingNotes", "SourcePage", "Summary", "TransferorDocketLawFirm"], "low_coverage_fields": {"TransferorCourtName": 0.8428657435279951, "TransferorCourtId": 1.6255267910897049, "TransferredFrom": 1.2642986152919928, "DateFiled": 48.88621312462372, "IsRemoval": 0.060204695966285374, "AssignedTo": 48.76580373269115, "S3Html": 48.88621312462372, "TransferorDocketNum": 1.2040939193257074, "Flags": 45.75556893437688, "AttorneysGpt": 37.086092715231786, "IsTransferred": 48.94641782059001}, "total_fields_in_period": 115}, "2025-02": {"item_count": 2005, "missing_fields": ["AddedDateIso", "BaseFilename", "CauseFromReport", "LawFirms", "MdlCat", "NosFromReport", "PlaintiffsGpt", "ProcessingNotes", "SourcePage", "Summary", "TransferorDocketLawFirm", "TransferredFrom"], "low_coverage_fields": {"TransferorCourtName": 10.922693266832919, "TransferorCourtId": 6.433915211970074, "Allegations": 9.326683291770573, "InitialFilingDate": 9.276807980049876, "TransferorDocketNum": 11.571072319201996}, "total_fields_in_period": 63}, "2023-11": {"item_count": 4, "missing_fields": ["AddedDateIso", "BaseFilename", "CauseFromReport", "LawFirms", "MdlCat", "NosFromReport", "PlaintiffsGpt", "ProcessingNotes", "SourcePage", "Summary", "TransferorCourtName", "TransferorDocketLawFirm", "TransferredFrom"], "low_coverage_fields": {"TransferorCourtId": 25.0, "InitialFilingDate": 25.0, "PendingCto": 25.0, "TransferorDocketNum": 25.0}, "total_fields_in_period": 50}, "2023-12": {"item_count": 4, "missing_fields": ["AddedDateIso", "BaseFilename", "CauseFromReport", "InitialFilingDate", "LawFirms", "MdlCat", "NosFromReport", "PendingCto", "PlaintiffsGpt", "ProcessingNotes", "SourcePage", "Summary", "TransferorCourtId", "TransferorCourtName", "TransferorDocketLawFirm", "TransferorDocketNum", "TransferredFrom"], "low_coverage_fields": {"Attorney": 25.0, "DateFiled": 25.0, "IsRemoval": 25.0, "AssignedTo": 25.0, "S3Html": 25.0, "TransferredIn": 25.0, "Flags": 25.0, "AttorneysGpt": 25.0, "IsTransferred": 25.0}, "total_fields_in_period": 44}, "0000-00": {"item_count": 1, "missing_fields": ["AddedDate", "AddedDateIso", "AddedOn", "Allegations", "AssignedTo", "Attorney", "AttorneysGpt", "BaseFilename", "Cause", "CauseFromReport", "DateFiled", "Flags", "InitialFilingDate", "IsRemoval", "IsTransferred", "Juris<PERSON>", "JuryDemand", "LawFirm", "LawFirms", "MdlCat", "MdlNum", "NewFilename", "Nos", "NosFromReport", "Office", "OriginalFilename", "PendingCto", "Plaintiff", "PlaintiffsGpt", "ProcessingNotes", "S3Html", "S3Link", "SourcePage", "Summary", "Title", "TransferorCourtId", "TransferorCourtName", "TransferorDocketLawFirm", "TransferorDocketNum", "TransferredFrom", "TransferredIn", "Versus"], "low_coverage_fields": {}, "total_fields_in_period": 4}, "2023-10": {"item_count": 1, "missing_fields": ["AddedDateIso", "BaseFilename", "CauseFromReport", "InitialFilingDate", "LawFirms", "MdlCat", "NosFromReport", "PendingCto", "PlaintiffsGpt", "ProcessingNotes", "SourcePage", "Summary", "TransferorCourtId", "TransferorCourtName", "TransferorDocketLawFirm", "TransferorDocketNum", "TransferredFrom"], "low_coverage_fields": {"MdlNum": 0.0}, "total_fields_in_period": 41}, "2024-02": {"item_count": 2, "missing_fields": ["AddedDateIso", "BaseFilename", "CauseFromReport", "InitialFilingDate", "LawFirms", "MdlCat", "NosFromReport", "PlaintiffsGpt", "ProcessingNotes", "SourcePage", "Summary", "TransferorDocketLawFirm", "TransferredFrom"], "low_coverage_fields": {}, "total_fields_in_period": 50}, "2024-03": {"item_count": 12, "missing_fields": ["AddedDateIso", "BaseFilename", "CauseFromReport", "InitialFilingDate", "LawFirms", "MdlCat", "NosFromReport", "PlaintiffsGpt", "ProcessingNotes", "SourcePage", "Summary", "TransferorCourtId", "TransferorDocketLawFirm", "TransferredFrom"], "low_coverage_fields": {"TransferorCourtName": 8.333333333333332, "Attorney": 25.0, "DateFiled": 25.0, "IsRemoval": 25.0, "AssignedTo": 25.0, "S3Html": 16.666666666666664, "TransferredIn": 25.0, "PendingCto": 8.333333333333332, "TransferorDocketNum": 8.333333333333332, "Flags": 25.0, "AttorneysGpt": 25.0, "IsTransferred": 25.0}, "total_fields_in_period": 61}, "2035-06": {"item_count": 1, "missing_fields": ["AddedDateIso", "AssignedTo", "Attorney", "AttorneysGpt", "BaseFilename", "CauseFromReport", "DateFiled", "Flags", "InitialFilingDate", "IsRemoval", "IsTransferred", "LawFirms", "MdlCat", "NosFromReport", "PendingCto", "PlaintiffsGpt", "ProcessingNotes", "S3Html", "SourcePage", "Summary", "TransferorCourtId", "TransferorCourtName", "TransferorDocketLawFirm", "TransferorDocketNum", "TransferredFrom", "TransferredIn"], "low_coverage_fields": {}, "total_fields_in_period": 23}, "2023-06": {"item_count": 1, "missing_fields": ["AddedDateIso", "Allegations", "Attorney", "BaseFilename", "CauseFromReport", "InitialFilingDate", "LawFirms", "MdlCat", "NosFromReport", "Plaintiff", "PlaintiffsGpt", "ProcessingNotes", "S3Html", "SourcePage", "Summary", "TransferorCourtId", "TransferorDocketLawFirm", "TransferredFrom"], "low_coverage_fields": {}, "total_fields_in_period": 37}, "2018-03": {"item_count": 1, "missing_fields": ["AddedDateIso", "Allegations", "BaseFilename", "CauseFromReport", "DateFiled", "Flags", "InitialFilingDate", "LawFirms", "MdlCat", "NosFromReport", "PlaintiffsGpt", "ProcessingNotes", "SourcePage", "Summary", "TransferorCourtId", "TransferorCourtName", "TransferorDocketLawFirm", "TransferorDocketNum", "TransferredFrom", "TransferredIn"], "low_coverage_fields": {}, "total_fields_in_period": 35}, "2023-03": {"item_count": 1, "missing_fields": ["AddedDateIso", "AssignedTo", "Attorney", "AttorneysGpt", "BaseFilename", "CauseFromReport", "DateFiled", "Flags", "InitialFilingDate", "IsRemoval", "IsTransferred", "LawFirms", "MdlCat", "NosFromReport", "PendingCto", "PlaintiffsGpt", "ProcessingNotes", "S3Html", "SourcePage", "Summary", "TransferorCourtId", "TransferorCourtName", "TransferorDocketLawFirm", "TransferorDocketNum", "TransferredFrom", "TransferredIn"], "low_coverage_fields": {}, "total_fields_in_period": 23}, "2024-01": {"item_count": 2, "missing_fields": ["AddedDateIso", "AssignedTo", "Attorney", "AttorneysGpt", "BaseFilename", "CauseFromReport", "DateFiled", "Flags", "InitialFilingDate", "IsRemoval", "IsTransferred", "LawFirms", "MdlCat", "NosFromReport", "PendingCto", "PlaintiffsGpt", "ProcessingNotes", "S3Html", "SourcePage", "Summary", "TransferorCourtId", "TransferorCourtName", "TransferorDocketLawFirm", "TransferorDocketNum", "TransferredFrom", "TransferredIn"], "low_coverage_fields": {}, "total_fields_in_period": 23}, "2023-08": {"item_count": 1, "missing_fields": ["AddedDateIso", "AssignedTo", "Attorney", "AttorneysGpt", "BaseFilename", "CauseFromReport", "DateFiled", "Flags", "InitialFilingDate", "IsRemoval", "IsTransferred", "LawFirms", "MdlCat", "NosFromReport", "PendingCto", "PlaintiffsGpt", "ProcessingNotes", "S3Html", "SourcePage", "Summary", "TransferorCourtId", "TransferorCourtName", "TransferorDocketLawFirm", "TransferorDocketNum", "TransferredFrom", "TransferredIn"], "low_coverage_fields": {}, "total_fields_in_period": 23}}}