#!/usr/bin/env python3
"""
Analyze LLM cache format to understand schema for mapping script.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

import pickle
import hashlib
from typing import Dict, Any, List

def analyze_llm_cache():
    """Analyze the existing LLM cache to understand its format."""
    cache_file = "llm_response_cache.pkl"
    
    if not os.path.exists(cache_file):
        print(f"❌ LLM cache file not found: {cache_file}")
        return
    
    print("Analyzing LLM cache format...")
    
    try:
        with open(cache_file, 'rb') as f:
            llm_cache = pickle.load(f)
    except Exception as e:
        print(f"❌ Error loading LLM cache: {e}")
        return
    
    print(f"✅ Loaded LLM cache: {len(llm_cache):,} entries")
    
    # Analyze cache structure
    print(f"\n{'='*60}")
    print("LLM CACHE STRUCTURE ANALYSIS")
    print(f"{'='*60}")
    
    # Get sample keys and values
    sample_keys = list(llm_cache.keys())[:10]
    
    print(f"Sample cache keys:")
    for i, key in enumerate(sample_keys):
        print(f"  {i+1}. {repr(key)[:100]}...")
    
    # Analyze key patterns
    key_types = {}
    key_lengths = []
    
    for key in llm_cache.keys():
        key_type = type(key).__name__
        key_types[key_type] = key_types.get(key_type, 0) + 1
        
        if isinstance(key, str):
            key_lengths.append(len(key))
    
    print(f"\nKey Statistics:")
    print(f"  Key types: {key_types}")
    if key_lengths:
        print(f"  String key lengths: min={min(key_lengths)}, max={max(key_lengths)}, avg={sum(key_lengths)/len(key_lengths):.1f}")
    
    # Check if keys look like hashes
    if key_lengths:
        hash_like_32 = sum(1 for length in key_lengths if length == 32)
        hash_like_64 = sum(1 for length in key_lengths if length == 64)
        print(f"  Keys that look like MD5 hashes (32 chars): {hash_like_32}")
        print(f"  Keys that look like SHA256 hashes (64 chars): {hash_like_64}")
    
    # Check if keys contain text or look like generated text
    text_like_keys = 0
    hash_like_keys = 0
    
    for key in sample_keys:
        if isinstance(key, str):
            # Check if it's all hex characters (hash-like)
            if all(c in '0123456789abcdef' for c in key.lower()):
                hash_like_keys += 1
            elif any(word in key.lower() for word in ['ad', 'text', 'summary', 'title', 'body']):
                text_like_keys += 1
    
    print(f"  Sample keys that look like hashes: {hash_like_keys}/{len(sample_keys)}")
    print(f"  Sample keys that contain text words: {text_like_keys}/{len(sample_keys)}")
    
    # Analyze values
    print(f"\nSample cache values:")
    for i, key in enumerate(sample_keys[:5]):
        value = llm_cache[key]
        value_type = type(value).__name__
        if isinstance(value, str):
            value_preview = value[:100].replace('\n', ' ')
        elif isinstance(value, dict):
            value_preview = f"Dict with keys: {list(value.keys())[:5]}"
        elif isinstance(value, list):
            value_preview = f"List with {len(value)} items"
        else:
            value_preview = str(value)[:100]
        
        print(f"  {i+1}. {value_type}: {value_preview}...")
    
    return llm_cache

def detect_llm_key_format(llm_cache: Dict[str, Any]) -> str:
    """Detect how LLM cache keys are generated."""
    print(f"\n{'='*60}")
    print("DETECTING LLM KEY GENERATION PATTERN")
    print(f"{'='*60}")
    
    sample_keys = list(llm_cache.keys())[:20]
    
    # Test various hypotheses about key generation
    patterns = {
        'md5_hash': 0,
        'sha256_hash': 0,
        'text_based': 0,
        'composite': 0,
        'unknown': 0
    }
    
    for key in sample_keys:
        if isinstance(key, str):
            key_lower = key.lower()
            
            # Check for hash patterns
            if len(key) == 32 and all(c in '0123456789abcdef' for c in key_lower):
                patterns['md5_hash'] += 1
            elif len(key) == 64 and all(c in '0123456789abcdef' for c in key_lower):
                patterns['sha256_hash'] += 1
            elif '_' in key and any(word in key_lower for word in ['llm', 'ad', 'text']):
                patterns['composite'] += 1
            elif any(word in key_lower for word in ['summary', 'title', 'body', 'description']):
                patterns['text_based'] += 1
            else:
                patterns['unknown'] += 1
    
    print("Key pattern detection results:")
    for pattern, count in patterns.items():
        print(f"  {pattern}: {count}/{len(sample_keys)} ({count/len(sample_keys)*100:.1f}%)")
    
    # Determine most likely pattern
    most_likely = max(patterns.items(), key=lambda x: x[1])
    print(f"\nMost likely key format: {most_likely[0]}")
    
    return most_likely[0]

def check_text_relationship(llm_cache: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Try to understand the relationship between cache keys and text processing."""
    print(f"\n{'='*60}")
    print("CHECKING TEXT RELATIONSHIP")
    print(f"{'='*60}")
    
    # Load a few sample ads to test
    import boto3
    
    dynamodb = boto3.resource(
        'dynamodb',
        endpoint_url="http://localhost:8000",
        region_name='us-west-2',
        aws_access_key_id='fakeMyKeyId',
        aws_secret_access_key='fakeSecretAccessKey'
    )
    
    table = dynamodb.Table("FBAdArchive")
    response = table.scan(Limit=5)
    sample_ads = response['Items']
    
    print(f"Testing with {len(sample_ads)} sample ads...")
    
    relationships = []
    YAML_FIELDS = ["Title", "Body", "Summary", "LinkDescription", "PageName"]
    
    for i, ad in enumerate(sample_ads):
        ad_id = ad.get('AdArchiveID', 'unknown')
        
        # Test different text extraction methods
        # Method 1: New YAML fields (space separator)
        text_parts_new = []
        for field in YAML_FIELDS:
            value = ad.get(field)
            if value and str(value).strip():
                text_parts_new.append(str(value).strip())
        text_new = ' '.join(text_parts_new)
        
        # Method 2: Old .bak fields (pipe separator)
        text_parts_old = []
        for field in YAML_FIELDS:
            field_lc = field.lower()
            value = ad.get(field)
            if value and str(value).strip():
                text_parts_old.append(str(value).strip())
        text_old = ' | '.join(text_parts_old)
        
        # Generate potential cache keys
        keys_to_test = [
            # Direct text
            text_new,
            text_old,
            # MD5 hashes
            hashlib.md5(text_new.encode()).hexdigest(),
            hashlib.md5(text_old.encode()).hexdigest(),
            # Composite keys
            f"llm_{ad_id}_{hashlib.md5(text_new.encode()).hexdigest()}",
            f"llm_{ad_id}_{hashlib.md5(text_old.encode()).hexdigest()}",
        ]
        
        found_keys = []
        for test_key in keys_to_test:
            if test_key in llm_cache:
                found_keys.append(test_key)
        
        if found_keys:
            relationships.append({
                'ad_id': ad_id,
                'found_keys': found_keys,
                'text_new': text_new[:100],
                'text_old': text_old[:100]
            })
            print(f"  Ad {i+1} ({ad_id}): Found {len(found_keys)} matching keys")
        else:
            print(f"  Ad {i+1} ({ad_id}): No matching keys found")
    
    return relationships

def main():
    print("LLM CACHE FORMAT ANALYSIS")
    print("=" * 50)
    
    # Step 1: Analyze cache structure
    llm_cache = analyze_llm_cache()
    
    if not llm_cache:
        return
    
    # Step 2: Detect key format
    key_format = detect_llm_key_format(llm_cache)
    
    # Step 3: Check relationship with text processing
    relationships = check_text_relationship(llm_cache)
    
    # Step 4: Summary and recommendations
    print(f"\n{'='*60}")
    print("ANALYSIS SUMMARY")
    print(f"{'='*60}")
    
    print(f"LLM cache contains {len(llm_cache):,} entries")
    print(f"Detected key format: {key_format}")
    print(f"Found {len(relationships)} ads with matching cache entries")
    
    if relationships:
        print("\nMatching patterns found:")
        for rel in relationships:
            print(f"  Ad {rel['ad_id']}: {len(rel['found_keys'])} matching keys")
    
    print(f"\nRecommendations for mapping script:")
    if key_format == 'md5_hash':
        print("  - LLM cache uses MD5 hashes as keys")
        print("  - Need to test both old and new text processing methods")
        print("  - Map old text hash -> new text hash")
    elif key_format == 'composite':
        print("  - LLM cache uses composite keys (likely llm_adid_hash)")
        print("  - Map old composite -> new composite")
    elif key_format == 'text_based':
        print("  - LLM cache uses raw text as keys")
        print("  - Map old text -> new text directly")
    else:
        print("  - Need further investigation of key format")

if __name__ == "__main__":
    main()