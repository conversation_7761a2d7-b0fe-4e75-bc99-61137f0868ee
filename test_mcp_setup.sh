#!/bin/bash

echo "MCP Server Configuration Test"
echo "============================="
echo

echo "1. Checking Node.js installation..."
if [ -f "/Users/<USER>/.nvm/versions/node/v23.10.0/bin/node" ]; then
    echo "✅ Node.js v23.10.0 found"
    /Users/<USER>/.nvm/versions/node/v23.10.0/bin/node --version
else
    echo "❌ Node.js v23.10.0 not found at expected path"
fi
echo

echo "2. Checking npx..."
if [ -f "/Users/<USER>/.nvm/versions/node/v23.10.0/bin/npx" ]; then
    echo "✅ npx found"
    /Users/<USER>/.nvm/versions/node/v23.10.0/bin/npx --version
else
    echo "❌ npx not found at expected path"
fi
echo

echo "3. Checking uvx for fetch server..."
if command -v uvx &> /dev/null; then
    echo "✅ uvx found at: $(which uvx)"
else
    echo "❌ uvx not found (needed for fetch server)"
fi
echo

echo "4. Testing MCP servers (will timeout after 2 seconds each)..."
echo

echo "Testing filesystem server..."
timeout 2 /Users/<USER>/.nvm/versions/node/v23.10.0/bin/npx -y @modelcontextprotocol/server-filesystem /Users/<USER>/PycharmProjects/ 2>&1 | head -1
if [ $? -eq 124 ]; then
    echo "✅ Filesystem server starts successfully"
else
    echo "❌ Filesystem server failed to start"
fi
echo

echo "Testing memory server..."
timeout 2 /Users/<USER>/.nvm/versions/node/v23.10.0/bin/npx -y @modelcontextprotocol/server-memory 2>&1 | head -1
if [ $? -eq 124 ]; then
    echo "✅ Memory server starts successfully"
else
    echo "❌ Memory server failed to start"
fi
echo

echo "5. Checking Claude config file..."
if [ -f "/Users/<USER>/Library/Application Support/Claude/claude_desktop_config.json" ]; then
    echo "✅ Config file exists"
    python3 -m json.tool "/Users/<USER>/Library/Application Support/Claude/claude_desktop_config.json" > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo "✅ Config file is valid JSON"
    else
        echo "❌ Config file has JSON syntax errors"
    fi
else
    echo "❌ Config file not found"
fi
echo

echo "Test complete!"