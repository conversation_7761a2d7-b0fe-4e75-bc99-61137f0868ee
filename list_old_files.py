#!/usr/bin/env python3
"""
Script to list files in specific directories, sorted by creation and modification dates.
"""
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any

from rich.console import Console
from rich.table import Table
from rich import box
from rich.text import Text
from rich.panel import Panel

def get_file_info(filepath: Path, project_root: Path) -> Dict[str, Any]:
    """Get file information including creation and modification times."""
    stat = filepath.stat()
    try:
        rel_path = filepath.relative_to(project_root)
    except ValueError:
        rel_path = filepath
    return {
        'path': rel_path,
        'created': stat.st_birthtime if hasattr(stat, 'st_birthtime') else stat.st_ctime,
        'modified': stat.st_mtime,
        'size': stat.st_size
    }

def format_timestamp(timestamp: float) -> str:
    """Format timestamp to a readable string."""
    return datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')

def format_size(size: int) -> str:
    """Format file size in a human-readable format."""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size < 1024.0:
            return f"{size:.1f}{unit}"
        size /= 1024.0
    return f"{size:.1f}TB"

def scan_directories(project_root: Path) -> List[Dict[str, Any]]:
    """Scan the target directories and collect file information."""
    # Get the absolute path of the current script's directory
    script_dir = Path(__file__).resolve().parent
    
    # Define the project root as the parent of the script's directory
    project_root = script_dir
    
    # Define target directories relative to the project root
    target_dirs = [
        project_root,  # Project root
        project_root / 'src' / 'scripts',  # src/scripts
    ]
    
    # Only add src directory if it's not the same as project_root/src/scripts
    if (project_root / 'src').exists() and (project_root / 'src').is_dir():
        target_dirs.append(project_root / 'src')  # src (only top-level files)
    
    # File extensions to include
    include_extensions = {'.py', '.sh', '.json', '.csv', '.yaml', '.yml', '.md', '.txt'}
    
    all_files = []
    
    for target_dir in target_dirs:
        if not target_dir.exists():
            print(f"[yellow]Warning: Directory not found: {target_dir}[/yellow]", file=sys.stderr)
            continue
            
        for item in target_dir.iterdir():
            # Skip hidden files and directories
            if item.name.startswith('.'):
                continue
                
            # For src directory, skip subdirectories and only include top-level files
            if target_dir.name == 'src' and item.is_dir():
                continue
                
            # Check if file has one of the allowed extensions
            if item.is_file() and item.suffix.lower() in include_extensions:
                try:
                    file_info = get_file_info(item, project_root)
                    all_files.append(file_info)
                except (OSError, PermissionError) as e:
                    print(f"[red]Error accessing {item}: {e}[/red]", file=sys.stderr)
    
    return all_files

def display_files_table(files: List[Dict[str, Any]], sort_by: str, console: Console) -> None:
    """Display files in a rich table."""
    title = f"Files sorted by {sort_by} (oldest first)"
    table = Table(title=title, box=box.ROUNDED, show_header=True, header_style="bold magenta")
    
    # Add columns
    table.add_column("Created", style="dim", width=20)
    table.add_column("Modified", style="dim", width=20)
    table.add_column("Size", justify="right", style="cyan", width=10)
    table.add_column("Path", style="green")
    
    # Sort files
    sorted_files = sorted(files, key=lambda x: x[sort_by])
    
    # Add rows
    for f in sorted_files:
        # Color code by file extension
        path_text = Text()
        path_parts = str(f['path']).split(os.sep)
        
        # Style each part of the path
        for i, part in enumerate(path_parts):
            if i > 0:
                path_text.append("/", style="dim")
            
            # Style directories differently from files
            if i < len(path_parts) - 1 or not any(part.endswith(ext) for ext in ['.py', '.json', '.csv', '.md']):
                path_text.append(part, style="blue")
            else:
                path_text.append(part, style="green")
        
        table.add_row(
            format_timestamp(f['created']),
            format_timestamp(f['modified']),
            format_size(f['size']),
            path_text
        )
    
    console.print(table)

def main():
    console = Console()
    
    with console.status("[bold green]Scanning directories...") as status:
        # Start scanning from the script's directory
        files = scan_directories(Path(__file__).resolve().parent)
    
    if not files:
        console.print("[yellow]No files found in the specified directories.[/yellow]")
        return
    
    # Display tables
    display_files_table(files, 'created', console)
    console.print("\n")
    display_files_table(files, 'modified', console)
    
    # Print summary in a panel
    total_size = sum(f['size'] for f in files)
    summary = f"[bold]Total files:[/bold] {len(files):,}\n" \
              f"[bold]Total size:[/bold] {format_size(total_size)}"
    
    console.print(Panel(
        summary,
        title="[bold green]Summary[/bold green]",
        border_style="green",
        padding=(1, 2)
    ))

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n[red]An error occurred: {e}[/red]", file=sys.stderr)
        sys.exit(1)
