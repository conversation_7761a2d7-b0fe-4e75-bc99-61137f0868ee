{"deepseek-reasoner": {"max_tokens": 8192, "max_input_tokens": 64000, "max_output_tokens": 8192, "input_cost_per_token": 5.5e-07, "input_cost_per_token_cache_hit": 1.4e-07, "cache_read_input_token_cost": 1.4e-07, "cache_creation_input_token_cost": 0.0, "output_cost_per_token": 2.19e-06, "litellm_provider": "deepseek", "mode": "chat", "supports_assistant_prefill": true, "supports_prompt_caching": true}, "openrouter/deepseek/deepseek-r1:free": {"max_tokens": 8192, "max_input_tokens": 64000, "max_output_tokens": 8192, "input_cost_per_token": 0.0, "input_cost_per_token_cache_hit": 0.0, "cache_read_input_token_cost": 0.0, "cache_creation_input_token_cost": 0.0, "output_cost_per_token": 0.0, "litellm_provider": "openrouter", "mode": "chat", "supports_assistant_prefill": true, "supports_prompt_caching": true}, "openrouter/deepseek/deepseek-chat:free": {"max_tokens": 8192, "max_input_tokens": 64000, "max_output_tokens": 8192, "input_cost_per_token": 0.0, "input_cost_per_token_cache_hit": 0.0, "cache_read_input_token_cost": 0.0, "cache_creation_input_token_cost": 0.0, "output_cost_per_token": 0.0, "litellm_provider": "openrouter", "mode": "chat", "supports_assistant_prefill": true, "supports_prompt_caching": true}, "openrouter/deepseek/deepseek-chat-v3-0324": {"max_tokens": 8192, "max_input_tokens": 64000, "max_output_tokens": 8192, "input_cost_per_token": 5.5e-07, "input_cost_per_token_cache_hit": 1.4e-07, "cache_read_input_token_cost": 1.4e-07, "cache_creation_input_token_cost": 0.0, "output_cost_per_token": 2.19e-06, "litellm_provider": "openrouter", "mode": "chat", "supports_assistant_prefill": true, "supports_prompt_caching": true}, "openrouter/deepseek/deepseek-chat-v3-0324:free": {"max_tokens": 131072, "max_input_tokens": 131072, "max_output_tokens": 131072, "input_cost_per_token": 0, "output_cost_per_token": 0, "litellm_provider": "openrouter", "supports_prompt_caching": true, "mode": "chat", "supports_tool_choice": true}, "fireworks_ai/accounts/fireworks/models/deepseek-r1": {"max_tokens": 160000, "max_input_tokens": 128000, "max_output_tokens": 20480, "litellm_provider": "fireworks_ai", "input_cost_per_token": 8e-06, "output_cost_per_token": 8e-06, "mode": "chat"}, "fireworks_ai/accounts/fireworks/models/deepseek-v3-0324": {"max_tokens": 160000, "max_input_tokens": 100000, "max_output_tokens": 8192, "litellm_provider": "fireworks_ai", "input_cost_per_token": 9e-07, "output_cost_per_token": 9e-07, "mode": "chat"}, "openrouter/openrouter/quasar-alpha": {"max_input_tokens": 1000000, "max_output_tokens": 32000, "input_cost_per_token": 0.0, "output_cost_per_token": 0.0, "litellm_provider": "openrouter", "mode": "chat", "supports_vision": true, "supports_function_calling": true, "supports_system_messages": true, "supports_prompt_caching": true}, "openrouter/openrouter/optimus-alpha": {"max_input_tokens": 1000000, "max_output_tokens": 32000, "input_cost_per_token": 0.0, "output_cost_per_token": 0.0, "litellm_provider": "openrouter", "mode": "chat"}, "openrouter/openai/gpt-4o-mini": {"max_tokens": 16384, "max_input_tokens": 128000, "max_output_tokens": 16384, "input_cost_per_token": 1.5e-07, "output_cost_per_token": 6e-07, "input_cost_per_token_batches": 7.5e-08, "output_cost_per_token_batches": 3e-07, "cache_read_input_token_cost": 7.5e-08, "litellm_provider": "openrouter", "mode": "chat", "supports_function_calling": true, "supports_parallel_function_calling": true, "supports_response_schema": true, "supports_vision": true, "supports_prompt_caching": true, "supports_system_messages": true}, "anthropic/claude-3-7-sonnet-20250219": {"max_tokens": 8192, "max_input_tokens": 200000, "max_output_tokens": 8192, "input_cost_per_token": 3e-06, "output_cost_per_token": 1.5e-05, "cache_creation_input_token_cost": 3.75e-06, "cache_read_input_token_cost": 3e-07, "litellm_provider": "anthropic", "mode": "chat", "supports_function_calling": true, "supports_vision": true, "tool_use_system_prompt_tokens": 159, "supports_assistant_prefill": true, "supports_pdf_input": true, "supports_prompt_caching": true, "supports_response_schema": true, "deprecation_date": "2025-10-01", "supports_tool_choice": true}, "openai/gpt-4.5-preview": {"max_tokens": 16384, "max_input_tokens": 128000, "max_output_tokens": 16384, "input_cost_per_token": 7.5e-05, "output_cost_per_token": 0.00015, "cache_read_input_token_cost": 3.75e-05, "litellm_provider": "openai", "mode": "chat", "supports_function_calling": true, "supports_parallel_function_calling": true, "supports_response_schema": true, "supports_vision": true, "supports_prompt_caching": true, "supports_system_messages": true, "supports_tool_choice": true}, "gemini/gemini-2.5-pro-exp-03-25": {"max_tokens": 8192, "max_input_tokens": 1048576, "max_output_tokens": 64000, "max_images_per_prompt": 3000, "max_videos_per_prompt": 10, "max_video_length": 1, "max_audio_length_hours": 8.4, "max_audio_per_prompt": 1, "max_pdf_size_mb": 30, "input_cost_per_image": 0, "input_cost_per_video_per_second": 0, "input_cost_per_audio_per_second": 0, "input_cost_per_token": 0, "input_cost_per_character": 0, "input_cost_per_token_above_128k_tokens": 0, "input_cost_per_character_above_128k_tokens": 0, "input_cost_per_image_above_128k_tokens": 0, "input_cost_per_video_per_second_above_128k_tokens": 0, "input_cost_per_audio_per_second_above_128k_tokens": 0, "output_cost_per_token": 0, "output_cost_per_character": 0, "output_cost_per_token_above_128k_tokens": 0, "output_cost_per_character_above_128k_tokens": 0, "litellm_provider": "gemini", "mode": "chat", "supports_system_messages": true, "supports_function_calling": true, "supports_vision": true, "supports_audio_input": true, "supports_video_input": true, "supports_pdf_input": true, "supports_response_schema": true, "supports_tool_choice": true, "source": "https://cloud.google.com/vertex-ai/generative-ai/pricing"}, "vertex_ai/gemini-2.5-pro-exp-03-25": {"max_tokens": 8192, "max_input_tokens": 1048576, "max_output_tokens": 64000, "max_images_per_prompt": 3000, "max_videos_per_prompt": 10, "max_video_length": 1, "max_audio_length_hours": 8.4, "max_audio_per_prompt": 1, "max_pdf_size_mb": 30, "input_cost_per_image": 0, "input_cost_per_video_per_second": 0, "input_cost_per_audio_per_second": 0, "input_cost_per_token": 0, "input_cost_per_character": 0, "input_cost_per_token_above_128k_tokens": 0, "input_cost_per_character_above_128k_tokens": 0, "input_cost_per_image_above_128k_tokens": 0, "input_cost_per_video_per_second_above_128k_tokens": 0, "input_cost_per_audio_per_second_above_128k_tokens": 0, "output_cost_per_token": 0, "output_cost_per_character": 0, "output_cost_per_token_above_128k_tokens": 0, "output_cost_per_character_above_128k_tokens": 0, "litellm_provider": "vertex_ai-language-models", "mode": "chat", "supports_system_messages": true, "supports_function_calling": true, "supports_vision": true, "supports_audio_input": true, "supports_video_input": true, "supports_pdf_input": true, "supports_response_schema": true, "supports_tool_choice": true, "source": "https://cloud.google.com/vertex-ai/generative-ai/pricing"}, "vertex_ai/gemini-2.5-pro-preview-03-25": {"max_tokens": 8192, "max_input_tokens": 1048576, "max_output_tokens": 64000, "max_images_per_prompt": 3000, "max_videos_per_prompt": 10, "max_video_length": 1, "max_audio_length_hours": 8.4, "max_audio_per_prompt": 1, "max_pdf_size_mb": 30, "input_cost_per_image": 0, "input_cost_per_video_per_second": 0, "input_cost_per_audio_per_second": 0, "input_cost_per_token": 1.25e-06, "input_cost_per_character": 0, "input_cost_per_token_above_128k_tokens": 0, "input_cost_per_character_above_128k_tokens": 0, "input_cost_per_image_above_128k_tokens": 0, "input_cost_per_video_per_second_above_128k_tokens": 0, "input_cost_per_audio_per_second_above_128k_tokens": 0, "output_cost_per_token": 1e-05, "output_cost_per_character": 0, "output_cost_per_token_above_128k_tokens": 0, "output_cost_per_character_above_128k_tokens": 0, "litellm_provider": "vertex_ai-language-models", "mode": "chat", "supports_system_messages": true, "supports_function_calling": true, "supports_vision": true, "supports_audio_input": true, "supports_video_input": true, "supports_pdf_input": true, "supports_response_schema": true, "supports_tool_choice": true, "source": "https://cloud.google.com/vertex-ai/generative-ai/pricing"}, "openrouter/google/gemini-2.5-pro-preview-03-25": {"max_tokens": 8192, "max_input_tokens": 1048576, "max_output_tokens": 64000, "max_images_per_prompt": 3000, "max_videos_per_prompt": 10, "max_video_length": 1, "max_audio_length_hours": 8.4, "max_audio_per_prompt": 1, "max_pdf_size_mb": 30, "input_cost_per_image": 0, "input_cost_per_video_per_second": 0, "input_cost_per_audio_per_second": 0, "input_cost_per_token": 1.25e-06, "input_cost_per_character": 0, "input_cost_per_token_above_128k_tokens": 0, "input_cost_per_character_above_128k_tokens": 0, "input_cost_per_image_above_128k_tokens": 0, "input_cost_per_video_per_second_above_128k_tokens": 0, "input_cost_per_audio_per_second_above_128k_tokens": 0, "output_cost_per_token": 1e-05, "output_cost_per_character": 0, "output_cost_per_token_above_128k_tokens": 0, "output_cost_per_character_above_128k_tokens": 0, "litellm_provider": "vertex_ai-language-models", "mode": "chat", "supports_system_messages": true, "supports_function_calling": true, "supports_vision": true, "supports_audio_input": true, "supports_video_input": true, "supports_pdf_input": true, "supports_response_schema": true, "supports_tool_choice": true, "source": "https://cloud.google.com/vertex-ai/generative-ai/pricing"}, "openrouter/google/gemini-2.5-pro-exp-03-25:free": {"max_tokens": 8192, "max_input_tokens": 1048576, "max_output_tokens": 64000, "max_images_per_prompt": 3000, "max_videos_per_prompt": 10, "max_video_length": 1, "max_audio_length_hours": 8.4, "max_audio_per_prompt": 1, "max_pdf_size_mb": 30, "input_cost_per_image": 0, "input_cost_per_video_per_second": 0, "input_cost_per_audio_per_second": 0, "input_cost_per_token": 0, "input_cost_per_character": 0, "input_cost_per_token_above_128k_tokens": 0, "input_cost_per_character_above_128k_tokens": 0, "input_cost_per_image_above_128k_tokens": 0, "input_cost_per_video_per_second_above_128k_tokens": 0, "input_cost_per_audio_per_second_above_128k_tokens": 0, "output_cost_per_token": 0, "output_cost_per_character": 0, "output_cost_per_token_above_128k_tokens": 0, "output_cost_per_character_above_128k_tokens": 0, "litellm_provider": "openrouter", "mode": "chat", "supports_system_messages": true, "supports_function_calling": true, "supports_vision": true, "supports_audio_input": true, "supports_video_input": true, "supports_pdf_input": true, "supports_response_schema": true, "supports_tool_choice": true, "source": "https://cloud.google.com/vertex-ai/generative-ai/pricing"}, "openrouter/x-ai/grok-3-beta": {"max_tokens": 131072, "max_input_tokens": 131072, "max_output_tokens": 131072, "input_cost_per_token": 3e-06, "output_cost_per_token": 1.5e-05, "litellm_provider": "openrouter", "mode": "chat"}, "openrouter/x-ai/grok-3-mini-beta": {"max_tokens": 131072, "max_input_tokens": 131072, "max_output_tokens": 131072, "input_cost_per_token": 3e-07, "output_cost_per_token": 5e-07, "litellm_provider": "openrouter", "mode": "chat"}, "openrouter/x-ai/grok-3-fast-beta": {"max_tokens": 131072, "max_input_tokens": 131072, "max_output_tokens": 131072, "input_cost_per_token": 5e-06, "output_cost_per_token": 2.5e-05, "litellm_provider": "openrouter", "mode": "chat"}, "openrouter/x-ai/grok-3-mini-fast-beta": {"max_tokens": 131072, "max_input_tokens": 131072, "max_output_tokens": 131072, "input_cost_per_token": 6e-07, "output_cost_per_token": 4e-06, "litellm_provider": "openrouter", "mode": "chat"}, "openrouter/google/gemini-2.0-flash-exp:free": {"max_tokens": 8192, "max_input_tokens": 1048576, "max_output_tokens": 8192, "max_images_per_prompt": 3000, "max_videos_per_prompt": 10, "max_video_length": 1, "max_audio_length_hours": 8.4, "max_audio_per_prompt": 1, "max_pdf_size_mb": 30, "litellm_provider": "openrouter", "mode": "chat", "supports_system_messages": true, "supports_function_calling": true, "supports_vision": true, "supports_response_schema": true, "supports_audio_output": true, "supports_tool_choice": true}}