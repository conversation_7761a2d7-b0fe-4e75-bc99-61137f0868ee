#!/usr/bin/env python3
"""
Comprehensive cache analysis and splitting script.
Analyzes all FBAdArchive items against existing caches using both lowercase and uppercase field processing.
Creates separate cache files and detailed documentation.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

import pickle
import hashlib
import boto3
from typing import Dict, Any, List, Tuple, Set
from tqdm import tqdm
import json

def load_all_ads():
    """Load ALL ads from FBAdArchive."""
    print("Loading ALL ads from FBAdArchive...")
    
    dynamodb = boto3.resource(
        'dynamodb',
        endpoint_url="http://localhost:8000",
        region_name='us-west-2',
        aws_access_key_id='fakeMyKeyId',
        aws_secret_access_key='fakeSecretAccessKey'
    )
    
    table = dynamodb.Table("FBAdArchive")
    
    all_ads = []
    scan_kwargs = {}
    
    while True:
        response = table.scan(**scan_kwargs)
        batch = response['Items']
        all_ads.extend(batch)
        
        if len(all_ads) % 10000 == 0:
            print(f"  Loaded {len(all_ads):,} ads...")
        
        if 'LastEvaluatedKey' not in response:
            break
        scan_kwargs['ExclusiveStartKey'] = response['LastEvaluatedKey']
    
    print(f"Total ads loaded: {len(all_ads):,}")
    return all_ads

def load_existing_caches():
    """Load existing cache files."""
    print("Loading existing cache files...")
    
    # Load NER cache
    ner_cache_file = "ner_results_cache.pkl"
    if os.path.exists(ner_cache_file):
        with open(ner_cache_file, 'rb') as f:
            ner_cache = pickle.load(f)
        print(f"  NER cache: {len(ner_cache):,} entries")
    else:
        ner_cache = {}
        print("  NER cache: Not found")
    
    # Load embedding cache
    embedding_cache_file = "embedding_cache_roberta-large-v1.pkl"
    if os.path.exists(embedding_cache_file):
        with open(embedding_cache_file, 'rb') as f:
            embedding_cache = pickle.load(f)
        print(f"  Embedding cache: {len(embedding_cache):,} entries")
    else:
        embedding_cache = {}
        print("  Embedding cache: Not found")
    
    return ner_cache, embedding_cache

def extract_text_lowercase(ad: Dict[str, Any]) -> str:
    """Extract text using LOWERCASE field processing (current classifier logic)."""
    YAML_FIELDS = ["Title", "Body", "Summary", "LinkDescription", "PageName"]
    
    # Convert field names to lowercase
    text_processing_fields_lc = [field.lower() for field in YAML_FIELDS]
    
    # Convert ad data keys to lowercase for matching
    ad_data_lower_keys = {k.lower(): v for k, v in ad.items()}
    
    text_parts = []
    for field_name_lc in text_processing_fields_lc:
        value = ad_data_lower_keys.get(field_name_lc)
        
        if value is None:
            continue
        
        # Convert to string and strip
        value_str = str(value).strip()
        
        if not value_str:
            continue
        
        text_parts.append(value_str)
    
    return ' '.join(text_parts) if text_parts else ""

def extract_text_uppercase(ad: Dict[str, Any]) -> str:
    """Extract text using UPPERCASE field processing (original YAML capitalization)."""
    YAML_FIELDS = ["Title", "Body", "Summary", "LinkDescription", "PageName"]
    
    text_parts = []
    for field_name in YAML_FIELDS:
        value = ad.get(field_name)
        
        if value is None:
            continue
        
        # Convert to string and strip
        value_str = str(value).strip()
        
        if not value_str:
            continue
        
        text_parts.append(value_str)
    
    return ' '.join(text_parts) if text_parts else ""

def analyze_ner_cache_matches(ads: List[Dict[str, Any]], ner_cache: Dict[str, Any]) -> Tuple[Dict[str, Any], Dict[str, Any], Dict[str, Any]]:
    """Analyze NER cache matches for both lowercase and uppercase processing."""
    print(f"\nAnalyzing NER cache matches for {len(ads):,} ads...")
    
    # Results tracking
    lowercase_matches = {}
    uppercase_matches = {}
    
    lowercase_stats = {
        'total_ads': len(ads),
        'ads_with_text': 0,
        'composite_hits': 0,
        'raw_text_hits': 0,
        'total_hits': 0,
        'misses': 0
    }
    
    uppercase_stats = {
        'total_ads': len(ads),
        'ads_with_text': 0,
        'composite_hits': 0,
        'raw_text_hits': 0,
        'total_hits': 0,
        'misses': 0
    }
    
    for ad in tqdm(ads, desc="Analyzing NER cache"):
        ad_id = ad.get('AdArchiveID', ad.get('ad_id', 'unknown'))
        
        # Test lowercase processing
        text_lowercase = extract_text_lowercase(ad)
        if text_lowercase:
            lowercase_stats['ads_with_text'] += 1
            
            # Generate cache keys for lowercase
            text_hash_lc = hashlib.md5(text_lowercase.encode()).hexdigest()
            composite_key_lc = f"ner_{ad_id}_{text_hash_lc}"
            raw_text_key_lc = text_lowercase
            
            # Check lowercase matches
            if composite_key_lc in ner_cache:
                lowercase_matches[composite_key_lc] = ner_cache[composite_key_lc]
                lowercase_stats['composite_hits'] += 1
                lowercase_stats['total_hits'] += 1
            elif raw_text_key_lc in ner_cache:
                lowercase_matches[raw_text_key_lc] = ner_cache[raw_text_key_lc]
                lowercase_stats['raw_text_hits'] += 1
                lowercase_stats['total_hits'] += 1
            else:
                lowercase_stats['misses'] += 1
        
        # Test uppercase processing
        text_uppercase = extract_text_uppercase(ad)
        if text_uppercase:
            uppercase_stats['ads_with_text'] += 1
            
            # Generate cache keys for uppercase
            text_hash_uc = hashlib.md5(text_uppercase.encode()).hexdigest()
            composite_key_uc = f"ner_{ad_id}_{text_hash_uc}"
            raw_text_key_uc = text_uppercase
            
            # Check uppercase matches
            if composite_key_uc in ner_cache:
                uppercase_matches[composite_key_uc] = ner_cache[composite_key_uc]
                uppercase_stats['composite_hits'] += 1
                uppercase_stats['total_hits'] += 1
            elif raw_text_key_uc in ner_cache:
                uppercase_matches[raw_text_key_uc] = ner_cache[raw_text_key_uc]
                uppercase_stats['raw_text_hits'] += 1
                uppercase_stats['total_hits'] += 1
            else:
                uppercase_stats['misses'] += 1
    
    # Calculate hit rates
    lowercase_stats['hit_rate'] = (lowercase_stats['total_hits'] / lowercase_stats['ads_with_text'] * 100) if lowercase_stats['ads_with_text'] > 0 else 0
    uppercase_stats['hit_rate'] = (uppercase_stats['total_hits'] / uppercase_stats['ads_with_text'] * 100) if uppercase_stats['ads_with_text'] > 0 else 0
    
    return lowercase_matches, uppercase_matches, {'lowercase': lowercase_stats, 'uppercase': uppercase_stats}

def analyze_embedding_cache_matches(ads: List[Dict[str, Any]], embedding_cache: Dict[str, Any]) -> Tuple[Dict[str, Any], Dict[str, Any], Dict[str, Any]]:
    """Analyze embedding cache matches for both lowercase and uppercase processing."""
    print(f"\nAnalyzing embedding cache matches for {len(ads):,} ads...")
    
    # Results tracking
    lowercase_matches = {}
    uppercase_matches = {}
    
    lowercase_stats = {
        'total_ads': len(ads),
        'ads_with_text': 0,
        'hits': 0,
        'misses': 0
    }
    
    uppercase_stats = {
        'total_ads': len(ads),
        'ads_with_text': 0,
        'hits': 0,
        'misses': 0
    }
    
    # Determine cache key format (hash vs text)
    sample_keys = list(embedding_cache.keys())[:10]
    all_keys_are_hashes = all(len(str(key)) == 32 and all(c in '0123456789abcdef' for c in str(key).lower()) for key in sample_keys)
    
    for ad in tqdm(ads, desc="Analyzing embedding cache"):
        # Test lowercase processing
        text_lowercase = extract_text_lowercase(ad)
        if text_lowercase:
            lowercase_stats['ads_with_text'] += 1
            
            # Check lowercase matches
            if all_keys_are_hashes:
                # Cache uses hashed keys
                text_hash_lc = hashlib.md5(text_lowercase.encode()).hexdigest()
                if text_hash_lc in embedding_cache:
                    lowercase_matches[text_hash_lc] = embedding_cache[text_hash_lc]
                    lowercase_stats['hits'] += 1
                else:
                    lowercase_stats['misses'] += 1
            else:
                # Cache uses text keys
                if text_lowercase in embedding_cache:
                    lowercase_matches[text_lowercase] = embedding_cache[text_lowercase]
                    lowercase_stats['hits'] += 1
                else:
                    lowercase_stats['misses'] += 1
        
        # Test uppercase processing
        text_uppercase = extract_text_uppercase(ad)
        if text_uppercase:
            uppercase_stats['ads_with_text'] += 1
            
            # Check uppercase matches
            if all_keys_are_hashes:
                # Cache uses hashed keys
                text_hash_uc = hashlib.md5(text_uppercase.encode()).hexdigest()
                if text_hash_uc in embedding_cache:
                    uppercase_matches[text_hash_uc] = embedding_cache[text_hash_uc]
                    uppercase_stats['hits'] += 1
                else:
                    uppercase_stats['misses'] += 1
            else:
                # Cache uses text keys
                if text_uppercase in embedding_cache:
                    uppercase_matches[text_uppercase] = embedding_cache[text_uppercase]
                    uppercase_stats['hits'] += 1
                else:
                    uppercase_stats['misses'] += 1
    
    # Calculate hit rates
    lowercase_stats['hit_rate'] = (lowercase_stats['hits'] / lowercase_stats['ads_with_text'] * 100) if lowercase_stats['ads_with_text'] > 0 else 0
    uppercase_stats['hit_rate'] = (uppercase_stats['hits'] / uppercase_stats['ads_with_text'] * 100) if uppercase_stats['ads_with_text'] > 0 else 0
    lowercase_stats['cache_key_format'] = 'hash' if all_keys_are_hashes else 'text'
    uppercase_stats['cache_key_format'] = 'hash' if all_keys_are_hashes else 'text'
    
    return lowercase_matches, uppercase_matches, {'lowercase': lowercase_stats, 'uppercase': uppercase_stats}

def save_cache_files(ner_lowercase, ner_uppercase, embedding_lowercase, embedding_uppercase):
    """Save the split cache files."""
    print("\nSaving split cache files...")
    
    # Save NER caches
    with open("ner_cache_lowercase.pkl", 'wb') as f:
        pickle.dump(ner_lowercase, f)
    print(f"  Saved ner_cache_lowercase.pkl: {len(ner_lowercase):,} entries")
    
    with open("ner_cache_uppercase.pkl", 'wb') as f:
        pickle.dump(ner_uppercase, f)
    print(f"  Saved ner_cache_uppercase.pkl: {len(ner_uppercase):,} entries")
    
    # Save embedding caches
    with open("embedding_cache_roberta_lowercase.pkl", 'wb') as f:
        pickle.dump(embedding_lowercase, f)
    print(f"  Saved embedding_cache_roberta_lowercase.pkl: {len(embedding_lowercase):,} entries")
    
    with open("embedding_cache_roberta_uppercase.pkl", 'wb') as f:
        pickle.dump(embedding_uppercase, f)
    print(f"  Saved embedding_cache_roberta_uppercase.pkl: {len(embedding_uppercase):,} entries")

def generate_documentation(ner_stats, embedding_stats):
    """Generate comprehensive documentation."""
    print("\nGenerating documentation...")
    
    doc_content = f"""# Cache Analysis and Structure Documentation

## Overview
This document describes the cache key generation, structure, and analysis results for the FBAdArchive processing system.

## Field Processing Methods

### Lowercase Field Processing (Current Classifier Logic)
```python
YAML_FIELDS = ["Title", "Body", "Summary", "LinkDescription", "PageName"]

# Step 1: Convert field names to lowercase
text_processing_fields_lc = [field.lower() for field in YAML_FIELDS]
# Result: ["title", "body", "summary", "linkdescription", "pagename"]

# Step 2: Convert ad data keys to lowercase for matching
ad_data_lower_keys = {{k.lower(): v for k, v in ad.items()}}

# Step 3: Extract values using lowercase field names
text_parts = []
for field_name_lc in text_processing_fields_lc:
    value = ad_data_lower_keys.get(field_name_lc)
    if value is not None and str(value).strip():
        text_parts.append(str(value).strip())

# Step 4: Join text parts
combined_text = ' '.join(text_parts)
```

### Uppercase Field Processing (Original YAML Capitalization)
```python
YAML_FIELDS = ["Title", "Body", "Summary", "LinkDescription", "PageName"]

# Step 1: Use field names as-is (with original capitalization)
# Step 2: Extract values using exact field names
text_parts = []
for field_name in YAML_FIELDS:
    value = ad.get(field_name)
    if value is not None and str(value).strip():
        text_parts.append(str(value).strip())

# Step 3: Join text parts
combined_text = ' '.join(text_parts)
```

## Cache Key Generation

### NER Cache Keys

#### Composite Key Format
```python
ad_id = ad.get('AdArchiveID', ad.get('ad_id', 'unknown'))
text_hash = hashlib.md5(combined_text.encode()).hexdigest()
composite_key = f"ner_{{ad_id}}_{{text_hash}}"
```

**Example:**
- Ad ID: `614697441055080`
- Combined text: `"FREE and EASY - sign up in 1 minute! People who used Roundup..."`
- Text hash: `54a5560afbfb501ab5db7d765907c962`
- Composite key: `ner_614697441055080_54a5560afbfb501ab5db7d765907c962`

#### Raw Text Key Format
```python
raw_text_key = combined_text
```

**Example:**
- Raw text key: `"FREE and EASY - sign up in 1 minute! People who used Roundup products may be eligible..."`

### Embedding Cache Keys

#### Hash Key Format (Current)
```python
text_hash = hashlib.md5(combined_text.encode()).hexdigest()
cache_key = text_hash
```

**Example:**
- Combined text: `"FREE and EASY - sign up in 1 minute! People who used Roundup..."`
- Cache key: `54a5560afbfb501ab5db7d765907c962`

## Analysis Results

### NER Cache Analysis
- **Original cache size**: {len(pickle.load(open("ner_results_cache.pkl", 'rb')))} entries
- **Lowercase matches**: {len(pickle.load(open("ner_cache_lowercase.pkl", 'rb')))} entries ({ner_stats['lowercase']['hit_rate']:.2f}% of FBAdArchive)
- **Uppercase matches**: {len(pickle.load(open("ner_cache_uppercase.pkl", 'rb')))} entries ({ner_stats['uppercase']['hit_rate']:.2f}% of FBAdArchive)

#### Lowercase Processing Stats
- Total ads processed: {ner_stats['lowercase']['total_ads']:,}
- Ads with text: {ner_stats['lowercase']['ads_with_text']:,}
- Composite key hits: {ner_stats['lowercase']['composite_hits']:,}
- Raw text hits: {ner_stats['lowercase']['raw_text_hits']:,}
- Total hits: {ner_stats['lowercase']['total_hits']:,}
- Misses: {ner_stats['lowercase']['misses']:,}
- **Hit rate: {ner_stats['lowercase']['hit_rate']:.2f}%**

#### Uppercase Processing Stats
- Total ads processed: {ner_stats['uppercase']['total_ads']:,}
- Ads with text: {ner_stats['uppercase']['ads_with_text']:,}
- Composite key hits: {ner_stats['uppercase']['composite_hits']:,}
- Raw text hits: {ner_stats['uppercase']['raw_text_hits']:,}
- Total hits: {ner_stats['uppercase']['total_hits']:,}
- Misses: {ner_stats['uppercase']['misses']:,}
- **Hit rate: {ner_stats['uppercase']['hit_rate']:.2f}%**

### Embedding Cache Analysis
- **Original cache size**: {len(pickle.load(open("embedding_cache_roberta-large-v1.pkl", 'rb')))} entries
- **Cache key format**: {embedding_stats['lowercase']['cache_key_format']}
- **Lowercase matches**: {len(pickle.load(open("embedding_cache_roberta_lowercase.pkl", 'rb')))} entries ({embedding_stats['lowercase']['hit_rate']:.2f}% of FBAdArchive)
- **Uppercase matches**: {len(pickle.load(open("embedding_cache_roberta_uppercase.pkl", 'rb')))} entries ({embedding_stats['uppercase']['hit_rate']:.2f}% of FBAdArchive)

#### Lowercase Processing Stats
- Total ads processed: {embedding_stats['lowercase']['total_ads']:,}
- Ads with text: {embedding_stats['lowercase']['ads_with_text']:,}
- Hits: {embedding_stats['lowercase']['hits']:,}
- Misses: {embedding_stats['lowercase']['misses']:,}
- **Hit rate: {embedding_stats['lowercase']['hit_rate']:.2f}%**

#### Uppercase Processing Stats
- Total ads processed: {embedding_stats['uppercase']['total_ads']:,}
- Ads with text: {embedding_stats['uppercase']['ads_with_text']:,}
- Hits: {embedding_stats['uppercase']['hits']:,}
- Misses: {embedding_stats['uppercase']['misses']:,}
- **Hit rate: {embedding_stats['uppercase']['hit_rate']:.2f}%**

## Key Differences Between Processing Methods

### Text Extraction Differences
1. **Field Name Casing**:
   - Lowercase: Uses `["title", "body", "summary", "linkdescription", "pagename"]`
   - Uppercase: Uses `["Title", "Body", "Summary", "LinkDescription", "PageName"]`

2. **Data Key Conversion**:
   - Lowercase: Converts all DynamoDB field names to lowercase before lookup
   - Uppercase: Uses original DynamoDB field names (capitalized)

3. **Text Processing**:
   - Both methods use identical string processing (strip, join with spaces)
   - Both use the same MD5 hashing algorithm

### Impact on Cache Keys
- Different text extraction → Different combined text → Different MD5 hash → Different cache key
- This explains why the same logical content can have different cache entries

## Recommendations

### For Consistent Cache Usage
1. **Choose one processing method** and stick with it consistently
2. **Regenerate caches** if switching methods to avoid duplicate entries
3. **Document the chosen method** clearly for future development

### For Current State
- **Lowercase processing**: {ner_stats['lowercase']['hit_rate']:.1f}% NER coverage, {embedding_stats['lowercase']['hit_rate']:.1f}% embedding coverage
- **Uppercase processing**: {ner_stats['uppercase']['hit_rate']:.1f}% NER coverage, {embedding_stats['uppercase']['hit_rate']:.1f}% embedding coverage

## File Outputs
- `ner_cache_lowercase.pkl`: NER cache entries matching lowercase field processing
- `ner_cache_uppercase.pkl`: NER cache entries matching uppercase field processing  
- `embedding_cache_roberta_lowercase.pkl`: Embedding cache entries matching lowercase field processing
- `embedding_cache_roberta_uppercase.pkl`: Embedding cache entries matching uppercase field processing
"""

    with open("cache_analysis_documentation.md", 'w') as f:
        f.write(doc_content)
    
    print("  Saved cache_analysis_documentation.md")

def print_summary_stats(ner_stats, embedding_stats):
    """Print summary statistics."""
    print(f"\n{'='*80}")
    print("SUMMARY STATISTICS")
    print(f"{'='*80}")
    
    print(f"\nNER Cache Coverage:")
    print(f"  ner_cache_lowercase.pkl: {ner_stats['lowercase']['hit_rate']:.2f}%")
    print(f"  ner_cache_uppercase.pkl: {ner_stats['uppercase']['hit_rate']:.2f}%")
    
    print(f"\nEmbedding Cache Coverage:")
    print(f"  embedding_cache_roberta_lowercase.pkl: {embedding_stats['lowercase']['hit_rate']:.2f}%")
    print(f"  embedding_cache_roberta_uppercase.pkl: {embedding_stats['uppercase']['hit_rate']:.2f}%")
    
    # Determine which method has better coverage
    ner_better = "lowercase" if ner_stats['lowercase']['hit_rate'] > ner_stats['uppercase']['hit_rate'] else "uppercase"
    embedding_better = "lowercase" if embedding_stats['lowercase']['hit_rate'] > embedding_stats['uppercase']['hit_rate'] else "uppercase"
    
    print(f"\nBest Coverage:")
    print(f"  NER: {ner_better} processing ({ner_stats[ner_better]['hit_rate']:.2f}%)")
    print(f"  Embedding: {embedding_better} processing ({embedding_stats[embedding_better]['hit_rate']:.2f}%)")

def main():
    print("COMPREHENSIVE CACHE ANALYSIS AND SPLITTING")
    print("=" * 80)
    
    # Load all data
    ads = load_all_ads()
    ner_cache, embedding_cache = load_existing_caches()
    
    if not ads:
        print("❌ No ads loaded")
        return
    
    if not ner_cache and not embedding_cache:
        print("❌ No caches loaded")
        return
    
    # Analyze NER cache matches
    ner_lowercase, ner_uppercase, ner_stats = analyze_ner_cache_matches(ads, ner_cache)
    
    # Analyze embedding cache matches  
    embedding_lowercase, embedding_uppercase, embedding_stats = analyze_embedding_cache_matches(ads, embedding_cache)
    
    # Save split cache files
    save_cache_files(ner_lowercase, ner_uppercase, embedding_lowercase, embedding_uppercase)
    
    # Generate documentation
    generate_documentation(ner_stats, embedding_stats)
    
    # Print summary
    print_summary_stats(ner_stats, embedding_stats)

if __name__ == "__main__":
    main()