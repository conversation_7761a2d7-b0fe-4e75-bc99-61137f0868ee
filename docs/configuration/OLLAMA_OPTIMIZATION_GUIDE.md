# Ollama Optimization Guide for llama3.2-vision

## Realistic Performance Expectations

The llama3.2-vision models are **large multimodal models** that process both text and images. They are NOT optimized for speed but for accuracy. Realistic processing times:

- **llama3.2-vision:11b-instruct-q4_K_M**: 20-60 seconds per image
- **llama32-vision-ocr (optimized)**: 15-40 seconds per image
- Factors affecting speed: Image size, text complexity, GPU/CPU, memory bandwidth

## Optimization Steps Implemented

### 1. Created Optimized Model
```bash
ollama create llama32-vision-ocr -f Modelfile.llama32-vision-optimized
```

Key optimizations:
- Reduced context window: `num_ctx 2048` (from default 4096)
- Limited output tokens: `num_predict 512`
- Lower temperature: `temperature 0.1`
- Focused system prompt for OCR tasks

### 2. Configuration Updates
- Increased timeout to 3600 seconds (1 hour)
- Set concurrency to 1 (serial processing)
- Adjusted batch sizes to 10-25 items

### 3. Environment Setup
```bash
# For M4 Mac with unified memory
export OLLAMA_NUM_PARALLEL=1
export OLLAMA_KEEP_ALIVE=-1
export OLLAMA_MAX_LOADED_MODELS=1
ollama serve
```

## Usage Recommendations

### 1. Use Deferred Processing (Strongly Recommended)
```bash
# Scrape without image processing
python -m src.lib.fb_ads.orchestrator --defer-image-processing

# Process images later when you have time
python -m src.scripts.process_image_queue --process --date 20250528 --model llama32-vision-ocr --concurrency 1
```

### 2. Monitor Performance
```bash
# Watch Ollama logs
journalctl -u ollama -f

# Or if running in terminal
# Look for timing information in the output
```

### 3. Process in Small Batches
```bash
# Process 50 images at a time
python -m src.scripts.process_image_queue --process --date 20250528 --batch-size 50 --concurrency 1

# Check progress
python -m src.scripts.process_image_queue --show-summary --date 20250528
```

## Alternative Approaches

### 1. Use Cloud GPU Services
- RunPod, Vast.ai, Lambda Labs for faster GPU processing
- Can achieve 5-10x speedup with proper GPUs

### 2. Use Different Models
- **Moondream** (1.8B params): Much faster but less accurate
- **CogVLM**: Good balance of speed and accuracy
- **GPT-4V API**: Fast but expensive

### 3. Preprocessing
- Resize images before processing (max 1024x1024)
- Convert to grayscale for pure text
- Use image enhancement for low quality images

## Performance Monitoring

Check actual processing times:
```sql
sqlite3 data/image_queue/image_queue.db "
SELECT 
  AVG(processing_time_ms/1000.0) as avg_seconds,
  MIN(processing_time_ms/1000.0) as min_seconds,
  MAX(processing_time_ms/1000.0) as max_seconds,
  COUNT(*) as total_processed
FROM image_queue 
WHERE status='processed' 
  AND processing_time_ms IS NOT NULL
  AND scrape_date='20250528';"
```

## Troubleshooting Timeouts

If still getting timeouts:

1. **Increase timeout further**:
   Edit `src/config/ollama/config.json`:
   ```json
   "timeout": 7200,  // 2 hours
   ```

2. **Check Ollama is responding**:
   ```bash
   curl http://localhost:11434/api/tags
   ```

3. **Test with a single image**:
   ```bash
   # Create test script
   echo 'Test image' > test_image.txt
   python -c "
   from src.lib.llava_vision import LlavaImageExtractor
   import asyncio
   extractor = LlavaImageExtractor(model_name='llama32-vision-ocr')
   # Test with small timeout
   "
   ```

4. **Consider CPU vs GPU**:
   - M4 Macs use unified memory
   - Set `num_gpu -1` for full GPU usage
   - Monitor with Activity Monitor

## Expected Timeline

For 280 images at 30 seconds average:
- Total time: ~140 minutes (2.3 hours)
- With breaks/retries: 3-4 hours realistic

This is why deferred processing is crucial - let it run overnight or during downtime.