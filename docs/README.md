# LexGenius Documentation

Welcome to the LexGenius documentation. This directory contains all technical documentation, guides, and references for the LexGenius project.

## 📚 Documentation Structure

### 🎯 [Facebook Ads](./facebook-ads/)
Documentation for Facebook Ad processing, categorization, and analysis.
- [FB Ad Classifier README](./facebook-ads/FB_AD_CLASSIFIER_README.md) - Main classifier documentation
- [Categorizer Documentation](./facebook-ads/fb_ad_categorizer.md) - Detailed categorizer guide
- [Batch Image Hash Update](./facebook-ads/BATCH_FB_IMAGE_HASH_UPDATE.md) - Image hash processing
- [Batch Image Text Extract](./facebook-ads/BATCH_IMAGE_TEXT_EXTRACT.md) - Text extraction from images
- [Local Image Queue](./facebook-ads/LOCAL_IMAGE_QUEUE.md) - Image queue management
- [Quick Start Guide](./facebook-ads/QUICK_START.md) - Getting started quickly
- [Implementation Summary](./facebook-ads/IMPLEMENTATION_SUMMARY.md) - Technical implementation details
- [Data Flows](./facebook-ads/DATA_FLOWS.md) - Data processing workflows
- [Architecture Guides](./facebook-ads/) - Various architecture documentation

### 🔍 [Classification](./classification/)
Campaign classification and hybrid classifier documentation.
- [Campaign Classification Guide](./classification/CAMPAIGN_CLASSIFICATION_GUIDE.md) - Main classification guide
- [Complete Classification Guide](./classification/COMPLETE_CLASSIFICATION_GUIDE.md) - Comprehensive guide
- [Hybrid Classifier Final](./classification/HYBRID_CLASSIFIER_FINAL.md) - Final hybrid classifier design
- [Hybrid Classifier Usage](./classification/HYBRID_CLASSIFIER_USAGE.md) - How to use the hybrid classifier
- [Classifier README](./classification/CLASSIFIER_README.md) - Classifier overview
- [Classification Improvements](./classification/CLASSIFICATION_IMPROVEMENTS.md) - Recent improvements
- [Classifier Results Summary](./classification/CLASSIFIER_RESULTS_SUMMARY.md) - Performance results

### 💻 [Applications](./applications/)
User interface and application documentation.
- [Streamlit App Guide](./applications/STREAMLIT_APP_GUIDE.md) - Main Streamlit application guide
- [Enhanced App Guide](./applications/ENHANCED_APP_GUIDE.md) - Enhanced features guide
- [Final App Guide](./applications/FINAL_APP_GUIDE.md) - Final application documentation
- [Run Simple App](./applications/RUN_SIMPLE_APP.md) - Running the simple version
- [Streamlit Versions](./applications/) - Various Streamlit version guides (V2, V3, Fixed)

### ⚙️ [Configuration](./configuration/)
Setup and configuration documentation.
- [Configuration Guide](./configuration/CONFIGURATION_GUIDE.md) - Main configuration guide
- [Setup MCP](./configuration/setup_mcp.md) - MCP setup instructions
- [Environment Variables](./configuration/envs.md) - Environment configuration
- [Main Parameters](./configuration/main_parameters.md) - Parameter documentation
- [Ollama Optimization](./configuration/OLLAMA_OPTIMIZATION_GUIDE.md) - Ollama optimization guide
- [GraphQL Parser Compatibility](./configuration/graphql_parser_compatibility.md) - GraphQL compatibility

### 🔌 [API & Integration](./api-integration/)
API documentation and integration guides.
- [GPT Refactor Guide](./api-integration/gpt_refactor_guide.md) - GPT integration refactoring
- [Prompts](./api-integration/prompts/) - System and user prompts
  - [FB Ad Categorizer Prompts](./api-integration/fb_ad_categorizer_prompts/) - Categorizer prompts
  - [Post Processor Prompts](./api-integration/prompts/post_processor/) - Post-processing prompts

### 🛠️ [Development](./development/)
Development guides and conventions.
- [Module Structure](./development/MODULE_STRUCTURE.md) - Project module organization
- [Conventions](./development/CONVENTIONS.md) - Coding conventions
- [Transform Refactor Guide](./development/transform_refactor_guide.md) - Transform module refactoring
- [Law Firm Normalization](./development/law_firm_normalization.md) - Law firm name normalization
- [JSON Safety](./development/JSON_SAFETY.md) - JSON handling best practices
- [Reports Documentation](./development/documentation.md) - Reports module documentation

### 📊 [Operations](./operations/)
Operational guides and workflows.
- [Workflow Summary](./operations/WORKFLOW_SUMMARY.md) - Main workflow documentation
- [Weekly Reports](./operations/weekly_reports.md) - Weekly reporting guide
- [Deferred Processing Fix](./operations/DEFERRED_PROCESSING_FIX.md) - Deferred processing solutions
- [Reprocess Failed Usage](./operations/REPROCESS_FAILED_USAGE.md) - Reprocessing failed items
- [Completed Fixes](./operations/COMPLETED_FIXES.md) - List of completed fixes
- [TODO](./operations/TODO.md) - Project TODO list
- [Flowcharts](./operations/) - Process flowcharts and diagrams

### 🔬 [Research](./research/)
Research documentation and analysis tools.
- [Analysis Tools Guide](./research/ANALYSIS_TOOLS_GUIDE.md) - Analysis tools documentation
- [Quick Reference Card](./research/QUICK_REFERENCE_CARD.md) - Quick reference guide
- [AFFF Research](./research/AFFF/) - AFFF-related research
- [Research Prompts](./research/) - AI prompts and research documentation

## 🚀 Getting Started

1. **New to LexGenius?** Start with the [Configuration Guide](./configuration/CONFIGURATION_GUIDE.md)
2. **Working with Facebook Ads?** Check the [FB Ad Classifier README](./facebook-ads/FB_AD_CLASSIFIER_README.md)
3. **Building Applications?** See the [Streamlit App Guide](./applications/STREAMLIT_APP_GUIDE.md)
4. **Contributing?** Review the [Conventions](./development/CONVENTIONS.md) and [Module Structure](./development/MODULE_STRUCTURE.md)

## 📖 Additional Resources

- **Issue Tracking**: Check [TODO](./operations/TODO.md) for current tasks
- **Recent Updates**: See [Completed Fixes](./operations/COMPLETED_FIXES.md)
- **Architecture**: Review flowcharts in the [Operations](./operations/) directory

## 🔍 Finding Documentation

All documentation is organized by topic. If you're looking for specific information:
- Use your IDE's file search to find keywords across all .md files
- Check the relevant category directory based on your topic
- Refer to this README for navigation

## 📝 Documentation Standards

When adding new documentation:
1. Place it in the appropriate category directory
2. Use clear, descriptive filenames
3. Include a header with the document's purpose
4. Update this README if adding a major new document
5. Follow the conventions outlined in [Conventions](./development/CONVENTIONS.md)

---
*Last updated: January 2025*