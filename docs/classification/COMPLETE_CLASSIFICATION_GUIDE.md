# Complete Facebook Ad Classification Guide

A comprehensive end-to-end guide for classifying Facebook ads using the hybrid campaign classifier.

## Table of Contents
1. [Quick Start](#quick-start)
2. [Full Workflow](#full-workflow)
3. [Initial Setup](#initial-setup)
4. [Running Classifications](#running-classifications)
5. [Analyzing Results](#analyzing-results)
6. [Improving Rules](#improving-rules)
7. [Using Streamlit App](#using-streamlit-app)
8. [Production Workflow](#production-workflow)
9. [Troubleshooting](#troubleshooting)

## Quick Start

```bash
# 1. Classify ads with current rules
python src/scripts/hybrid_campaign_classifier_m4.py \
    --limit 1000 \
    --save-results results.csv

# 2. Improve rules based on results
python src/scripts/hybrid_campaign_classifier_m4.py \
    --limit 1000 \
    --improve-rules \
    --save-improved-config improved_config.json

# 3. Test improved rules
python src/scripts/hybrid_campaign_classifier_m4.py \
    --config improved_config.json \
    --limit 100 \
    --save-results test_results.csv

# 4. Apply to all ads and update DynamoDB
python src/scripts/hybrid_campaign_classifier_m4.py \
    --config improved_config.json \
    --update-dynamodb
```

## Full Workflow

### Step 1: Initial Classification

First, run the classifier to see current performance:

```bash
python src/scripts/hybrid_campaign_classifier_m4.py \
    --limit 5000 \
    --save-results initial_results.csv \
    --cache-file embeddings.pkl
```

**What this does:**
- Loads 5000 ads from DynamoDB
- Classifies using current rules + embeddings
- Saves results to CSV for analysis
- Caches embeddings for faster subsequent runs

**Expected output:**
```
✓ Loaded 5000 ads in 3.2 seconds
Computing campaign embeddings...
Processing 3500 ads with valid text content
Classifying ads... ━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 100% 0:00:00
✓ Classified 3500 ads in 12.5 seconds
Speed: 280.0 ads/second

Average Confidence: 0.148
Categories Found: 47
```

### Step 2: Analyze Current Performance

Check what's working and what isn't:

```bash
# View low-confidence classifications
python src/scripts/analyze_classified_ads.py \
    --output analysis.xlsx \
    --min-confidence 0.0 \
    --max-confidence 0.3

# Find misclassifications
python src/scripts/analyze_misclassifications.py initial_results.csv \
    --category "Hologic Biomarker Implant Products Liability"
```

### Step 3: Generate Improved Rules

Use the `--improve-rules` flag to analyze patterns:

```bash
python src/scripts/hybrid_campaign_classifier_m4.py \
    --limit 5000 \
    --improve-rules \
    --save-improved-config improved_config.json \
    --cache-file embeddings.pkl
```

**What this analyzes:**
- Low-confidence classifications (< 0.5)
- Extracts frequent terms from misclassified ads
- Filters out ImageText descriptors
- Suggests new triggers for each category

**Example output:**
```
Suggested Rule Improvements
╭─────────────────────────────────────┬──────────────────────────────────────╮
│ Category                            │ Suggested New Triggers               │
├─────────────────────────────────────┼──────────────────────────────────────┤
│ AFFF Products Liability             │ foam, firefighter, pfas, aqueous     │
│ Depo-Provera Products Liability     │ birth control, injection, shot       │
│ Data Breach                         │ exposed, personal information, hack   │
╰─────────────────────────────────────┴──────────────────────────────────────╯
✓ Saved improved config to improved_config.json
```

### Step 4: Review and Edit Improved Config

Open `improved_config.json` and review the suggestions:

```json
{
  "LitigationName": "AFFF Products Liability",
  "triggers": [
    "afff",
    "firefighting foam",
    "foam",  // New suggestion
    "pfas"   // New suggestion
  ],
  "include": ["firefighters", "foam"],
  "exclude": ["insurance payout"]
}
```

**Manual improvements to make:**
1. Remove overly broad terms
2. Add specific product names
3. Add exclude terms to prevent false positives

### Step 5: Test Improved Rules

Before applying to all data, test on a small sample:

```bash
python src/scripts/hybrid_campaign_classifier_m4.py \
    --config improved_config.json \
    --limit 1000 \
    --save-results test_improved.csv
```

Compare results:
```bash
# See if confidence improved
python src/scripts/merge_classification_results.py test_improved.csv \
    --output comparison.xlsx
```

### Step 6: Apply to Production

Once satisfied with improvements:

```bash
# Classify all ads with improved rules
python src/scripts/hybrid_campaign_classifier_m4.py \
    --config improved_config.json \
    --update-dynamodb \
    --batch-size 200
```

**What `--update-dynamodb` does:**
- Updates each ad record with:
  - `campaign_v2`: New classification
  - `campaign_confidence`: Confidence score
  - `campaign_method`: How it was classified
  - `updated_at`: Timestamp

## Initial Setup

### 1. Environment Setup
```bash
# Activate conda environment
conda activate lexgenius

# Install dependencies if needed
mamba install pytorch sentence-transformers pandas numpy rich boto3 accelerate
```

### 2. Start Local DynamoDB
```bash
# If using Docker
docker run -p 8000:8000 amazon/dynamodb-local

# Or using your local setup
# Make sure it's running on port 8000
```

### 3. Verify Configuration
```bash
# Check current campaign rules
cat src/config/fb_ad_categorizer/campaign_config.json | jq '.[0:3]'

# Check DynamoDB connection
python src/scripts/inspect_dynamodb_schema.py
```

## Running Classifications

### Basic Classification
```bash
python src/scripts/hybrid_campaign_classifier_m4.py
```

### With All Options
```bash
python src/scripts/hybrid_campaign_classifier_m4.py \
    --config src/config/fb_ad_categorizer/campaign_config.json \
    --limit 10000 \
    --batch-size 200 \
    --save-results results_$(date +%Y%m%d).csv \
    --update-dynamodb \
    --improve-rules \
    --save-improved-config improved_$(date +%Y%m%d).json \
    --cache-file fb_embeddings.pkl \
    --use-llm \
    --llm-backend mlx
```

### Option Explanations

| Option | Purpose | Default |
|--------|---------|---------|
| `--config` | Campaign rules JSON file | campaign_config.json |
| `--limit` | Number of ads to process | No limit |
| `--batch-size` | Processing batch size | 100 |
| `--save-results` | Save classifications to CSV | None |
| `--update-dynamodb` | Write results back to DB | False |
| `--improve-rules` | Analyze and suggest improvements | False |
| `--save-improved-config` | Save improved rules | None |
| `--cache-file` | Embedding cache file | embedding_cache.pkl |
| `--use-llm` | Use local LLM for hard cases | False |
| `--llm-backend` | LLM backend (mlx/llama_cpp) | mlx |

## Analyzing Results

### 1. Export Unique Ads (Smaller Files)
```bash
# Export only unique Title/Body combinations
python src/scripts/export_unique_ads.py \
    --limit 10000 \
    --output unique_ads.csv
```

### 2. Analyze Classifications
```bash
# Generate detailed analysis
python src/scripts/analyze_classified_ads.py \
    --output full_analysis.xlsx \
    --export-review low_confidence_review.xlsx
```

### 3. Find Specific Issues
```bash
# Why are kidney cancer ads classified as Hologic?
python src/scripts/analyze_misclassifications.py results.csv \
    --category "Hologic Biomarker Implant Products Liability"

# Find AFFF ads classified as Other
python src/scripts/analyze_misclassifications.py results.csv \
    --find-misclassified "AFFF Products Liability" "Other"
```

## Improving Rules

### Automatic Improvement Process

1. **Run with --improve-rules**
   ```bash
   python src/scripts/hybrid_campaign_classifier_m4.py \
       --limit 5000 \
       --improve-rules \
       --save-improved-config auto_improved.json
   ```

2. **What it analyzes:**
   - Ads with confidence < 0.5
   - Extracts frequent terms (ignoring descriptors)
   - Prioritizes terms related to category name
   - Suggests top 5-10 new triggers per category

3. **Review suggestions:**
   - ✅ Good: "depo-provera", "kidney cancer", "data breach"
   - ❌ Bad: "visible", "graphic", "text", "appears"

### Manual Rule Improvement

1. **Fix common issues:**
   ```bash
   python src/scripts/fix_campaign_rules.py \
       campaign_config.json \
       --output fixed_config.json
   ```

2. **What it fixes:**
   - Adds include/exclude rules
   - Prevents cross-contamination
   - Adds missing product variations

### Advanced Rule Analysis

```bash
# Use improved analyzer for better suggestions
python src/scripts/improved_rule_analyzer.py \
    --config campaign_config.json \
    --results results.csv \
    --output smart_improved.json
```

## Using Streamlit App

### 1. Prepare Data
```bash
# Export manageable file
python src/scripts/export_unique_ads.py \
    --limit 5000 \
    --output review_batch.csv
```

### 2. Launch App
```bash
streamlit run src/streamlit_app/campaign_classifier_app.py
```

### 3. Workflow in App
1. **Upload** review_batch.csv
2. **Filter** to confidence < 0.5
3. **Browse** and recategorize ads
4. **Generate** rules for patterns you see
5. **Export** improved configuration

### 4. Apply Changes
```bash
# Use exported rules from app
python src/scripts/hybrid_campaign_classifier_m4.py \
    --config streamlit_improved_rules.json \
    --update-dynamodb
```

## Production Workflow

### Daily Classification
```bash
#!/bin/bash
# daily_classify.sh

DATE=$(date +%Y%m%d)
CONFIG="src/config/fb_ad_categorizer/campaign_config.json"

# Run classification
python src/scripts/hybrid_campaign_classifier_m4.py \
    --config $CONFIG \
    --save-results daily_results_$DATE.csv \
    --update-dynamodb \
    --cache-file production_embeddings.pkl

# Generate report
python src/scripts/analyze_classified_ads.py \
    --output daily_report_$DATE.xlsx \
    --min-confidence 0.0 \
    --max-confidence 0.3

# Email notification
echo "Classification complete. Low confidence: $(grep -c ',0\.[0-2]' daily_results_$DATE.csv)" | mail -s "Daily Classification Report" <EMAIL>
```

### Weekly Rule Improvement
```bash
#!/bin/bash
# weekly_improve.sh

# Aggregate week's data
cat daily_results_*.csv > weekly_results.csv

# Generate improvements
python src/scripts/hybrid_campaign_classifier_m4.py \
    --improve-rules \
    --save-improved-config weekly_improved_$(date +%Y%m%d).json

# Test improvements
python src/scripts/hybrid_campaign_classifier_m4.py \
    --config weekly_improved_$(date +%Y%m%d).json \
    --limit 1000 \
    --save-results test_weekly.csv

# Manual review checkpoint
echo "Review improved rules before applying to production"
```

## Troubleshooting

### Common Issues

1. **"No module named 'torch'"**
   ```bash
   conda activate lexgenius
   mamba install pytorch
   ```

2. **"DynamoDB connection refused"**
   ```bash
   # Start local DynamoDB
   docker run -p 8000:8000 amazon/dynamodb-local
   ```

3. **"KeyError: 'title'"**
   - DynamoDB uses Title/Body (capitalized)
   - The scripts handle both cases automatically

4. **Low confidence scores**
   - Normal for first run (avg ~0.15)
   - Improves with rule refinement
   - Use --improve-rules regularly

5. **Memory issues**
   ```bash
   # Reduce batch size
   --batch-size 50
   
   # Process in chunks
   --limit 1000
   ```

### Performance Tips

1. **Use embedding cache**
   - Always specify --cache-file
   - Speeds up subsequent runs by 10x

2. **Optimize batch size**
   - M4 Mac: 200-500 works well
   - Adjust based on memory usage

3. **Parallel processing**
   - The loader uses all CPU cores
   - Don't run multiple instances

### Validation

After updates, verify:
```bash
# Check a few specific ads
python -c "
import boto3
dynamodb = boto3.resource('dynamodb', endpoint_url='http://localhost:8000')
table = dynamodb.Table('FBAdArchive')
response = table.get_item(Key={'AdArchiveID': 'YOUR_AD_ID'})
print(response['Item'].get('campaign_v2'))
print(response['Item'].get('campaign_confidence'))
"
```

## Best Practices

1. **Iterative Improvement**
   - Start with small batches (1000 ads)
   - Improve rules incrementally
   - Test before full deployment

2. **Focus on High-Impact**
   - Prioritize high-duplicate ads
   - Fix low-confidence categories first
   - Add specific product names

3. **Monitor Metrics**
   - Track average confidence over time
   - Monitor "Other" category percentage
   - Review new patterns weekly

4. **Document Changes**
   - Keep rule change history
   - Note why excludes were added
   - Track performance improvements

This complete workflow will help you continuously improve your campaign classifications!