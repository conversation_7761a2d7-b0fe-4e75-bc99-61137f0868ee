# Report Configuration Documentation

This document explains how to configure the display settings for various sections, sponsorships, and the overall layout of the generated reports (both web and email versions), based on the provided Python code (`config.py`, `renderer.py`, `data_loader.py`).

## 1. Global Section Visibility (`config.py`)

The `ReportConfig` class in `config.py` contains boolean flags that act as master switches for enabling or disabling entire sections of the report. If a flag is set to `False`, the corresponding section will not be loaded, processed, or rendered in either the web or email versions.

**Key Flags:**

* `show_upcoming_hearings`: Set to `True` to display the "Upcoming Hearings" section.
* `show_calendly`: Set to `True` to display the "Book a Demo" (Calendly) section.
* `show_afff_stats`: Set to `True` to display the "AFFF By The Numbers" section.
* `show_special_reports`: Set to `True` to display the "Special Reports" section.
* `show_news`: Set to `True` to display the "Recent JPML Panel & CMOs" (News) section.
* `show_announcements`: Set to `True` to display the "Announcements" section.
* `show_summary`: Set to `True` to display the "Report Summary" section (including the MDL chart).
* `show_litigation`: Set to `True` to display the "Litigation by Tort Report" section.
* `show_filings`: Set to `True` to display the "Detailed Filings Report" section.
* `show_adspy`: Set to `True` to display the "AdSpy Report" section.

**Example:** To hide the "News" section completely from all reports, set `show_news: False` in your configuration dictionary passed to `ReportConfig`.

## 2. Section-Specific Display & Layout (`renderer.py`)

The `ReportRenderer` class, specifically the `_define_section_config()` method, defines the detailed configuration, behavior, and order of each report section. The order of dictionaries in the list returned by this method determines the order in which sections appear in the final report.

**Structure of a Section Definition:**

Each dictionary in the list defines a section with the following key properties:

* `id`: A unique identifier used for HTML elements (e.g., `announcements`).
* `title`: The display title of the section (e.g., "Announcements").
* `show_flag_attr`: The name of the corresponding boolean flag in `ReportConfig` (e.g., `'show_announcements'`). The section only renders if this flag is `True`.
* `data_attr`: (Optional) The key in the `report_data` dictionary holding the data for this section (e.g., `'announcements'`).
* `icon_class`: CSS classes for the section's icon (e.g., `'fas fa-bullhorn'`).
* `web_template_wrapper`: Jinja template used for the section's outer structure *on the web* (e.g., `_section_collapsable_web.html`, `_section_simple_wrapper.html`).
* `web_content_template`: Jinja template used for the section's inner content *on the web* (e.g., `_announcements_content.html`).
* `email_template_type`: Controls how the section renders *in email*. Key options:
    * `'full'`: Renders the complete content, always expanded.
    * `'collapsed'`: Renders only the title/header in the email (content is omitted or hidden). *Note: Email clients generally don't support interactive collapsing.*
    * `'special_summary'`: Used for the main report summary section.
* `email_content_template`: (Optional) Jinja template for the section's content *in email*. Often the same as `web_content_template` if `email_template_type` is `'full'`.
* `web_default_expanded`: A boolean (`True`/`False`) indicating whether a *collapsable* web section should be expanded or collapsed *by default* when the page loads. Users can typically toggle this interactively on the web.
* `sponsorship_slot`: (Optional) Specifies which numbered sponsorship (`sponsorship_1`, `sponsorship_2`, etc.) should be displayed below this section.

**Example: Announcements Section Configuration**

Let's look at the "Announcements" section definition to answer your specific question:

```python
{
    'id': 'announcements',
    'title': 'Announcements',
    'show_flag_attr': 'show_announcements',
    'data_attr': 'announcements',
    'icon_class': 'fas fa-bullhorn',
    'web_template_wrapper': 'includes/sections/_section_collapsable_web.html', # Uses the collapsable web wrapper
    'web_content_template': 'includes/_announcements_content.html',
    'email_template_type': 'full',  # Set to 'full' - means it appears EXPANDED in email
    'email_content_template': 'includes/_announcements_content.html',
    'web_default_expanded': True,  # Set to True - means it appears EXPANDED by default on the web
},
Web Behavior: web_template_wrapper uses a collapsable template, and web_default_expanded is True. This means the section will appear expanded by default when the web report loads, but users can likely collapse/expand it.Email Behavior: email_template_type is set to 'full'. This means the section content will be fully rendered and visible (expanded) in the email version. It will not be collapsed.How to Make Announcements Collapsed in Email:To make the Announcements section appear collapsed in the email (i.e., only show the header), you would need to change its definition in _define_section_config():Modify email_template_type from 'full' to 'collapsed'.Ensure the corresponding email rendering logic in the base Jinja template handles the 'collapsed' type appropriately (e.g., by only rendering the header part of the section).# Modified example for collapsed email announcements
{
    'id': 'announcements',
    'title': 'Announcements',
    'show_flag_attr': 'show_announcements',
    # ... other properties ...
    'email_template_type': 'collapsed', # CHANGED
    # email_content_template might not be needed if collapsed
    'web_default_expanded': True, # Stays expanded on web
},
3. Sponsorship Configuration (data_loader.py)Sponsorships are configured in StaticDataLoader within data_loader.py.Types of Sponsorships:Header Sponsorship (load_general_sponsorships -> header_sponsorship_data):Appears at the top of the report.Controlled by the display: True/False flag within header_sponsorship_data.Uses distinct web_ad_copy and email_ad_copy for different formats.Includes logo_svg_link, background_color, and cta_link.Numbered Sponsorships (load_general_sponsorships -> sponsorship_1, sponsorship_2, etc.):These are inserted below specific sections based on the sponsorship_slot defined in _define_section_config().Controlled by display: True/False within each numbered sponsor's dictionary.Defined using raw html content, allowing flexible formatting. Ensure the HTML is email-client compatible.Litigation-Specific Sponsorships (load_litigation_sponsorships):Displayed within the "Litigation by Tort Report" section, associated with specific MDL numbers.The dictionary is keyed by the MDL number (as a string, e.g., "2738").Each MDL sponsor has:display: True/False: Controls visibility for that specific MDL.logo_svg_link, background_color, cta_link.Separate web_ad_copy and email_ad_copy.The _get_litigation_sponsor_for_template helper in renderer.py retrieves this data based on the MDL number and whether the context is web or email.Configuration Keys for Sponsorships:display: (Boolean) Master switch for the specific sponsorship slot/MDL.logo_svg_link: (String) URL to the sponsor's logo (SVG preferred).background_color: (String) CSS background color for the sponsorship block (e.g., #646cff99).web_ad_copy: (String) HTML/Text copy for the web version.email_ad_copy: (String) HTML/Text copy for the email version.cta_link: (String) URL for the call-to-action button/link.html: (String) Used for numbered sponsors, contains the full HTML block for the sponsorship display.4. Overall LayoutSection Order: Primarily controlled by the order of section dictionaries in the list returned by _define_section_config() in renderer.py.Templates: The overall HTML structure, headers, footers, and how sections are assembled is defined in the Jinja templates, starting with base.html and including various files from includes/. Modifications to these templates will change the fundamental layout.CSS: Styling is handled via CSS, likely linked in base.html and potentially within specific templates. Email styling relies heavily on CSS inlining performed by Premailer in renderer.py and requires careful