# Guide for Refactoring Transform Module to Use MistralOCRService

This guide provides steps for updating the `src/lib/transform` module to use the new `MistralOCRService` instead of direct `mistral_ocr` usage.

## Step 1: Identify Files to Update

Look for files in the transform module that:
- Import `mistral_ocr`
- Use OCR services directly
- Process images or documents via Mistral

## Step 2: Update Imports

Replace existing imports:

```python
# BEFORE
from src.lib.mistral_ocr import extract_text, process_image
# OR
import src.lib.mistral_ocr as ocr

# AFTER
from src.lib.ai.services.vision.ocr_service import MistralOCRService
from src.lib.ai.orchestration.ai_coordinator import AICoordinator
```

## Step 3: Update Initialization Code

Replace direct initialization with the new service pattern:

```python
# BEFORE
ocr_client = mistral_ocr.Client(api_key="your_api_key")

# AFTER
# Option 1: Using AICoordinator (recommended)
config = {
    "mistral_api_key": "your_api_key",
    "vision_service": "mistral"
}
ai_coordinator = AICoordinator(config)
# Use the coordinator for OCR tasks

# Option 2: Direct service initialization (if needed)
config = {
    "mistral_api_key": "your_api_key",
    "mistral_model": "mistral-large-latest"
}
ocr_service = MistralOCRService(config)
await ocr_service.initialize()
```

## Step 4: Update Function Calls

Replace direct function calls with service methods:

```python
# BEFORE
text = mistral_ocr.extract_text(image_data)
# OR
text = ocr_client.process_image(image_data)

# AFTER
# Option 1: Using AICoordinator (recommended)
await ai_coordinator.initialize()
text = await ai_coordinator.extract_image_text(image_data)

# Option 2: Direct service usage
text = await ocr_service.extract_text(image_data)
```

## Step 5: Handle Enhanced Features

The new service offers more capabilities:

```python
# Image analysis with custom prompt
analysis = await ai_coordinator.analyze_image(
    image_data,
    prompt="Describe all the elements in this image and their relationships"
)

# Support for file paths and URLs
text = await ocr_service.extract_text("/path/to/image.jpg")
# OR
text = await ocr_service.extract_text("https://example.com/image.jpg")
```

## Step 6: Error Handling

Update error handling to match the new service:

```python
# BEFORE
try:
    text = mistral_ocr.extract_text(image_data)
except Exception as e:
    logger.error(f"OCR failed: {e}")

# AFTER
try:
    text = await ocr_service.extract_text(image_data)
except AIServiceError as e:
    logger.error(f"OCR service error: {e}")
except Exception as e:
    logger.error(f"Unexpected error during OCR: {e}")
```

## Step 7: Cleanup

Ensure proper cleanup for async resources:

```python
# AFTER
try:
    # Use OCR service...
finally:
    # Cleanup
    await ocr_service.close()
    # OR if using coordinator
    await ai_coordinator.close()
```

## Step 8: Testing

Update any tests to match the new service pattern:

```python
# Mock the new service in tests
@patch("src.lib.ai.services.vision.ocr_service.MistralOCRService")
async def test_ocr_functionality(mock_ocr_service):
    # Set up mock
    mock_instance = AsyncMock()
    mock_instance.extract_text.return_value = "Mocked text"
    mock_ocr_service.return_value = mock_instance
    
    # Test the function that uses OCR
    result = await your_function_that_uses_ocr()
    
    # Verify
    assert mock_instance.extract_text.called
    assert "Mocked text" in result
```

## Example: Complete Refactoring

Here's a complete before and after example:

### Before:

```python
import logging
from src.lib.mistral_ocr import Client as OCRClient

logger = logging.getLogger(__name__)

class DocumentProcessor:
    def __init__(self, api_key):
        self.ocr_client = OCRClient(api_key=api_key)
    
    def process_document(self, image_data):
        try:
            text = self.ocr_client.extract_text(image_data)
            return {
                "success": True,
                "text": text,
                "length": len(text)
            }
        except Exception as e:
            logger.error(f"Document processing failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
```

### After:

```python
import logging
import asyncio
from src.lib.ai.services.vision.ocr_service import MistralOCRService
from src.lib.ai.exceptions import AIServiceError

logger = logging.getLogger(__name__)

class DocumentProcessor:
    def __init__(self, api_key):
        self.config = {
            "mistral_api_key": api_key,
            "mistral_model": "mistral-large-latest"
        }
        self.ocr_service = None
    
    async def initialize(self):
        if not self.ocr_service:
            self.ocr_service = MistralOCRService(self.config)
            await self.ocr_service.initialize()
    
    async def process_document(self, image_data):
        try:
            await self.initialize()
            text = await self.ocr_service.extract_text(image_data)
            return {
                "success": True,
                "text": text,
                "length": len(text)
            }
        except AIServiceError as e:
            logger.error(f"OCR service error: {e}")
            return {
                "success": False,
                "error": str(e)
            }
        except Exception as e:
            logger.error(f"Unexpected error during document processing: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def close(self):
        if self.ocr_service:
            await self.ocr_service.close()
            self.ocr_service = None
    
    # Synchronous wrapper for backwards compatibility
    def process_document_sync(self, image_data):
        loop = asyncio.get_event_loop()
        try:
            return loop.run_until_complete(self.process_document(image_data))
        finally:
            loop.run_until_complete(self.close())
```

This guide provides a comprehensive approach for refactoring the transform module to use the new MistralOCRService.