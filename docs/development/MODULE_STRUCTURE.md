# LexGenius Library - Modular Architecture

This document outlines the modular architecture of the LexGenius library, which has been refactored for better separation of concerns, maintainability, and to eliminate circular dependencies.

## Module Structure

The library is organized into the following modules:

### 1. Core (`src/lib/core/`)
Contains fundamental utilities and services used across the library:
- Configuration management
- File handling
- HTML processing
- Image handling
- PDF extraction
- Post-processing
- Report generation

### 2. Docket Processing (`src/lib/docket/`)
Handles court dockets, case information, and legal documents:
- Docket file management
- Docket processing and validation
- Litigation classification
- PACER integration
- District court managers
- MDL processing

### 3. Facebook Ads (`src/lib/fb_ads/`)
Provides tools for handling Facebook ad data:
- Ad categorization
- Ad classification
- Ad orchestration
- Session management
- API client
- Archive management
- Image utilities

### 4. AI Integrations (`src/lib/ai/`)
AI service integrations:
- DeepSeek interface
- GPT interface
- LLaVA vision processing
- Mistral OCR
- AI orchestration

### 5. Storage (`src/lib/storage/`)
Storage backend interfaces:
- S3 management
- DynamoDB management
- Cloud invalidation

### 6. JPML (`src/lib/jpml/`)
Judicial Panel on Multidistrict Litigation utilities:
- JPML data management
- Statistics reporting
- Filing reconciliation

### 7. Utilities (`src/lib/utils/`)
Common utilities and helpers:
- Anti-fingerprint utilities
- Attorney data management
- Law firm utilities
- Cleanup tools
- Configuration loaders
- Feature flags

### 8. API Clients (`src/lib/clients/`)
External API client implementations:
- DeepSeek client
- GPT client
- Mistral OCR client

## Import Guidelines

To maintain modularity and prevent circular dependencies:

1. Use relative imports when possible:
   ```python
   from .file_handler import FileHandler  # Same directory
   from ..utils.common import try_remove  # Parent directory
   ```

2. Import specific modules rather than entire packages:
   ```python
   from src.lib.utils.common import try_remove  # Good
   from src.lib.utils import *  # Avoid
   ```

3. For fallback imports, use absolute paths:
   ```python
   try:
       from ..storage.s3_manager import S3Manager
   except ImportError:
       from src.lib.storage.s3_manager import S3Manager
   ```

4. Avoid circular dependencies by moving shared functionality to common modules (utils).

## Module Exports

Each module's exports are defined in its `__init__.py` file. The main `src/lib/__init__.py` exports components from all modules, making them available through the `src.lib` namespace.