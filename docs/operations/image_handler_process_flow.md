# Image Handler Process Flow: process_and_upload_ad_image

## Overview
This flowchart documents the decision process for the `process_and_upload_ad_image` method in `src/lib/fb_ads/image_handler.py`. The method handles checking S3 existence, downloading images (with proxy support), processing, uploading, and managing PHash calculations.

## Return Values
- `(key, True)`: File exists on S3
- `(key, False)`: File did NOT exist but was successfully downloaded/uploaded
- `(None, False)`: File did NOT exist and download/upload FAILED
- `(None, None)`: S3 check failed or process was skipped early (e.g., bad URL)
- `(None, check_result)`: Blocked or proxy error during download after check

## Decision Flow

```mermaid
flowchart TD
    Start([Start: process_and_upload_ad_image]) --> ValidateURL{Is image_url valid?<br/>- Not empty<br/>- Is string<br/>- Starts with http}
    
    ValidateURL -->|No| ReturnNoneNone[Return None, None<br/>Invalid URL]
    ValidateURL -->|Yes| BuildS3Key[Build S3 key:<br/>s3_prefix/ad_archive_id/creative_id.jpg]
    
    BuildS3Key --> CheckS3{Check if file exists in S3<br/>s3_manager.file_exists}
    
    CheckS3 -->|S3 Error| S3CheckError[Log S3 ClientError<br/>or Generic Error]
    S3CheckError --> ReturnNoneNone2[Return None, None<br/>S3 check failed]
    
    CheckS3 -->|Exists| CleanupTemp1{Temp file exists?}
    CleanupTemp1 -->|Yes| RemoveTemp1[Remove temp file]
    CleanupTemp1 -->|No| ReturnKeyTrue[Return s3_key, True<br/>File already exists]
    RemoveTemp1 --> ReturnKeyTrue
    
    CheckS3 -->|Not Exists| CheckPHash{Check if PHash<br/>already exists<br/>in FBImageHash table?}
    
    CheckPHash -->|Error| LogPHashError[Log PHash check error<br/>Continue anyway]
    CheckPHash -->|Exists| SetPHashFlag[Set existing_phash_record flag<br/>Skip text extraction if deferred]
    CheckPHash -->|Not Exists| PrepareDownload[Prepare for download]
    LogPHashError --> PrepareDownload
    SetPHashFlag --> PrepareDownload
    
    PrepareDownload --> GetSession{Get session from<br/>session_manager}
    
    GetSession -->|No Session| LogNoSession[Log error:<br/>Session not available]
    LogNoSession --> ReturnNoneResult[Return None, s3_exists_result]
    
    GetSession -->|Session OK| CheckProxy{Is proxy enabled<br/>for image downloads?}
    
    CheckProxy -->|Yes| GetProxySettings{Get proxy settings<br/>from session_manager}
    CheckProxy -->|No| SetupHeaders[Setup headers<br/>and timeout]
    
    GetProxySettings -->|Failed| LogProxyError[Log error:<br/>Proxy required but failed]
    LogProxyError --> ReturnNoneResult2[Return None, s3_exists_result]
    
    GetProxySettings -->|Success| SetupHeaders
    
    SetupHeaders --> DownloadImage[Download image with session.get<br/>headers, timeout, proxies]
    
    DownloadImage --> CheckResponse{Check response}
    
    CheckResponse -->|Proxy Error 407| LogProxyAuth[Log Proxy Auth Error<br/>407 Authentication Required]
    LogProxyAuth --> ReturnNoneResult3[Return None, s3_exists_result]
    
    CheckResponse -->|Blocked/Rate Limited<br/>403, 429, 503| HandleBlock[Handle block/rate limit<br/>via session_manager]
    HandleBlock --> ReturnNoneResult4[Return None, s3_exists_result]
    
    CheckResponse -->|Other HTTP Error| RaiseHTTPError[raise_for_status<br/>triggers exception]
    RaiseHTTPError --> HandleRequestException[Handle RequestException]
    HandleRequestException --> ReturnNoneResult5[Return None, s3_exists_result]
    
    CheckResponse -->|Success 200| ProcessContent[Process downloaded content<br/>Read chunks into BytesIO buffer]
    
    ProcessContent --> CheckContentLength{Content length > 0?}
    
    CheckContentLength -->|No| LogEmptyContent[Log warning:<br/>Empty content]
    LogEmptyContent --> ReturnNoneResult6[Return None, s3_exists_result]
    
    CheckContentLength -->|Yes| LogBandwidth[Log bandwidth usage<br/>via bandwidth_logger]
    
    LogBandwidth --> SaveLocally[Save image to JPEG<br/>_save_image_to_jpeg method]
    
    SaveLocally -->|Failed| LogSaveError[Log error:<br/>Failed to save/convert]
    LogSaveError --> ReturnNoneResult7[Return None, s3_exists_result]
    
    SaveLocally -->|Success| CheckPHashNeeded{Need to calculate PHash?<br/>- hash_manager exists<br/>- saved_locally true<br/>- no existing_phash_record}
    
    CheckPHashNeeded -->|No| SkipPHash[Skip PHash calculation]
    CheckPHashNeeded -->|Yes| CalculatePHash[Calculate PHash<br/>from saved JPEG file]
    
    CalculatePHash -->|Error| LogPHashCalcError[Log PHash calculation error<br/>Continue with upload]
    CalculatePHash -->|Success| StorePHash[Store PHash in DynamoDB<br/>with ImageText empty]
    
    StorePHash -->|Failed| LogPHashStoreWarn[Log warning:<br/>Failed to store PHash]
    StorePHash -->|Success| LogPHashSuccess[Log success:<br/>PHash stored]
    
    SkipPHash --> UploadToS3[Upload to S3<br/>s3_manager.upload_file]
    LogPHashCalcError --> UploadToS3
    LogPHashStoreWarn --> UploadToS3
    LogPHashSuccess --> UploadToS3
    
    UploadToS3 --> CheckUploadResult{Check upload result}
    
    CheckUploadResult -->|Success<br/>uploaded/reuploaded| ReturnKeyFalse[Return s3_key, False<br/>Successfully uploaded new file]
    CheckUploadResult -->|Failed| LogUploadError[Log upload error]
    LogUploadError --> ReturnNoneFalse[Return None, False<br/>Upload failed]
    
    %% Exception handlers
    DownloadImage -.->|IOError| HandleIOError[Handle IOError]
    SaveLocally -.->|Exception| HandleGenericError[Handle Generic Exception]
    HandleIOError --> ReturnNoneResult8[Return None, s3_exists_result]
    HandleGenericError --> ReturnNoneResult9[Return None, s3_exists_result]
    
    %% Cleanup
    ReturnNoneNone --> Cleanup[Finally: Cleanup temp file<br/>if exists]
    ReturnNoneNone2 --> Cleanup
    ReturnKeyTrue --> Cleanup
    ReturnNoneResult --> Cleanup
    ReturnNoneResult2 --> Cleanup
    ReturnNoneResult3 --> Cleanup
    ReturnNoneResult4 --> Cleanup
    ReturnNoneResult5 --> Cleanup
    ReturnNoneResult6 --> Cleanup
    ReturnNoneResult7 --> Cleanup
    ReturnNoneResult8 --> Cleanup
    ReturnNoneResult9 --> Cleanup
    ReturnKeyFalse --> Cleanup
    ReturnNoneFalse --> Cleanup
    
    Cleanup --> End([End])
```

## Key Decision Points

### 1. URL Validation
- Checks if URL is valid (not empty, is string, starts with http)
- Invalid URLs return `(None, None)` immediately

### 2. S3 Existence Check
- Uses `s3_manager.file_exists()` to check if image already exists
- If exists, returns `(s3_key, True)` after cleaning up any temp files
- S3 check errors return `(None, None)`

### 3. PHash Check
- Checks if PHash already exists in FBImageHash table for this ad
- If exists, sets flag to skip text extraction during deferred processing
- Errors are logged but don't stop the process

### 4. Session and Proxy Setup
- Requires valid session from session_manager
- If proxy is enabled, gets authenticated proxy settings
- Missing session or proxy setup failure returns `(None, s3_exists_result)`

### 5. Image Download
- Downloads with configured headers, timeout, and proxies
- Handles multiple failure scenarios:
  - Proxy authentication errors (407)
  - Rate limiting/blocks (403, 429, 503)
  - Other HTTP errors
  - Empty content

### 6. Local Processing
- Saves image as JPEG using `_save_image_to_jpeg()`
- Handles image format conversions (RGBA, P mode, etc.)
- Save failures return `(None, s3_exists_result)`

### 7. PHash Calculation
- Only calculates if:
  - hash_manager exists
  - Image saved successfully
  - No existing PHash record found
- Stores PHash with empty ImageText (filled later by LLaVA)
- PHash errors are logged but don't prevent upload

### 8. S3 Upload
- Uses `s3_manager.upload_file()` 
- Success returns `(s3_key, False)` (didn't exist initially)
- Failure returns `(None, False)`

### 9. Cleanup
- Always removes temporary files in finally block
- Handles cleanup errors gracefully

## Error Handling Patterns

1. **Network Errors**: Return `(None, s3_exists_result)`
2. **Proxy Errors**: Special handling for 407 authentication
3. **Rate Limiting**: Calls `session_manager.handle_block_or_rate_limit()`
4. **File I/O Errors**: Logged with optional verbose stack traces
5. **S3 Errors**: ClientError handled with error code extraction

## PHash Integration

The PHash (perceptual hash) is calculated and stored in the FBImageHash DynamoDB table with:
- `PHash`: The hash value (Partition Key)
- `AdArchiveID`: The ad archive ID (Sort Key)
- `AdCreativeID`: The creative ID
- `S3Key`: The S3 location
- `LastUpdated`: ISO timestamp
- `ImageText`: Empty string (populated later by LLaVA vision model)

This allows for:
- Duplicate image detection across ads
- Deferred text extraction processing
- Image similarity analysis