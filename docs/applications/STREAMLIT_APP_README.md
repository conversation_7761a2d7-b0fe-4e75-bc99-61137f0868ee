# Campaign Classifier Streamlit App

A comprehensive web interface for managing Facebook ad campaign classifications.

## Features

### 🎯 Core Functionality
- **Browse & Categorize**: View and recategorize ads with an intuitive interface
- **Unique Content Only**: Automatically deduplicates Title/Body combinations
- **Batch Operations**: Apply multiple category changes at once
- **Search & Filter**: Find specific ads by content, category, or confidence

### 📊 Analytics Dashboard
- Real-time classification metrics
- Category distribution charts
- Confidence score analysis
- Performance tracking

### 🔧 Rule Management
- Generate rules from categorized data
- Test rules on sample text
- Export rules in JSON format
- Visual rule editor

### 📈 Advanced Features
- Confusion pair detection
- Low-confidence ad identification
- Duplicate content tracking
- Analytics report generation

## Installation

```bash
# Install Streamlit and dependencies
pip install streamlit plotly pandas numpy boto3

# For mamba users
mamba install streamlit plotly pandas numpy boto3
```

## Usage

### 1. Export Unique Ads (Reduce File Size)

First, export only unique Title/Body combinations to reduce file size:

```bash
# Export from DynamoDB (creates smaller file)
python src/scripts/export_unique_ads.py \
    --limit 10000 \
    --output unique_ads.csv

# Filter by confidence
python src/scripts/export_unique_ads.py \
    --min-confidence 0.3 \
    --output medium_confidence_ads.csv

# Export specific category
python src/scripts/export_unique_ads.py \
    --category "AFFF Products Liability" \
    --output afff_ads.csv
```

### 2. Launch Streamlit App

```bash
# Run the app
streamlit run src/streamlit_app/campaign_classifier_app.py

# Or with specific port
streamlit run src/streamlit_app/campaign_classifier_app.py --server.port 8080
```

### 3. Using the App

#### Loading Data
1. **From DynamoDB**: Select limit and click "Load Data"
2. **From CSV**: Upload the exported unique ads file
3. **Sample Data**: Use built-in sample for testing

#### Browsing & Categorizing
1. Use filters to find specific ads
2. Expand ads to see full content
3. Change category using dropdown
4. Click "Update Category" to mark for change
5. Apply all changes in "Manage Categories" tab

#### Creating Rules
1. Go to "Rule Generator" tab
2. Select a category
3. Click "Generate Rules" to extract patterns
4. Review and modify suggested rules
5. Export as JSON for use in classifier

#### Analytics
- View category distribution
- Check confidence scores
- Identify problem areas
- Export comprehensive reports

## Interface Overview

### Tab 1: Overview
- Key metrics (total ads, categories, confidence)
- Interactive pie chart of categories
- Confidence distribution histogram

### Tab 2: Browse & Categorize
- Advanced filtering options
- Expandable ad cards
- Inline recategorization
- Bulk operations

### Tab 3: Manage Categories
- Current category list with stats
- Add new categories
- Apply pending changes
- Category merge/split tools

### Tab 4: Rule Generator
- Automatic rule extraction
- Manual rule editing
- Rule testing interface
- JSON export

### Tab 5: Analytics
- Performance metrics
- Trend analysis
- Confusion detection
- Report generation

## Tips for Effective Use

### 1. Start with Low Confidence
Filter for confidence < 0.5 to focus on ads that need attention

### 2. Use Search Effectively
Search for specific terms to find related ads quickly

### 3. Batch Similar Ads
Find ads with similar content and recategorize them together

### 4. Test Rules Before Export
Use the rule tester to ensure rules work as expected

### 5. Regular Reviews
Export analytics reports weekly to track improvements

## Workflow Example

```bash
# 1. Export unique ads (smaller file)
python src/scripts/export_unique_ads.py --limit 5000 --output review_batch.csv

# 2. Launch app
streamlit run src/streamlit_app/campaign_classifier_app.py

# 3. In app:
#    - Upload review_batch.csv
#    - Filter to confidence < 0.5
#    - Review and recategorize
#    - Generate rules for new patterns
#    - Export updated data

# 4. Update classifier config
python src/scripts/hybrid_campaign_classifier_m4.py \
    --config new_rules.json \
    --update-dynamodb
```

## Performance Optimization

- **File Size**: Use `export_unique_ads.py` to create manageable files
- **Loading Speed**: Limit records when loading from DynamoDB
- **Browser Memory**: Work with batches of 1000-5000 ads at a time

## Export Formats

- **CSV**: For Excel/Google Sheets
- **JSON**: For programmatic use
- **Analytics Report**: Comprehensive JSON with all metrics

## Troubleshooting

1. **Large Files**: Use export_unique_ads.py first
2. **Slow Loading**: Reduce limit or use filters
3. **Memory Issues**: Process in smaller batches
4. **DynamoDB Connection**: Ensure local instance is running

## Future Enhancements

- Multi-user support with change tracking
- A/B testing for rules
- ML model integration
- Real-time classification preview
- Audit trail for changes