# Streamlit App Usage Guide

## Quick Answers

### Which files to use?
The Streamlit app accepts these inputs:

1. **Classification Results CSV** (from hybrid classifier):
   - `hybrid_results.csv` - Full results (may be large)
   - `results_summary.csv` - Clean version from view_hybrid_results.py
   - `unique_low_confidence.csv` - Deduplicated low-confidence ads from export_unique_ads.py (RECOMMENDED)

2. **Campaign Config JSON** (optional):
   - `src/config/fb_ad_categorizer/campaign_config.json` - Current rules
   - `smart_improved_config.json` - Improved rules from smart_rule_improver.py

### Does it update the old rules file automatically?
**NO** - The Streamlit app does NOT automatically update your campaign_config.json file. It only:
- Generates rule suggestions based on your categorizations
- Exports rules as individual JSON files that you download
- You must manually integrate these into your main config

## Step-by-Step Usage

### 1. Prepare Your Data
First, create a manageable file with unique ads:

```bash
# Export unique low-confidence ads (recommended)
python src/scripts/export_unique_ads.py \
    --limit 5000 \
    --output streamlit_review.csv \
    --min-confidence 0.0 \
    --max-confidence 0.3
```

### 2. Launch the App
```bash
streamlit run src/streamlit_app/campaign_classifier_app.py
```

### 3. Load Your Data

In the sidebar:
1. Select **"Upload CSV File"**
2. Upload `streamlit_review.csv`
3. Optionally upload your current config JSON

### 4. Use the Five Tabs

#### 📊 Overview Tab
- See distribution of categories
- Check average confidence
- Identify problem areas (high "Other" %)

#### 🔍 Browse & Categorize Tab
**This is where you do manual categorization:**
1. Filter by:
   - Category (e.g., "Other")
   - Confidence range (e.g., 0.0-0.3)
   - Search terms
2. Review individual ads
3. Click "Change Category" to recategorize
4. Click "Apply All Changes" when done

#### 📋 Manage Categories Tab
- View all current categories
- Add new litigation types
- See category statistics

#### 🔧 Rule Generator Tab
**This generates rules from your categorizations:**
1. Select a category
2. Click "Generate Rules"
3. Review the suggested triggers/includes
4. Click "Export Rules as JSON"
5. Download the file (e.g., `AFFF_Products_Liability_rules.json`)

#### 📈 Analytics Tab
- See performance metrics
- Identify confusion pairs
- Export analytics report

## How to Apply Generated Rules

### The app generates individual rule files, NOT a complete config

1. **Download rules for each category:**
   ```
   AFFF_Products_Liability_rules.json
   Data_Breach_rules.json
   etc.
   ```

2. **Manually merge into your main config:**
   ```python
   # merge_rules.py
   import json
   
   # Load current config
   with open('src/config/fb_ad_categorizer/campaign_config.json', 'r') as f:
       config = json.load(f)
   
   # Load new rule
   with open('AFFF_Products_Liability_rules.json', 'r') as f:
       new_rule = json.load(f)
   
   # Update the specific category
   for i, item in enumerate(config):
       if item['LitigationName'] == new_rule['LitigationName']:
           config[i] = new_rule
           break
   
   # Save updated config
   with open('updated_campaign_config.json', 'w') as f:
       json.dump(config, f, indent=2)
   ```

3. **Test the updated config:**
   ```bash
   python src/scripts/hybrid_campaign_classifier_m4.py \
       --config updated_campaign_config.json \
       --limit 1000 \
       --save-results test_streamlit_rules.csv
   ```

## Typical Workflow

1. **Export low-confidence ads** → 2. **Upload to Streamlit** → 3. **Manually categorize "Other" ads** → 4. **Generate rules for improved categories** → 5. **Download individual rule JSONs** → 6. **Manually merge into main config** → 7. **Test improvements**

## Important Notes

- The app works with deduplicated data (unique Title/Body combinations)
- Changes are only applied within the app session
- You must export and manually integrate rule improvements
- The app does NOT connect to or update DynamoDB directly
- Generated rules are based on term frequency in categorized ads

## Alternative: Direct DynamoDB Loading

The app can load directly from DynamoDB:
1. Select "Load from DynamoDB" in sidebar
2. Set limit (e.g., 1000 records)
3. Click "Load Data"

Note: This loads raw data, not deduplicated, so file sizes may be large.

## Tips for Best Results

1. **Focus on high-volume misclassifications** - Start with categories that have many "Other" ads
2. **Review in batches** - Filter confidence 0.0-0.1, then 0.1-0.2, etc.
3. **Look for patterns** - If you see many similar ads, create a new category
4. **Test generated rules** - Always verify rules improve classification before full deployment
5. **Export your work** - Save categorized data regularly using the Export button