# Mistral Batch OCR Examples

## 📋 Overview

This document provides practical examples and workflows for using the Mistral Batch OCR script in various scenarios.

## 🎯 Basic Usage Examples

### Single Date Processing

Process all PDFs for a specific date:

```bash
# Process today's PDFs
python src/scripts/mistral_batch_ocr.py --date $(date +%Y%m%d)

# Process a specific date
python src/scripts/mistral_batch_ocr.py --date 20250115

# Process with custom settings
python src/scripts/mistral_batch_ocr.py \
  --date 20250115 \
  --batch-size 50 \
  --poll-interval 15
```

### Date Range Processing

Process multiple days efficiently:

```bash
# Process a week
python src/scripts/mistral_batch_ocr.py \
  --start-date 20250115 \
  --end-date 20250121

# Process a month
python src/scripts/mistral_batch_ocr.py \
  --start-date 20250101 \
  --end-date 20250131

# Process with optimized settings for large ranges
python src/scripts/mistral_batch_ocr.py \
  --start-date 20250101 \
  --end-date 20250107 \
  --batch-size 200 \
  --max-workers 8 \
  --poll-interval 30
```

### Monitoring and Status

Check job statuses and progress:

```bash
# Quick status check
python src/scripts/mistral_batch_ocr.py --show-jobs

# List all jobs
python src/scripts/mistral_batch_ocr.py --list-jobs

# Filter by status
python src/scripts/mistral_batch_ocr.py --list-jobs --status RUNNING
python src/scripts/mistral_batch_ocr.py --list-jobs --status FAILED

# Detailed job information
python src/scripts/mistral_batch_ocr.py \
  --job-id batch_abc123def456 \
  --show-status \
  --copy-url
```

## 🏭 Production Workflows

### Daily Processing Pipeline

Automated daily processing script:

```bash
#!/bin/bash
# daily_ocr_pipeline.sh

set -e

DATE=$(date +%Y%m%d)
LOG_FILE="logs/daily_ocr_${DATE}.log"

echo "Starting daily OCR processing for $DATE" | tee -a $LOG_FILE

# Check if data directory exists
if [ ! -d "data/$DATE/dockets" ]; then
    echo "No data directory found for $DATE" | tee -a $LOG_FILE
    exit 0
fi

# Count PDFs to process
PDF_COUNT=$(find data/$DATE/dockets -name "*.pdf" | wc -l)
echo "Found $PDF_COUNT PDFs to potentially process" | tee -a $LOG_FILE

# Run OCR processing
python src/scripts/mistral_batch_ocr.py \
  --date $DATE \
  --batch-size 100 \
  --poll-interval 10 \
  2>&1 | tee -a $LOG_FILE

if [ $? -eq 0 ]; then
    echo "OCR processing completed successfully for $DATE" | tee -a $LOG_FILE
    
    # Count generated MD files
    MD_COUNT=$(find data/$DATE/dockets -name "*.md" | wc -l)
    echo "Generated $MD_COUNT MD files" | tee -a $LOG_FILE
    
    # Send success notification (optional)
    # curl -X POST https://hooks.slack.com/... -d "{'text':'OCR completed for $DATE: $MD_COUNT files'}"
else
    echo "OCR processing failed for $DATE" | tee -a $LOG_FILE
    exit 1
fi
```

### Weekly Batch Processing

Process a full week of data:

```bash
#!/bin/bash
# weekly_ocr_batch.sh

# Calculate date range for current week
START_DATE=$(date -d "last monday" +%Y%m%d)
END_DATE=$(date -d "next sunday" +%Y%m%d)

echo "Processing week: $START_DATE to $END_DATE"

# High-performance settings for bulk processing
python src/scripts/mistral_batch_ocr.py \
  --start-date $START_DATE \
  --end-date $END_DATE \
  --batch-size 200 \
  --max-workers 8 \
  --poll-interval 30

# Generate summary report
echo "Weekly OCR Summary:"
for date in $(seq -f "%Y%m%d" $(date -d $START_DATE +%s) 86400 $(date -d $END_DATE +%s)); do
    if [ -d "data/$date/dockets" ]; then
        pdf_count=$(find data/$date/dockets -name "*.pdf" | wc -l)
        md_count=$(find data/$date/dockets -name "*.md" | wc -l)
        echo "$date: $md_count/$pdf_count PDFs processed"
    fi
done
```

### Incremental Processing

Process only new files since last run:

```bash
#!/bin/bash
# incremental_ocr.sh

LAST_RUN_FILE=".last_ocr_run"
CURRENT_TIME=$(date +%s)

# Get last run time or default to 24 hours ago
if [ -f $LAST_RUN_FILE ]; then
    LAST_RUN=$(cat $LAST_RUN_FILE)
else
    LAST_RUN=$((CURRENT_TIME - 86400))
fi

echo "Processing files modified since $(date -d @$LAST_RUN)"

# Find recently modified PDFs
find data/*/dockets -name "*.pdf" -newermt "$(date -d @$LAST_RUN)" | while read pdf; do
    md="${pdf%.pdf}.md"
    if [ ! -f "$md" ]; then
        echo "Need to process: $pdf"
        date_dir=$(echo $pdf | cut -d'/' -f2)
        echo $date_dir >> dates_to_process.tmp
    fi
done

# Process unique dates
if [ -f dates_to_process.tmp ]; then
    sort -u dates_to_process.tmp | while read date; do
        echo "Processing date: $date"
        python src/scripts/mistral_batch_ocr.py --date $date
    done
    rm dates_to_process.tmp
fi

# Update last run time
echo $CURRENT_TIME > $LAST_RUN_FILE
```

## 🔧 Configuration Examples

### High-Performance Configuration

For powerful systems with fast internet:

```bash
python src/scripts/mistral_batch_ocr.py \
  --start-date 20250101 \
  --end-date 20250131 \
  --batch-size 500 \
  --max-workers 16 \
  --poll-interval 5
```

### Conservative Configuration

For limited resources or slower connections:

```bash
python src/scripts/mistral_batch_ocr.py \
  --date 20250115 \
  --batch-size 25 \
  --max-workers 1 \
  --poll-interval 60
```

### Custom Data Directory

Using a different data location:

```bash
python src/scripts/mistral_batch_ocr.py \
  --data-dir /mnt/legal_data \
  --date 20250115 \
  --batch-size 100
```

## 📊 Monitoring Examples

### Real-time Progress Monitoring

Monitor processing in real-time:

```bash
#!/bin/bash
# monitor_ocr.sh

# Terminal 1: Run the processing
python src/scripts/mistral_batch_ocr.py --date 20250115 &
OCR_PID=$!

# Terminal 2: Monitor progress
while kill -0 $OCR_PID 2>/dev/null; do
    clear
    echo "=== OCR Progress Monitor ==="
    echo "Time: $(date)"
    echo
    
    # Show job status
    python src/scripts/mistral_batch_ocr.py --show-jobs
    
    echo
    echo "=== Recent Log Entries ==="
    tail -10 data/20250115/logs/mistral_ocr.log
    
    sleep 30
done

echo "OCR processing completed!"
```

### Progress Dashboard

Create a simple dashboard:

```bash
#!/bin/bash
# ocr_dashboard.sh

DATE=${1:-$(date +%Y%m%d)}

echo "OCR Dashboard for $DATE"
echo "========================"

# File counts
PDF_COUNT=$(find data/$DATE/dockets -name "*.pdf" 2>/dev/null | wc -l)
MD_COUNT=$(find data/$DATE/dockets -name "*.md" 2>/dev/null | wc -l)
COMPLETION_RATE=$((MD_COUNT * 100 / PDF_COUNT))

echo "PDFs found: $PDF_COUNT"
echo "MD files generated: $MD_COUNT"
echo "Completion rate: $COMPLETION_RATE%"
echo

# Job status
echo "Active Jobs:"
python src/scripts/mistral_batch_ocr.py --list-jobs --status RUNNING 2>/dev/null || echo "No active jobs"
echo

# Recent activity
echo "Recent Activity:"
if [ -f "data/$DATE/logs/mistral_ocr.log" ]; then
    tail -5 data/$DATE/logs/mistral_ocr.log | grep -E "(INFO|ERROR|WARNING)"
else
    echo "No log file found"
fi
```

## 🔄 Integration Examples

### Integration with Main Pipeline

Combine with existing LexGenius workflows:

```bash
#!/bin/bash
# integrated_pipeline.sh

DATE=${1:-$(date +%Y%m%d)}

echo "Running integrated pipeline for $DATE"

# Step 1: Run main data collection
echo "Step 1: Data collection..."
python src/main.py --params config/scraper.yml --date $DATE

# Step 2: OCR processing
echo "Step 2: OCR processing..."
python src/scripts/mistral_batch_ocr.py --date $DATE --batch-size 100

# Step 3: Data transformation
echo "Step 3: Data transformation..."
python src/main.py --params config/transform.yml --date $DATE

# Step 4: Report generation
echo "Step 4: Report generation..."
python src/main.py --params config/report.yml --date $DATE

echo "Pipeline completed for $DATE"
```

### Pre-processing Hook

Run OCR automatically after data collection:

```bash
#!/bin/bash
# post_scrape_hook.sh
# Add this to your scraper configuration

DATE=$1
COURT_ID=$2

echo "Post-scrape hook triggered for $DATE/$COURT_ID"

# Check if new PDFs were downloaded
NEW_PDFS=$(find data/$DATE/dockets/$COURT_ID -name "*.pdf" -newer .last_scrape_$COURT_ID 2>/dev/null | wc -l)

if [ $NEW_PDFS -gt 0 ]; then
    echo "Found $NEW_PDFS new PDFs, triggering OCR..."
    python src/scripts/mistral_batch_ocr.py --date $DATE &
fi

touch .last_scrape_$COURT_ID
```

## 📈 Performance Optimization Examples

### Batch Size Optimization

Find optimal batch size for your system:

```bash
#!/bin/bash
# optimize_batch_size.sh

TEST_DATE="20250115"
BATCH_SIZES=(25 50 100 200 500)

for size in "${BATCH_SIZES[@]}"; do
    echo "Testing batch size: $size"
    
    start_time=$(date +%s)
    
    python src/scripts/mistral_batch_ocr.py \
      --date $TEST_DATE \
      --batch-size $size \
      --max-workers 4 \
      > test_batch_${size}.log 2>&1
    
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    
    echo "Batch size $size completed in $duration seconds"
    
    # Extract statistics from log
    processed=$(grep "Completed:" test_batch_${size}.log | tail -1 || echo "0")
    echo "Results: $processed"
    echo "---"
done
```

### Parallel Processing Tuning

Optimize worker configuration:

```bash
#!/bin/bash
# tune_workers.sh

TEST_DATE="20250115"
WORKER_COUNTS=(1 2 4 8 16)

for workers in "${WORKER_COUNTS[@]}"; do
    echo "Testing with $workers workers"
    
    # Monitor system resources
    iostat -x 1 > iostat_${workers}w.log &
    IOSTAT_PID=$!
    
    python src/scripts/mistral_batch_ocr.py \
      --date $TEST_DATE \
      --max-workers $workers \
      --batch-size 100
    
    kill $IOSTAT_PID
    
    echo "Completed test with $workers workers"
    echo "Check iostat_${workers}w.log for resource usage"
    echo "---"
done
```

## 🛠️ Development and Testing

### Local Testing Setup

Set up a test environment:

```bash
#!/bin/bash
# setup_test_env.sh

# Create test data structure
mkdir -p test_data/20250115/dockets/{case_001,case_002,case_003}

# Create sample PDFs (mock files)
for case in case_001 case_002 case_003; do
    for doc in {001..005}; do
        echo "%PDF-1.4 Mock PDF content for testing" > test_data/20250115/dockets/$case/doc_$doc.pdf
    done
done

# Create some existing MD files to test skipping
echo "# Existing MD file" > test_data/20250115/dockets/case_001/doc_001.md

echo "Test environment created in test_data/"
echo "Use: python src/scripts/mistral_batch_ocr.py --data-dir test_data --date 20250115"
```

### API Testing

Test API connectivity and functionality:

```bash
#!/bin/bash
# test_api.sh

echo "Testing Mistral API connectivity..."

# Test 1: Basic API connection
python -c "
import os
from mistralai import Mistral

try:
    client = Mistral(api_key=os.environ['MISTRAL_API_KEY'])
    jobs = client.batch.jobs.list()
    print(f'✓ API connection successful. Found {len(jobs.data)} existing jobs.')
except Exception as e:
    print(f'✗ API connection failed: {e}')
    exit(1)
"

# Test 2: File upload capability
python -c "
import os
from mistralai import Mistral

client = Mistral(api_key=os.environ['MISTRAL_API_KEY'])
try:
    test_content = '{\"custom_id\": \"test\", \"body\": {\"messages\": [{\"role\": \"user\", \"content\": \"test\"}]}}'
    batch_data = client.files.upload(
        file={'file_name': 'test.jsonl', 'content': test_content.encode('utf-8')},
        purpose='batch'
    )
    print(f'✓ File upload successful. File ID: {batch_data.id}')
except Exception as e:
    print(f'✗ File upload failed: {e}')
"

echo "API tests completed"
```

### Quality Assurance

Validate processing results:

```bash
#!/bin/bash
# qa_check.sh

DATE=${1:-$(date +%Y%m%d)}

echo "Quality Assurance Check for $DATE"
echo "================================="

# Check 1: File pairs validation
echo "1. Checking PDF-MD file pairs..."
missing_md=0
for pdf in $(find data/$DATE/dockets -name "*.pdf"); do
    md="${pdf%.pdf}.md"
    if [ ! -f "$md" ]; then
        echo "Missing MD: $pdf"
        ((missing_md++))
    fi
done
echo "Missing MD files: $missing_md"

# Check 2: Content validation
echo "2. Checking MD file content..."
empty_md=0
for md in $(find data/$DATE/dockets -name "*.md"); do
    if [ ! -s "$md" ]; then
        echo "Empty MD: $md"
        ((empty_md++))
    elif ! grep -q "OCR Extracted Text" "$md"; then
        echo "Invalid format: $md"
    fi
done
echo "Empty/invalid MD files: $empty_md"

# Check 3: Processing completeness
pdf_count=$(find data/$DATE/dockets -name "*.pdf" | wc -l)
md_count=$(find data/$DATE/dockets -name "*.md" | wc -l)
completion_rate=$((md_count * 100 / pdf_count))

echo "3. Processing statistics:"
echo "Total PDFs: $pdf_count"
echo "Total MDs: $md_count"
echo "Completion rate: $completion_rate%"

if [ $completion_rate -eq 100 ]; then
    echo "✓ Processing completed successfully"
else
    echo "⚠ Processing incomplete"
fi
```

## 📚 Advanced Use Cases

### Selective Processing

Process only specific types of documents:

```bash
#!/bin/bash
# selective_processing.sh

DATE="20250115"

# Find PDFs matching specific patterns
find data/$DATE/dockets -name "*complaint*.pdf" -o -name "*motion*.pdf" | while read pdf; do
    md="${pdf%.pdf}.md"
    if [ ! -f "$md" ]; then
        echo "Processing legal document: $pdf"
        # Create temporary directory with just this file
        temp_dir="temp_selective_$$"
        mkdir -p $temp_dir/$DATE/dockets/temp
        cp "$pdf" $temp_dir/$DATE/dockets/temp/
        
        # Process just this file
        python src/scripts/mistral_batch_ocr.py \
          --data-dir $temp_dir \
          --date $DATE \
          --batch-size 1
        
        # Copy result back
        if [ -f "$temp_dir/$DATE/dockets/temp/$(basename $md)" ]; then
            cp "$temp_dir/$DATE/dockets/temp/$(basename $md)" "$md"
            echo "Completed: $md"
        fi
        
        # Cleanup
        rm -rf $temp_dir
    fi
done
```

### Parallel Date Processing

Process multiple dates simultaneously:

```bash
#!/bin/bash
# parallel_dates.sh

DATES=("20250115" "20250116" "20250117" "20250118" "20250119")

# Process each date in parallel
for date in "${DATES[@]}"; do
    {
        echo "Starting processing for $date"
        python src/scripts/mistral_batch_ocr.py \
          --date $date \
          --batch-size 50 \
          > logs/ocr_$date.log 2>&1
        echo "Completed processing for $date"
    } &
done

# Wait for all background jobs to complete
wait
echo "All dates processed"

# Generate summary
for date in "${DATES[@]}"; do
    if [ -f "logs/ocr_$date.log" ]; then
        completed=$(grep -c "Saved OCR result" logs/ocr_$date.log)
        echo "$date: $completed files processed"
    fi
done
```

These examples provide a comprehensive foundation for using the Mistral Batch OCR script in various real-world scenarios. Adapt them to your specific needs and environment!