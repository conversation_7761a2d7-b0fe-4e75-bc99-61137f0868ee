# Mistral Batch OCR User Guide

## 📖 Overview

The Mistral Batch OCR script provides automated PDF text extraction using Mistral's batch API for 50% cost savings. It processes PDFs in the LexGenius data structure and creates corresponding markdown files with high-quality extracted text.

## 🎯 Key Features

- **Batch API Processing**: Always uses batch API for 50% discount
- **Automated Discovery**: Finds PDFs in date-organized directories
- **Smart Skipping**: Automatically skips PDFs that already have MD files
- **Backup Directory Exclusion**: Automatically excludes `_bak` directories
- **Missing File Recovery**: Check and download specific missing results
- **Result Recovery**: Download results from completed batch jobs
- **Real-time Monitoring**: Live progress updates during batch processing
- **Job Management**: List, monitor, and download batch job results
- **Comprehensive Logging**: Detailed logs for debugging and auditing
- **High-Quality Output**: Structured markdown with proper page separation

## 🛠️ Prerequisites

### Environment Setup

1. **API Key Configuration**
   ```bash
   export MISTRAL_API_KEY="your_mistral_api_key_here"
   ```

2. **Dependencies Installation**
   ```bash
   conda install pyperclip pymupdf  # For clipboard and fallback functionality
   # Other dependencies should already be in environment.yml
   ```

3. **Data Structure**
   Ensure your data follows the expected structure:
   ```
   data/
   └── YYYYMMDD/
       └── dockets/
           ├── document_001.pdf
           ├── document_002.pdf
           └── document_001.md  # (if already processed)
   ```

## 🚀 Basic Usage

### Process Single Date (Batch API)

```bash
python src/scripts/mistral_batch_ocr.py --date 20250115
```

### Process Date Range (Batch API)

```bash
python src/scripts/mistral_batch_ocr.py --start-date 20250101 --end-date 20250131
```

### Recover Undownloaded Results

```bash
# Check for PDFs without MD files
python src/scripts/mistral_batch_ocr.py --check-missing

# Download specific missing result
python src/scripts/mistral_batch_ocr.py --download-result "paed_25_02781_BORCHICH_v_NORDISK_AS_et_al.pdf"

# Recover ALL undownloaded results from successful jobs
python src/scripts/mistral_batch_ocr.py --cancel-pending
```

### Legacy Batch Job Management

```bash
# List all batch jobs
python src/scripts/mistral_batch_ocr.py --list-jobs

# Cancel pending jobs and download completed results
python src/scripts/mistral_batch_ocr.py --cancel-pending

# Cancel pending jobs without downloading results
python src/scripts/mistral_batch_ocr.py --cancel-pending --no-download

# Filter jobs by status
python src/scripts/mistral_batch_ocr.py --list-jobs --status SUCCESS
```

### Job Details

```bash
# Show detailed job information
python src/scripts/mistral_batch_ocr.py --job-id abc123def456 --show-status

# Copy job URL to clipboard
python src/scripts/mistral_batch_ocr.py --job-id abc123def456 --show-status --copy-url
```

## ⚙️ Configuration Options

### Command Line Arguments

| Argument | Description | Default | Example |
|----------|-------------|---------|---------|
| `--date` | Single date to process | Today | `--date 20250115` |
| `--start-date` | Start of date range | None | `--start-date 20250101` |
| `--end-date` | End of date range | None | `--end-date 20250131` |
| `--data-dir` | Data directory path | `data` | `--data-dir /path/to/data` |
| `--check-missing` | Check for missing MD files | Flag | `--check-missing` |
| `--download-result` | Download specific result | Filename | `--download-result file.pdf` |

### Job Management Arguments

| Argument | Description | Usage |
|----------|-------------|-------|
| `--list-jobs` | List all batch jobs | Can combine with `--status` |
| `--status` | Filter by job status | `QUEUED`, `RUNNING`, `SUCCESS`, `FAILED` |
| `--cancel-pending` | Cancel pending jobs | Standalone or with `--no-download` |
| `--no-download` | Skip result download | Use with `--cancel-pending` |
| `--job-id` | Specific job ID | Combine with `--show-status` |
| `--show-status` | Show detailed job info | Requires `--job-id` |
| `--copy-url` | Copy job URL to clipboard | Combine with `--job-id` |

## 📊 Understanding the Output

### Processing Display

The script displays real-time processing status:

```
┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃                              Mistral OCR Status                                ┃
┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩
│ Metric                    │    Count │ Percentage │
├───────────────────────────┼──────────┼────────────┤
│ Total PDFs                │      150 │      100%  │
│ Skipped (MD exists)       │       25 │       16.7%│
│ Queued for processing     │      125 │       83.3%│
│ In progress               │        1 │        0.7%│
│ Completed                 │       60 │       40.0%│
│ Failed                    │        5 │        3.3%│
└───────────────────────────┴──────────┴────────────┘

                              Processing PDF 61/125                               
┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━┓
┃ Current File                                                ┃ Status        ┃
┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━┩
│ case_document_12345.pdf                                     │ Processing... │
└─────────────────────────────────────────────────────────────┴───────────────┘
```

### Legacy Batch Jobs Table

```
┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃                                All Batch Jobs                                   ┃
┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩
│ Job ID           │ Status  │ Model            │ Created        │ Metadata        │
├──────────────────┼─────────┼──────────────────┼────────────────┼─────────────────┤
│ abc123def456...  │ SUCCESS │ pixtral-large... │ 2025-01-15 14:30│ {"job_type":... │
│ def456ghi789...  │ RUNNING │ pixtral-large... │ 2025-01-15 15:45│ {"job_type":... │
│ ghi789jkl012...  │ FAILED  │ pixtral-large... │ 2025-01-15 16:20│ {"job_type":... │
└──────────────────┴─────────┴──────────────────┴────────────────┴─────────────────┘
```

## 📁 Output Files

### Generated MD Files

For each processed PDF, the script creates an MD file with:

```markdown
# OCR Extracted Text - document_123.pdf

**Source PDF:** document_123.pdf
**Processed:** 2025-01-15T14:30:45.123456
**Method:** Direct OCR API call
**Pages:** 15

---

--- Page 1 ---
[Page 1 content in structured markdown format]

--- Page 2 ---
[Page 2 content in structured markdown format]

...
```

### Fallback Extraction

If OCR API fails, PyMuPDF fallback creates:

```markdown
# OCR Extracted Text - document_123.pdf

**Source PDF:** document_123.pdf
**Processed:** 2025-01-15T14:30:45.123456
**Method:** Manual text extraction (PyMuPDF fallback)
**Pages:** 15

---

--- Page 1 ---
[Page 1 raw text content]

--- Page 2 ---
[Page 2 raw text content]

...
```

### Log Files

Comprehensive logs are saved to:
```
data/logs/mistral_ocr.log
```

Log entries include:
- File discovery and processing decisions
- OCR API calls and responses
- Fallback processing events
- Error messages and stack traces
- Performance metrics and timing information

## 🔄 Processing Workflow

### Batch API Processing

1. **Discovery Phase**
   - Scans specified date directories
   - Excludes `_bak` directories automatically
   - Identifies PDF files
   - Checks for existing MD files
   - Builds processing queue

2. **Batch Job Creation**
   - Encodes all PDFs as base64
   - Creates JSONL batch file
   - Uploads to Mistral
   - Submits batch job with metadata

3. **Monitoring & Download**
   - Monitors job progress in real-time
   - Downloads results when complete
   - Creates MD files in original PDF locations
   - Saves raw results for recovery

### Legacy Batch Job Management

1. **Job Discovery**
   - Lists all existing batch jobs
   - Identifies job status and metadata
   - Filters by status if specified

2. **Job Cancellation**
   - Cancels jobs with QUEUED or RUNNING status
   - Skips already completed or failed jobs
   - Provides detailed feedback on each operation

3. **Result Download**
   - Downloads output files from successful jobs
   - Saves raw results as JSONL files
   - Processes results into MD files automatically
   - Maps custom IDs back to original PDF files

## 🎛️ Advanced Usage

### Custom Data Directory

```bash
python src/scripts/mistral_batch_ocr.py \
  --date 20250115 \
  --data-dir /custom/path/to/data
```

### Batch Job Cleanup

```bash
# Cancel all pending jobs and download successful results
python src/scripts/mistral_batch_ocr.py --cancel-pending

# Cancel pending jobs without downloading (cleanup only)
python src/scripts/mistral_batch_ocr.py --cancel-pending --no-download

# Download results from specific successful job
python src/scripts/mistral_batch_ocr.py --job-id abc123def456 --show-status
```

### Monitoring Legacy Jobs

```bash
# Monitor all jobs
python src/scripts/mistral_batch_ocr.py --list-jobs

# Monitor only running jobs
python src/scripts/mistral_batch_ocr.py --list-jobs --status RUNNING

# Get detailed info about specific job
python src/scripts/mistral_batch_ocr.py \
  --job-id abc123def456 \
  --show-status \
  --copy-url
```

## 📈 Performance Considerations

### Batch API Performance

- **Cost savings**: 50% discount compared to direct API
- **Processing time**: Depends on queue, typically 5-30 minutes for large batches
- **High-quality output**: Structured markdown with proper formatting
- **Batch size**: Can process hundreds of PDFs in a single job
- **Parallel processing**: Mistral handles parallelization internally

### Resource Usage

- **Memory**: Low usage during monitoring
- **Network**: Single upload/download for entire batch
- **Disk**: Temporary JSONL file during upload
- **CPU**: Minimal usage, mostly waiting for job completion

### Recovery Benefits

- **Persistent results**: Batch results remain available after completion
- **Multiple downloads**: Can re-download results if needed
- **Partial recovery**: Can download specific files from completed batches

## 🔍 Monitoring and Debugging

### Real-time Monitoring

The script provides live updates showing:
- Overall processing statistics
- Current file being processed
- Success/failure counts
- Processing methods used (OCR vs fallback)

### Log Analysis

Check logs for detailed information:
```bash
tail -f data/logs/mistral_ocr.log
```

Common log patterns:
- `INFO: Successfully processed file.pdf -> file.md` - Successful OCR
- `INFO: Fallback extraction successful for file.pdf` - PyMuPDF used
- `ERROR: Error processing file.pdf` - Processing failures
- `WARNING: Directory does not exist` - Configuration issues

### Status Codes (Legacy Jobs)

| Status | Description | Action Required |
|--------|-------------|-----------------|
| `QUEUED` | Job waiting to start | Can cancel or wait |
| `RUNNING` | Job actively processing | Can cancel or monitor |
| `SUCCESS` | Job completed successfully | Download results |
| `FAILED` | Job failed | Check logs, results may be partial |
| `TIMEOUT_EXCEEDED` | Job timed out | Results may be incomplete |
| `CANCELLED` | Job was cancelled | No results available |

## 🚨 Best Practices

### Before Running

1. **Verify API key** is set and valid
2. **Check data structure** follows expected format
3. **Ensure sufficient disk space** for results
4. **Review existing MD files** to avoid reprocessing

### During Processing

1. **Monitor progress** through the live display
2. **Check logs** for any error patterns
3. **Don't interrupt** processing unless necessary
4. **Watch for fallback usage** in logs

### After Processing

1. **Verify results** by sampling MD files
2. **Check completion statistics** match expectations
3. **Review fallback usage** for quality assessment
4. **Clean up legacy batch jobs** if needed

### Legacy Job Management

1. **Regularly clean up** old batch jobs
2. **Download results** from successful jobs before cancelling
3. **Monitor job status** to identify patterns
4. **Cancel pending jobs** before running new processing

## 🔗 Integration

### With Other Scripts

The Mistral OCR script integrates well with other LexGenius components:

```bash
# Process court data, then run OCR
python src/main.py --params config/scraper.yml
python src/scripts/mistral_batch_ocr.py --date $(date +%Y%m%d)

# Generate reports with OCR-enhanced data
python src/main.py --params config/report.yml
```

### Automation Examples

```bash
#!/bin/bash
# Daily OCR processing script
DATE=$(date +%Y%m%d)

# Clean up old batch jobs first
python src/scripts/mistral_batch_ocr.py --cancel-pending

# Process new PDFs
python src/scripts/mistral_batch_ocr.py --date $DATE

if [ $? -eq 0 ]; then
    echo "OCR processing completed successfully for $DATE"
else
    echo "OCR processing failed for $DATE" >&2
    exit 1
fi
```

## 🆕 Migration from Batch Processing

If you have existing batch jobs from the previous version:

1. **List existing jobs**: `python src/scripts/mistral_batch_ocr.py --list-jobs`
2. **Download successful results**: `python src/scripts/mistral_batch_ocr.py --cancel-pending`
3. **Cancel remaining jobs**: `python src/scripts/mistral_batch_ocr.py --cancel-pending --no-download`
4. **Reprocess any remaining PDFs**: Use normal date-based processing

## 📚 Next Steps

- Review [API Reference](api_reference.md) for technical details
- Check [Troubleshooting Guide](troubleshooting.md) for common issues
- Explore [Examples](examples.md) for specific use cases
- Read [Mistral OCR API Documentation](https://docs.mistral.ai/capabilities/vision/) for API details