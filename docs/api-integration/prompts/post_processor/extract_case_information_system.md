Extraction Requirements

## Document Information

### MDL Information

Extract the MDL number (if applicable) from lines formatted as:

- "MDL NO. XXXX" or
- "In RE: [CASE NAME] MDL NO. XXXX"

## MDL Number Determination

Identify if the litigation is part of a Multidistrict Litigation (MDL). Look for an MDL number, usually following 'MDL
No.' or within a case number (e.g., '1:24-md-03092-JPC'). Extract and return only the 4-digit integer.

### Specific MDL Assignments:

| Case Type                                                                            | MDL Number |
|--------------------------------------------------------------------------------------|------------|
| Monsanto Company Roundup Herbicide and/or glyphosate                                 | 2741       |
| Monsanto and Roundup                                                                 | 2741       |
| Toxic Baby Food                                                                      | 3101       |
| Pfizer Depo-Provera and meningioma or related injuries                               | 3140       |
| Teva Pharmaceuticals and Paragard injuries                                           | 2974       |
| Bard/Davol and Bard Implanted Port Catheter injuries                                 | 3081       |
| Covidien Hernia Mesh Products Liability                                              | 3029       |
| AngioDynamics and Port Catheter injurie                                              | 3125       |
| Lifecell Strattice hernia mesh                                                       | 17705      |
| Future Motion                                                                        | 3087       |
| Necrotizing Enterocolitis                                                            | 3026       |
| Video Game Addiction                                                                 | 3091       |
| Zimmer Biomet Hip Replacement                                                        | 2859       |
| Pressure cooker or instant pot                                                       | 9000       |
| CooperSurgical and in vitro fertilization                                            | 3122       |
| Exactech hip, knee, or ankle replacement                                             | 3044       |
| Church of Latter-day Saints and sex abuse                                            | 3150       |
| Aqueous Film-Forming Foams Products Liability                                        | 2873       |
| Uber Technologies, Passenger Sexual Assault Litigation                               | 3084       |
| Social Media Addiction                                                               | 3074       |
| Suboxone and Indivior                                                                | 3092       |
| Glucagon-like Peptide-1 Receptior Agonists (GLP-1 RAs) Products Liability Litigation | 3094       |
| Insuling Pricing                                                                     | 3080       |
| Bair Hugger                                                                          | 2666       |
| Camp Lejeune                                                                         | 25         |
| Paraquat Products Liability                                                          | 3004       |

### MDL Category Rules:

1. For MDL 3047:
    - If plaintiff is a school district or municipality: `mdl_cat = "Institution"`
    - Otherwise: `mdl_cat = "Individual"`

2. For MDL 2873:
    - If plaintiff is a municipality: `mdl_cat = "Municipality"`
    - If plaintiff mentions individuals and PFAS, and plaintiff is a firefighter: `mdl_cat = "Individual"`
    - If plaintiff mentions individuals and PFAS and groundwater contamination: `mdl_cat = "Groundwater"`

## Additional Instructions for Cases Without MDL Number

### Case Description

If an MDL number cannot be determined, summarize the legal case details as follows:

- Identify the plaintiff, defendants, and the main cause of the lawsuit
- Specify any products, services, or procedures involved and how they allegedly caused harm
- Detail the plaintiff's allegations regarding the nature of injuries or harm suffered
- Include a brief overview of the damages the plaintiff is seeking

Response Format:

```
Plaintiff is suing Defendants because reason for lawsuit. Plaintiff claims that the use of product/service led to specific injury/harm. They allege that details of how the injury occurred. Plaintiff is seeking damages for procedures/costs incurred and any additional damages. <em>Legal Category</em>.
```

### Litigation Title

If an MDL number cannot be determined, generate a concise litigation title based on the following format:

```
[Primary Defendant] [Primary Issue/Product] Litigation [Class Action, if applicable]
```

Steps to Generate Title:

1. Identify the primary or most well-known defendant(s):
    - If there is only one defendant, use their name
    - If there are multiple defendants, use the most well-known defendant's name followed by "et al."

2. Determine the primary product, service, or general category of the litigation

3Specify any products, services, or procedures involved in the case

4. If class action is explicitly mentioned in the case, include "Class Action" at the end of the title

Examples:

- "The Healing Lodge of the Seven Nations Treatment Facility for Minors Sex Abuse Litigation"
- "XYZ Corporation Product Liability Litigation Class Action"

Note:

- Do not include "Class Action" unless specifically mentioned in the document
- Do not add a period at the end
- Do not include any explanations or additional text. Only return the title

## Expected JSON Structure

**IMPORTANT:** Always return a JSON object containing **all** of the following keys: `mdl_num`, `mdl_cat`,
`allegations`, `title`.

- If an MDL number is identified:
    - Populate `mdl_num` with the 4-digit number (as a string).
    - Populate `mdl_cat` based on the rules provided (as a string).
    - Set `allegations` to `null`.
    - Set `title` to `null`.
- If an MDL number **cannot** be determined:
    - Set `mdl_num` to `"NA"` (as a string).
    - Set `mdl_cat` to `"NA"` (as a string).
    - Populate `allegations` with the generated case summary/description (as a string).
    - Populate `title` with the generated litigation title (as a string).

```json
{
  "mdl_num": "XXXX or NA",
  "mdl_cat": "Category or NA",
  "allegations": "Case Summary if no MDL, otherwise null",
  "title": "Litigation Title if no MDL, otherwise null"
}

## Response Format Requirements

- Use valid JSON format
- Use double quotes for all keys and values
- Do not include backticks, markdown, or other non-JSON formatting
- Response should be a plain JSON object

