**Assembly Step (Application Logic - No LLM Prompts)**

After running these four LLM steps:

1.  Your application checks the output of Step 1. If `classification_needed` is `false`, it generates the minimal "General Advertising" JSON (`{"Category": "General Advertising", "LitigationName": "General Advertising", ... other fields null or default}`) and finishes.
2.  If `classification_needed` is `true`, your application proceeds through Steps 2, 3, and 4.
3.  Your application takes the JSON outputs from Step 2 (`defendant_company`, `product_service`, `issues`, `mdl_number`), Step 3 (`category`, `litigation_type`), and Step 4 (`litigation_name`).
4.  It assembles these pieces into the final, complete JSON structure required by the original prompt:

    ```json
    {
      "Category": "{output_from_step3.category}",
      "Company": "{output_from_step2.defendant_company}",
      "Product": "{output_from_step2.product_service}",
      "Injuries": {output_from_step2.issues}, // Use the list directly
      "Litigation_Type": "{output_from_step3.litigation_type}",
      "LitigationName": "{output_from_step4.litigation_name}",
      "MdlNumber": "{output_from_step2.mdl_number}"
    }
    ```

This multi-step approach breaks down the complexity, improves focus for the LLM at each stage, and should yield more reliable and accurate results. Remember to handle potential errors or unexpected outputs from the LLM at each step in your application code.

**Important Integration Note:**

*   **MDL Numbers:** in new Step 0 prompt.
*   **Workflow:**
    1.  Run **Step 0** (`identify_litigation`).
    2.  If Step 0 returns a specific `LitigationName` (not "No Specific Litigation Identified"), your application can potentially *skip* Steps 1-4 and directly use the known information (like `MdlNum`, `Category`, etc.) associated with that `LitigationName` from your internal list/database to construct the final JSON output. This makes the process much more efficient for known cases.
    3.  If Step 0 returns `"No Specific Litigation Identified"`, then proceed with **Step 1 (Triage)** as previously defined. The subsequent steps (2, 3, 4) will then attempt to classify the ad based on general rules rather than a pre-defined litigation.
*   **Step 2 Revision:** Because Step 0 now handles matching known litigations (including their MDL numbers), Step 2 (`extract_entities`) should be revised again to **REMOVE** the `mdl_number` field entirely from its output. It should only extract `defendant_company`, `product_service`, and `issues`.

Let me know if you want the revised Step 2 prompts without the `mdl_number`.