#!/bin/bash
#
# AFFF Filings Analysis Pipeline
# This script runs all steps to analyze AFFF filings data
#

set -e  # Exit on error

echo "================================================"
echo "AFFF FILINGS ANALYSIS PIPELINE"
echo "================================================"
echo ""

# Check if we're in the project root
if [ ! -f "AFFF-Filings-Case-Shell.csv" ]; then
    echo "Error: AFFF-Filings-Case-Shell.csv not found in current directory"
    echo "Please run this script from the project root directory"
    exit 1
fi

# Step 1: Parse the raw CSV data
echo "Step 1: Parsing AFFF filings CSV..."
echo "-----------------------------------"
python test_files/demo_afff_parser.py
echo ""

# Step 2: Merge attorney data with law firm information
echo "Step 2: Merging attorney data with law firms..."
echo "-----------------------------------------------"
python test_files/merge_attorney_data.py
echo ""

# Step 3: Analyze filings by law firm and date
echo "Step 3: Analyzing filings by law firm and date..."
echo "-------------------------------------------------"
python test_files/analyze_filings.py
echo ""

# Step 4: Generate visualization charts
echo "Step 4: Creating visualization charts..."
echo "---------------------------------------"
python test_files/visualize_filings.py
echo ""

echo "================================================"
echo "ANALYSIS COMPLETE!"
echo "================================================"
echo ""
echo "Generated files:"
echo "  - test_files/parsed_afff_filings.csv"
echo "  - test_files/attorney_filings.json"
echo "  - test_files/attorney_comprehensive_data.json"
echo "  - test_files/filing_analysis.json"
echo "  - test_files/filing_analysis_charts.png"
echo "  - test_files/filing_summary_stats.json"
echo ""
echo "Key Statistics:"
# Display summary stats
if [ -f "test_files/filing_summary_stats.json" ]; then
    echo "  - Top Firm: $(python -c "import json; data=json.load(open('test_files/filing_summary_stats.json')); print(f\"{data['top_firm']['name']} ({data['top_firm']['filings']} filings - {data['top_firm']['percentage']}%)\")")"
    echo "  - Busiest Month: $(python -c "import json; data=json.load(open('test_files/filing_summary_stats.json')); print(f\"{data['busiest_month']['month']} ({data['busiest_month']['count']} filings)\")")"
    echo "  - Busiest Day: $(python -c "import json; data=json.load(open('test_files/filing_summary_stats.json')); print(f\"{data['busiest_day']['date']} ({data['busiest_day']['count']} filings)\")")"
    echo "  - Average per Attorney: $(python -c "import json; data=json.load(open('test_files/filing_summary_stats.json')); print(f\"{data['average_filings_per_attorney']} filings\")")"
fi
echo ""