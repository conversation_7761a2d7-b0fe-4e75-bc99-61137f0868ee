{"permissions": {"allow": ["<PERSON><PERSON>(chmod:*)", "Bash(grep:*)", "Bash(ls:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(pip show:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(sed:*)", "mcp__fetch-mcp__fetch_html", "mcp__fetch-mcp__fetch_markdown", "mcp__filesystem__write_file", "mcp__sequential-thinking__sequentialthinking", "Bash(./run_pipeline:*)", "Bash(rm:*)", "<PERSON><PERSON>(touch:*)", "Bash(rg:*)", "Bash(grep:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(python3:*)", "Bash(streamlit run:*)", "Bash(sqlite3:*)", "<PERSON><PERSON>(ollama list:*)", "<PERSON><PERSON>(ollama create:*)", "Bash(find:*)", "<PERSON><PERSON>(true)", "Bash(cp:*)", "Bash(aws dynamodb scan:*)", "<PERSON><PERSON>(jq:*)", "<PERSON><PERSON>(ollama search:*)", "mcp__fetch__fetch_html", "Bash(mamba install:*)", "Bash(kill:*)", "mcp__fetch__fetch_markdown", "<PERSON><PERSON>(timeout:*)", "mcp__filesystem__create_directory", "mcp__fetch__fetch_txt", "WebFetch(domain:docs.mistral.ai)", "Bash(claude mcp add ide:*)", "Bash(claude mcp add fetch:*)", "<PERSON><PERSON>(claude mcp:*)", "<PERSON><PERSON>(claude config --help)", "<PERSON><PERSON>(claude config list)", "<PERSON><PERSON>(diff:*)", "<PERSON><PERSON>(crontab:*)", "Bash(git add:*)", "WebFetch(domain:github.com)", "mcp__filesystem__read_file", "<PERSON><PERSON>(cat:*)", "Bash(/opt/homebrew/bin/uv run --project /Users/<USER>/PycharmProjects/quick-data-mcp/quick-data-mcp python /Users/<USER>/PycharmProjects/quick-data-mcp/quick-data-mcp/main.py)", "<PERSON><PERSON>(cut:*)", "Bash(awk:*)", "Bash(awk -F',' 'NR > 1 && $6 == \"\" {count++} END {print \"Rows without attorney names:\", count}' parsed_afff_filings.csv)", "Bash(aws s3 ls:*)", "Bash(aws cloudfront:*)", "mcp__filesystem__list_directory", "<PERSON><PERSON>(env)", "Bash(npm root:*)", "Ba<PERSON>(strace:*)", "Bash(aws dynamodb list-tables:*)", "Bash(node:*)", "Bash(uvx:*)", "Bash(/Users/<USER>/PycharmProjects/fb-auto-categorizer/run_optimization.sh:*)", "Bash(mamba search:*)", "Bash(npx --version)", "Bash(npx:*)", "Bash(/Users/<USER>/.nvm/versions/node/v23.10.0/bin/npx --version)", "Bash(/Users/<USER>/.nvm/versions/node/v23.10.0/bin/npx -y @modelcontextprotocol/server-memory)", "Bash(PATH=\"/Users/<USER>/.nvm/versions/node/v23.10.0/bin:/opt/homebrew/bin:/usr/local/bin:/usr/bin:/bin\" NODE_PATH=\"/Users/<USER>/.nvm/versions/node/v23.10.0/lib/node_modules\" /Users/<USER>/.nvm/versions/node/v23.10.0/bin/npx -y @modelcontextprotocol/server-filesystem /Users/<USER>/PycharmProjects/)", "Bash(./test_mcp_setup.sh:*)", "mcp__ide__getDiagnostics", "Bash(npm ls:*)", "Bash(npm install:*)", "Bash(/Users/<USER>/.nvm/versions/node/v23.10.0/bin/node /Users/<USER>/.nvm/versions/node/v23.10.0/lib/node_modules/@modelcontextprotocol/server-filesystem/dist/index.js /Users/<USER>/PycharmProjects/)", "Bash(/Users/<USER>/.nvm/versions/node/v23.10.0/bin/npx @modelcontextprotocol/server-filesystem --help 2 >& 1)", "Bash(xattr:*)", "Bash(claude --version)", "<PERSON><PERSON>(claude:*)"], "deny": []}, "enableAllProjectMcpServers": false, "disabledMcpjsonServers": ["test-server"]}