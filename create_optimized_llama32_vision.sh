#\!/bin/bash
# Create optimized llama32-vision model for OCR

# Pull the base model if not already present
ollama pull llama32-vision:11b-instruct-q5_K_M

# Create a custom model with OCR-specific parameters
cat > Modelfile.llama32-vision-ocr << 'MODELFILE'
FROM llama32-vision:11b-instruct-q5_K_M

# OCR-optimized parameters
PARAMETER temperature 0.1
PARAMETER top_p 0.9
PARAMETER repeat_penalty 1.1
PARAMETER num_ctx 8192
PARAMETER num_predict 4096

# System prompt for OCR
SYSTEM You are an OCR system. Extract all text from images accurately. Output only the text found in the image, preserving layout when possible. If no text is found, respond with "NO_TEXT_FOUND".
MODEL<PERSON>LE

# Create the optimized model
ollama create llama32-vision-ocr-optimized -f Modelfile.llama32-vision-ocr

echo "Created optimized model: llama32-vision-ocr-optimized"
