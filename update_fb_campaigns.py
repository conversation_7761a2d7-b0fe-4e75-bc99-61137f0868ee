#!/usr/bin/env python3
"""
<PERSON>ript to update FBAdArchive DynamoDB table with campaign names using vector_clusterer.py

This script:
1. Scans the entire FBAdArchive table
2. Uses vector_clusterer to apply rule-based campaign categorization
3. Updates campaign field based on rules from campaign_config.json
4. Updates summary field if it contains NA/null/None/"summary generation failed"/"skipped" and matches a rule
5. Sets campaign to 'Other' if no rules match

Usage:
    python update_fb_campaigns.py --dry-run  # Output to CSV without updating DB
    python update_fb_campaigns.py            # Actually update the database
"""

import asyncio
import json
import logging
import re
import sys
import argparse
from pathlib import Path
from typing import Dict, Any, List, Optional
import pandas as pd
from tqdm import tqdm
from datetime import datetime
import os

# Add the project root and src to the Python path BEFORE any imports
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / 'src'))
sys.path.insert(0, str(project_root))

# Set PYTHONPATH environment variable as well
os.environ['PYTHONPATH'] = f"{project_root / 'src'}:{project_root}:{os.environ.get('PYTHONPATH', '')}"

# Suppress the transformer import error by redirecting stderr temporarily
import io
old_stderr = sys.stderr
sys.stderr = io.StringIO()

try:
    # Import from lib directly since we added src to path
    from lib.fb_ads.vector_clusterer import VectorClusterer
    from lib.fb_archive_manager import FBAdArchiveManager
finally:
    # Restore stderr
    sys.stderr = old_stderr

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# Summary patterns that indicate failed/missing summaries (case insensitive)
FAILED_SUMMARY_PATTERNS = [
    r'^\s*na\s*$',
    r'^\s*null\s*$', 
    r'^\s*none\s*$',
    r'summary\s+generation\s+failed',
    r'skipped',
    r'^\s*$'  # Empty string
]

def is_failed_summary(summary: Any) -> bool:
    """Check if summary indicates a failed generation"""
    if summary is None:
        return True
    
    summary_str = str(summary).strip().lower()
    
    for pattern in FAILED_SUMMARY_PATTERNS:
        if re.search(pattern, summary_str, re.IGNORECASE):
            return True
    
    return False

async def main(dry_run: bool = False):
    """Main function to update FBAdArchive with campaign names"""
    
    # Configuration
    config = {
        'use_local_dynamodb': True,
        'local_dynamodb_port': 8000,
        'dynamodb_table': 'FBAdArchive',
        'aws_region': 'us-east-1'
    }
    
    # Paths to configuration files
    campaign_config_path = project_root / 'src' / 'config' / 'fb_ad_categorizer' / 'campaign_config.json'
    campaign_skip_terms_path = project_root / 'src' / 'config' / 'fb_ad_categorizer' / 'campaign_skip_terms.json'
    
    logger.info(f"Running in {'DRY RUN' if dry_run else 'LIVE UPDATE'} mode")
    logger.info("Initializing FBAdArchiveManager...")
    
    # Initialize FBAdArchiveManager
    fb_archive = FBAdArchiveManager(
        config=config,
        use_local=config['use_local_dynamodb']
    )
    
    logger.info("Initializing VectorClusterer in rules-only mode...")
    
    # Initialize VectorClusterer in rules-only mode
    try:
        vector_clusterer = VectorClusterer(
            config=config,
            session=None,  # aiohttp session required even if not used
            campaign_config_path=str(campaign_config_path),
            campaign_skip_terms_path=str(campaign_skip_terms_path),
            embedding_stop_terms_path=None,  # Not needed for rules-only
            use_local=config['use_local_dynamodb'],
            rules_only=True  # Only use rules, no vector embeddings
        )
    except Exception as e:
        logger.error(f"Failed to initialize VectorClusterer: {e}")
        return
    
    logger.info("Scanning FBAdArchive table...")
    
    # Scan the entire table
    try:
        # scan_table returns a generator, so we need to convert to list
        logger.info("Starting table scan (this may take a while for large tables)...")
        items = []
        scan_count = 0
        for item in fb_archive.scan_table():
            items.append(item)
            scan_count += 1
            if scan_count % 1000 == 0:
                logger.info(f"Scanned {scan_count} items so far...")
        logger.info(f"Found {len(items)} items in FBAdArchive table")
    except Exception as e:
        logger.error(f"Failed to scan table: {e}")
        return
    
    if not items:
        logger.warning("No items found in table")
        return
    
    # Convert to DataFrame for easier handling
    df = pd.DataFrame(items)
    
    # Ensure required columns exist
    required_cols = ['AdArchiveID', 'Title', 'Body']
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        logger.error(f"Missing required columns: {missing_cols}")
        return
    
    # Add campaign column if it doesn't exist
    if 'campaign' not in df.columns:
        df['campaign'] = None
    
    # For dry run, find unique Title/Body combinations
    if dry_run:
        logger.info("Finding unique Title/Body combinations...")
        # Create a hash of Title+Body for grouping
        df['title_body_hash'] = df.apply(
            lambda row: hash(f"{row.get('Title', '')}|{row.get('Body', '')}"), 
            axis=1
        )
        
        # Group by the hash and keep track of all AdArchiveIDs for each unique combination
        unique_groups = df.groupby('title_body_hash').agg({
            'AdArchiveID': lambda x: list(x),
            'Title': 'first',
            'Body': 'first',
            'Summary': 'first',
            'campaign': 'first'
        }).reset_index()
        
        logger.info(f"Found {len(unique_groups)} unique Title/Body combinations from {len(df)} total ads")
        
        # Process unique combinations
        results = []
        for idx, group in tqdm(unique_groups.iterrows(), total=len(unique_groups), 
                             desc="Processing unique combinations"):
            # Create ad_dict for vector_clusterer
            ad_dict = {
                'title': str(group['Title'] or ''),
                'body': str(group['Body'] or '')
            }
            
            # Apply rule-based categorization
            vector_clusterer.get_category_for_ad_data(ad_dict)
            
            # Get results
            new_campaign = ad_dict.get('campaign', 'Other')
            original_summary = group['Summary'] or ''
            
            # Check if summary should be updated
            new_summary = original_summary
            if new_campaign != 'Other' and is_failed_summary(original_summary):
                new_summary = ad_dict.get('summary', original_summary)
            
            # Add only one result per unique Title/Body combination
            # Use the first AdArchiveID as a representative
            representative_ad_id = group['AdArchiveID'][0] if isinstance(group['AdArchiveID'], list) else group['AdArchiveID']
            
            results.append({
                'AdArchiveID': representative_ad_id,
                'Campaign': new_campaign,
                'Summary': new_summary,
                'Title': group['Title'],
                'Body': group['Body']
            })
        
        # Create output DataFrame and save to CSV
        output_df = pd.DataFrame(results)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"fb_campaigns_dry_run_{timestamp}.csv"
        output_df.to_csv(output_file, index=False)
        
        logger.info(f"\nDry run complete! Results saved to: {output_file}")
        
        # Print summary statistics
        campaign_counts = output_df['Campaign'].value_counts()
        logger.info("\nCampaign distribution:")
        for campaign, count in campaign_counts.items():
            logger.info(f"  {campaign}: {count}")
        
    else:
        # Live update mode
        # Track updates
        updates_made = 0
        summary_updates = 0
        campaign_counts = {}
        
        logger.info("Processing ads with vector_clusterer rules...")
        
        # Process each item
        for idx, row in tqdm(df.iterrows(), total=len(df), desc="Processing ads"):
            ad_id = row['AdArchiveID']
            
            # Create ad_dict in the format expected by vector_clusterer
            # Only pass Title and Body to vector_clusterer
            ad_dict = {
                'title': str(row.get('Title', '')),
                'body': str(row.get('Body', ''))
            }
            
            # Get the original values from the row
            original_campaign = row.get('campaign')
            original_summary = row.get('Summary', '')
            
            # Apply rule-based categorization
            # This modifies ad_dict in-place to add 'campaign' and possibly 'summary'
            vector_clusterer.get_category_for_ad_data(ad_dict)
            
            # Check if we need to update
            update_needed = False
            update_fields = {}
            
            # Check campaign update
            new_campaign = ad_dict.get('campaign', 'Other')
            if new_campaign != original_campaign:
                update_fields['campaign'] = new_campaign
                update_needed = True
                
                # Track campaign counts
                campaign_counts[new_campaign] = campaign_counts.get(new_campaign, 0) + 1
            
            # Check summary update (only if failed summary and rule matched)
            if new_campaign != 'Other' and is_failed_summary(original_summary):
                # If a rule matched and summary was failed, update with rule name
                # vector_clusterer will have added 'summary' to ad_dict if a rule matched
                new_summary = ad_dict.get('summary', original_summary)
                if new_summary != original_summary:
                    update_fields['Summary'] = new_summary
                    summary_updates += 1
                    update_needed = True
            
            # Update DynamoDB if needed
            if update_needed:
                try:
                    # Update the item in DynamoDB
                    update_expression_parts = []
                    expression_values = {}
                    
                    for field, value in update_fields.items():
                        attr_name = f":{field.lower()}"
                        update_expression_parts.append(f"{field} = {attr_name}")
                        expression_values[attr_name] = value
                    
                    update_expression = "SET " + ", ".join(update_expression_parts)
                    
                    await asyncio.to_thread(
                        fb_archive.table.update_item,
                        Key={'AdArchiveID': ad_id},
                        UpdateExpression=update_expression,
                        ExpressionAttributeValues=expression_values
                    )
                    
                    updates_made += 1
                    
                    if updates_made % 100 == 0:
                        logger.info(f"Updated {updates_made} items so far...")
                        
                except Exception as e:
                    logger.error(f"Failed to update item {ad_id}: {e}")
        
        # Print summary
        logger.info("\n" + "="*50)
        logger.info("UPDATE SUMMARY")
        logger.info("="*50)
        logger.info(f"Total items processed: {len(df)}")
        logger.info(f"Total updates made: {updates_made}")
        logger.info(f"Summary fields updated: {summary_updates}")
        logger.info("\nCampaign distribution:")
        
        # Sort campaign counts by value
        sorted_campaigns = sorted(campaign_counts.items(), key=lambda x: x[1], reverse=True)
        for campaign, count in sorted_campaigns:
            logger.info(f"  {campaign}: {count}")
        
        logger.info("\nUpdate complete!")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Update FBAdArchive with campaign names")
    parser.add_argument('--dry-run', action='store_true', 
                       help='Run in dry mode - output CSV without updating database')
    args = parser.parse_args()
    
    asyncio.run(main(dry_run=args.dry_run))