**Objective**: I would like to be able to skip downloading PDFs for certain cases.

**Scenario**: I have a case where I want to skip downloading the PDF and only download the HTML. This is because the PDF is not available and the HTML is a better source of information.

**Example**:
- Case: "<PERSON> v. <PERSON>"
- Court: "New Jersey District Court"
- Attorney: "<PERSON>"
- Law Firm: "<PERSON>"
- Flags: ["MDL2738"] 


**Configuration File (@src/config/pacer/ignore_download.json)**

```
   [                                                                                                                                                                     
       {                                                                                                                                                                 
           "court_id": "njd",                                                                                                                                            
           "attorney_name": "<PERSON>",                                                                                                                            
           "law_firm": "<PERSON>",                                                                                                                                  
           "flags": ["MDL2738"],                                                                                                                                         
           "report_law_firm": "<PERSON> ; OnderLaw LLC",                                                                                            
           "mdl_num": "2738"                                                                                                                                             
       },                                                                                                                                                                
       ...                                                                                                                                                               
   ]                                                                                                                                                                     
   ```

When I refer to the data dict, use the relevant dict name that is being used in the pacer, data_transformer, module...

**Docket Processor Logic (@src/lib/pacer/docket_processor.py)**

When processing a case:

1. Check if the case matches any entry in ignore_download.json where:
    - `court` == "njd"
    - `law_firm` == "<PERSON>" (case-insensitive)
    - `attorney_name` == "<PERSON>ubbs" (case-insensitive)
    - One of the `flags` == "MDL2738"

2. If matched:
    - Download only the HTML (skip PDF download)
    - Add `"html_only": True` to the case information dictionary
    - Title-case the `versus` field (e.g., "JOHN DOE v. JANE ROE" -> "John Doe v. Jane Roe")
    - Title-case each plaintiff in the `plaintiff` list (e.g., ["JAYCE WILSON"] -> ["Jayce Wilson"])
    - Set `s3_link` to the full CDN URL using `s3_html` (e.g., "/20250529/html/file.html" -> "https://cdn.lexgenius.ai/20250529/html/file.html")

**Data Transformer Logic (src/lib/data_transformer/)**

For cases where `"html_only": True`:

1. Skip PDF extraction and LLM processing
2. Assign the following values from the matching ignore_download entry:
   ```                                                                                                                                                                      
   data["law_firm"] = ignore_download["report_law_firm"]                                                                                                                 
   data["s3_link"] = data["s3_html"]  # Use the HTML S3 link                                                                                                                        
   data["mdl_num"] = ignore_download["mdl_num"]
    ```
3. Proceed to uploading to dynamodb.

**Implementation Notes:**
1. The matching should be case-insensitive for attorney names and law firms
2. The flags array should support multiple values for future flexibility
3. The html_only flag should be added to the case dict only when all conditions are met
4. The original case information should still be included in the report, just with the specified fields overridden